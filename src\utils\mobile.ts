// 移动端检测和适配工具函数

/**
 * 检测是否为移动设备
 */
export const isMobile = (): boolean => {
  const userAgent = navigator.userAgent.toLowerCase()
  const mobileKeywords = [
    'android', 'iphone', 'ipad', 'ipod', 'blackberry', 
    'windows phone', 'mobile', 'tablet'
  ]
  return mobileKeywords.some(keyword => userAgent.includes(keyword))
}

/**
 * 检测是否为触摸设备
 */
export const isTouchDevice = (): boolean => {
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0
}

/**
 * 获取屏幕尺寸信息
 */
export const getScreenInfo = () => {
  return {
    width: window.innerWidth,
    height: window.innerHeight,
    ratio: window.devicePixelRatio || 1,
    orientation: window.innerWidth > window.innerHeight ? 'landscape' : 'portrait'
  }
}

/**
 * 检测是否为小屏幕设备
 */
export const isSmallScreen = (): boolean => {
  return window.innerWidth <= 768
}

/**
 * 检测是否为超小屏幕设备
 */
export const isExtraSmallScreen = (): boolean => {
  return window.innerWidth <= 480
}

/**
 * 防抖函数 - 用于优化触摸事件
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func.apply(null, args), wait)
  }
}

/**
 * 节流函数 - 用于优化滚动事件
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func.apply(null, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

/**
 * 触摸手势检测
 */
export class TouchGestureDetector {
  private startX: number = 0
  private startY: number = 0
  private endX: number = 0
  private endY: number = 0
  private startTime: number = 0
  private endTime: number = 0

  constructor(private element: HTMLElement) {
    this.bindEvents()
  }

  private bindEvents() {
    this.element.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: true })
    this.element.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: true })
  }

  private handleTouchStart(e: TouchEvent) {
    const touch = e.touches[0]
    this.startX = touch.clientX
    this.startY = touch.clientY
    this.startTime = Date.now()
  }

  private handleTouchEnd(e: TouchEvent) {
    const touch = e.changedTouches[0]
    this.endX = touch.clientX
    this.endY = touch.clientY
    this.endTime = Date.now()
    this.detectGesture()
  }

  private detectGesture() {
    const deltaX = this.endX - this.startX
    const deltaY = this.endY - this.startY
    const deltaTime = this.endTime - this.startTime
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)

    // 检测点击
    if (distance < 10 && deltaTime < 300) {
      this.onTap?.()
      return
    }

    // 检测滑动
    if (distance > 30) {
      const angle = Math.atan2(deltaY, deltaX) * 180 / Math.PI
      
      if (Math.abs(angle) < 45) {
        this.onSwipeRight?.()
      } else if (Math.abs(angle) > 135) {
        this.onSwipeLeft?.()
      } else if (angle > 45 && angle < 135) {
        this.onSwipeDown?.()
      } else if (angle < -45 && angle > -135) {
        this.onSwipeUp?.()
      }
    }
  }

  // 手势回调函数
  onTap?: () => void
  onSwipeLeft?: () => void
  onSwipeRight?: () => void
  onSwipeUp?: () => void
  onSwipeDown?: () => void

  destroy() {
    this.element.removeEventListener('touchstart', this.handleTouchStart.bind(this))
    this.element.removeEventListener('touchend', this.handleTouchEnd.bind(this))
  }
}

/**
 * 移动端视口适配
 */
export const setupViewport = () => {
  // 设置视口元标签
  let viewport = document.querySelector('meta[name="viewport"]') as HTMLMetaElement
  if (!viewport) {
    viewport = document.createElement('meta')
    viewport.name = 'viewport'
    if (document.head) {
      document.head.appendChild(viewport)
    }
  }
  
  viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover'

  // 防止iOS Safari缩放
  document.addEventListener('gesturestart', (e) => {
    e.preventDefault()
  })

  // 防止双击缩放
  let lastTouchEnd = 0
  document.addEventListener('touchend', (e) => {
    const now = Date.now()
    if (now - lastTouchEnd <= 300) {
      e.preventDefault()
    }
    lastTouchEnd = now
  }, false)
}

/**
 * 移动端键盘适配
 */
export const setupKeyboardAdaptation = () => {
  const originalHeight = window.innerHeight
  
  const handleResize = () => {
    const currentHeight = window.innerHeight
    const heightDiff = originalHeight - currentHeight
    
    // 键盘弹起时的处理
    if (heightDiff > 150) {
      document.body.style.height = `${currentHeight}px`
      document.body.classList.add('keyboard-open')
    } else {
      document.body.style.height = ''
      document.body.classList.remove('keyboard-open')
    }
  }

  window.addEventListener('resize', handleResize)
  
  // 输入框获得焦点时的处理
  document.addEventListener('focusin', (e) => {
    if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
      setTimeout(() => {
        e.target.scrollIntoView({ behavior: 'smooth', block: 'center' })
      }, 300)
    }
  })
}

/**
 * 移动端性能优化
 */
export const optimizePerformance = () => {
  // 启用硬件加速
  const style = document.createElement('style')
  style.textContent = `
    * {
      -webkit-transform: translateZ(0);
      -webkit-backface-visibility: hidden;
      -webkit-perspective: 1000;
    }
  `
  if (document.head) {
    document.head.appendChild(style)
  }

  // 优化滚动性能
  document.addEventListener('touchmove', (e) => {
    if (e.target instanceof HTMLElement) {
      const scrollable = e.target.closest('[data-scrollable]')
      if (!scrollable) {
        e.preventDefault()
      }
    }
  }, { passive: false })
}

/**
 * 移动端网络状态检测
 */
export const getNetworkInfo = () => {
  const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection
  
  return {
    online: navigator.onLine,
    effectiveType: connection?.effectiveType || 'unknown',
    downlink: connection?.downlink || 0,
    rtt: connection?.rtt || 0,
    saveData: connection?.saveData || false
  }
}

/**
 * 移动端存储管理
 */
export class MobileStorage {
  private static readonly PREFIX = 'mobile_app_'

  static set(key: string, value: any): void {
    try {
      const serializedValue = JSON.stringify(value)
      localStorage.setItem(this.PREFIX + key, serializedValue)
    } catch (error) {
      console.warn('Failed to save to localStorage:', error)
    }
  }

  static get<T>(key: string, defaultValue?: T): T | null {
    try {
      const item = localStorage.getItem(this.PREFIX + key)
      return item ? JSON.parse(item) : defaultValue || null
    } catch (error) {
      console.warn('Failed to read from localStorage:', error)
      return defaultValue || null
    }
  }

  static remove(key: string): void {
    localStorage.removeItem(this.PREFIX + key)
  }

  static clear(): void {
    Object.keys(localStorage)
      .filter(key => key.startsWith(this.PREFIX))
      .forEach(key => localStorage.removeItem(key))
  }

  static getStorageInfo() {
    const used = new Blob(Object.values(localStorage)).size
    const quota = (navigator as any).storage?.estimate ? 
      (navigator as any).storage.estimate().then((estimate: any) => estimate.quota) : 
      5 * 1024 * 1024 // 5MB fallback
    
    return { used, quota }
  }
}

/**
 * 移动端应用管理器
 */
export class MobileAppManager {
  private static instance: MobileAppManager
  private gestureDetectors: Map<HTMLElement, any> = new Map()
  
  constructor() {
    // 初始化配置
  }
  
  static getInstance(): MobileAppManager {
    if (!MobileAppManager.instance) {
      MobileAppManager.instance = new MobileAppManager()
    }
    return MobileAppManager.instance
  }
  
  /**
   * 创建手势检测器
   */
  createGesture(element: HTMLElement, options?: any) {
    // 简化的手势检测器实现
    const detector = {
      destroy: () => {
        // 清理事件监听器
      }
    }
    this.gestureDetectors.set(element, detector)
    return detector
  }
  
  /**
   * 移除手势检测器
   */
  removeGesture(element: HTMLElement) {
    const detector = this.gestureDetectors.get(element)
    if (detector) {
      detector.destroy()
      this.gestureDetectors.delete(element)
    }
  }
  
  /**
   * 清理资源
   */
  cleanup() {
    this.gestureDetectors.forEach(detector => detector.destroy())
    this.gestureDetectors.clear()
  }
}

/**
 * 移动端初始化函数
 */
export const initMobileApp = () => {
  if (isMobile() || isTouchDevice()) {
    setupViewport()
    setupKeyboardAdaptation()
    optimizePerformance()
    
    // 添加移动端标识类
    document.body.classList.add('mobile-device')
    
    if (isTouchDevice()) {
      document.body.classList.add('touch-device')
    }
    
    if (isSmallScreen()) {
      document.body.classList.add('small-screen')
    }
    
    if (isExtraSmallScreen()) {
      document.body.classList.add('extra-small-screen')
    }
    
    console.log('Mobile app initialized')
    
    return MobileAppManager.getInstance()
  }
}

// 导出常用的响应式断点
export const BREAKPOINTS = {
  xs: 480,
  sm: 768,
  md: 1024,
  lg: 1200,
  xl: 1920
} as const

// 导出设备类型枚举
export enum DeviceType {
  MOBILE = 'mobile',
  TABLET = 'tablet',
  DESKTOP = 'desktop'
}

/**
 * 获取设备类型
 */
export const getDeviceType = (): DeviceType => {
  const width = window.innerWidth
  
  if (width <= BREAKPOINTS.sm) {
    return DeviceType.MOBILE
  } else if (width <= BREAKPOINTS.md) {
    return DeviceType.TABLET
  } else {
    return DeviceType.DESKTOP
  }
}