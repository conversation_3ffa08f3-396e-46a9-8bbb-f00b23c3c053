import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useLoadingStore = defineStore('loading', () => {
  // 路由加载状态
  const isRouteLoading = ref(false)
  const routeLoadingText = ref('页面加载中...')
  
  // 全局加载状态
  const isGlobalLoading = ref(false)
  const globalLoadingText = ref('加载中...')
  
  // 显示路由加载
  const showRouteLoading = (text = '页面加载中...') => {
    routeLoadingText.value = text
    isRouteLoading.value = true
  }
  
  // 隐藏路由加载
  const hideRouteLoading = () => {
    isRouteLoading.value = false
  }
  
  // 显示全局加载
  const showGlobalLoading = (text = '加载中...') => {
    globalLoadingText.value = text
    isGlobalLoading.value = true
  }
  
  // 隐藏全局加载
  const hideGlobalLoading = () => {
    isGlobalLoading.value = false
  }
  
  // 自动隐藏路由加载（带延迟）
  const autoHideRouteLoading = (delay = 1500) => {
    setTimeout(() => {
      hideRouteLoading()
    }, delay)
  }
  
  return {
    // 状态
    isRouteLoading,
    routeLoadingText,
    isGlobalLoading,
    globalLoadingText,
    
    // 方法
    showRouteLoading,
    hideRouteLoading,
    showGlobalLoading,
    hideGlobalLoading,
    autoHideRouteLoading
  }
})