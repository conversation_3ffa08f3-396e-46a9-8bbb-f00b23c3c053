import { ref, nextTick } from 'vue'
import type { Ref } from 'vue'

// 微交互类型定义
export interface InteractionConfig {
  // 悬停效果
  hover?: {
    scale?: number
    lift?: number
    glow?: boolean
    glowColor?: string
    duration?: number
  }
  
  // 点击效果
  click?: {
    ripple?: boolean
    rippleColor?: string
    particles?: boolean
    particleCount?: number
    scale?: number
    duration?: number
  }
  
  // 焦点效果
  focus?: {
    glow?: boolean
    glowColor?: string
    scale?: number
    duration?: number
  }
  
  // 加载效果
  loading?: {
    shimmer?: boolean
    pulse?: boolean
    duration?: number
  }
  
  // 错误效果
  error?: {
    shake?: boolean
    shakeIntensity?: number
    glowColor?: string
    duration?: number
  }
  
  // 成功效果
  success?: {
    bounce?: boolean
    glow?: boolean
    glowColor?: string
    particles?: boolean
    duration?: number
  }
}

// 默认配置
const defaultConfig: InteractionConfig = {
  hover: {
    scale: 1.02,
    lift: 2,
    glow: true,
    glowColor: '#667eea',
    duration: 300
  },
  click: {
    ripple: true,
    rippleColor: '#667eea',
    particles: false,
    particleCount: 8,
    scale: 0.98,
    duration: 200
  },
  focus: {
    glow: true,
    glowColor: '#667eea',
    scale: 1.01,
    duration: 200
  },
  loading: {
    shimmer: true,
    pulse: false,
    duration: 2000
  },
  error: {
    shake: true,
    shakeIntensity: 5,
    glowColor: '#ff6b6b',
    duration: 300
  },
  success: {
    bounce: true,
    glow: true,
    glowColor: '#4facfe',
    particles: true,
    duration: 600
  }
}

// 微交互状态管理
interface InteractionState {
  isHovering: boolean
  isClicking: boolean
  isFocused: boolean
  isLoading: boolean
  hasError: boolean
  isSuccess: boolean
}

// 动画实例管理
interface AnimationInstance {
  id: string
  element: HTMLElement
  animation: Animation
  cleanup: () => void
}

class ModernInteractionManager {
  private animations = new Map<string, AnimationInstance>()
  private animationId = 0

  // 创建动画ID
  private createAnimationId(): string {
    return `animation-${++this.animationId}-${Date.now()}`
  }

  // 清理动画
  private cleanupAnimation(id: string) {
    const instance = this.animations.get(id)
    if (instance) {
      instance.animation.cancel()
      instance.cleanup()
      this.animations.delete(id)
    }
  }

  // 悬停效果
  applyHoverEffect(element: HTMLElement, config: InteractionConfig['hover'] = {}) {
    const finalConfig = { ...defaultConfig.hover, ...config }
    const id = this.createAnimationId()

    const originalTransform = element.style.transform
    const originalBoxShadow = element.style.boxShadow

    const hoverAnimation = element.animate([
      {
        transform: originalTransform || 'none',
        boxShadow: originalBoxShadow || 'none'
      },
      {
        transform: `translateY(-${finalConfig.lift}px) scale(${finalConfig.scale})`,
        boxShadow: finalConfig.glow 
          ? `0 ${finalConfig.lift! * 2}px ${finalConfig.lift! * 4}px rgba(102, 126, 234, 0.3), 0 0 20px ${finalConfig.glowColor}`
          : originalBoxShadow || 'none'
      }
    ], {
      duration: finalConfig.duration,
      fill: 'forwards',
      easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
    })

    this.animations.set(id, {
      id,
      element,
      animation: hoverAnimation,
      cleanup: () => {
        element.style.transform = originalTransform
        element.style.boxShadow = originalBoxShadow
      }
    })

    return id
  }

  // 移除悬停效果
  removeHoverEffect(element: HTMLElement, animationId?: string) {
    if (animationId) {
      this.cleanupAnimation(animationId)
      return
    }

    // 查找并移除该元素的所有悬停动画
    for (const [id, instance] of this.animations) {
      if (instance.element === element) {
        this.cleanupAnimation(id)
      }
    }
  }

  // 点击效果
  applyClickEffect(element: HTMLElement, event: MouseEvent, config: InteractionConfig['click'] = {}) {
    const finalConfig = { ...defaultConfig.click, ...config }
    const id = this.createAnimationId()

    // 缩放动画
    const scaleAnimation = element.animate([
      { transform: 'scale(1)' },
      { transform: `scale(${finalConfig.scale})` },
      { transform: 'scale(1)' }
    ], {
      duration: finalConfig.duration,
      easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
    })

    // 波纹效果
    if (finalConfig.ripple) {
      this.createRippleEffect(element, event, finalConfig.rippleColor!)
    }

    // 粒子效果
    if (finalConfig.particles) {
      this.createParticleEffect(element, event, finalConfig.particleCount!)
    }

    this.animations.set(id, {
      id,
      element,
      animation: scaleAnimation,
      cleanup: () => {}
    })

    // 自动清理
    setTimeout(() => {
      this.cleanupAnimation(id)
    }, finalConfig.duration! + 100)

    return id
  }

  // 创建波纹效果
  private createRippleEffect(element: HTMLElement, event: MouseEvent, color: string) {
    const rect = element.getBoundingClientRect()
    const size = Math.max(rect.width, rect.height)
    const x = event.clientX - rect.left - size / 2
    const y = event.clientY - rect.top - size / 2

    const ripple = document.createElement('div')
    ripple.style.cssText = `
      position: absolute;
      left: ${x}px;
      top: ${y}px;
      width: ${size}px;
      height: ${size}px;
      border-radius: 50%;
      background: radial-gradient(circle, ${color}40 0%, ${color}20 50%, transparent 100%);
      pointer-events: none;
      z-index: 1000;
    `

    element.style.position = element.style.position || 'relative'
    element.style.overflow = 'hidden'
    element.appendChild(ripple)

    const animation = ripple.animate([
      { transform: 'scale(0)', opacity: '1' },
      { transform: 'scale(2)', opacity: '0' }
    ], {
      duration: 600,
      easing: 'ease-out'
    })

    animation.onfinish = () => {
      ripple.remove()
    }
  }

  // 创建粒子效果
  private createParticleEffect(element: HTMLElement, event: MouseEvent, count: number) {
    const rect = element.getBoundingClientRect()
    const centerX = event.clientX - rect.left
    const centerY = event.clientY - rect.top

    for (let i = 0; i < count; i++) {
      const particle = document.createElement('div')
      const angle = (360 / count) * i
      const distance = 30 + Math.random() * 20
      
      particle.style.cssText = `
        position: absolute;
        left: ${centerX}px;
        top: ${centerY}px;
        width: 4px;
        height: 4px;
        border-radius: 50%;
        background: #667eea;
        pointer-events: none;
        z-index: 1001;
      `

      element.style.position = element.style.position || 'relative'
      element.appendChild(particle)

      const animation = particle.animate([
        {
          transform: 'translate(0, 0) scale(1)',
          opacity: '1'
        },
        {
          transform: `translate(${Math.cos(angle * Math.PI / 180) * distance}px, ${Math.sin(angle * Math.PI / 180) * distance}px) scale(0)`,
          opacity: '0'
        }
      ], {
        duration: 800,
        easing: 'ease-out',
        delay: Math.random() * 100
      })

      animation.onfinish = () => {
        particle.remove()
      }
    }
  }

  // 焦点效果
  applyFocusEffect(element: HTMLElement, config: InteractionConfig['focus'] = {}) {
    const finalConfig = { ...defaultConfig.focus, ...config }
    const id = this.createAnimationId()

    const originalBoxShadow = element.style.boxShadow
    const originalTransform = element.style.transform

    const focusAnimation = element.animate([
      {
        boxShadow: originalBoxShadow || 'none',
        transform: originalTransform || 'none'
      },
      {
        boxShadow: finalConfig.glow 
          ? `0 0 0 2px ${finalConfig.glowColor}40, 0 0 20px ${finalConfig.glowColor}60`
          : originalBoxShadow || 'none',
        transform: `scale(${finalConfig.scale})`
      }
    ], {
      duration: finalConfig.duration,
      fill: 'forwards',
      easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
    })

    this.animations.set(id, {
      id,
      element,
      animation: focusAnimation,
      cleanup: () => {
        element.style.boxShadow = originalBoxShadow
        element.style.transform = originalTransform
      }
    })

    return id
  }

  // 移除焦点效果
  removeFocusEffect(animationId: string) {
    this.cleanupAnimation(animationId)
  }

  // 错误效果
  applyErrorEffect(element: HTMLElement, config: InteractionConfig['error'] = {}) {
    const finalConfig = { ...defaultConfig.error, ...config }
    const id = this.createAnimationId()

    const shakeAnimation = element.animate([
      { transform: 'translateX(0)' },
      { transform: `translateX(-${finalConfig.shakeIntensity}px)` },
      { transform: `translateX(${finalConfig.shakeIntensity}px)` },
      { transform: `translateX(-${finalConfig.shakeIntensity! / 2}px)` },
      { transform: `translateX(${finalConfig.shakeIntensity! / 2}px)` },
      { transform: 'translateX(0)' }
    ], {
      duration: finalConfig.duration,
      easing: 'ease-in-out'
    })

    // 添加错误发光效果
    const originalBoxShadow = element.style.boxShadow
    element.style.boxShadow = `0 0 20px ${finalConfig.glowColor}`

    this.animations.set(id, {
      id,
      element,
      animation: shakeAnimation,
      cleanup: () => {
        element.style.boxShadow = originalBoxShadow
      }
    })

    // 自动清理
    setTimeout(() => {
      this.cleanupAnimation(id)
    }, finalConfig.duration! + 100)

    return id
  }

  // 成功效果
  applySuccessEffect(element: HTMLElement, config: InteractionConfig['success'] = {}) {
    const finalConfig = { ...defaultConfig.success, ...config }
    const id = this.createAnimationId()

    const bounceAnimation = element.animate([
      { transform: 'scale(1)' },
      { transform: 'scale(1.1)' },
      { transform: 'scale(0.95)' },
      { transform: 'scale(1.05)' },
      { transform: 'scale(1)' }
    ], {
      duration: finalConfig.duration,
      easing: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)'
    })

    // 添加成功发光效果
    if (finalConfig.glow) {
      const originalBoxShadow = element.style.boxShadow
      element.style.boxShadow = `0 0 30px ${finalConfig.glowColor}`
      
      setTimeout(() => {
        element.style.boxShadow = originalBoxShadow
      }, finalConfig.duration!)
    }

    this.animations.set(id, {
      id,
      element,
      animation: bounceAnimation,
      cleanup: () => {}
    })

    // 自动清理
    setTimeout(() => {
      this.cleanupAnimation(id)
    }, finalConfig.duration! + 100)

    return id
  }

  // 清理所有动画
  cleanup() {
    for (const [id] of this.animations) {
      this.cleanupAnimation(id)
    }
  }
}

// 全局交互管理器实例
const interactionManager = new ModernInteractionManager()

// 组合式函数
export function useModernInteractions(config: InteractionConfig = {}) {
  const state: Ref<InteractionState> = ref({
    isHovering: false,
    isClicking: false,
    isFocused: false,
    isLoading: false,
    hasError: false,
    isSuccess: false
  })

  const currentAnimations = ref<string[]>([])

  // 应用悬停效果
  const applyHover = (element: HTMLElement) => {
    if (state.value.isHovering) return
    
    state.value.isHovering = true
    const animationId = interactionManager.applyHoverEffect(element, config.hover)
    currentAnimations.value.push(animationId)
  }

  // 移除悬停效果
  const removeHover = (element: HTMLElement) => {
    if (!state.value.isHovering) return
    
    state.value.isHovering = false
    interactionManager.removeHoverEffect(element)
    currentAnimations.value = []
  }

  // 应用点击效果
  const applyClick = (element: HTMLElement, event: MouseEvent) => {
    state.value.isClicking = true
    const animationId = interactionManager.applyClickEffect(element, event, config.click)
    
    setTimeout(() => {
      state.value.isClicking = false
    }, config.click?.duration || defaultConfig.click!.duration!)
    
    return animationId
  }

  // 应用焦点效果
  const applyFocus = (element: HTMLElement) => {
    if (state.value.isFocused) return
    
    state.value.isFocused = true
    const animationId = interactionManager.applyFocusEffect(element, config.focus)
    currentAnimations.value.push(animationId)
  }

  // 移除焦点效果
  const removeFocus = () => {
    if (!state.value.isFocused) return
    
    state.value.isFocused = false
    currentAnimations.value.forEach(id => {
      interactionManager.removeFocusEffect(id)
    })
    currentAnimations.value = []
  }

  // 应用错误效果
  const applyError = (element: HTMLElement) => {
    state.value.hasError = true
    const animationId = interactionManager.applyErrorEffect(element, config.error)
    
    setTimeout(() => {
      state.value.hasError = false
    }, config.error?.duration || defaultConfig.error!.duration!)
    
    return animationId
  }

  // 应用成功效果
  const applySuccess = (element: HTMLElement) => {
    state.value.isSuccess = true
    const animationId = interactionManager.applySuccessEffect(element, config.success)
    
    setTimeout(() => {
      state.value.isSuccess = false
    }, config.success?.duration || defaultConfig.success!.duration!)
    
    return animationId
  }

  // 清理函数
  const cleanup = () => {
    currentAnimations.value.forEach(id => {
      interactionManager.cleanupAnimation(id)
    })
    currentAnimations.value = []
    
    // 重置状态
    state.value = {
      isHovering: false,
      isClicking: false,
      isFocused: false,
      isLoading: false,
      hasError: false,
      isSuccess: false
    }
  }

  return {
    state,
    applyHover,
    removeHover,
    applyClick,
    applyFocus,
    removeFocus,
    applyError,
    applySuccess,
    cleanup
  }
}

// 指令式API
export const modernInteractions = {
  hover: (element: HTMLElement, config?: InteractionConfig['hover']) => 
    interactionManager.applyHoverEffect(element, config),
  
  removeHover: (element: HTMLElement, animationId?: string) => 
    interactionManager.removeHoverEffect(element, animationId),
  
  click: (element: HTMLElement, event: MouseEvent, config?: InteractionConfig['click']) => 
    interactionManager.applyClickEffect(element, event, config),
  
  focus: (element: HTMLElement, config?: InteractionConfig['focus']) => 
    interactionManager.applyFocusEffect(element, config),
  
  removeFocus: (animationId: string) => 
    interactionManager.removeFocusEffect(animationId),
  
  error: (element: HTMLElement, config?: InteractionConfig['error']) => 
    interactionManager.applyErrorEffect(element, config),
  
  success: (element: HTMLElement, config?: InteractionConfig['success']) => 
    interactionManager.applySuccessEffect(element, config),
  
  cleanup: () => interactionManager.cleanup()
}

export default useModernInteractions