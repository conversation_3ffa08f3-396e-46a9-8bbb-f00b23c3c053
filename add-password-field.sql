-- 添加密码字段到users表
ALTER TABLE users ADD COLUMN password_hash VARCHAR(255);

-- 为现有用户设置默认密码（临时，实际应用中应该要求用户重新设置密码）
-- 默认密码: 123456 (bcrypt hash)
UPDATE users SET password_hash = '$2b$10$rOzJqKqQQxQQxQQxQQxQQeJ7J7J7J7J7J7J7J7J7J7J7J7J7J7J7J7' WHERE password_hash IS NULL;

-- 设置password_hash为NOT NULL
ALTER TABLE users MODIFY COLUMN password_hash VARCHAR(255) NOT NULL;

-- 创建索引以提高查询性能
CREATE INDEX idx_users_email ON users(email);

-- 显示表结构确认修改
DESCRIBE users;