const mysql = require('mysql2/promise');

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'ev_management',
  charset: 'utf8mb4'
};

async function checkAllTables() {
  let connection;
  
  try {
    console.log('🔌 连接到MySQL数据库...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    const tables = ['users', 'vehicles', 'batteries', 'charging_stations', 'charging_sessions', 'carbon_credit', 'driving_behavior', 'fleet_management', 'edge_devices'];
    
    for (const table of tables) {
      try {
        console.log(`\n📋 ${table} 表字段:`);
        console.log('='.repeat(30));
        const [columns] = await connection.execute(`DESCRIBE ${table}`);
        
        const fieldNames = columns.map(col => col.Field);
        console.log(`字段列表: ${fieldNames.join(', ')}`);
        
      } catch (error) {
        console.log(`❌ 表 ${table} 不存在或查询失败: ${error.message}`);
      }
    }
    
  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 数据库连接已关闭');
    }
  }
}

// 运行检查脚本
checkAllTables();