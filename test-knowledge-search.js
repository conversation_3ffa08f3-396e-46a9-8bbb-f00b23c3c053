const mysql = require('mysql2/promise');

async function testKnowledgeSearch() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: '123456',
    database: 'ev_management'
  });
  
  try {
    console.log('🔍 测试知识库搜索...\n');
    
    const query = '电池健康度';
    const [rows] = await connection.execute(`
      SELECT question, answer, category 
      FROM ai_knowledge_base 
      WHERE is_active = TRUE 
      AND (
        question LIKE CONCAT('%', ?, '%')
        OR answer LIKE CONCAT('%', ?, '%')
        OR keywords LIKE CONCAT('%', ?, '%')
      )
      ORDER BY priority DESC, id DESC
      LIMIT 3
    `, [query, query, query]);
    
    console.log('搜索结果数量:', rows.length);
    rows.forEach((row, index) => {
      console.log(`${index + 1}. [${row.category}] ${row.question}`);
      console.log(`   答案: ${row.answer.substring(0, 100)}...\n`);
    });
    
    // 测试其他关键词
    const testQueries = ['充电站', 'ADAS', '车辆管理'];
    
    for (const testQuery of testQueries) {
      console.log(`🔍 搜索关键词: ${testQuery}`);
      const [testRows] = await connection.execute(`
        SELECT question, answer, category 
        FROM ai_knowledge_base 
        WHERE is_active = TRUE 
        AND (
          question LIKE CONCAT('%', ?, '%')
          OR answer LIKE CONCAT('%', ?, '%')
          OR keywords LIKE CONCAT('%', ?, '%')
        )
        ORDER BY priority DESC, id DESC
        LIMIT 2
      `, [testQuery, testQuery, testQuery]);
      
      console.log(`   找到 ${testRows.length} 条相关知识\n`);
    }
    
  } catch (error) {
    console.error('搜索失败:', error.message);
  } finally {
    await connection.end();
  }
}

testKnowledgeSearch();
