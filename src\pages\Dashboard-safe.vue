<template>
  <div class="dashboard-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div>
        <h1 class="page-title">EVAdmin Pro 管理系统</h1>
        <p class="page-subtitle">电动车智能管理平台</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" size="small">
          <el-icon class="mr-1"><Plus /></el-icon>
          新建
        </el-button>
        <el-button size="small">
          <el-icon class="mr-1"><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-content">
          <div class="stat-info">
            <p class="stat-label">在线车辆</p>
            <p class="stat-value">1,234</p>
          </div>
          <div class="stat-icon blue">
            <el-icon><Odometer /></el-icon>
          </div>
        </div>
        <div class="stat-footer">
          <span class="stat-change positive">+12%</span>
          <span class="stat-period">较昨日</span>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-content">
          <div class="stat-info">
            <p class="stat-label">充电站</p>
            <p class="stat-value">89</p>
          </div>
          <div class="stat-icon green">
            <el-icon><Lightning /></el-icon>
          </div>
        </div>
        <div class="stat-footer">
          <span class="stat-change positive">+5%</span>
          <span class="stat-period">较昨日</span>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-content">
          <div class="stat-info">
            <p class="stat-label">总里程</p>
            <p class="stat-value">45.6万</p>
          </div>
          <div class="stat-icon purple">
            <el-icon><Odometer /></el-icon>
          </div>
        </div>
        <div class="stat-footer">
          <span class="stat-change positive">+8%</span>
          <span class="stat-period">较昨日</span>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-content">
          <div class="stat-info">
            <p class="stat-label">节能减排</p>
            <p class="stat-value">2.3吨</p>
          </div>
          <div class="stat-icon orange">
            <el-icon><Sunny /></el-icon>
          </div>
        </div>
        <div class="stat-footer">
          <span class="stat-change positive">+15%</span>
          <span class="stat-period">较昨日</span>
        </div>
      </div>
    </div>

    <!-- 功能模块 -->
    <div class="modules-section">
      <h2 class="section-title">核心功能</h2>
      <div class="modules-grid">
        <router-link to="/vehicle-network" class="module-card">
          <div class="module-icon blue">
            <el-icon><DataBoard /></el-icon>
          </div>
          <div class="module-info">
            <h3 class="module-title">车辆网络</h3>
            <p class="module-desc">实时监控车辆状态</p>
          </div>
        </router-link>

        <router-link to="/battery-management" class="module-card">
          <div class="module-icon green">
            <el-icon><Lightning /></el-icon>
          </div>
          <div class="module-info">
            <h3 class="module-title">电池管理</h3>
            <p class="module-desc">电池健康度监控</p>
          </div>
        </router-link>

        <router-link to="/charging-service" class="module-card">
          <div class="module-icon purple">
            <el-icon><Lightning /></el-icon>
          </div>
          <div class="module-info">
            <h3 class="module-title">充电服务</h3>
            <p class="module-desc">充电站管理</p>
          </div>
        </router-link>

        <router-link to="/ai-assistant" class="module-card">
          <div class="module-icon orange">
            <el-icon><ChatDotRound /></el-icon>
          </div>
          <div class="module-info">
            <h3 class="module-title">AI助手</h3>
            <p class="module-desc">智能分析建议</p>
          </div>
        </router-link>
      </div>
    </div>

    <!-- 实时数据 -->
    <div class="realtime-section">
      <h2 class="section-title">实时数据</h2>
      <div class="realtime-grid">
        <div class="realtime-card">
          <h3 class="card-title">车辆状态分布</h3>
          <div class="chart-placeholder">
            <div class="chart-item">
              <div class="chart-bar" style="height: 60%"></div>
              <span class="chart-label">行驶中</span>
            </div>
            <div class="chart-item">
              <div class="chart-bar" style="height: 30%"></div>
              <span class="chart-label">充电中</span>
            </div>
            <div class="chart-item">
              <div class="chart-bar" style="height: 10%"></div>
              <span class="chart-label">维护中</span>
            </div>
          </div>
        </div>

        <div class="realtime-card">
          <h3 class="card-title">充电站使用率</h3>
          <div class="usage-list">
            <div class="usage-item">
              <span class="station-name">北京站点A</span>
              <div class="usage-bar">
                <div class="usage-fill" style="width: 85%"></div>
              </div>
              <span class="usage-percent">85%</span>
            </div>
            <div class="usage-item">
              <span class="station-name">上海站点B</span>
              <div class="usage-bar">
                <div class="usage-fill" style="width: 72%"></div>
              </div>
              <span class="usage-percent">72%</span>
            </div>
            <div class="usage-item">
              <span class="station-name">深圳站点C</span>
              <div class="usage-bar">
                <div class="usage-fill" style="width: 91%"></div>
              </div>
              <span class="usage-percent">91%</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { 
  Plus, Refresh, Odometer, Lightning, Sunny, 
  DataBoard, ChatDotRound 
} from '@element-plus/icons-vue'
</script>

<style scoped>
.dashboard-container {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 8px 0;
}

.page-subtitle {
  font-size: 16px;
  color: #718096;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.stat-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.stat-label {
  font-size: 14px;
  color: #718096;
  margin: 0 0 8px 0;
}

.stat-value {
  font-size: 32px;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.blue { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.stat-icon.green { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.stat-icon.purple { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.stat-icon.orange { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }

.stat-footer {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stat-change {
  font-size: 14px;
  font-weight: 600;
}

.stat-change.positive {
  color: #38a169;
}

.stat-period {
  font-size: 14px;
  color: #718096;
}

.modules-section {
  margin-bottom: 40px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 24px 0;
}

.modules-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.module-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-decoration: none;
  color: inherit;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.module-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  text-decoration: none;
  color: inherit;
}

.module-icon {
  width: 56px;
  height: 56px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  color: white;
  flex-shrink: 0;
}

.module-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 8px 0;
}

.module-desc {
  font-size: 14px;
  color: #718096;
  margin: 0;
}

.realtime-section {
  margin-bottom: 40px;
}

.realtime-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

.realtime-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 20px 0;
}

.chart-placeholder {
  display: flex;
  align-items: end;
  justify-content: space-around;
  height: 200px;
  padding: 20px 0;
}

.chart-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.chart-bar {
  width: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px 4px 0 0;
  transition: height 0.3s ease;
}

.chart-label {
  font-size: 12px;
  color: #718096;
}

.usage-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.usage-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.station-name {
  font-size: 14px;
  color: #1a202c;
  min-width: 100px;
}

.usage-bar {
  flex: 1;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
}

.usage-fill {
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.usage-percent {
  font-size: 14px;
  font-weight: 600;
  color: #1a202c;
  min-width: 40px;
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .modules-grid {
    grid-template-columns: 1fr;
  }
  
  .realtime-grid {
    grid-template-columns: 1fr;
  }
}
</style>