<template>
  <div class="dashboard-container">
    <!-- 顶部欢迎区域 -->
    <div class="welcome-section">
      <div class="welcome-content">
        <div class="welcome-text">
          <h1 class="welcome-title">
            <span class="gradient-text">EVAdmin Pro</span>
            <span class="title-badge">智能管理平台</span>
          </h1>
          <p class="welcome-subtitle">
            欢迎回来，{{ currentUser }}！今天是 {{ currentDate }}
          </p>
          <div class="quick-stats">
            <div class="quick-stat-item">
              <span class="quick-stat-value">{{ totalVehicles }}</span>
              <span class="quick-stat-label">在线车辆</span>
            </div>
            <div class="quick-stat-item">
              <span class="quick-stat-value">{{ totalStations }}</span>
              <span class="quick-stat-label">充电站</span>
            </div>
            <div class="quick-stat-item">
              <span class="quick-stat-value">{{ systemHealth }}%</span>
              <span class="quick-stat-label">系统健康度</span>
            </div>
          </div>
        </div>
        <div class="welcome-actions">
          <el-button type="primary" size="large" class="action-btn">
            <el-icon><Plus /></el-icon>
            快速添加
          </el-button>
          <el-button size="large" class="action-btn" @click="refreshData">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </div>
      <div class="welcome-visual">
        <div class="floating-cards">
          <div class="floating-card card-1">
            <el-icon><Odometer /></el-icon>
          </div>
          <div class="floating-card card-2">
            <el-icon><Lightning /></el-icon>
          </div>
          <div class="floating-card card-3">
            <el-icon><TrendCharts /></el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 核心指标卡片 -->
    <div class="metrics-section">
      <div class="metrics-grid">
        <div class="metric-card primary">
          <div class="metric-header">
            <div class="metric-icon">
              <el-icon><Odometer /></el-icon>
            </div>
            <div class="metric-trend">
              <el-icon class="trend-icon up"><TrendCharts /></el-icon>
              <span class="trend-value">+12.5%</span>
            </div>
          </div>
          <div class="metric-content">
            <h3 class="metric-title">在线车辆</h3>
            <div class="metric-value">{{ formatNumber(totalVehicles) }}</div>
            <p class="metric-subtitle">较昨日增长 {{ vehicleGrowth }}%</p>
          </div>
          <div class="metric-chart">
            <div class="mini-chart">
              <div class="chart-bar" v-for="(value, index) in vehicleChartData" :key="index"
                   :style="{ height: value + '%' }"></div>
            </div>
          </div>
        </div>

        <div class="metric-card success">
          <div class="metric-header">
            <div class="metric-icon">
              <el-icon><Lightning /></el-icon>
            </div>
            <div class="metric-trend">
              <el-icon class="trend-icon up"><TrendCharts /></el-icon>
              <span class="trend-value">+8.3%</span>
            </div>
          </div>
          <div class="metric-content">
            <h3 class="metric-title">充电站点</h3>
            <div class="metric-value">{{ totalStations }}</div>
            <p class="metric-subtitle">平均使用率 {{ stationUsage }}%</p>
          </div>
          <div class="metric-progress">
            <el-progress :percentage="stationUsage" :show-text="false"
                        stroke-width="6" color="#10b981" />
          </div>
        </div>

        <div class="metric-card warning">
          <div class="metric-header">
            <div class="metric-icon">
              <el-icon><Odometer /></el-icon>
            </div>
            <div class="metric-trend">
              <el-icon class="trend-icon up"><TrendCharts /></el-icon>
              <span class="trend-value">+15.2%</span>
            </div>
          </div>
          <div class="metric-content">
            <h3 class="metric-title">总行驶里程</h3>
            <div class="metric-value">{{ formatMileage(totalMileage) }}</div>
            <p class="metric-subtitle">本月新增 {{ monthlyMileage }} 万公里</p>
          </div>
          <div class="metric-chart">
            <div class="progress-ring">
              <svg class="progress-ring-svg" width="60" height="60">
                <circle class="progress-ring-circle-bg" cx="30" cy="30" r="25" />
                <circle class="progress-ring-circle" cx="30" cy="30" r="25"
                        :stroke-dasharray="circumference"
                        :stroke-dashoffset="circumference - (mileageProgress / 100) * circumference" />
              </svg>
              <div class="progress-text">{{ mileageProgress }}%</div>
            </div>
          </div>
        </div>

        <div class="metric-card info">
          <div class="metric-header">
            <div class="metric-icon">
              <el-icon><Sunny /></el-icon>
            </div>
            <div class="metric-trend">
              <el-icon class="trend-icon up"><TrendCharts /></el-icon>
              <span class="trend-value">+22.1%</span>
            </div>
          </div>
          <div class="metric-content">
            <h3 class="metric-title">碳减排量</h3>
            <div class="metric-value">{{ carbonReduction }}</div>
            <p class="metric-subtitle">相当于种植 {{ treesEquivalent }} 棵树</p>
          </div>
          <div class="metric-visual">
            <div class="carbon-visual">
              <el-icon class="carbon-icon"><Sunny /></el-icon>
              <div class="carbon-particles">
                <div class="particle" v-for="i in 6" :key="i"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 智能功能模块 -->
    <div class="features-section">
      <div class="section-header">
        <h2 class="section-title">
          <el-icon class="title-icon"><Grid /></el-icon>
          智能功能中心
        </h2>
        <el-button text type="primary">查看全部</el-button>
      </div>

      <div class="features-grid">
        <router-link to="/vehicle-network" class="feature-card vehicle">
          <div class="feature-background">
            <div class="feature-pattern"></div>
          </div>
          <div class="feature-content">
            <div class="feature-header">
              <div class="feature-icon">
                <el-icon><DataBoard /></el-icon>
              </div>
              <div class="feature-status">
                <el-badge :value="activeVehicles" class="status-badge" />
              </div>
            </div>
            <h3 class="feature-title">车辆网络监控</h3>
            <p class="feature-desc">实时监控车辆状态、位置追踪、性能分析</p>
            <div class="feature-stats">
              <div class="stat-item">
                <span class="stat-value">{{ activeVehicles }}</span>
                <span class="stat-label">在线</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ offlineVehicles }}</span>
                <span class="stat-label">离线</span>
              </div>
            </div>
          </div>
          <div class="feature-hover-effect"></div>
        </router-link>

        <router-link to="/battery-management" class="feature-card battery">
          <div class="feature-background">
            <div class="feature-pattern"></div>
          </div>
          <div class="feature-content">
            <div class="feature-header">
              <div class="feature-icon">
                <el-icon><Lightning /></el-icon>
              </div>
              <div class="feature-status">
                <el-badge :value="batteryAlerts" type="warning" class="status-badge" />
              </div>
            </div>
            <h3 class="feature-title">电池健康管理</h3>
            <p class="feature-desc">电池健康度监控、寿命预测、维护提醒</p>
            <div class="feature-stats">
              <div class="stat-item">
                <span class="stat-value">{{ avgBatteryHealth }}%</span>
                <span class="stat-label">平均健康度</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ batteryAlerts }}</span>
                <span class="stat-label">预警</span>
              </div>
            </div>
          </div>
          <div class="feature-hover-effect"></div>
        </router-link>

        <router-link to="/charging-service" class="feature-card charging">
          <div class="feature-background">
            <div class="feature-pattern"></div>
          </div>
          <div class="feature-content">
            <div class="feature-header">
              <div class="feature-icon">
                <el-icon><Lightning /></el-icon>
              </div>
              <div class="feature-status">
                <el-badge :value="chargingStations" type="success" class="status-badge" />
              </div>
            </div>
            <h3 class="feature-title">充电服务网络</h3>
            <p class="feature-desc">充电站管理、预约服务、收费统计</p>
            <div class="feature-stats">
              <div class="stat-item">
                <span class="stat-value">{{ chargingStations }}</span>
                <span class="stat-label">站点</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ avgUsageRate }}%</span>
                <span class="stat-label">使用率</span>
              </div>
            </div>
          </div>
          <div class="feature-hover-effect"></div>
        </router-link>

        <router-link to="/ai-assistant" class="feature-card ai">
          <div class="feature-background">
            <div class="feature-pattern"></div>
          </div>
          <div class="feature-content">
            <div class="feature-header">
              <div class="feature-icon">
                <el-icon><ChatDotRound /></el-icon>
              </div>
              <div class="feature-status">
                <el-badge value="AI" type="info" class="status-badge" />
              </div>
            </div>
            <h3 class="feature-title">AI智能助手</h3>
            <p class="feature-desc">智能分析、预测维护、优化建议</p>
            <div class="feature-stats">
              <div class="stat-item">
                <span class="stat-value">{{ aiRecommendations }}</span>
                <span class="stat-label">建议</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ aiAccuracy }}%</span>
                <span class="stat-label">准确率</span>
              </div>
            </div>
          </div>
          <div class="feature-hover-effect"></div>
        </router-link>
      </div>
    </div>

    <!-- 实时数据分析 -->
    <div class="analytics-section">
      <div class="section-header">
        <h2 class="section-title">
          <el-icon class="title-icon"><TrendCharts /></el-icon>
          实时数据分析
        </h2>
        <div class="time-filter">
          <el-radio-group v-model="timeRange" size="small">
            <el-radio-button label="today">今日</el-radio-button>
            <el-radio-button label="week">本周</el-radio-button>
            <el-radio-button label="month">本月</el-radio-button>
          </el-radio-group>
        </div>
      </div>

      <div class="analytics-grid">
        <div class="analytics-card chart-card">
          <div class="card-header">
            <h3 class="card-title">车辆状态分布</h3>
            <el-dropdown>
              <el-button text type="primary">
                <el-icon><MoreFilled /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item>导出数据</el-dropdown-item>
                  <el-dropdown-item>设置提醒</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
          <div class="chart-container">
            <div class="status-chart">
              <div class="status-item" v-for="(item, index) in vehicleStatusData" :key="index">
                <div class="status-indicator" :style="{ backgroundColor: item.color }"></div>
                <div class="status-info">
                  <span class="status-label">{{ item.label }}</span>
                  <span class="status-value">{{ item.value }}</span>
                  <span class="status-percent">{{ item.percent }}%</span>
                </div>
                <div class="status-bar">
                  <div class="status-fill" :style="{ width: item.percent + '%', backgroundColor: item.color }"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="analytics-card performance-card">
          <div class="card-header">
            <h3 class="card-title">系统性能监控</h3>
            <el-tag :type="systemStatus.type" size="small">{{ systemStatus.text }}</el-tag>
          </div>
          <div class="performance-metrics">
            <div class="metric-row">
              <div class="metric-item">
                <div class="metric-label">CPU使用率</div>
                <div class="metric-progress">
                  <el-progress :percentage="cpuUsage" :show-text="false" stroke-width="8" />
                  <span class="metric-value">{{ cpuUsage }}%</span>
                </div>
              </div>
              <div class="metric-item">
                <div class="metric-label">内存使用率</div>
                <div class="metric-progress">
                  <el-progress :percentage="memoryUsage" :show-text="false" stroke-width="8" color="#f56c6c" />
                  <span class="metric-value">{{ memoryUsage }}%</span>
                </div>
              </div>
              <div class="metric-item">
                <div class="metric-label">网络延迟</div>
                <div class="metric-progress">
                  <el-progress :percentage="networkLatency" :show-text="false" stroke-width="8" color="#e6a23c" />
                  <span class="metric-value">{{ networkLatency }}ms</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="analytics-card activity-card">
          <div class="card-header">
            <h3 class="card-title">最近活动</h3>
            <el-button text type="primary" size="small">查看全部</el-button>
          </div>
          <div class="activity-list">
            <div class="activity-item" v-for="(activity, index) in recentActivities" :key="index">
              <div class="activity-icon" :class="activity.type">
                <el-icon>
                  <component :is="activity.icon" />
                </el-icon>
              </div>
              <div class="activity-content">
                <p class="activity-text">{{ activity.text }}</p>
                <span class="activity-time">{{ activity.time }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import {
  Plus, Refresh, Odometer, Lightning, Sunny, TrendCharts,
  DataBoard, ChatDotRound, Grid, MoreFilled, Warning,
  SuccessFilled, InfoFilled
} from '@element-plus/icons-vue'

// 响应式数据
const currentUser = ref('管理员')
const currentDate = ref('')
const timeRange = ref('today')

// 核心指标数据
const totalVehicles = ref(1234)
const totalStations = ref(89)
const systemHealth = ref(96)
const vehicleGrowth = ref(12.5)
const stationUsage = ref(78)
const totalMileage = ref(456000)
const monthlyMileage = ref(12.8)
const carbonReduction = ref('2.3吨')
const treesEquivalent = ref(156)

// 功能模块数据
const activeVehicles = ref(1156)
const offlineVehicles = ref(78)
const batteryAlerts = ref(3)
const avgBatteryHealth = ref(94)
const chargingStations = ref(89)
const avgUsageRate = ref(78)
const aiRecommendations = ref(12)
const aiAccuracy = ref(96)

// 系统性能数据
const cpuUsage = ref(45)
const memoryUsage = ref(62)
const networkLatency = ref(28)

// 计算属性
const circumference = computed(() => 2 * Math.PI * 25)
const mileageProgress = computed(() => Math.round((monthlyMileage.value / 20) * 100))

const vehicleChartData = ref([60, 75, 45, 80, 65, 90, 70])

const systemStatus = computed(() => {
  const health = systemHealth.value
  if (health >= 90) return { type: 'success', text: '运行良好' }
  if (health >= 70) return { type: 'warning', text: '需要关注' }
  return { type: 'danger', text: '需要维护' }
})

const vehicleStatusData = ref([
  { label: '行驶中', value: 856, percent: 69, color: '#409eff' },
  { label: '充电中', value: 234, percent: 19, color: '#67c23a' },
  { label: '停车中', value: 98, percent: 8, color: '#e6a23c' },
  { label: '维护中', value: 46, percent: 4, color: '#f56c6c' }
])

const recentActivities = ref([
  {
    type: 'success',
    icon: 'SuccessFilled',
    text: '车辆 EV-001 完成充电，电量已达100%',
    time: '2分钟前'
  },
  {
    type: 'warning',
    icon: 'Warning',
    text: '充电站 CS-北京-001 使用率达到95%',
    time: '5分钟前'
  },
  {
    type: 'info',
    icon: 'InfoFilled',
    text: 'AI系统检测到3个优化建议',
    time: '10分钟前'
  },
  {
    type: 'success',
    icon: 'SuccessFilled',
    text: '新增车辆 EV-1235 成功接入网络',
    time: '15分钟前'
  }
])

// 方法
const formatNumber = (num: number) => {
  return num.toLocaleString()
}

const formatMileage = (num: number) => {
  return (num / 10000).toFixed(1) + '万公里'
}

const refreshData = () => {
  // 模拟数据刷新
  totalVehicles.value = Math.floor(Math.random() * 100) + 1200
  systemHealth.value = Math.floor(Math.random() * 10) + 90
  cpuUsage.value = Math.floor(Math.random() * 30) + 30
  memoryUsage.value = Math.floor(Math.random() * 40) + 40
  networkLatency.value = Math.floor(Math.random() * 20) + 20
}

const updateDateTime = () => {
  const now = new Date()
  currentDate.value = now.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  })
}

// 生命周期
onMounted(() => {
  updateDateTime()
  const timer = setInterval(updateDateTime, 60000) // 每分钟更新时间

  onUnmounted(() => {
    clearInterval(timer)
  })
})
</script>

<style scoped>
.dashboard-container {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 8px 0;
}

.page-subtitle {
  font-size: 16px;
  color: #718096;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.stat-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.stat-label {
  font-size: 14px;
  color: #718096;
  margin: 0 0 8px 0;
}

.stat-value {
  font-size: 32px;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.blue { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.stat-icon.green { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.stat-icon.purple { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.stat-icon.orange { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }

.stat-footer {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stat-change {
  font-size: 14px;
  font-weight: 600;
}

.stat-change.positive {
  color: #38a169;
}

.stat-period {
  font-size: 14px;
  color: #718096;
}

.modules-section {
  margin-bottom: 40px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 24px 0;
}

.modules-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.module-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-decoration: none;
  color: inherit;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.module-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  text-decoration: none;
  color: inherit;
}

.module-icon {
  width: 56px;
  height: 56px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  color: white;
  flex-shrink: 0;
}

.module-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 8px 0;
}

.module-desc {
  font-size: 14px;
  color: #718096;
  margin: 0;
}

.realtime-section {
  margin-bottom: 40px;
}

.realtime-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

.realtime-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 20px 0;
}

.chart-placeholder {
  display: flex;
  align-items: end;
  justify-content: space-around;
  height: 200px;
  padding: 20px 0;
}

.chart-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.chart-bar {
  width: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px 4px 0 0;
  transition: height 0.3s ease;
}

.chart-label {
  font-size: 12px;
  color: #718096;
}

.usage-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.usage-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.station-name {
  font-size: 14px;
  color: #1a202c;
  min-width: 100px;
}

.usage-bar {
  flex: 1;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
}

.usage-fill {
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.usage-percent {
  font-size: 14px;
  font-weight: 600;
  color: #1a202c;
  min-width: 40px;
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .modules-grid {
    grid-template-columns: 1fr;
  }
  
  .realtime-grid {
    grid-template-columns: 1fr;
  }
}
</style>