<template>
  <div class="charging-pricing">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <el-icon class="title-icon"><Money /></el-icon>
            价格管理
          </h1>
          <p class="page-description">充电价格设置、优惠策略管理</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="addPricing">
            <el-icon><Plus /></el-icon>
            新增价格策略
          </el-button>
        </div>
      </div>
    </div>

    <!-- 价格概览 -->
    <div class="pricing-overview">
      <div class="overview-cards">
        <div class="overview-card">
          <div class="card-icon current">
            <el-icon><Money /></el-icon>
          </div>
          <div class="card-content">
            <h3>当前电价</h3>
            <div class="card-value">¥{{ currentPrice }}/kWh</div>
            <div class="card-change positive">+5.2% 较上月</div>
          </div>
        </div>

        <div class="overview-card">
          <div class="card-icon promotions">
            <el-icon><Discount /></el-icon>
          </div>
          <div class="card-content">
            <h3>活跃优惠</h3>
            <div class="card-value">{{ activePromotions }}</div>
            <div class="card-change">{{ totalPromotions }} 总策略</div>
          </div>
        </div>

        <div class="overview-card">
          <div class="card-icon revenue">
            <el-icon><TrendCharts /></el-icon>
          </div>
          <div class="card-content">
            <h3>今日收入</h3>
            <div class="card-value">¥{{ todayRevenue }}</div>
            <div class="card-change positive">+12.8% 较昨日</div>
          </div>
        </div>

        <div class="overview-card">
          <div class="card-icon usage">
            <el-icon><Timer /></el-icon>
          </div>
          <div class="card-content">
            <h3>平均使用时长</h3>
            <div class="card-value">{{ avgUsageTime }}h</div>
            <div class="card-change">{{ totalUsageToday }}h 今日</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 价格策略表格 -->
    <div class="pricing-table-section">
      <div class="section-header">
        <h2>价格策略</h2>
        <div class="section-actions">
          <el-input
            v-model="searchQuery"
            placeholder="搜索策略"
            style="width: 300px"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
      </div>

      <el-table :data="pricingStrategies" style="width: 100%">
        <el-table-column prop="name" label="策略名称" width="200" />
        <el-table-column prop="type" label="类型" width="120">
          <template #default="scope">
            <el-tag :type="getPricingTypeColor(scope.row.type)" size="small">
              {{ getPricingTypeText(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="price" label="价格(¥/kWh)" width="120" />
        <el-table-column prop="timeRange" label="时间段" width="180" />
        <el-table-column prop="discount" label="折扣" width="100">
          <template #default="scope">
            <span v-if="scope.row.discount">{{ scope.row.discount }}%</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'active' ? 'success' : 'info'" size="small">
              {{ scope.row.status === 'active' ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="validUntil" label="有效期至" width="120" />
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button size="small" @click="editPricing(scope.row)">编辑</el-button>
            <el-button 
              size="small" 
              :type="scope.row.status === 'active' ? 'warning' : 'success'"
              @click="togglePricingStatus(scope.row)"
            >
              {{ scope.row.status === 'active' ? '禁用' : '启用' }}
            </el-button>
            <el-button size="small" type="danger" @click="deletePricing(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Money,
  Plus,
  Discount,
  TrendCharts,
  Timer,
  Search
} from '@element-plus/icons-vue'

// 响应式数据
const searchQuery = ref('')
const currentPrice = ref(1.2)
const activePromotions = ref(2)
const totalPromotions = ref(5)
const todayRevenue = ref(8650)
const avgUsageTime = ref(2.5)
const totalUsageToday = ref(45)

// 价格策略数据
const pricingStrategies = ref([
  {
    id: 1,
    name: '标准价格',
    type: 'standard',
    price: 1.2,
    timeRange: '全天',
    discount: null,
    status: 'active',
    validUntil: '长期有效'
  },
  {
    id: 2,
    name: '夜间优惠',
    type: 'night',
    price: 0.8,
    timeRange: '22:00-06:00',
    discount: 33,
    status: 'active',
    validUntil: '2024-12-31'
  },
  {
    id: 3,
    name: '高峰价格',
    type: 'peak',
    price: 1.5,
    timeRange: '08:00-10:00, 18:00-20:00',
    discount: null,
    status: 'active',
    validUntil: '长期有效'
  },
  {
    id: 4,
    name: '会员专享',
    type: 'member',
    price: 1.0,
    timeRange: '全天',
    discount: 17,
    status: 'active',
    validUntil: '2024-06-30'
  },
  {
    id: 5,
    name: '节假日特价',
    type: 'holiday',
    price: 0.9,
    timeRange: '节假日全天',
    discount: 25,
    status: 'inactive',
    validUntil: '2024-12-31'
  }
])

// 方法
const getPricingTypeColor = (type: string) => {
  const colors = {
    standard: '',
    night: 'success',
    peak: 'warning',
    member: 'primary',
    holiday: 'danger'
  }
  return colors[type] || ''
}

const getPricingTypeText = (type: string) => {
  const texts = {
    standard: '标准',
    night: '夜间',
    peak: '高峰',
    member: '会员',
    holiday: '节假日'
  }
  return texts[type] || type
}

const addPricing = () => {
  ElMessage.info('新增价格策略功能开发中...')
}

const editPricing = (pricing: any) => {
  ElMessage.info(`编辑价格策略: ${pricing.name}`)
}

const togglePricingStatus = (pricing: any) => {
  pricing.status = pricing.status === 'active' ? 'inactive' : 'active'
  ElMessage.success(`价格策略已${pricing.status === 'active' ? '启用' : '禁用'}`)
}

const deletePricing = (pricing: any) => {
  ElMessage.warning(`删除价格策略: ${pricing.name}`)
}
</script>

<style scoped>
.charging-pricing {
  padding: 24px;
  background: #f8fafc;
  min-height: 100vh;
}

/* 页面头部样式 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 32px;
  color: white;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 32px;
}

.page-description {
  font-size: 16px;
  opacity: 0.9;
  margin: 0;
}

/* 价格概览样式 */
.pricing-overview {
  margin-bottom: 32px;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
}

.overview-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.card-icon.current {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.card-icon.promotions {
  background: linear-gradient(135deg, #f093fb, #f5576c);
}

.card-icon.revenue {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.card-icon.usage {
  background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.card-content h3 {
  font-size: 14px;
  color: #718096;
  margin: 0 0 8px 0;
  font-weight: 500;
}

.card-value {
  font-size: 24px;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 4px 0;
}

.card-change {
  font-size: 12px;
  color: #718096;
}

.card-change.positive {
  color: #38a169;
}

/* 表格区域样式 */
.pricing-table-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.section-header h2 {
  font-size: 20px;
  font-weight: 600;
  color: #1a202c;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .overview-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .overview-cards {
    grid-template-columns: 1fr;
  }
  
  .header-content {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }
}
</style>
