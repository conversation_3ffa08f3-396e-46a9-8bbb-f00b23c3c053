import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios'
import { ElMessage } from 'element-plus'

// API响应接口
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

// 请求配置接口
interface RequestConfig extends AxiosRequestConfig {
  retry?: number
  retryDelay?: number
  showError?: boolean
}

// 创建axios实例
const createApiInstance = (): AxiosInstance => {
  const instance = axios.create({
    baseURL: '/api',
    timeout: 30000, // 增加超时时间到30秒
    headers: {
      'Content-Type': 'application/json'
    }
  })

  // 请求拦截器
  instance.interceptors.request.use(
    (config) => {
      // 添加请求时间戳，避免缓存
      if (config.method === 'get') {
        config.params = {
          ...config.params,
          _t: Date.now()
        }
      }
      
      console.log(`[API Request] ${config.method?.toUpperCase()} ${config.url}`)
      return config
    },
    (error) => {
      console.error('[API Request Error]', error)
      return Promise.reject(error)
    }
  )

  // 响应拦截器
  instance.interceptors.response.use(
    (response: AxiosResponse) => {
      console.log(`[API Response] ${response.config.method?.toUpperCase()} ${response.config.url} - ${response.status}`)
      return response
    },
    async (error: AxiosError) => {
      const config = error.config as RequestConfig
      
      console.error(`[API Error] ${config?.method?.toUpperCase()} ${config?.url}`, {
        status: error.response?.status,
        message: error.message,
        code: error.code
      })

      // 处理网络错误
      if (error.code === 'NETWORK_ERROR' || error.code === 'ERR_NETWORK') {
        if (config?.showError !== false) {
          ElMessage.error('网络连接失败，请检查网络设置')
        }
        return Promise.reject(new Error('网络连接失败'))
      }

      // 处理超时错误
      if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
        if (config?.showError !== false) {
          ElMessage.error('请求超时，请稍后重试')
        }
        return Promise.reject(new Error('请求超时'))
      }

      // 处理服务器错误
      if (error.response?.status && error.response.status >= 500) {
        if (config?.showError !== false) {
          ElMessage.error('服务器内部错误，请稍后重试')
        }
        return Promise.reject(new Error('服务器错误'))
      }

      // 处理客户端错误
      if (error.response?.status && error.response.status >= 400 && error.response.status < 500) {
        const message = (error.response.data as any)?.message || '请求失败'
        if (config?.showError !== false) {
          ElMessage.error(message)
        }
        return Promise.reject(new Error(message))
      }

      return Promise.reject(error)
    }
  )

  return instance
}

// 重试函数
const retryRequest = async <T>(
  requestFn: () => Promise<AxiosResponse<T>>,
  retries: number = 3,
  delay: number = 1000
): Promise<AxiosResponse<T>> => {
  try {
    return await requestFn()
  } catch (error) {
    if (retries > 0) {
      console.log(`[API Retry] Retrying request, ${retries} attempts left`)
      await new Promise(resolve => setTimeout(resolve, delay))
      return retryRequest(requestFn, retries - 1, delay * 2)
    }
    throw error
  }
}

// 创建API实例
const api = createApiInstance()

// 导出带重试功能的API方法
export const apiWithRetry = {
  get: <T = any>(url: string, config?: RequestConfig) => {
    const retries = config?.retry ?? 2
    const delay = config?.retryDelay ?? 1000
    return retryRequest(() => api.get<T>(url, config), retries, delay)
  },
  
  post: <T = any>(url: string, data?: any, config?: RequestConfig) => {
    const retries = config?.retry ?? 1
    const delay = config?.retryDelay ?? 1000
    return retryRequest(() => api.post<T>(url, data, config), retries, delay)
  },
  
  put: <T = any>(url: string, data?: any, config?: RequestConfig) => {
    const retries = config?.retry ?? 1
    const delay = config?.retryDelay ?? 1000
    return retryRequest(() => api.put<T>(url, data, config), retries, delay)
  },
  
  delete: <T = any>(url: string, config?: RequestConfig) => {
    const retries = config?.retry ?? 1
    const delay = config?.retryDelay ?? 1000
    return retryRequest(() => api.delete<T>(url, config), retries, delay)
  }
}

// 导出默认API实例
export default api

// 导出类型
export type { ApiResponse, RequestConfig }