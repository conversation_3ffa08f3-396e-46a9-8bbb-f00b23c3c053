-- 更新AI聊天日志表以支持文件分析
ALTER TABLE ai_chat_logs 
ADD COLUMN file_name VARCHAR(255) NULL AFTER response,
ADD COLUMN file_type VARCHAR(100) NULL AFTER file_name,
ADD COLUMN file_size INT NULL AFTER file_type;

-- 创建AI助手配置表
CREATE TABLE IF NOT EXISTS ai_assistant_config (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL DEFAULT '小E',
  full_name VARCHAR(200) NOT NULL DEFAULT 'EVAdmin Pro AI Assistant',
  description TEXT NOT NULL,
  avatar_url VARCHAR(500) DEFAULT '/images/ai-avatar.svg',
  model VARCHAR(100) DEFAULT 'gpt-4o',
  temperature DECIMAL(3,2) DEFAULT 0.7,
  max_tokens INT DEFAULT 2000,
  capabilities JSON,
  system_prompt TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入默认AI助手配置
INSERT INTO ai_assistant_config (
  name, 
  full_name, 
  description, 
  avatar_url, 
  capabilities,
  system_prompt
) VALUES (
  '小E',
  'EVAdmin Pro AI Assistant',
  '我是EVAdmin Pro的专属AI助手，专门为新能源汽车管理系统提供智能服务。我可以帮您解答系统使用问题、分析数据、处理文件，还能识别图片和视频内容。',
  '/images/ai-avatar.svg',
  JSON_ARRAY(
    '文本对话和问答',
    '文件内容分析', 
    '图片和视频识别',
    '系统功能指导',
    '数据分析建议',
    '故障诊断帮助'
  ),
  '你是EVAdmin Pro新能源汽车智能综合管理系统的专属AI助手，名字叫"小E"。你需要以友好、专业的态度帮助用户使用系统，回答问题，分析数据。'
) ON DUPLICATE KEY UPDATE
  updated_at = CURRENT_TIMESTAMP;

-- 更新知识库数据，添加更多系统相关信息
INSERT INTO ai_knowledge_base (category, question, answer, keywords, priority) VALUES
('系统架构', '系统使用了什么技术栈', '系统采用现代化技术栈：前端使用Vue 3 + TypeScript + Element Plus，后端使用Node.js + Express + TypeScript，数据库使用MySQL 8.0，支持实时通信WebSocket，集成高德地图API，采用Docker容器化部署。', '技术栈,Vue,Node.js,MySQL,架构', 5),
('系统架构', '系统有哪些核心模块', '系统包含六大核心模块：1. 智能电池管理 - 电池监控和优化 2. 智能充电服务 - 充电站管理和预约 3. 车辆管理 - 车辆信息和状态监控 4. ADAS系统 - 高级驾驶辅助 5. 车联网交互 - 远程控制和诊断 6. 用户生态 - 个性化服务和社区', '模块,功能,电池,充电,车辆,ADAS', 5),
('数据分析', '如何查看系统统计数据', '系统提供多维度数据分析：1. 仪表板显示关键指标概览 2. 各模块都有专门的统计页面 3. 支持自定义时间范围查询 4. 提供图表和报表导出功能 5. 实时数据更新和告警', '统计,数据,分析,图表,报表', 4),
('故障处理', '系统出现问题如何处理', '遇到问题时的处理步骤：1. 查看系统状态页面确认服务状态 2. 检查网络连接和权限设置 3. 尝试刷新页面或重新登录 4. 查看错误提示信息 5. 联系技术支持获取帮助', '故障,问题,处理,支持,错误', 4),
('安全管理', '系统如何保障数据安全', '系统采用多层安全保障：1. JWT身份认证和授权 2. 数据传输HTTPS加密 3. 数据库访问权限控制 4. 操作日志审计 5. 定期安全扫描和更新 6. 用户隐私保护机制', '安全,数据,加密,权限,隐私', 5)
ON DUPLICATE KEY UPDATE
  answer = VALUES(answer),
  updated_at = CURRENT_TIMESTAMP;
