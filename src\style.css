/* 导入现代化主题样式 */
@import './styles/modern-theme.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: var(--font-family);
  line-height: 1.5;
  font-weight: 400;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 全局现代化样式覆盖 */
body {
  background: var(--bg-primary);
  color: var(--text-primary);
  overflow-x: hidden;
}

#app {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
  position: relative;
}

/* 现代化Element Plus组件样式覆盖 */
.modern-button {
  border-radius: var(--radius-lg) !important;
  font-weight: var(--font-medium) !important;
  transition: all var(--duration-normal) var(--ease-smooth) !important;
  border: none !important;
  position: relative !important;
  overflow: hidden !important;
}

.modern-button--primary {
  background: var(--primary-gradient) !important;
  box-shadow: var(--modern-shadow-md) var(--primary-shadow) !important;
}

.modern-button--primary:hover {
  transform: translateY(-2px) !important;
  box-shadow: var(--modern-shadow-lg) var(--primary-shadow) !important;
}

.modern-button--success {
  background: var(--success-gradient) !important;
  box-shadow: var(--modern-shadow-md) var(--success-shadow) !important;
}

.modern-button--warning {
  background: var(--warning-gradient) !important;
  box-shadow: var(--modern-shadow-md) var(--warning-shadow) !important;
}

.modern-button--danger {
  background: var(--danger-gradient) !important;
  box-shadow: var(--modern-shadow-md) var(--danger-shadow) !important;
}

.el-card {
  background: var(--bg-glass) !important;
  backdrop-filter: var(--glass-backdrop) !important;
  border: 1px solid var(--border-glass) !important;
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--modern-shadow-lg) rgba(0, 0, 0, 0.1) !important;
}

.el-input__wrapper {
  background: var(--bg-glass) !important;
  backdrop-filter: var(--glass-backdrop-sm) !important;
  border: 1px solid var(--border-glass) !important;
  border-radius: var(--radius-lg) !important;
  transition: all var(--duration-normal) var(--ease-smooth) !important;
}

.el-input__wrapper:hover {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 1px var(--primary-color) !important;
}

.el-input__wrapper.is-focus {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 2px var(--primary-shadow) !important;
}

.el-table {
  background: var(--bg-glass) !important;
  backdrop-filter: var(--glass-backdrop) !important;
  border-radius: var(--radius-xl) !important;
  overflow: hidden !important;
}

.el-table__header {
  background: linear-gradient(135deg, var(--bg-glass) 0%, var(--bg-glass-hover) 100%) !important;
}

.el-table__row:hover {
  background: var(--bg-glass-hover) !important;
}

.el-dialog {
  background: var(--bg-glass) !important;
  backdrop-filter: var(--glass-backdrop) !important;
  border: 1px solid var(--border-glass) !important;
  border-radius: var(--radius-2xl) !important;
  box-shadow: var(--modern-shadow-2xl) rgba(0, 0, 0, 0.3) !important;
}

.el-message {
  background: var(--bg-glass) !important;
  backdrop-filter: var(--glass-backdrop) !important;
  border: 1px solid var(--border-glass) !important;
  border-radius: var(--radius-lg) !important;
  box-shadow: var(--modern-shadow-lg) rgba(0, 0, 0, 0.2) !important;
}

/* 现代化滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-glass);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-gradient);
  border-radius: var(--radius-full);
  transition: background var(--duration-normal) var(--ease-smooth);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--primary-light) 0%, var(--secondary-color) 100%);
}

