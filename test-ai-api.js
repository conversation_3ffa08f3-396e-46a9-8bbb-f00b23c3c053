const axios = require('axios');

async function testAIAPI() {
  const baseURL = 'http://localhost:3001/api/ai-enhanced';
  
  console.log('🤖 测试AI助手API...\n');
  
  try {
    // 测试AI信息获取
    console.log('1. 测试AI信息获取...');
    const infoResponse = await axios.get(`${baseURL}/info`);
    console.log('✅ AI信息:', infoResponse.data.data.name, '-', infoResponse.data.data.description);
    
    // 测试AI状态检查
    console.log('\n2. 测试AI状态检查...');
    const statusResponse = await axios.get(`${baseURL}/status`);
    console.log('✅ AI状态:', statusResponse.data.status, '-', statusResponse.data.message);
    console.log('OpenAI连接:', statusResponse.data.openaiConnected ? '✅ 已连接' : '❌ 未连接');
    
    // 测试聊天功能
    console.log('\n3. 测试聊天功能...');
    const chatResponse = await axios.post(`${baseURL}/chat`, {
      message: '系统有哪些核心功能？',
      context: []
    });
    
    if (chatResponse.data.success) {
      console.log('✅ 聊天测试成功');
      console.log('AI回复:', chatResponse.data.response.substring(0, 100) + '...');
    } else {
      console.log('❌ 聊天测试失败:', chatResponse.data.message);
    }
    
    // 测试知识库搜索
    console.log('\n4. 测试不同类型的问题...');
    const questions = [
      '电池健康度如何计算？',
      '如何查找充电站？',
      'ADAS功能怎么使用？'
    ];
    
    for (const question of questions) {
      try {
        const response = await axios.post(`${baseURL}/chat`, {
          message: question,
          context: []
        });
        
        if (response.data.success) {
          console.log(`✅ "${question}" - 回复长度: ${response.data.response.length}字符`);
        } else {
          console.log(`❌ "${question}" - 失败: ${response.data.message}`);
        }
      } catch (error) {
        console.log(`❌ "${question}" - 错误: ${error.message}`);
      }
    }
    
    console.log('\n🎉 AI API测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  }
}

testAIAPI();
