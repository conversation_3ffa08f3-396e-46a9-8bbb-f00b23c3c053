<template>
  <div class="login-container">
    <!-- 简洁现代背景 -->
    <div class="modern-background">
      <!-- 几何装饰元素 -->
      <div class="geometric-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
        <div class="shape shape-4"></div>
      </div>
      
      <!-- 动态粒子 -->
      <div class="floating-particles">
        <div 
          v-for="i in 8" 
          :key="`particle-${i}`" 
          class="particle"
          :style="getParticleStyle(i)"
        />
      </div>
    </div>

    <!-- 左右布局容器 -->
    <div class="main-layout">
      <!-- 左侧3D旋转动画区域 -->
      <div class="left-section">
        <div class="animation-container">
          <!-- 新的3D旋转动画 -->
          <div class="mainWrap">
            <div class="wrapper">
              <div class="c1">
                <div class="c2">
                  <div class="c3">
                    <div class="rect1">
                      <div class="miniC">
                        <div class="miniC1"></div>
                        <div class="miniC2"></div>
                        <div class="miniC3"></div>
                        <div class="miniC4"></div>
                      </div>
                      <div class="c4">
                        <div class="rect2"><div class="rect3"></div></div>
                      </div>
                      <div class="c5"></div>
                      <div class="c6"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="loading-text">新能源汽车系统管理平台</div>
          <div class="loading-subtitle">智能 · 绿色 · 未来</div>
        </div>
      </div>

      <!-- 右侧登录区域 -->
      <div class="right-section">
        <div class="login-content">
      <!-- Brutalist登录卡片 -->
      <div class="brutalist-login-card">
        <div class="card-title">
          <span class="title-text">EV SYSTEM</span>
        </div>
        <div class="card-form">
          <!-- 邮箱输入 -->
          <div class="input-group">
            <input 
              v-model="form.email"
              required 
              :placeholder="isLogin ? 'Email/Username' : '邮箱地址'" 
              class="brutalist-input" 
              type="email" 
            />
          </div>
          
          <!-- 注册模式下的姓名输入 -->
          <div v-if="!isLogin" class="input-group">
            <input 
              v-model="form.name"
              required 
              placeholder="姓名" 
              class="brutalist-input" 
              type="text" 
            />
          </div>
          
          <!-- 注册模式下的手机号输入 -->
          <div v-if="!isLogin" class="input-group">
            <input 
              v-model="form.phone"
              required 
              placeholder="手机号码" 
              class="brutalist-input" 
              type="tel" 
            />
          </div>
          
          <!-- 密码输入 -->
          <div class="input-group">
            <input 
              v-model="form.password"
              required 
              :placeholder="isLogin ? 'Password' : '密码（至少6位）'" 
              class="brutalist-input" 
              type="password" 
            />
          </div>
          
          <!-- 注册模式下的确认密码输入 -->
          <div v-if="!isLogin" class="input-group">
            <input 
              v-model="form.confirmPassword"
              required 
              placeholder="确认密码" 
              class="brutalist-input" 
              type="password" 
            />
          </div>
          
          <button 
            class="brutalist-btn" 
            type="submit"
            @click="handleSubmit"
            :disabled="loading"
          >
            {{ loading ? (isLogin ? 'ACCESSING...' : '注册中...') : (isLogin ? 'ENTER ZONE' : '创建账户') }}
          </button>
        </div>
      </div>

      <!-- 切换登录/注册 -->
      <div class="mode-switch">
        <span class="switch-text">
          {{ isLogin ? 'New to EV System?' : 'Already have access?' }}
        </span>
        <button class="switch-btn" @click="toggleMode">
          {{ isLogin ? 'CREATE ACCOUNT' : 'LOGIN NOW' }}
        </button>
      </div>

      <!-- 第三方登录 -->
      <div class="social-login">
        <div class="divider">
          <span>ALTERNATIVE ACCESS</span>
        </div>
        <div class="social-buttons">
          <button class="social-btn github-btn" @click="handleSocialLogin('github')">
            <span>GITHUB</span>
          </button>
          <button class="social-btn google-btn" @click="handleSocialLogin('google')">
            <span>GOOGLE</span>
          </button>
        </div>
      </div>
        </div>
      </div>
    </div>

    <!-- 版权信息 -->
    <div class="login-footer">
      <p>&copy; 2025 新能源汽车智能管理系统. 绿色出行，智慧未来.</p>
    </div>

    <!-- 登录成功加载动画 -->
    <BikeLoader 
      v-if="showLoginLoader" 
      :fullscreen="true" 
      :text="loginLoaderText" 
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { BikeLoader } from '@/components'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

// 状态管理
const isLogin = ref(true)
const loading = ref(false)
const showLoginLoader = ref(false)
const loginLoaderText = ref('ACCESSING SYSTEM...')

// 表单数据
const form = reactive({
  email: '',
  password: '',
  name: '',
  phone: '',
  confirmPassword: ''
})

// 简化的粒子动画样式
const getParticleStyle = (index: number) => {
  const delay = index * 0.3
  const duration = 4 + Math.random() * 2
  const size = 6 + Math.random() * 8
  const left = Math.random() * 100
  const top = Math.random() * 100
  
  return {
    left: `${left}%`,
    top: `${top}%`,
    width: `${size}px`,
    height: `${size}px`,
    animationDelay: `${delay}s`,
    animationDuration: `${duration}s`
  }
}

// 切换登录/注册模式
const toggleMode = () => {
  isLogin.value = !isLogin.value
  // 清空表单
  Object.assign(form, {
    email: '',
    password: '',
    name: '',
    phone: '',
    confirmPassword: ''
  })
}

// 处理表单提交
const handleSubmit = async () => {
  if (isLogin.value) {
    await handleLogin()
  } else {
    await handleRegister()
  }
}

// 表单验证
const validateForm = () => {
  if (!form.email || !form.password) {
    ElMessage.error('请填写所有必填字段')
    return false
  }
  
  // 邮箱格式验证
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(form.email)) {
    ElMessage.error('请输入有效的邮箱地址')
    return false
  }
  
  // 密码长度验证
  if (form.password.length < 6) {
    ElMessage.error('密码长度至少6位')
    return false
  }
  
  // 注册模式下的额外验证
  if (!isLogin.value) {
    if (!form.name || !form.phone) {
      ElMessage.error('请填写所有必填字段')
      return false
    }
    
    if (form.password !== form.confirmPassword) {
      ElMessage.error('两次输入的密码不一致')
      return false
    }
    
    // 手机号格式验证
    const phoneRegex = /^1[3-9]\d{9}$/
    if (!phoneRegex.test(form.phone)) {
      ElMessage.error('请输入有效的手机号码')
      return false
    }
  }
  
  return true
}

// 处理登录
const handleLogin = async () => {
  if (!validateForm()) return
  
  try {
    loading.value = true
    const result = await authStore.login({
      email: form.email,
      password: form.password,
      remember: false
    })
    
    if (result.success) {
      ElMessage.success('ACCESS GRANTED!')
      
      // 显示加载动画
      loading.value = false
      showLoginLoader.value = true
      loginLoaderText.value = 'ACCESSING SYSTEM...'
      
      // 延迟跳转
      setTimeout(() => {
        const redirect = router.currentRoute.value.query.redirect as string
        router.push(redirect || '/')
      }, 2000)
    } else {
      ElMessage.error(result.message || 'ACCESS DENIED')
    }
  } catch (error) {
    console.error('Login error:', error)
    ElMessage.error('ACCESS DENIED')
  } finally {
    loading.value = false
  }
}

// 处理注册
const handleRegister = async () => {
  if (!validateForm()) return
  
  try {
    loading.value = true
    const result = await authStore.register({
      email: form.email,
      name: form.name,
      phone: form.phone,
      password: form.password
    })
    
    if (result.success) {
      ElMessage.success('注册成功！请登录')
      // 切换到登录模式
      isLogin.value = true
      // 保留邮箱，清空其他字段
      const email = form.email
      Object.assign(form, {
        email,
        password: '',
        name: '',
        phone: '',
        confirmPassword: ''
      })
    } else {
      ElMessage.error(result.message || '注册失败')
    }
  } catch (error) {
    console.error('Register error:', error)
    ElMessage.error('注册失败，请重试')
  } finally {
    loading.value = false
  }
}



// 处理第三方登录
const handleSocialLogin = async (provider: string) => {
  try {
    loading.value = true
    ElMessage.info(`正在跳转到${provider}登录...`)
    
    // 构建第三方登录URL
    const baseUrl = window.location.origin
    const redirectUri = `${baseUrl}/auth/callback/${provider.toLowerCase()}`
    
    let authUrl = ''
    
    if (provider.toLowerCase() === 'github') {
      // GitHub OAuth配置
      const clientId = import.meta.env.VITE_GITHUB_CLIENT_ID || 'your_github_client_id'
      const scope = 'user:email'
      authUrl = `https://github.com/login/oauth/authorize?client_id=${clientId}&redirect_uri=${encodeURIComponent(redirectUri)}&scope=${scope}&state=${generateRandomState()}`
    } else if (provider.toLowerCase() === 'google') {
      // Google OAuth配置
      const clientId = import.meta.env.VITE_GOOGLE_CLIENT_ID || 'your_google_client_id'
      const scope = 'openid email profile'
      authUrl = `https://accounts.google.com/oauth/authorize?client_id=${clientId}&redirect_uri=${encodeURIComponent(redirectUri)}&scope=${encodeURIComponent(scope)}&response_type=code&state=${generateRandomState()}`
    }
    
    if (authUrl) {
      // 保存状态到sessionStorage
      sessionStorage.setItem('oauth_provider', provider.toLowerCase())
      sessionStorage.setItem('oauth_redirect', router.currentRoute.value.query.redirect as string || '/')
      
      // 跳转到第三方登录页面
      window.location.href = authUrl
    } else {
      ElMessage.error(`${provider}登录配置错误`)
    }
  } catch (error) {
    console.error(`${provider} login error:`, error)
    ElMessage.error(`${provider}登录失败`)
  } finally {
    loading.value = false
  }
}

// 生成随机状态码
const generateRandomState = () => {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)
}

// 处理OAuth回调
const handleOAuthCallback = async () => {
  const urlParams = new URLSearchParams(window.location.search)
  const code = urlParams.get('code')
  const state = urlParams.get('state')
  const provider = sessionStorage.getItem('oauth_provider')
  
  if (code && provider) {
    try {
      loading.value = true
      showLoginLoader.value = true
      loginLoaderText.value = '正在验证第三方登录...'
      
      const response = await fetch('/api/auth/oauth/callback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          provider,
          code,
          state
        })
      })
      
      const result = await response.json()
      
      if (result.success && result.data) {
        // 保存用户信息
        authStore.user = result.data.user
        authStore.token = result.data.token
        
        localStorage.setItem('auth_token', result.data.token)
        localStorage.setItem('auth_user', JSON.stringify(result.data.user))
        
        ElMessage.success('登录成功！')
        
        // 跳转到目标页面
        const redirect = sessionStorage.getItem('oauth_redirect') || '/'
        sessionStorage.removeItem('oauth_provider')
        sessionStorage.removeItem('oauth_redirect')
        
        setTimeout(() => {
          router.push(redirect)
        }, 1000)
      } else {
        ElMessage.error(result.message || '第三方登录失败')
        showLoginLoader.value = false
      }
    } catch (error) {
      console.error('OAuth callback error:', error)
      ElMessage.error('第三方登录验证失败')
      showLoginLoader.value = false
    } finally {
      loading.value = false
    }
  }
}

// 组件挂载时检查是否是OAuth回调
const checkOAuthCallback = () => {
  const urlParams = new URLSearchParams(window.location.search)
  if (urlParams.get('code')) {
    handleOAuthCallback()
  }
}

// 在组件挂载时执行
onMounted(() => {
  checkOAuthCallback()
})
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  background: linear-gradient(135deg, 
    #1a1a2e 0%, 
    #16213e 25%, 
    #0f3460 50%, 
    #533483 75%, 
    #7209b7 100%
  );
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
  overflow: hidden;
}

/* 主布局容器 */
.main-layout {
  display: flex;
  width: 100%;
  height: 100vh;
  position: relative;
  z-index: 1;
}

/* 左侧区域 */
.left-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

/* 右侧区域 */
.right-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

/* 3D旋转动画容器 */
.animation-container {
  text-align: center;
  color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 20px;
}

/* 新的3D旋转动画样式 */
.mainWrap {
  position: relative;
  perspective: 1000px;
  transform-origin: center center;
}

.mainWrap div {
  transform-origin: center center;
}

.wrapper {
  position: relative;
}

.c1 {
  border-radius: 100%;
  height: 200px;
  width: 200px;
  border: 1px solid #e7b439;
  animation: 
    rotFirst 30s linear infinite,
    fadeIn 2s forwards;
  transform: rotateX(10deg) rotateY(10deg);
  transform-style: preserve-3d;
}

.c1 .c2 {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotateX(10deg) rotateY(10deg);
  border-radius: 100%;
  height: 190px;
  width: 190px;
  border: 1px solid #ffb61e;
  border-style: dashed;
}

.c1 .c2 .c3 {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotateX(10deg) rotateY(10deg);
  border: 1px solid #ffb61e;
  height: 180px;
  width: 180px;
  border-radius: 100%;
  animation: changeColor 30s linear infinite;
}

.c1 .c2 .c3 .rect1 {
  border: 1px solid #ffb61e;
  border-style: dotted;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotateX(10deg) rotateY(10deg);
  height: 120px;
  width: 120px;
}

.c1 .c2 .c3 .rect1 .miniC {
  text-align: center;
  height: 120px;
  width: 120px;
  position: relative;
}

.c1 .c2 .c3 .rect1 .miniC .box {
  height: 10px;
  width: 10px;
  border: 1px solid #ffb61e;
  border-radius: 100%;
  margin: 4px;
}

.c1 .c2 .c3 .rect1 .miniC .miniC1 {
  position: absolute;
  top: -12%;
  left: 50%;
  transform: translate(-50%, -50%) rotateX(10deg) rotateY(10deg);
  height: 20px;
  width: 20px;
  border: 3px solid #ffb61e;
  border-radius: 100%;
  animation: changeColor 30s linear infinite;
}

.c1 .c2 .c3 .rect1 .miniC .miniC2 {
  position: absolute;
  top: 50%;
  left: -12%;
  transform: translate(-50%, -50%) rotateX(10deg) rotateY(10deg);
  height: 20px;
  width: 20px;
  border: 3px solid #ffb61e;
  border-radius: 100%;
  animation: changeColor 30s linear infinite;
}

.c1 .c2 .c3 .rect1 .miniC .miniC3 {
  position: absolute;
  top: 50%;
  left: 112%;
  transform: translate(-50%, -50%) rotateX(10deg) rotateY(10deg);
  height: 20px;
  width: 20px;
  border: 3px solid #ffb61e;
  border-radius: 100%;
  animation: changeColor 30s linear infinite;
}

.c1 .c2 .c3 .rect1 .miniC .miniC4 {
  position: absolute;
  top: 112%;
  left: 50%;
  transform: translate(-50%, -50%) rotateX(10deg) rotateY(10deg);
  height: 20px;
  width: 20px;
  border: 3px solid #ffb61e;
  border-radius: 100%;
  animation: changeColor 30s linear infinite;
}

.c1 .c2 .c3 .rect1 .c4 {
  border: 1px solid #ffb61e;
  height: 120px;
  width: 120px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotateX(10deg) rotateY(10deg);
  border-radius: 100%;
  border-style: dotted;
  animation: changeColor 30s linear infinite;
}

.c1 .c2 .c3 .rect1 .c4 .rect2 {
  border: 1px solid #ffb61e;
  height: 80px;
  width: 80px;
  margin: 20px auto 0px auto;
  animation: rotminiC 10s linear infinite;
}

.c1 .c2 .c3 .rect1 .c4 .rect3 {
  border: 1px solid #ffb61e;
  height: 80px;
  width: 80px;
  transform: rotate(135deg);
  transform-origin: center center;
}

.c1 .c2 .c3 .rect1 .c5 {
  border: 1px solid #ffb61e;
  height: 70px;
  width: 70px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotateX(10deg) rotateY(10deg);
  border-radius: 100%;
  animation: changeColor 30s linear infinite;
}

.c1 .c2 .c3 .rect1 .c6 {
  border: 3px solid #ffb61e;
  height: 50px;
  width: 50px;
  animation: changeColor 30s linear infinite;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotateX(10deg) rotateY(10deg);
  border-radius: 100%;
}

/* 悬停状态 */
.c1:hover,
.c1 .c2:hover,
.c1 .c2 .c3:hover {
  box-shadow: 0 0 20px #efb61e;
  cursor: pointer;
  transition: 
    transform 0.5s ease-in-out,
    box-shadow 0.5s ease-in-out;
}

/* 动画关键帧 */
@keyframes rotFirst {
  0% {
    transform: rotate(-360deg);
  }
  100% {
    transform: rotate(0deg);
  }
}

@keyframes rotminiC {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(-360deg);
  }
}

@keyframes changeColor {
  0% {
    box-shadow: 0px 0px 70px #fa9542;
    border: 3px solid #fa9642;
  }
}

/* 淡入动画 */
@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}


 


/* 加载文本 */
.loading-text {
  font-size: 1.8rem;
  font-weight: 700;
  color: white;
  margin-top: 80px;
  margin-bottom: 10px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  animation: textGlow 3s ease-in-out infinite;
}

@keyframes textGlow {
  0%, 100% { text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3); }
  50% { text-shadow: 0 0 20px rgba(52, 211, 153, 0.8); }
}

.loading-subtitle {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  letter-spacing: 2px;
  margin-top: 5px;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.circle-3 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

.floating-particles {
  position: absolute;
  width: 100%;
  height: 100%;
}

.particle {
  position: absolute;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  animation: particleFloat linear infinite;
}

@keyframes particleFloat {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
    opacity: 0;
  }
}

.login-content {
  width: 100%;
  max-width: 420px;
  padding: 20px;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-bottom: 1.5rem;
}

.logo-icon {
  font-size: 2.5rem;
  color: white;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

.logo-text {
  font-size: 1.8rem;
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.login-title {
  font-size: 2rem;
  font-weight: 700;
  color: white;
  margin: 0 0 0.5rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.login-subtitle {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.login-form-card {
  margin-bottom: 1.5rem;
}

.login-form {
  padding: 2rem;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.submit-button {
  width: 100%;
  height: 48px;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
}

.form-switch {
  text-align: center;
  color: var(--el-text-color-regular);
}

.switch-text {
  margin-right: 8px;
}

.social-login {
  text-align: center;
}

.divider {
  position: relative;
  margin: 1.5rem 0;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: rgba(255, 255, 255, 0.2);
  z-index: 1;
}

.divider span {
  background: transparent;
  padding: 0 1rem;
  position: relative;
  z-index: 2;
  backdrop-filter: blur(10px);
}

.social-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.social-button {
  flex: 1;
  max-width: 120px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
}

.social-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.login-footer {
  position: absolute;
  bottom: 20px;
  text-align: center;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  z-index: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-content {
    max-width: 90%;
    padding: 10px;
  }
  
  .login-form {
    padding: 1.5rem;
  }
  
  .login-title {
    font-size: 1.5rem;
  }
  
  .social-buttons {
    flex-direction: column;
  }
  
  .social-button {
    max-width: none;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .login-form-card {
    background: rgba(0, 0, 0, 0.3);
  }
}

/* 动画增强 */
.login-content {
  animation: slideInUp 0.8s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-form-card {
  transition: all 0.3s ease;
}

.login-form-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* 移除旧的复杂背景样式，保持简洁 */

/* 现代简洁背景 */
.modern-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.geometric-shapes {
  position: absolute;
  width: 100%;
  height: 100%;
}

.shape {
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  animation: shapeFloat 8s ease-in-out infinite;
}

.shape-1 {
  width: 120px;
  height: 120px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 80px;
  height: 80px;
  top: 70%;
  right: 15%;
  animation-delay: 2s;
}

.shape-3 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

.shape-4 {
  width: 60px;
  height: 60px;
  top: 30%;
  right: 30%;
  animation-delay: 6s;
}

@keyframes shapeFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

/* 移除旧的logo样式 */

.floating-particles {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.particle {
  position: absolute;
  background: linear-gradient(45deg, #34d399, #10b981);
  border-radius: 50%;
  opacity: 0.6;
  animation: particleFloat 6s ease-in-out infinite;
}

@keyframes particleFloat {
  0%, 100% { 
    transform: translateY(0px) scale(1);
    opacity: 0.6;
  }
  50% { 
    transform: translateY(-30px) scale(1.2);
    opacity: 0.8;
  }
}

/* Brutalist登录卡片样式 */
.brutalist-login-card {
  position: relative;
  perspective: 1000px;
  width: 320px;
  margin: 0 auto;
  z-index: 1;
  height: 100px;
  background: linear-gradient(135deg, #ff3366, #ff6b35);
  border: 4px solid #000;
  box-shadow: 
    8px 8px 0 #000, 
    16px 16px 0 rgba(255, 51, 102, 0.3);
  cursor: pointer;
  overflow: hidden;
  transition: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  transform-style: preserve-3d;
}

.brutalist-login-card:hover {
  height: 320px;
  transform: translateZ(20px) rotateX(5deg) rotateY(-5deg);
  box-shadow: 
    12px 12px 0 #000, 
    24px 24px 0 rgba(255, 51, 102, 0.4), 
    0 0 50px rgba(255, 51, 102, 0.6);
}

.card-title {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: inherit;
  transition: all 0.4s ease;
}

.title-text {
  color: #000;
  font-weight: 800;
  font-size: 18px;
  text-transform: uppercase;
  letter-spacing: 3px;
  text-shadow: 2px 2px 0 rgba(255, 255, 255, 0.3);
  transition: all 0.4s ease;
}

.brutalist-login-card:hover .title-text {
  opacity: 0;
  transform: translateY(-30px) scale(0.8);
}

.card-form {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  box-sizing: border-box;
  opacity: 0;
  transform: translateY(30px) scale(0.8);
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.brutalist-login-card:hover .card-form {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.input-group {
  position: relative;
  width: 100%;
  margin-bottom: 16px;
}

.brutalist-input {
  width: 100%;
  padding: 12px 10px;
  background: rgba(255, 255, 255, 0.9);
  border: 3px solid #000;
  font-weight: 700;
  color: #000;
  font-size: 14px;
  box-shadow: 4px 4px 0 #000;
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  outline: none;
}

.brutalist-input:focus {
  transform: translate(2px, 2px);
  box-shadow: 2px 2px 0 #000;
}

.brutalist-input::placeholder {
  color: #000;
  opacity: 0.6;
  text-transform: uppercase;
  font-size: 12px;
  letter-spacing: 1px;
}

.brutalist-btn {
  width: 100%;
  padding: 12px;
  background: #000;
  color: #fff;
  border: none;
  font-weight: 800;
  text-transform: uppercase;
  letter-spacing: 2px;
  cursor: pointer;
  font-size: 14px;
  box-shadow: 4px 4px 0 rgba(255, 255, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.brutalist-btn:hover:not(:disabled) {
  transform: translate(2px, 2px);
  box-shadow: 2px 2px 0 rgba(255, 255, 255, 0.3);
  background: #333;
}

.brutalist-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* 闪光效果 */
.brutalist-login-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  transition: left 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.brutalist-login-card:hover::before {
  left: 100%;
}

/* 几何装饰 */
.brutalist-login-card::after {
  content: "";
  position: absolute;
  top: -4px;
  right: -4px;
  width: 20px;
  height: 20px;
  background: #000;
  clip-path: polygon(0 0, 100% 0, 100% 100%);
  transition: all 0.6s ease;
}

.brutalist-login-card:hover::after {
  transform: scale(1) rotate(0deg);
  background: #34d399;
}

/* 模式切换样式 */
.mode-switch {
  text-align: center;
  margin: 30px 0 20px;
  z-index: 1;
  position: relative;
}

.switch-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  display: block;
  margin-bottom: 10px;
}

.switch-btn {
  background: transparent;
  color: #34d399;
  border: 2px solid #34d399;
  padding: 8px 20px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 12px;
}

.switch-btn:hover {
  background: #34d399;
  color: #000;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(52, 211, 153, 0.3);
}

/* 社交登录样式 */
.social-login {
  margin-top: 20px;
  z-index: 1;
  position: relative;
}

.divider {
  text-align: center;
  margin: 20px 0;
  position: relative;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 2px;
  background: rgba(255, 255, 255, 0.2);
}

.divider span {
  background: linear-gradient(135deg, #1a1a2e, #533483);
  padding: 0 15px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
  z-index: 1;
}

.social-buttons {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.social-btn {
  flex: 1;
  padding: 10px 15px;
  border: 2px solid #000;
  background: rgba(255, 255, 255, 0.9);
  color: #000;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 11px;
  box-shadow: 3px 3px 0 #000;
}

.social-btn:hover {
  transform: translate(1px, 1px);
  box-shadow: 2px 2px 0 #000;
}

.github-btn:hover {
  background: #333;
  color: #fff;
}

.google-btn:hover {
  background: #db4437;
  color: #fff;
  border-color: #db4437;
}

.login-footer p {
  color: #d1fae5 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
  text-align: center;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-layout {
    flex-direction: column;
    height: auto;
    min-height: 100vh;
  }
  
  .left-section {
    padding: 20px;
    min-height: 40vh;
  }
  
  .right-section {
    padding: 20px;
    min-height: 60vh;
  }
  

  
  .loading-text {
    font-size: 1.4rem;
  }
  
  .brutalist-login-card {
    width: 90%;
    max-width: 300px;
  }
  
  .social-buttons {
    flex-direction: column;
  }
  
  .social-btn {
    max-width: none;
  }
}
</style>