<template>
  <div class="chart-component">
    <!-- 图表头部 -->
    <div class="chart-header" v-if="showHeader">
      <div class="chart-title">
        <h3>{{ title }}</h3>
        <p v-if="subtitle" class="chart-subtitle">{{ subtitle }}</p>
      </div>
      
      <div class="chart-controls" v-if="showControls">
        <!-- 时间范围选择 -->
        <el-select 
          v-if="showTimeRange"
          v-model="selectedTimeRange"
          size="small"
          style="width: 120px;"
          @change="handleTimeRangeChange"
        >
          <el-option label="今日" value="today" />
          <el-option label="本周" value="week" />
          <el-option label="本月" value="month" />
          <el-option label="本年" value="year" />
        </el-select>
        
        <!-- 图表类型切换 -->
        <div class="modern-button-group" v-if="showTypeSwitch">
          <ModernButton 
            v-for="type in chartTypes"
            :key="type.value"
            :type="currentChartType === type.value ? 'primary' : 'default'"
            size="small"
            @click="switchChartType(type.value)"
          >
            <el-icon><component :is="type.icon" /></el-icon>
          </ModernButton>
        </div>
        
        <!-- 刷新按钮 -->
        <div style="position: relative;">
          <ModernLoader v-if="loading" />
          <ModernButton 
            size="small"
            :icon="Refresh"
            @click="refreshChart"
            :disabled="loading"
          >
            刷新
          </ModernButton>
        </div>
        
        <!-- 全屏按钮 -->
        <ModernButton 
          size="small"
          :icon="FullScreen"
          @click="toggleFullscreen"
        >
          全屏
        </ModernButton>
      </div>
    </div>
    
    <!-- 图表容器 -->
    <div 
      class="chart-container"
      :class="{ 'fullscreen': isFullscreen, 'loading': loading }"
      ref="chartContainer"
    >
      <!-- 加载状态 -->
      <ModernLoader v-if="loading" />
      
      <!-- 错误状态 -->
      <div v-else-if="error" class="chart-error">
        <el-icon class="error-icon" :size="32">
          <Warning />
        </el-icon>
        <div class="error-text">{{ error }}</div>
        <ModernButton size="small" @click="retryLoad">重试</ModernButton>
      </div>
      
      <!-- 无数据状态 -->
      <div v-else-if="!hasData" class="chart-empty">
        <el-icon class="empty-icon" :size="32">
          <Document />
        </el-icon>
        <div class="empty-text">暂无数据</div>
      </div>
      
      <!-- ECharts 图表 -->
      <v-chart 
        v-else
        ref="chartRef"
        class="chart"
        :option="chartOption"
        :theme="theme"
        autoresize
        @click="handleChartClick"
        @mouseover="handleChartMouseover"
        @mouseout="handleChartMouseout"
      />
    </div>
    
    <!-- 图表说明 -->
    <div class="chart-legend" v-if="showLegend && legendData.length > 0">
      <div class="legend-title">图例说明</div>
      <div class="legend-items">
        <div 
          v-for="item in legendData"
          :key="item.name"
          class="legend-item"
          :class="{ disabled: item.disabled }"
          @click="toggleLegendItem(item)"
        >
          <div 
            class="legend-color"
            :style="{ backgroundColor: item.color }"
          ></div>
          <span class="legend-name">{{ item.name }}</span>
          <span class="legend-value" v-if="item.value">{{ item.value }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import {
  BarChart,
  LineChart,
  PieChart,
  ScatterChart,
  RadarChart,
  GaugeChart
} from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  ToolboxComponent,
  DataZoomComponent,
  VisualMapComponent
} from 'echarts/components';
import VChart from 'vue-echarts';
import {
  Refresh,
  FullScreen,
  Loading,
  Warning,
  Document,
  PieChart as PieChartIcon
} from '@element-plus/icons-vue';
import { BarChart3 as BarChartIcon, TrendingUp as LineChartIcon } from 'lucide-vue-next';
import { ElMessage } from 'element-plus';
import ModernButton from '@/components/ModernButton.vue';
import ModernLoader from '@/components/ui/ModernLoader.vue';

// 注册 ECharts 组件
use([
  CanvasRenderer,
  BarChart,
  LineChart,
  PieChart,
  ScatterChart,
  RadarChart,
  GaugeChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  ToolboxComponent,
  DataZoomComponent,
  VisualMapComponent
]);

// 组件属性
interface Props {
  title?: string;
  subtitle?: string;
  type?: 'line' | 'bar' | 'pie' | 'scatter' | 'radar' | 'gauge';
  data?: any[];
  option?: any;
  height?: string;
  theme?: string;
  showHeader?: boolean;
  showControls?: boolean;
  showTimeRange?: boolean;
  showTypeSwitch?: boolean;
  showLegend?: boolean;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  subtitle: '',
  type: 'line',
  data: () => [],
  option: () => ({}),
  height: '400px',
  theme: 'default',
  showHeader: true,
  showControls: true,
  showTimeRange: true,
  showTypeSwitch: true,
  showLegend: false,
  autoRefresh: false,
  refreshInterval: 30000
});

// 组件事件
const emit = defineEmits<{
  refresh: [];
  timeRangeChange: [range: string];
  chartClick: [params: any];
  chartTypeChange: [type: string];
}>();

// 组件状态
const chartRef = ref();
const chartContainer = ref<HTMLElement>();
const loading = ref(false);
const error = ref('');
const isFullscreen = ref(false);
const selectedTimeRange = ref('today');
const currentChartType = ref<'line' | 'bar' | 'pie' | 'scatter' | 'radar' | 'gauge'>(props.type);
const legendData = ref<any[]>([]);

// 图表类型配置
const chartTypes = [
  { value: 'line', icon: LineChartIcon, name: '折线图' },
  { value: 'bar', icon: BarChartIcon, name: '柱状图' },
  { value: 'pie', icon: PieChartIcon, name: '饼图' }
];

// 自动刷新定时器
let refreshTimer: NodeJS.Timeout | null = null;

// 计算属性
const hasData = computed(() => {
  return props.data && props.data.length > 0;
});

const chartOption = computed(() => {
  if (!hasData.value) return {};
  
  // 如果传入了自定义配置，直接使用
  if (Object.keys(props.option).length > 0) {
    return props.option;
  }
  
  // 根据图表类型生成默认配置
  return generateDefaultOption();
});

// 生成默认图表配置
const generateDefaultOption = () => {
  const baseOption = {
    title: {
      text: props.title,
      subtext: props.subtitle,
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: 'transparent',
      textStyle: {
        color: '#fff'
      }
    },
    legend: {
      top: '10%',
      left: 'center'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    toolbox: {
      feature: {
        saveAsImage: {
          title: '保存为图片'
        },
        dataZoom: {
          title: {
            zoom: '区域缩放',
            back: '区域缩放还原'
          }
        }
      }
    }
  };
  
  switch (currentChartType.value) {
    case 'line':
      return {
        ...baseOption,
        xAxis: {
          type: 'category',
          data: props.data.map(item => item.name || item.x)
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          type: 'line',
          data: props.data.map(item => item.value || item.y),
          smooth: true,
          lineStyle: {
            width: 3
          },
          areaStyle: {
            opacity: 0.3
          }
        }]
      };
      
    case 'bar':
      return {
        ...baseOption,
        xAxis: {
          type: 'category',
          data: props.data.map(item => item.name || item.x)
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          type: 'bar',
          data: props.data.map(item => item.value || item.y),
          itemStyle: {
            borderRadius: [4, 4, 0, 0]
          }
        }]
      };
      
    case 'pie':
      return {
        ...baseOption,
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        series: [{
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['50%', '60%'],
          data: props.data.map(item => ({
            name: item.name,
            value: item.value
          })),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          label: {
            show: true,
            formatter: '{b}: {d}%'
          }
        }]
      };
      
    default:
      return baseOption;
  }
};

// 切换图表类型
const switchChartType = (type: string) => {
  currentChartType.value = type as 'line' | 'bar' | 'pie' | 'scatter' | 'radar' | 'gauge';
  emit('chartTypeChange', type);
};

// 处理时间范围变化
const handleTimeRangeChange = (range: string) => {
  emit('timeRangeChange', range);
};

// 刷新图表
const refreshChart = async () => {
  loading.value = true;
  error.value = '';
  
  try {
    emit('refresh');
    await nextTick();
    
    // 模拟加载延迟
    await new Promise(resolve => setTimeout(resolve, 500));
    
    ElMessage.success('图表刷新成功');
  } catch (err) {
    error.value = '图表刷新失败';
    ElMessage.error('图表刷新失败');
  } finally {
    loading.value = false;
  }
};

// 重试加载
const retryLoad = () => {
  error.value = '';
  refreshChart();
};

// 切换全屏
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value;
  
  if (isFullscreen.value) {
    document.body.style.overflow = 'hidden';
  } else {
    document.body.style.overflow = '';
  }
  
  // 延迟调整图表大小
  setTimeout(() => {
    if (chartRef.value && chartRef.value.resize) {
      chartRef.value.resize();
    }
  }, 300);
};

// 处理图表点击事件
const handleChartClick = (params: any) => {
  emit('chartClick', params);
};

// 处理图表鼠标悬停
const handleChartMouseover = (params: any) => {
  // 可以在这里添加悬停效果
};

// 处理图表鼠标离开
const handleChartMouseout = (params: any) => {
  // 可以在这里添加离开效果
};

// 切换图例项
const toggleLegendItem = (item: any) => {
  item.disabled = !item.disabled;
  
  // 更新图表显示
  if (chartRef.value) {
    chartRef.value.dispatchAction({
      type: 'legendToggleSelect',
      name: item.name
    });
  }
};

// 启动自动刷新
const startAutoRefresh = () => {
  if (props.autoRefresh && props.refreshInterval > 0) {
    refreshTimer = setInterval(() => {
      refreshChart();
    }, props.refreshInterval);
  }
};

// 停止自动刷新
const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
    refreshTimer = null;
  }
};

// 监听数据变化
watch(() => props.data, () => {
  if (chartRef.value && chartRef.value.resize) {
    nextTick(() => {
      chartRef.value.resize();
    });
  }
}, { deep: true });

// 监听图表类型变化
watch(() => props.type, (newType) => {
  currentChartType.value = newType;
});

// 生命周期
onMounted(() => {
  startAutoRefresh();
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    if (chartRef.value && chartRef.value.resize) {
      chartRef.value.resize();
    }
  });
});

onUnmounted(() => {
  stopAutoRefresh();
  
  // 退出全屏状态
  if (isFullscreen.value) {
    document.body.style.overflow = '';
  }
});
</script>

<style scoped lang="scss">
.chart-component {
  background: #ffffff;
  border-radius: 8px;
  overflow: hidden;
  
  .chart-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid #e5e7eb;
    background: #f8fafc;
    
    .chart-title {
      h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #1f2937;
      }
      
      .chart-subtitle {
        margin: 4px 0 0 0;
        font-size: 14px;
        color: #6b7280;
      }
    }
    
    .chart-controls {
      display: flex;
      align-items: center;
      gap: 12px;
      
      .modern-button-group {
        display: flex;
        gap: 4px;
        border-radius: 6px;
        padding: 2px;
        background: rgba(0, 0, 0, 0.05);
      }
    }
  }
  
  .chart-container {
    position: relative;
    height: v-bind(height);
    
    &.fullscreen {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 9999;
      background: #ffffff;
      height: 100vh;
    }
    
    &.loading {
      pointer-events: none;
    }
    
    .chart {
      width: 100%;
      height: 100%;
    }
    
    .chart-loading,
    .chart-error,
    .chart-empty {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: rgba(255, 255, 255, 0.9);
      
      .loading-icon {
        color: #409EFF;
        animation: spin 1s linear infinite;
        margin-bottom: 12px;
      }
      
      .error-icon {
        color: #F56C6C;
        margin-bottom: 12px;
      }
      
      .empty-icon {
        color: #9CA3AF;
        margin-bottom: 12px;
      }
      
      .loading-text,
      .error-text,
      .empty-text {
        font-size: 14px;
        color: #6b7280;
        margin-bottom: 12px;
      }
    }
  }
  
  .chart-legend {
    padding: 16px 20px;
    border-top: 1px solid #e5e7eb;
    background: #f8fafc;
    
    .legend-title {
      font-size: 14px;
      font-weight: 500;
      color: #374151;
      margin-bottom: 12px;
    }
    
    .legend-items {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      
      .legend-item {
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
        padding: 4px 8px;
        border-radius: 4px;
        transition: background-color 0.2s ease;
        
        &:hover {
          background-color: #e5e7eb;
        }
        
        &.disabled {
          opacity: 0.5;
          
          .legend-color {
            background-color: #d1d5db !important;
          }
        }
        
        .legend-color {
          width: 12px;
          height: 12px;
          border-radius: 2px;
        }
        
        .legend-name {
          font-size: 12px;
          color: #374151;
        }
        
        .legend-value {
          font-size: 12px;
          color: #6b7280;
          font-weight: 500;
        }
      }
    }
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .chart-component {
    .chart-header {
      flex-direction: column;
      align-items: stretch;
      gap: 12px;
      
      .chart-controls {
        justify-content: center;
        flex-wrap: wrap;
      }
    }
    
    .chart-legend {
      .legend-items {
        justify-content: center;
      }
    }
  }
}
</style>