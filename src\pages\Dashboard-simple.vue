<template>
  <div class="dashboard-container">
    <div class="dashboard-header">
      <h1>EV管理系统仪表板</h1>
      <p>当前时间: {{ currentTime }}</p>
    </div>
    
    <div class="stats-grid">
      <div class="stat-card">
        <h3>在线车辆</h3>
        <div class="stat-value">{{ vehicleStats.online }}</div>
        <div class="stat-label">辆</div>
      </div>
      
      <div class="stat-card">
        <h3>充电站</h3>
        <div class="stat-value">{{ chargingStats.total }}</div>
        <div class="stat-label">个</div>
      </div>
      
      <div class="stat-card">
        <h3>平均电池</h3>
        <div class="stat-value">{{ batteryStats.average }}%</div>
        <div class="stat-label">电量</div>
      </div>
      
      <div class="stat-card">
        <h3>系统状态</h3>
        <div class="stat-value" :class="systemStatus.class">{{ systemStatus.text }}</div>
        <div class="stat-label">运行中</div>
      </div>
    </div>
    
    <div class="data-section">
      <h2>实时数据</h2>
      <div class="data-list">
        <div v-for="item in realtimeData" :key="item.id" class="data-item">
          <span class="device-id">{{ item.device_id }}</span>
          <span class="data-type">{{ item.data_type }}</span>
          <span class="data-value">{{ item.text_value }} {{ item.unit }}</span>
          <span class="timestamp">{{ formatTime(item.timestamp) }}</span>
        </div>
      </div>
    </div>
    
    <div class="loading-info" v-if="loading">
      <p>正在加载数据...</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

// 响应式数据
const currentTime = ref('')
const loading = ref(true)
const realtimeData = ref<any[]>([])

const vehicleStats = ref({
  online: 0,
  total: 0
})

const chargingStats = ref({
  total: 0,
  available: 0
})

const batteryStats = ref({
  average: 0
})

const systemStatus = ref({
  text: '正常',
  class: 'status-normal'
})

// 定时器
let timeTimer: number | null = null
let dataTimer: number | null = null

// 格式化时间
const formatTime = (timestamp: string | number) => {
  const date = new Date(timestamp)
  return date.toLocaleString('zh-CN')
}

// 更新当前时间
const updateTime = () => {
  currentTime.value = new Date().toLocaleString('zh-CN')
}

// 获取实时数据
const fetchRealtimeData = async () => {
  try {
    console.log('正在获取实时数据...')
    const response = await fetch('/api/edge-devices/data?deviceIds=EV001,EV002,EV003,CS001,CS002&limit=20')
    
    if (response.ok) {
      const result = await response.json()
      console.log('API响应:', result)
      
      if (result.success && result.data) {
        realtimeData.value = result.data
        
        // 计算统计数据
        const vehicles = result.data.filter((item: any) => item.device_id.startsWith('EV'))
        const stations = result.data.filter((item: any) => item.device_id.startsWith('CS'))
        const batteries = result.data.filter((item: any) => item.data_type === 'battery_level')
        
        vehicleStats.value = {
          online: new Set(vehicles.map((v: any) => v.device_id)).size,
          total: 3
        }
        
        chargingStats.value = {
          total: new Set(stations.map((s: any) => s.device_id)).size,
          available: 2
        }
        
        if (batteries.length > 0) {
          const avgBattery = batteries.reduce((sum: number, item: any) => {
            const value = parseInt(item.text_value) || 0
            return sum + value
          }, 0) / batteries.length
          batteryStats.value.average = Math.round(avgBattery)
        }
        
        systemStatus.value = {
          text: '正常',
          class: 'status-normal'
        }
        
        loading.value = false
        console.log('数据更新成功:', {
          vehicles: vehicleStats.value,
          stations: chargingStats.value,
          battery: batteryStats.value
        })
      }
    } else {
      console.error('API请求失败:', response.status)
      systemStatus.value = {
        text: '异常',
        class: 'status-error'
      }
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    systemStatus.value = {
      text: '错误',
      class: 'status-error'
    }
  }
}

// 组件挂载
onMounted(() => {
  console.log('Dashboard组件已挂载')
  
  // 开始更新时间
  updateTime()
  timeTimer = window.setInterval(updateTime, 1000)
  
  // 立即获取数据
  fetchRealtimeData()
  
  // 定期获取数据
  dataTimer = window.setInterval(fetchRealtimeData, 10000)
})

// 组件卸载
onUnmounted(() => {
  if (timeTimer) {
    clearInterval(timeTimer)
  }
  if (dataTimer) {
    clearInterval(dataTimer)
  }
})
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-header {
  text-align: center;
  margin-bottom: 30px;
}

.dashboard-header h1 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  text-align: center;
}

.stat-card h3 {
  color: #666;
  margin-bottom: 10px;
  font-size: 14px;
}

.stat-value {
  font-size: 32px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 5px;
}

.stat-value.status-normal {
  color: #27ae60;
}

.stat-value.status-error {
  color: #e74c3c;
}

.stat-label {
  color: #999;
  font-size: 12px;
}

.data-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.data-section h2 {
  color: #2c3e50;
  margin-bottom: 20px;
}

.data-list {
  max-height: 400px;
  overflow-y: auto;
}

.data-item {
  display: grid;
  grid-template-columns: 100px 120px 1fr 150px;
  gap: 15px;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
  align-items: center;
}

.device-id {
  font-weight: bold;
  color: #3498db;
}

.data-type {
  color: #666;
  font-size: 14px;
}

.data-value {
  color: #2c3e50;
  font-weight: 500;
}

.timestamp {
  color: #999;
  font-size: 12px;
}

.loading-info {
  text-align: center;
  padding: 40px;
  color: #666;
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .data-item {
    grid-template-columns: 1fr;
    gap: 5px;
  }
}
</style>