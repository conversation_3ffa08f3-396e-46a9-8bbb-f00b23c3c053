<template>
  <div class="mobile-tabbar" :class="{ 'tabbar-hidden': isHidden }">
    <div class="tabbar-content">
      <div 
        v-for="tab in tabs" 
        :key="tab.path"
        class="tab-item"
        :class="{ 'tab-active': isActive(tab.path) }"
        @click="navigateTo(tab.path)"
      >
        <div class="tab-icon-wrapper">
          <component :is="tab.icon" class="tab-icon" />
          <div v-if="tab.badge" class="tab-badge">{{ tab.badge }}</div>
        </div>
        <span class="tab-label">{{ tab.label }}</span>
      </div>
    </div>
    
    <!-- 安全区域占位 -->
    <div class="safe-area-bottom"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { 
  House
} from '@element-plus/icons-vue'
import { Battery, Zap as Charging, Car, Wifi as Connection, User, Shield } from 'lucide-vue-next'

interface TabItem {
  path: string
  label: string
  icon: any
  badge?: string | number
}

interface Props {
  autoHide?: boolean
  showBadges?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  autoHide: false,
  showBadges: true
})

const router = useRouter()
const route = useRoute()
const isHidden = ref(false)
const lastScrollY = ref(0)

// 底部导航标签配置
const tabs = ref<TabItem[]>([
  {
    path: '/dashboard',
    label: '首页',
    icon: House
  },
  {
    path: '/battery',
    label: '电池',
    icon: Battery,
    badge: props.showBadges ? '2' : undefined
  },
  {
    path: '/charging',
    label: '充电',
    icon: Charging
  },
  {
    path: '/adas',
    label: '驾驶',
    icon: Car
  },
  {
    path: '/network',
    label: '车联网',
    icon: Connection,
    badge: props.showBadges ? '!' : undefined
  }
])

// 检查当前路由是否激活
const isActive = (path: string): boolean => {
  return route.path === path || route.path.startsWith(path + '/')
}

// 导航到指定路径
const navigateTo = (path: string) => {
  if (route.path !== path) {
    router.push(path)
  }
}

// 自动隐藏底部导航栏
const handleScroll = () => {
  if (!props.autoHide) return
  
  const currentScrollY = window.scrollY
  if (currentScrollY > lastScrollY.value && currentScrollY > 100) {
    isHidden.value = true
  } else {
    isHidden.value = false
  }
  lastScrollY.value = currentScrollY
}

// 更新徽章数量
const updateBadges = () => {
  if (!props.showBadges) return
  
  // 模拟动态徽章更新
  const batteryTab = tabs.value.find(tab => tab.path === '/battery')
  const networkTab = tabs.value.find(tab => tab.path === '/network')
  
  if (batteryTab) {
    // 模拟电池告警数量
    const alertCount = Math.floor(Math.random() * 5)
    batteryTab.badge = alertCount > 0 ? alertCount.toString() : undefined
  }
  
  if (networkTab) {
    // 模拟网络状态提醒
    const hasAlert = Math.random() > 0.7
    networkTab.badge = hasAlert ? '!' : undefined
  }
}

// 触觉反馈
const hapticFeedback = () => {
  if ('vibrate' in navigator) {
    navigator.vibrate(10) // 轻微震动10ms
  }
}

// 处理标签点击
const handleTabClick = (tab: TabItem) => {
  hapticFeedback()
  navigateTo(tab.path)
}

onMounted(() => {
  // 监听滚动事件
  if (props.autoHide) {
    window.addEventListener('scroll', handleScroll, { passive: true })
  }
  
  // 定期更新徽章
  if (props.showBadges) {
    updateBadges()
    setInterval(updateBadges, 30000) // 每30秒更新一次
  }
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped>
.mobile-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.tabbar-hidden {
  transform: translateY(100%);
}

.tabbar-content {
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 8px 0;
  min-height: 60px;
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  padding: 4px 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 12px;
  margin: 0 4px;
  position: relative;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.tab-item:active {
  transform: scale(0.95);
  background: rgba(0, 0, 0, 0.05);
}

.tab-active {
  color: #667eea;
}

.tab-active .tab-icon-wrapper {
  transform: scale(1.1);
}

.tab-icon-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 2px;
  transition: transform 0.2s ease;
}

.tab-icon {
  width: 24px;
  height: 24px;
  transition: all 0.2s ease;
}

.tab-active .tab-icon {
  color: #667eea;
}

.tab-badge {
  position: absolute;
  top: -6px;
  right: -6px;
  min-width: 16px;
  height: 16px;
  background: #ff4757;
  color: white;
  border-radius: 8px;
  font-size: 10px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 4px;
  box-shadow: 0 2px 4px rgba(255, 71, 87, 0.3);
  animation: badge-pulse 2s infinite;
}

@keyframes badge-pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.tab-label {
  font-size: 10px;
  font-weight: 500;
  color: #666;
  transition: color 0.2s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 60px;
}

.tab-active .tab-label {
  color: #667eea;
  font-weight: 600;
}

.safe-area-bottom {
  height: env(safe-area-inset-bottom, 0);
  background: inherit;
}

/* 暗色模式适配 */
@media (prefers-color-scheme: dark) {
  .mobile-tabbar {
    background: rgba(30, 30, 30, 0.95);
    border-top-color: rgba(255, 255, 255, 0.1);
  }
  
  .tab-item:active {
    background: rgba(255, 255, 255, 0.1);
  }
  
  .tab-label {
    color: #999;
  }
  
  .tab-active .tab-label {
    color: #667eea;
  }
}

/* 横屏适配 */
@media screen and (orientation: landscape) and (max-height: 500px) {
  .tabbar-content {
    padding: 4px 0;
    min-height: 50px;
  }
  
  .tab-item {
    padding: 2px 6px;
  }
  
  .tab-icon {
    width: 20px;
    height: 20px;
  }
  
  .tab-label {
    font-size: 9px;
  }
  
  .tab-badge {
    min-width: 14px;
    height: 14px;
    font-size: 9px;
    top: -4px;
    right: -4px;
  }
}

/* 超小屏幕适配 */
@media screen and (max-width: 320px) {
  .tab-label {
    display: none;
  }
  
  .tab-item {
    padding: 8px 4px;
  }
  
  .tab-icon {
    width: 28px;
    height: 28px;
  }
}

/* 高DPI屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .mobile-tabbar {
    border-top-width: 0.5px;
  }
}

/* 触摸优化 */
@media (hover: none) and (pointer: coarse) {
  .tab-item {
    min-height: 48px; /* 确保触摸目标足够大 */
  }
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
  .tab-item,
  .tab-icon,
  .tab-icon-wrapper,
  .mobile-tabbar {
    transition: none;
  }
  
  .tab-badge {
    animation: none;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .mobile-tabbar {
    border-top: 2px solid;
    background: white;
  }
  
  .tab-active {
    background: #000;
    color: #fff;
  }
}
</style>