// 支付服务模块
import { ElMessage } from 'element-plus'

// 支付方式枚举
export enum PaymentMethod {
  WECHAT = 'wechat',
  ALIPAY = 'alipay',
  BANK_CARD = 'bank_card',
  BALANCE = 'balance'
}

// 支付状态枚举
export enum PaymentStatus {
  PENDING = 'pending',
  SUCCESS = 'success',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded'
}

// 订单类型枚举
export enum OrderType {
  CHARGING = 'charging',
  SERVICE = 'service',
  MAINTENANCE = 'maintenance',
  SUBSCRIPTION = 'subscription'
}

// 支付订单接口
export interface PaymentOrder {
  id: string
  orderNo: string
  type: OrderType
  title: string
  description: string
  amount: number
  originalAmount?: number
  discount?: number
  paymentMethod?: PaymentMethod
  status: PaymentStatus
  createTime: Date
  payTime?: Date
  refundTime?: Date
  userId: string
  stationId?: string
  serviceId?: string
  metadata?: Record<string, any>
}

// 支付请求接口
export interface PaymentRequest {
  orderId: string
  amount: number
  paymentMethod: PaymentMethod
  returnUrl?: string
  notifyUrl?: string
}

// 支付响应接口
export interface PaymentResponse {
  success: boolean
  paymentId: string
  paymentUrl?: string
  qrCode?: string
  message: string
  data?: any
}

// 账单统计接口
export interface BillStatistics {
  totalAmount: number
  totalOrders: number
  successOrders: number
  failedOrders: number
  refundAmount: number
  monthlyAmount: number
  yearlyAmount: number
  averageAmount: number
}

// 支付配置
const PAYMENT_CONFIG = {
  timeout: 300000, // 5分钟超时
  retryTimes: 3,
  apiBaseUrl: '/api/payment'
}

// 模拟支付订单数据
const mockOrders: PaymentOrder[] = [
  {
    id: 'order-001',
    orderNo: 'CHG20241201001',
    type: OrderType.CHARGING,
    title: '充电费用',
    description: '国贸充电站 - 快充桩A01',
    amount: 45.80,
    originalAmount: 52.00,
    discount: 6.20,
    paymentMethod: PaymentMethod.WECHAT,
    status: PaymentStatus.SUCCESS,
    createTime: new Date(Date.now() - 2 * 60 * 60 * 1000),
    payTime: new Date(Date.now() - 2 * 60 * 60 * 1000 + 30000),
    userId: 'user-001',
    stationId: 'station-001'
  },
  {
    id: 'order-002',
    orderNo: 'SRV20241201002',
    type: OrderType.SERVICE,
    title: '维护服务',
    description: '电池健康检测服务',
    amount: 120.00,
    paymentMethod: PaymentMethod.ALIPAY,
    status: PaymentStatus.SUCCESS,
    createTime: new Date(Date.now() - 24 * 60 * 60 * 1000),
    payTime: new Date(Date.now() - 24 * 60 * 60 * 1000 + 45000),
    userId: 'user-001',
    serviceId: 'service-001'
  },
  {
    id: 'order-003',
    orderNo: 'CHG20241201003',
    type: OrderType.CHARGING,
    title: '充电费用',
    description: '三里屯充电站 - 慢充桩B02',
    amount: 28.50,
    paymentMethod: PaymentMethod.BALANCE,
    status: PaymentStatus.PENDING,
    createTime: new Date(Date.now() - 10 * 60 * 1000),
    userId: 'user-001',
    stationId: 'station-002'
  }
]

// 支付服务类
class PaymentService {
  private orders: PaymentOrder[] = [...mockOrders]
  private retryCount = new Map<string, number>()

  // 创建支付订单
  async createOrder(orderData: Partial<PaymentOrder>): Promise<PaymentOrder> {
    const order: PaymentOrder = {
      id: `order-${Date.now()}`,
      orderNo: this.generateOrderNo(orderData.type || OrderType.CHARGING),
      type: OrderType.CHARGING,
      title: '',
      description: '',
      amount: 0,
      status: PaymentStatus.PENDING,
      createTime: new Date(),
      userId: 'user-001',
      ...orderData
    }

    this.orders.unshift(order)
    return order
  }

  // 发起支付
  async initiatePayment(request: PaymentRequest): Promise<PaymentResponse> {
    try {
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000))

      const order = this.orders.find(o => o.id === request.orderId)
      if (!order) {
        throw new Error('订单不存在')
      }

      if (order.status !== PaymentStatus.PENDING) {
        throw new Error('订单状态不允许支付')
      }

      // 模拟支付成功率（90%）
      const isSuccess = Math.random() > 0.1

      if (isSuccess) {
        order.status = PaymentStatus.SUCCESS
        order.paymentMethod = request.paymentMethod
        order.payTime = new Date()

        return {
          success: true,
          paymentId: `pay-${Date.now()}`,
          message: '支付成功',
          data: { orderId: order.id }
        }
      } else {
        order.status = PaymentStatus.FAILED
        return {
          success: false,
          paymentId: '',
          message: '支付失败，请重试'
        }
      }
    } catch (error) {
      return {
        success: false,
        paymentId: '',
        message: error instanceof Error ? error.message : '支付异常'
      }
    }
  }

  // 查询支付状态
  async queryPaymentStatus(orderId: string): Promise<PaymentOrder | null> {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    return this.orders.find(o => o.id === orderId) || null
  }

  // 取消支付
  async cancelPayment(orderId: string): Promise<boolean> {
    try {
      const order = this.orders.find(o => o.id === orderId)
      if (!order) {
        throw new Error('订单不存在')
      }

      if (order.status !== PaymentStatus.PENDING) {
        throw new Error('订单状态不允许取消')
      }

      order.status = PaymentStatus.CANCELLED
      return true
    } catch (error) {
      console.error('取消支付失败:', error)
      return false
    }
  }

  // 申请退款
  async requestRefund(orderId: string, reason: string): Promise<boolean> {
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      const order = this.orders.find(o => o.id === orderId)
      if (!order) {
        throw new Error('订单不存在')
      }

      if (order.status !== PaymentStatus.SUCCESS) {
        throw new Error('订单状态不允许退款')
      }

      order.status = PaymentStatus.REFUNDED
      order.refundTime = new Date()
      order.metadata = { ...order.metadata, refundReason: reason }

      return true
    } catch (error) {
      console.error('申请退款失败:', error)
      return false
    }
  }

  // 获取订单列表
  async getOrders(userId: string, status?: PaymentStatus): Promise<PaymentOrder[]> {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 300))

    let filteredOrders = this.orders.filter(o => o.userId === userId)
    
    if (status) {
      filteredOrders = filteredOrders.filter(o => o.status === status)
    }

    return filteredOrders.sort((a, b) => b.createTime.getTime() - a.createTime.getTime())
  }

  // 获取账单统计
  async getBillStatistics(userId: string): Promise<BillStatistics> {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))

    const userOrders = this.orders.filter(o => o.userId === userId)
    const successOrders = userOrders.filter(o => o.status === PaymentStatus.SUCCESS)
    const failedOrders = userOrders.filter(o => o.status === PaymentStatus.FAILED)
    const refundedOrders = userOrders.filter(o => o.status === PaymentStatus.REFUNDED)

    const now = new Date()
    const currentMonth = now.getMonth()
    const currentYear = now.getFullYear()

    const monthlyOrders = successOrders.filter(o => 
      o.payTime && 
      o.payTime.getMonth() === currentMonth && 
      o.payTime.getFullYear() === currentYear
    )

    const yearlyOrders = successOrders.filter(o => 
      o.payTime && o.payTime.getFullYear() === currentYear
    )

    const totalAmount = successOrders.reduce((sum, o) => sum + o.amount, 0)
    const refundAmount = refundedOrders.reduce((sum, o) => sum + o.amount, 0)
    const monthlyAmount = monthlyOrders.reduce((sum, o) => sum + o.amount, 0)
    const yearlyAmount = yearlyOrders.reduce((sum, o) => sum + o.amount, 0)

    return {
      totalAmount,
      totalOrders: userOrders.length,
      successOrders: successOrders.length,
      failedOrders: failedOrders.length,
      refundAmount,
      monthlyAmount,
      yearlyAmount,
      averageAmount: successOrders.length > 0 ? totalAmount / successOrders.length : 0
    }
  }

  // 生成订单号
  private generateOrderNo(type: OrderType): string {
    const prefix = {
      [OrderType.CHARGING]: 'CHG',
      [OrderType.SERVICE]: 'SRV',
      [OrderType.MAINTENANCE]: 'MNT',
      [OrderType.SUBSCRIPTION]: 'SUB'
    }[type]

    const date = new Date().toISOString().slice(0, 10).replace(/-/g, '')
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
    
    return `${prefix}${date}${random}`
  }

  // 获取支付方式显示名称
  getPaymentMethodName(method: PaymentMethod): string {
    const names = {
      [PaymentMethod.WECHAT]: '微信支付',
      [PaymentMethod.ALIPAY]: '支付宝',
      [PaymentMethod.BANK_CARD]: '银行卡',
      [PaymentMethod.BALANCE]: '余额支付'
    }
    return names[method] || '未知支付方式'
  }

  // 获取支付状态显示名称
  getPaymentStatusName(status: PaymentStatus): string {
    const names = {
      [PaymentStatus.PENDING]: '待支付',
      [PaymentStatus.SUCCESS]: '支付成功',
      [PaymentStatus.FAILED]: '支付失败',
      [PaymentStatus.CANCELLED]: '已取消',
      [PaymentStatus.REFUNDED]: '已退款'
    }
    return names[status] || '未知状态'
  }

  // 获取订单类型显示名称
  getOrderTypeName(type: OrderType): string {
    const names = {
      [OrderType.CHARGING]: '充电费用',
      [OrderType.SERVICE]: '服务费用',
      [OrderType.MAINTENANCE]: '维护费用',
      [OrderType.SUBSCRIPTION]: '订阅费用'
    }
    return names[type] || '未知类型'
  }
}

// 导出支付服务实例
export const paymentService = new PaymentService()

// 导出工具函数
export const formatAmount = (amount: number): string => {
  return `¥${amount.toFixed(2)}`
}

export const formatDateTime = (date: Date): string => {
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

export const formatDate = (date: Date): string => {
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}