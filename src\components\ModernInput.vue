<template>
  <div :class="['modern-input-wrapper', { 'focused': isFocused, 'error': hasError }]">
    <label v-if="label" :for="inputId" class="input-label">
      {{ label }}
      <span v-if="required" class="required-mark">*</span>
    </label>
    
    <div class="input-container">
      <div v-if="prefixIcon" class="input-prefix">
        <component :is="prefixIcon" class="prefix-icon" />
      </div>
      
      <input
        :id="inputId"
        :type="type"
        :value="modelValue"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :maxlength="maxlength"
        :class="['modern-input', { 'has-prefix': prefixIcon, 'has-suffix': suffixIcon || clearable }]"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
        @keydown="handleKeydown"
      />
      
      <div v-if="suffixIcon || (clearable && modelValue)" class="input-suffix">
        <button
          v-if="clearable && modelValue && !disabled"
          type="button"
          class="clear-button"
          @click="handleClear"
        >
          <X class="clear-icon" />
        </button>
        <component v-else-if="suffixIcon" :is="suffixIcon" class="suffix-icon" />
      </div>
      
      <div class="input-glow"></div>
    </div>
    
    <div v-if="hasError && errorMessage" class="error-message">
      {{ errorMessage }}
    </div>
    
    <div v-else-if="helperText" class="helper-text">
      {{ helperText }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue'
import { X } from 'lucide-vue-next'

interface Props {
  modelValue?: string | number
  type?: 'text' | 'password' | 'email' | 'number' | 'tel' | 'url' | 'search'
  label?: string
  placeholder?: string
  disabled?: boolean
  readonly?: boolean
  required?: boolean
  clearable?: boolean
  maxlength?: number
  prefixIcon?: any
  suffixIcon?: any
  errorMessage?: string
  helperText?: string
}

const props = withDefaults(defineProps<Props>(), {
  type: 'text',
  disabled: false,
  readonly: false,
  required: false,
  clearable: false
})

const emit = defineEmits<{
  'update:modelValue': [value: string | number]
  focus: [event: FocusEvent]
  blur: [event: FocusEvent]
  clear: []
  keydown: [event: KeyboardEvent]
}>()

const isFocused = ref(false)
const inputId = `modern-input-${Math.random().toString(36).substr(2, 9)}`

const hasError = computed(() => !!props.errorMessage)

const handleInput = (event: Event) => {
  const target = event.target as HTMLInputElement
  let value: string | number = target.value
  
  if (props.type === 'number') {
    value = target.valueAsNumber || 0
  }
  
  emit('update:modelValue', value)
}

const handleFocus = (event: FocusEvent) => {
  isFocused.value = true
  emit('focus', event)
}

const handleBlur = (event: FocusEvent) => {
  isFocused.value = false
  emit('blur', event)
}

const handleClear = () => {
  emit('update:modelValue', '')
  emit('clear')
}

const handleKeydown = (event: KeyboardEvent) => {
  emit('keydown', event)
}
</script>

<style scoped>
.modern-input-wrapper {
  position: relative;
  width: 100%;
}

.input-label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  text-shadow: var(--text-shadow);
}

.required-mark {
  color: var(--danger-color);
  margin-left: 2px;
}

.input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.modern-input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-base);
  font-family: var(--font-family);
  color: var(--text-primary);
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(var(--blur-sm));
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-md);
  transition: all var(--transition-duration) var(--transition-timing);
  outline: none;
}

.modern-input::placeholder {
  color: var(--text-muted);
  opacity: 0.7;
}

.modern-input:focus {
  border-color: var(--primary-color);
  box-shadow: 
    0 0 0 3px rgba(var(--primary-rgb), 0.1),
    0 0 20px rgba(var(--primary-rgb), 0.2);
  background: rgba(255, 255, 255, 0.08);
}

.modern-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: rgba(255, 255, 255, 0.02);
}

.modern-input.has-prefix {
  padding-left: 40px;
}

.modern-input.has-suffix {
  padding-right: 40px;
}

.input-prefix,
.input-suffix {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.input-prefix {
  left: var(--spacing-sm);
}

.input-suffix {
  right: var(--spacing-sm);
}

.prefix-icon,
.suffix-icon {
  width: 18px;
  height: 18px;
  color: var(--text-muted);
  transition: color var(--transition-duration);
}

.clear-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border: none;
  background: none;
  cursor: pointer;
  border-radius: 50%;
  transition: all var(--transition-duration);
  color: var(--text-muted);
}

.clear-button:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
}

.clear-icon {
  width: 14px;
  height: 14px;
}

.input-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: inherit;
  opacity: 0;
  background: linear-gradient(135deg, 
    rgba(var(--primary-rgb), 0.1) 0%, 
    rgba(var(--primary-rgb), 0.05) 100%);
  filter: blur(8px);
  transition: opacity var(--transition-duration);
  z-index: -1;
  pointer-events: none;
}

.modern-input-wrapper.focused .input-glow {
  opacity: 1;
}

.modern-input-wrapper.focused .prefix-icon,
.modern-input-wrapper.focused .suffix-icon {
  color: var(--primary-color);
}

/* Error state */
.modern-input-wrapper.error .modern-input {
  border-color: var(--danger-color);
  box-shadow: 
    0 0 0 3px rgba(var(--danger-rgb), 0.1),
    0 0 20px rgba(var(--danger-rgb), 0.2);
}

.modern-input-wrapper.error .input-glow {
  background: linear-gradient(135deg, 
    rgba(var(--danger-rgb), 0.1) 0%, 
    rgba(var(--danger-rgb), 0.05) 100%);
}

.modern-input-wrapper.error .prefix-icon,
.modern-input-wrapper.error .suffix-icon {
  color: var(--danger-color);
}

.error-message {
  margin-top: var(--spacing-xs);
  font-size: var(--font-size-sm);
  color: var(--danger-color);
  text-shadow: var(--text-shadow);
}

.helper-text {
  margin-top: var(--spacing-xs);
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  text-shadow: var(--text-shadow);
}

/* Shimmer effect */
.modern-input::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.1), 
    transparent);
  transition: left 0.6s;
  pointer-events: none;
}

.modern-input:focus::before {
  left: 100%;
}

/* Responsive design */
@media (max-width: 768px) {
  .modern-input {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
  }
  
  .modern-input.has-prefix {
    padding-left: 36px;
  }
  
  .modern-input.has-suffix {
    padding-right: 36px;
  }
}
</style>