const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

async function fixDatabase() {
  let connection;
  
  try {
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '123456',
      database: 'ev_management',
      multipleStatements: true
    });

    console.log('✅ 数据库连接成功');

    // 修复 edge_device_data 表结构
    console.log('🔧 修复 edge_device_data 表结构...');
    
    try {
      await connection.execute(`
        ALTER TABLE edge_device_data 
        ADD COLUMN text_value TEXT AFTER value
      `);
      console.log('✅ 添加 text_value 字段成功');
    } catch (error) {
      if (error.code === 'ER_DUP_FIELDNAME') {
        console.log('ℹ️  text_value 字段已存在');
      } else {
        console.log('⚠️  添加 text_value 字段失败:', error.message);
      }
    }

    try {
      await connection.execute(`
        ALTER TABLE edge_device_data 
        ADD COLUMN json_value JSON AFTER text_value
      `);
      console.log('✅ 添加 json_value 字段成功');
    } catch (error) {
      if (error.code === 'ER_DUP_FIELDNAME') {
        console.log('ℹ️  json_value 字段已存在');
      } else {
        console.log('⚠️  添加 json_value 字段失败:', error.message);
      }
    }

    // 修复 users 表结构
    console.log('🔧 修复 users 表结构...');
    
    const userFields = [
      { name: 'user_id', sql: 'ADD COLUMN user_id VARCHAR(50) UNIQUE AFTER id' },
      { name: 'username', sql: 'ADD COLUMN username VARCHAR(100) AFTER name' },
      { name: 'password_hash', sql: 'ADD COLUMN password_hash VARCHAR(255) AFTER username' },
      { name: 'role', sql: 'ADD COLUMN role ENUM("admin", "user", "fleet_manager") DEFAULT "user" AFTER user_type' },
      { name: 'last_login', sql: 'ADD COLUMN last_login TIMESTAMP NULL AFTER status' }
    ];

    for (const field of userFields) {
      try {
        await connection.execute(`ALTER TABLE users ${field.sql}`);
        console.log(`✅ 添加 ${field.name} 字段成功`);
      } catch (error) {
        if (error.code === 'ER_DUP_FIELDNAME') {
          console.log(`ℹ️  ${field.name} 字段已存在`);
        } else {
          console.log(`⚠️  添加 ${field.name} 字段失败:`, error.message);
        }
      }
    }

    // 更新现有用户数据
    console.log('🔧 更新现有用户数据...');
    
    try {
      await connection.execute(`
        UPDATE users SET 
          user_id = CONCAT('user_', UNIX_TIMESTAMP(), '_', SUBSTRING(MD5(RAND()), 1, 8)),
          username = name,
          password_hash = '$2b$10$defaulthashforexistingusers',
          role = CASE 
            WHEN user_type = 'enterprise' THEN 'admin'
            WHEN user_type = 'fleet' THEN 'fleet_manager'
            ELSE 'user'
          END
        WHERE user_id IS NULL OR user_id = ''
      `);
      console.log('✅ 用户数据更新成功');
    } catch (error) {
      console.log('⚠️  用户数据更新失败:', error.message);
    }

    // 创建索引
    console.log('🔧 创建索引...');
    
    const indexes = [
      { name: 'idx_user_id', sql: 'CREATE INDEX idx_user_id ON users(user_id)' },
      { name: 'idx_username', sql: 'CREATE INDEX idx_username ON users(username)' }
    ];

    for (const index of indexes) {
      try {
        await connection.execute(index.sql);
        console.log(`✅ 创建索引 ${index.name} 成功`);
      } catch (error) {
        if (error.code === 'ER_DUP_KEYNAME') {
          console.log(`ℹ️  索引 ${index.name} 已存在`);
        } else {
          console.log(`⚠️  创建索引 ${index.name} 失败:`, error.message);
        }
      }
    }

    // 验证表结构
    console.log('🔍 验证表结构...');
    
    const [edgeFields] = await connection.execute('DESCRIBE edge_device_data');
    console.log('edge_device_data 表字段:');
    edgeFields.forEach(field => {
      console.log(`  - ${field.Field}: ${field.Type}`);
    });

    const [userFields2] = await connection.execute('DESCRIBE users');
    console.log('users 表字段:');
    userFields2.forEach(field => {
      console.log(`  - ${field.Field}: ${field.Type}`);
    });

    // 创建测试用户（如果不存在）
    console.log('🔧 创建测试用户...');
    
    try {
      const testUsers = [
        {
          user_id: 'test_admin_001',
          username: '测试管理员',
          email: '<EMAIL>',
          name: '测试管理员',
          password_hash: '$2b$10$rOzJqQjQjQjQjQjQjQjQjOzJqQjQjQjQjQjQjQjQjQjQjQjQjQjQjQ', // 密码: 123456
          user_type: 'enterprise',
          role: 'admin',
          status: 'active'
        },
        {
          user_id: 'test_user_001',
          username: '测试用户',
          email: '<EMAIL>',
          name: '测试用户',
          password_hash: '$2b$10$rOzJqQjQjQjQjQjQjQjQjOzJqQjQjQjQjQjQjQjQjQjQjQjQjQjQjQ', // 密码: 123456
          user_type: 'individual',
          role: 'user',
          status: 'active'
        }
      ];

      for (const user of testUsers) {
        try {
          await connection.execute(`
            INSERT INTO users (user_id, username, email, name, password_hash, user_type, role, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE
            username = VALUES(username),
            password_hash = VALUES(password_hash),
            role = VALUES(role)
          `, [
            user.user_id, user.username, user.email, user.name,
            user.password_hash, user.user_type, user.role, user.status
          ]);
          console.log(`✅ 创建/更新测试用户: ${user.username} (${user.email})`);
        } catch (error) {
          console.log(`⚠️  创建测试用户失败: ${user.username}`, error.message);
        }
      }
    } catch (error) {
      console.log('⚠️  创建测试用户失败:', error.message);
    }

    console.log('🎉 数据库修复完成！');
    console.log('');
    console.log('测试账户信息:');
    console.log('管理员账户: <EMAIL> / 123456');
    console.log('普通用户: <EMAIL> / 123456');

  } catch (error) {
    console.error('❌ 数据库修复失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 执行修复
fixDatabase();