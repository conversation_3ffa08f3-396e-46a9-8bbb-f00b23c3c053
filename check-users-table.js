const mysql = require('mysql2/promise');
const { config } = require('dotenv');

config();

async function checkUsersTable() {
  try {
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      port: process.env.DB_PORT,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME
    });
    
    console.log('检查users表结构...');
    const [columns] = await connection.execute('DESCRIBE users');
    console.log('\nUsers表字段:');
    columns.forEach(col => {
      console.log(`- ${col.Field} (${col.Type}) ${col.Null === 'YES' ? 'NULL' : 'NOT NULL'}`);
    });
    
    console.log('\n检查vehicles表结构...');
    const [vehicleColumns] = await connection.execute('DESCRIBE vehicles');
    console.log('\nVehicles表字段:');
    vehicleColumns.forEach(col => {
      console.log(`- ${col.Field} (${col.Type}) ${col.Null === 'YES' ? 'NULL' : 'NOT NULL'}`);
    });
    
    await connection.end();
  } catch (error) {
    console.error('检查表结构失败:', error);
  }
}

checkUsersTable();