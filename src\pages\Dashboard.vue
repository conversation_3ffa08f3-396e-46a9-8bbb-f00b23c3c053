<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">首页</h1>
        <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">常用功能</p>
      </div>
      <div class="flex items-center space-x-3">
        <el-button type="primary" size="small">
          <el-icon class="mr-1">
            <Plus />
          </el-icon>
          自定义
        </el-button>
        <el-button size="small">
          <el-icon class="mr-1">
            <Refresh />
          </el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 快捷功能卡片 -->
    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
      <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow cursor-pointer">
        <div class="flex flex-col items-center text-center">
          <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center mb-3">
            <el-icon class="text-blue-600 dark:text-blue-400 text-xl">
              <DataBoard />
            </el-icon>
          </div>
          <span class="text-sm font-medium text-gray-900 dark:text-white">数据查询</span>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow cursor-pointer">
        <div class="flex flex-col items-center text-center">
          <div class="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center mb-3">
            <el-icon class="text-green-600 dark:text-green-400 text-xl">
              <Document />
            </el-icon>
          </div>
          <span class="text-sm font-medium text-gray-900 dark:text-white">新增凭证</span>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow cursor-pointer">
        <div class="flex flex-col items-center text-center">
          <div class="w-12 h-12 bg-cyan-100 dark:bg-cyan-900/20 rounded-lg flex items-center justify-center mb-3">
            <el-icon class="text-cyan-600 dark:text-cyan-400 text-xl">
              <ChatDotRound />
            </el-icon>
          </div>
          <span class="text-sm font-medium text-gray-900 dark:text-white">余额查询</span>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow cursor-pointer">
        <div class="flex flex-col items-center text-center">
          <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center mb-3">
            <el-icon class="text-purple-600 dark:text-purple-400 text-xl">
              <Files />
            </el-icon>
          </div>
          <span class="text-sm font-medium text-gray-900 dark:text-white">财务报表</span>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow cursor-pointer">
        <div class="flex flex-col items-center text-center">
          <div class="w-12 h-12 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center mb-3">
            <el-icon class="text-orange-600 dark:text-orange-400 text-xl">
              <TrendCharts />
            </el-icon>
          </div>
          <span class="text-sm font-medium text-gray-900 dark:text-white">数据统计</span>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow cursor-pointer">
        <div class="flex flex-col items-center text-center">
          <div class="w-12 h-12 bg-indigo-100 dark:bg-indigo-900/20 rounded-lg flex items-center justify-center mb-3">
            <el-icon class="text-indigo-600 dark:text-indigo-400 text-xl">
              <MoreFilled />
            </el-icon>
          </div>
          <span class="text-sm font-medium text-gray-900 dark:text-white">更多</span>
        </div>
      </div>
    </div>

    <!-- 第三板块标题 -->
    <div class="flex items-center justify-between pt-4">
      <h2 class="text-lg font-semibold text-gray-900 dark:text-white">第三板块标</h2>
      <el-button type="primary" size="small">
        <el-icon class="mr-1">
          <Plus />
        </el-icon>
        自定义
      </el-button>
    </div>

    <!-- 数据统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-500 dark:text-gray-400">综合评分</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white mt-1">9.9</p>
          </div>
          <div class="w-10 h-10 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center">
            <span class="text-orange-600 dark:text-orange-400 text-sm">●</span>
          </div>
        </div>
        <div class="mt-4 flex items-center">
          <span class="text-xs text-red-500">78% ▲</span>
          <span class="text-xs text-gray-500 dark:text-gray-400 ml-2">客户投诉率</span>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-500 dark:text-gray-400">现金</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white mt-1">750,420</p>
          </div>
          <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
            <span class="text-blue-600 dark:text-blue-400 text-sm">●</span>
          </div>
        </div>
        <div class="mt-4 flex items-center">
          <span class="text-xs text-green-500">88% ▼</span>
          <span class="text-xs text-gray-500 dark:text-gray-400 ml-2">毛利率</span>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-500 dark:text-gray-400">银行存款</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white mt-1">100,000</p>
          </div>
          <div class="w-10 h-10 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
            <span class="text-green-600 dark:text-green-400 text-sm">●</span>
          </div>
        </div>
        <div class="mt-4 flex items-center">
          <span class="text-xs text-red-500">58% ▲</span>
          <span class="text-xs text-gray-500 dark:text-gray-400 ml-2">营业收入增长率</span>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-500 dark:text-gray-400">存货</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white mt-1">100,000</p>
          </div>
          <div class="w-10 h-10 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
            <span class="text-purple-600 dark:text-purple-400 text-sm">●</span>
          </div>
        </div>
        <div class="mt-4 flex items-center">
          <span class="text-xs text-red-500">76% ▲</span>
          <span class="text-xs text-gray-500 dark:text-gray-400 ml-2">净利润增长率</span>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-500 dark:text-gray-400">应收账款</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white mt-1">100,000</p>
          </div>
          <div class="w-10 h-10 bg-cyan-100 dark:bg-cyan-900/20 rounded-lg flex items-center justify-center">
            <span class="text-cyan-600 dark:text-cyan-400 text-sm">●</span>
          </div>
        </div>
        <div class="mt-4 flex items-center">
          <span class="text-xs text-green-500">76% ▼</span>
          <span class="text-xs text-gray-500 dark:text-gray-400 ml-2">净资产收益率</span>
        </div>
      </div>
    </div>

    <!-- 财务指标 -->
    <div class="flex items-center justify-between pt-4">
      <h2 class="text-lg font-semibold text-gray-900 dark:text-white">财务指标</h2>
      <el-button type="primary" size="small">
        <el-icon class="mr-1">
          <Plus />
        </el-icon>
        自定义
      </el-button>
    </div>

    <!-- 图表区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 销售额趋势图 -->
      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-base font-semibold text-gray-900 dark:text-white">销售额趋势</h3>
          <span class="text-sm text-gray-500 dark:text-gray-400">单位：元</span>
        </div>
        <div class="h-64">
          <div class="w-full h-full flex items-end justify-between px-4">
            <div class="w-8 bg-blue-500 rounded-t" style="height: 60%"></div>
            <div class="w-8 bg-blue-500 rounded-t" style="height: 45%"></div>
            <div class="w-8 bg-blue-500 rounded-t" style="height: 35%"></div>
            <div class="w-8 bg-blue-500 rounded-t" style="height: 50%"></div>
            <div class="w-8 bg-blue-500 rounded-t" style="height: 55%"></div>
            <div class="w-8 bg-blue-500 rounded-t" style="height: 45%"></div>
            <div class="w-8 bg-blue-500 rounded-t" style="height: 40%"></div>
            <div class="w-8 bg-blue-500 rounded-t" style="height: 38%"></div>
            <div class="w-8 bg-blue-500 rounded-t" style="height: 42%"></div>
            <div class="w-8 bg-blue-500 rounded-t" style="height: 35%"></div>
          </div>
        </div>
      </div>

      <!-- 现金分析饼图 -->
      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-base font-semibold text-gray-900 dark:text-white">现金分析</h3>
          <span class="text-sm text-gray-500 dark:text-gray-400">单位：元</span>
        </div>
        <div class="h-64 flex items-center justify-center">
          <div class="relative w-48 h-48">
            <!-- 简化的饼图 -->
            <div class="w-full h-full rounded-full border-8 border-blue-400" style="border-right-color: #60a5fa; border-bottom-color: #34d399; border-left-color: #f59e0b; border-top-color: #8b5cf6;"></div>
            <div class="absolute inset-0 flex items-center justify-center">
              <div class="text-center">
                <p class="text-sm text-gray-500 dark:text-gray-400">总计</p>
                <p class="text-lg font-semibold text-gray-900 dark:text-white">1,000,000</p>
              </div>
            </div>
          </div>
          <div class="ml-8 space-y-2">
            <div class="flex items-center">
              <div class="w-3 h-3 bg-blue-400 rounded-full mr-2"></div>
              <span class="text-sm text-gray-600 dark:text-gray-300">固定资产</span>
            </div>
            <div class="flex items-center">
              <div class="w-3 h-3 bg-green-400 rounded-full mr-2"></div>
              <span class="text-sm text-gray-600 dark:text-gray-300">无形资产</span>
            </div>
            <div class="flex items-center">
              <div class="w-3 h-3 bg-yellow-400 rounded-full mr-2"></div>
              <span class="text-sm text-gray-600 dark:text-gray-300">长期投资</span>
            </div>
            <div class="flex items-center">
              <div class="w-3 h-3 bg-purple-400 rounded-full mr-2"></div>
              <span class="text-sm text-gray-600 dark:text-gray-300">流动资产</span>
            </div>
            <div class="flex items-center">
              <div class="w-3 h-3 bg-gray-400 rounded-full mr-2"></div>
              <span class="text-sm text-gray-600 dark:text-gray-300">其它</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { 
  Plus, Refresh, DataBoard, Document, ChatDotRound, Files, 
  TrendCharts, MoreFilled 
} from '@element-plus/icons-vue'
</script>

<style scoped>
/* 自定义样式 */
.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-5 {
  grid-template-columns: repeat(5, minmax(0, 1fr));
}

.grid-cols-6 {
  grid-template-columns: repeat(6, minmax(0, 1fr));
}

@media (min-width: 768px) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  
  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  
  .lg\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }
  
  .lg\:grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }
}

.gap-4 {
  gap: 1rem;
}

.gap-6 {
  gap: 1.5rem;
}

.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.shadow-sm {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.hover\:shadow-md:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.transition-shadow {
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.cursor-pointer {
  cursor: pointer;
}

.bg-white {
  background-color: #ffffff;
}

.dark .dark\:bg-gray-800 {
  background-color: #1f2937;
}

.text-gray-900 {
  color: #111827;
}

.dark .dark\:text-white {
  color: #ffffff;
}

.text-gray-500 {
  color: #6b7280;
}

.dark .dark\:text-gray-400 {
  color: #9ca3af;
}

.text-gray-600 {
  color: #4b5563;
}

.dark .dark\:text-gray-300 {
  color: #d1d5db;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

.flex-col {
  flex-direction: column;
}

.text-center {
  text-align: center;
}

.space-x-3 > * + * {
  margin-left: 0.75rem;
}

.space-y-2 > * + * {
  margin-top: 0.5rem;
}

.p-4 {
  padding: 1rem;
}

.p-6 {
  padding: 1.5rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.pt-4 {
  padding-top: 1rem;
}

.mt-1 {
  margin-top: 0.25rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mr-1 {
  margin-right: 0.25rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.ml-8 {
  margin-left: 2rem;
}

.w-8 {
  width: 2rem;
}

.w-10 {
  width: 2.5rem;
}

.w-12 {
  width: 3rem;
}

.w-3 {
  width: 0.75rem;
}

.w-48 {
  width: 12rem;
}

.w-full {
  width: 100%;
}

.h-8 {
  height: 2rem;
}

.h-10 {
  height: 2.5rem;
}

.h-12 {
  height: 3rem;
}

.h-3 {
  height: 0.75rem;
}

.h-48 {
  height: 12rem;
}

.h-64 {
  height: 16rem;
}

.h-full {
  height: 100%;
}

.bg-blue-100 {
  background-color: #dbeafe;
}

.dark .dark\:bg-blue-900\/20 {
  background-color: rgba(30, 58, 138, 0.2);
}

.bg-green-100 {
  background-color: #dcfce7;
}

.dark .dark\:bg-green-900\/20 {
  background-color: rgba(20, 83, 45, 0.2);
}

.bg-cyan-100 {
  background-color: #cffafe;
}

.dark .dark\:bg-cyan-900\/20 {
  background-color: rgba(22, 78, 99, 0.2);
}

.bg-purple-100 {
  background-color: #f3e8ff;
}

.dark .dark\:bg-purple-900\/20 {
  background-color: rgba(88, 28, 135, 0.2);
}

.bg-orange-100 {
  background-color: #fed7aa;
}

.dark .dark\:bg-orange-900\/20 {
  background-color: rgba(154, 52, 18, 0.2);
}

.bg-indigo-100 {
  background-color: #e0e7ff;
}

.dark .dark\:bg-indigo-900\/20 {
  background-color: rgba(49, 46, 129, 0.2);
}

.text-blue-600 {
  color: #2563eb;
}

.dark .dark\:text-blue-400 {
  color: #60a5fa;
}

.text-green-600 {
  color: #16a34a;
}

.dark .dark\:text-green-400 {
  color: #4ade80;
}

.text-cyan-600 {
  color: #0891b2;
}

.dark .dark\:text-cyan-400 {
  color: #22d3ee;
}

.text-purple-600 {
  color: #9333ea;
}

.dark .dark\:text-purple-400 {
  color: #c084fc;
}

.text-orange-600 {
  color: #ea580c;
}

.dark .dark\:text-orange-400 {
  color: #fb923c;
}

.text-indigo-600 {
  color: #4f46e5;
}

.dark .dark\:text-indigo-400 {
  color: #818cf8;
}

.text-red-500 {
  color: #ef4444;
}

.text-green-500 {
  color: #22c55e;
}

.bg-blue-500 {
  background-color: #3b82f6;
}

.bg-blue-400 {
  background-color: #60a5fa;
}

.bg-green-400 {
  background-color: #4ade80;
}

.bg-yellow-400 {
  background-color: #facc15;
}

.bg-purple-400 {
  background-color: #c084fc;
}

.bg-gray-400 {
  background-color: #9ca3af;
}

.rounded-t {
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}

.rounded-full {
  border-radius: 9999px;
}

.border-8 {
  border-width: 8px;
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.inset-0 {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
</style>