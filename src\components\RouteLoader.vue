<template>
  <Transition name="route-loader" appear>
    <div v-if="show" class="route-loader-overlay">
      <BikeLoader 
        :fullscreen="true" 
        :text="text" 
      />
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import BikeLoader from './BikeLoader.vue'

interface Props {
  text?: string
  duration?: number
}

const props = withDefaults(defineProps<Props>(), {
  text: '页面加载中...',
  duration: 1500
})

const show = ref(true)

onMounted(() => {
  setTimeout(() => {
    show.value = false
  }, props.duration)
})
</script>

<style scoped>
.route-loader-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 路由加载动画 */
.route-loader-enter-active {
  transition: all 0.3s ease-out;
}

.route-loader-leave-active {
  transition: all 0.5s ease-in;
}

.route-loader-enter-from {
  opacity: 0;
  transform: scale(0.9);
}

.route-loader-leave-to {
  opacity: 0;
  transform: scale(1.1);
}

/* Dark theme */
@media (prefers-color-scheme: dark) {
  .route-loader-overlay {
    background: rgba(0, 0, 0, 0.9);
  }
}
</style>