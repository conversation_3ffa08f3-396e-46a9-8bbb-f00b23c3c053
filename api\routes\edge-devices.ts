import { Router } from 'express'
import { db } from '../config/mysql'

const router = Router()



// 获取边缘设备列表
router.get('/', async (req, res) => {
  try {
    const devices = await db.query('SELECT * FROM edge_devices ORDER BY created_at DESC')
    
    // 确保返回的数据包含id字段（映射device_id到id）
    const formattedDevices = devices.map(device => ({
      ...device,
      id: device.device_id // 前端期望的id字段，使用device_id作为标识符
    }))
    
    res.json({
      success: true,
      data: formattedDevices
    })
  } catch (error) {
    console.error('获取边缘设备列表异常:', error)
    res.status(500).json({
      success: false,
      message: '获取边缘设备列表失败'
    })
  }
})



// 获取边缘设备数据
router.get('/data', async (req, res) => {
  try {
    const { device_id, deviceIds, start_date, end_date, limit = 100 } = req.query;
    const limitNum = typeof limit === 'string' ? parseInt(limit) : 100;
    
    let sql = 'SELECT * FROM edge_device_data WHERE 1=1';
    const params = [];
    
    // 支持单个设备ID或多个设备ID
    if (device_id) {
      sql += ' AND device_id = ?';
      params.push(device_id);
    } else if (deviceIds) {
      // 处理逗号分隔的设备ID列表
      const deviceIdList = deviceIds.toString().split(',').map(id => id.trim()).filter(id => id);
      if (deviceIdList.length > 0) {
        // 使用字符串拼接而不是参数绑定来避免ER_WRONG_ARGUMENTS错误
        const escapedIds = deviceIdList.map(id => `'${id.replace(/'/g, "''")}'`).join(',');
        sql += ` AND device_id IN (${escapedIds})`;
      }
    }
    
    if (start_date) {
      sql += ' AND timestamp >= ?';
      params.push(start_date);
    }
    
    if (end_date) {
      sql += ' AND timestamp <= ?';
      params.push(end_date);
    }
    
    sql += ` ORDER BY timestamp DESC LIMIT ${limitNum}`;

    const result = await db.query(sql, params);
    
    res.json({
      success: true,
      data: result || []
    });
  } catch (error) {
    console.error('[API Error] GET /edge-devices/data', error);
    res.status(500).json({
      success: false,
      message: '获取边缘设备数据失败',
      error: process.env.NODE_ENV === 'development' ? error.message : '服务器错误'
    });
  }
})

// 同步数据到云端
router.post('/sync', async (req, res) => {
  try {
    const { data } = req.body
    
    if (!data || !Array.isArray(data)) {
      return res.status(400).json({
        success: false,
        message: '无效的数据格式'
      })
    }
    
    console.log(`接收到${data.length}条数据同步请求`)
    
    // 批量插入数据
    const insertPromises = data.map(item => {
      const { device_id, data_type, value, text_value, json_value, unit, quality, timestamp } = item
      return db.query(
        'INSERT INTO edge_device_data (device_id, data_type, value, text_value, json_value, unit, quality, timestamp) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
        [
          device_id || null,
          data_type || 'sync_data',
          value !== undefined ? value : null,
          text_value || null,
          json_value ? JSON.stringify(json_value) : null,
          unit || null,
          quality !== undefined ? quality : null,
          timestamp ? new Date(timestamp).toISOString().slice(0, 19).replace('T', ' ') : new Date().toISOString().slice(0, 19).replace('T', ' ')
        ]
      )
    })
    
    await Promise.all(insertPromises)
    
    res.json({
      success: true,
      message: `成功同步${data.length}条数据`
    })
  } catch (error) {
    console.error('[API Error] POST /edge-devices/sync', error)
    res.status(500).json({
      success: false,
      message: '数据同步失败',
      error: process.env.NODE_ENV === 'development' ? error.message : '服务器错误'
    })
  }
})

// 执行边缘任务
router.post('/tasks', async (req, res) => {
  try {
    const { taskType, deviceId, config, name, priority } = req.body
    
    if (!taskType || !deviceId) {
      return res.status(400).json({
        success: false,
        error: '缺少必要参数'
      })
    }
    
    // 验证设备是否存在
    const devices = await db.query(
      'SELECT device_id FROM edge_devices WHERE device_id = ?',
      [deviceId]
    )
    
    if (devices.length === 0) {
      return res.status(404).json({
        success: false,
        error: '设备不存在'
      })
    }
    
    const taskId = `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    // 创建边缘任务记录
    const result = await db.query(
      'INSERT INTO edge_tasks (task_id, name, type, priority, status, device_id, config, started_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
      [taskId, name || `${taskType} 任务`, taskType, priority || 'medium', 'pending', deviceId, JSON.stringify(config || {}), new Date()]
    )
    
    if (!result.insertId) {
      console.error('创建边缘任务失败')
      return res.status(500).json({
        success: false,
        error: '创建任务失败'
      })
    }
    
    console.log(`创建边缘任务: ${taskId}, 类型: ${taskType}, 设备: ${deviceId}`)
    
    // 模拟任务处理（实际应用中这里会调用边缘计算服务）
    const processingTime = Math.random() * 2000 + 1000 // 1-3秒
    
    setTimeout(async () => {
      try {
        // 更新任务状态为完成
        await db.query(
          'UPDATE edge_tasks SET status = ?, completed_at = ?, output_data = ? WHERE task_id = ?',
          ['completed', new Date(), JSON.stringify({
            result: 'Task completed successfully',
            processing_time: processingTime
          }), taskId]
        )
        
        console.log(`任务 ${taskId} 执行完成`)
      } catch (updateError) {
        console.error(`更新任务状态失败: ${taskId}`, updateError)
      }
    }, processingTime)
    
    res.json({
      success: true,
      taskId,
      message: '任务已创建并开始执行',
      estimatedTime: Math.round(processingTime / 1000)
    })
  } catch (error) {
    console.error('创建边缘任务异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    })
  }
})

// 获取任务列表
router.get('/tasks', async (req, res) => {
  try {
    const { deviceId, status, limit = 50 } = req.query
    
    let query = `
      SELECT et.*, ed.device_id, ed.name as device_name, ed.type as device_type
      FROM edge_tasks et
      INNER JOIN edge_devices ed ON et.device_id = ed.device_id
    `
    const params = []
    const conditions = []
    
    if (deviceId) {
      conditions.push('et.device_id = ?')
      params.push(deviceId)
    }
    
    if (status) {
      conditions.push('et.status = ?')
      params.push(status)
    }
    
    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ')
    }
    
    query += ' ORDER BY et.created_at DESC LIMIT ?'
    params.push(Number(limit))
    
    const data = await db.query(query, params)
    
    res.json({
      success: true,
      data: data || []
    })
  } catch (error) {
    console.error('获取任务列表异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    })
  }
})

// 获取单个任务详情
router.get('/tasks/:taskId', async (req, res) => {
  try {
    const { taskId } = req.params
    
    const data = await db.query(`
      SELECT et.*, ed.device_id, ed.name as device_name, ed.type as device_type, ed.status as device_status
      FROM edge_tasks et
      INNER JOIN edge_devices ed ON et.device_id = ed.device_id
      WHERE et.task_id = ?
    `, [taskId])
    
    if (data.length === 0) {
      console.error('获取任务详情失败: 任务不存在')
      return res.status(404).json({
        success: false,
        error: '任务不存在'
      })
    }
    
    res.json({
      success: true,
      data: data[0]
    })
  } catch (error) {
    console.error('获取任务详情异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    })
  }
})

export default router