<template>
  <div class="modern-toggle" :class="[`size-${size}`, { disabled }]">
    <input 
      :id="inputId"
      type="checkbox" 
      class="toggle-input" 
      :checked="modelValue"
      :disabled="disabled"
      @change="handleChange"
    >
    <label :for="inputId" class="toggle-slider">
      <div class="slider-track">
        <div class="slider-thumb">
          <div class="thumb-inner">
            <div class="thumb-glow"></div>
          </div>
        </div>
        <div class="track-fill"></div>
        <div class="track-particles">
          <div class="particle" v-for="i in 6" :key="i"></div>
        </div>
      </div>
      <div class="toggle-labels" v-if="showLabels">
        <span class="label-off">{{ offLabel }}</span>
        <span class="label-on">{{ onLabel }}</span>
      </div>
    </label>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  modelValue: boolean
  disabled?: boolean
  size?: 'small' | 'default' | 'large'
  showLabels?: boolean
  onLabel?: string
  offLabel?: string
  variant?: 'primary' | 'success' | 'warning' | 'danger'
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'change', value: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  size: 'default',
  showLabels: false,
  onLabel: 'ON',
  offLabel: 'OFF',
  variant: 'primary'
})

const emit = defineEmits<Emits>()

// 生成唯一的input id
const inputId = computed(() => `modern-toggle-${Math.random().toString(36).substr(2, 9)}`)

const handleChange = (event: Event) => {
  if (props.disabled) return
  
  const target = event.target as HTMLInputElement
  const value = target.checked
  
  emit('update:modelValue', value)
  emit('change', value)
}
</script>

<style scoped>
.modern-toggle {
  position: relative;
  display: inline-block;
  user-select: none;
}

.modern-toggle.size-small {
  --toggle-width: 60px;
  --toggle-height: 30px;
  --thumb-size: 24px;
  --thumb-offset: 3px;
}

.modern-toggle.size-default {
  --toggle-width: 80px;
  --toggle-height: 40px;
  --thumb-size: 32px;
  --thumb-offset: 4px;
}

.modern-toggle.size-large {
  --toggle-width: 100px;
  --toggle-height: 50px;
  --thumb-size: 40px;
  --thumb-offset: 5px;
}

.toggle-input {
  opacity: 0;
  width: 0;
  height: 0;
  position: absolute;
}

.toggle-slider {
  position: relative;
  display: block;
  cursor: pointer;
  width: var(--toggle-width);
  height: var(--toggle-height);
}

.slider-track {
  position: relative;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
  border-radius: calc(var(--toggle-height) / 2);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  box-shadow: 
    inset 0 2px 4px rgba(0, 0, 0, 0.1),
    0 1px 3px rgba(0, 0, 0, 0.1);
}

.slider-thumb {
  position: absolute;
  top: var(--thumb-offset);
  left: var(--thumb-offset);
  width: var(--thumb-size);
  height: var(--thumb-size);
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 50%;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 
    0 4px 8px rgba(0, 0, 0, 0.15),
    0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 2;
}

.thumb-inner {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  overflow: hidden;
}

.thumb-glow {
  position: absolute;
  inset: 2px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, transparent 70%);
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.track-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 0;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: calc(var(--toggle-height) / 2);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1;
}

.track-particles {
  position: absolute;
  inset: 0;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 3px;
  height: 3px;
  background: #3b82f6;
  border-radius: 50%;
  opacity: 0;
  top: 50%;
  transform: translateY(-50%);
}

.particle:nth-child(1) { left: 15%; }
.particle:nth-child(2) { left: 25%; }
.particle:nth-child(3) { left: 35%; }
.particle:nth-child(4) { left: 45%; }
.particle:nth-child(5) { left: 55%; }
.particle:nth-child(6) { left: 65%; }

.toggle-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  font-size: 12px;
  color: #64748b;
}

.label-off,
.label-on {
  transition: all 0.3s ease;
}

/* 选中状态 */
.toggle-input:checked + .toggle-slider .slider-track {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.toggle-input:checked + .toggle-slider .slider-thumb {
  transform: translateX(calc(var(--toggle-width) - var(--thumb-size) - var(--thumb-offset) * 2));
  background: linear-gradient(135deg, #ffffff 0%, #f0fdf4 100%);
  box-shadow: 
    0 4px 12px rgba(16, 185, 129, 0.3),
    0 2px 6px rgba(16, 185, 129, 0.2);
}

.toggle-input:checked + .toggle-slider .thumb-glow {
  opacity: 1;
  background: radial-gradient(circle, rgba(16, 185, 129, 0.4) 0%, transparent 70%);
}

.toggle-input:checked + .toggle-slider .track-fill {
  width: 100%;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.toggle-input:checked + .toggle-slider .particle {
  opacity: 1;
  animation: particleFlow 2s linear infinite;
}

.toggle-input:checked + .toggle-slider .label-on {
  color: #10b981;
  font-weight: 600;
}

.toggle-input:checked + .toggle-slider .label-off {
  color: #94a3b8;
}

/* 悬停效果 */
.toggle-slider:hover .slider-thumb {
  transform: scale(1.05);
  box-shadow: 
    0 6px 16px rgba(0, 0, 0, 0.2),
    0 3px 8px rgba(0, 0, 0, 0.15);
}

.toggle-input:checked + .toggle-slider:hover .slider-thumb {
  transform: translateX(calc(var(--toggle-width) - var(--thumb-size) - var(--thumb-offset) * 2)) scale(1.05);
  box-shadow: 
    0 6px 20px rgba(16, 185, 129, 0.4),
    0 3px 10px rgba(16, 185, 129, 0.3);
}

.toggle-slider:hover .track-fill {
  box-shadow: inset 0 0 20px rgba(255, 255, 255, 0.2);
}

/* 禁用状态 */
.modern-toggle.disabled .toggle-slider {
  cursor: not-allowed;
  opacity: 0.5;
}

.modern-toggle.disabled .slider-thumb {
  filter: grayscale(1);
}

/* 焦点状态 */
.toggle-input:focus + .toggle-slider .slider-track {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* 激活状态 */
.toggle-input:active + .toggle-slider .slider-thumb {
  transform: scale(0.95);
}

.toggle-input:checked:active + .toggle-slider .slider-thumb {
  transform: translateX(calc(var(--toggle-width) - var(--thumb-size) - var(--thumb-offset) * 2)) scale(0.95);
}

/* 动画 */
@keyframes particleFlow {
  0% {
    opacity: 0;
    transform: translateY(-50%) scale(0);
  }
  50% {
    opacity: 1;
    transform: translateY(-50%) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateY(-50%) scale(0);
  }
}

/* 变体样式 */
.modern-toggle[data-variant="success"] .toggle-input:checked + .toggle-slider .track-fill {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.modern-toggle[data-variant="warning"] .toggle-input:checked + .toggle-slider .track-fill {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.modern-toggle[data-variant="danger"] .toggle-input:checked + .toggle-slider .track-fill {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modern-toggle.size-default {
    --toggle-width: 70px;
    --toggle-height: 35px;
    --thumb-size: 28px;
  }
  
  .toggle-labels {
    font-size: 11px;
  }
}
</style>