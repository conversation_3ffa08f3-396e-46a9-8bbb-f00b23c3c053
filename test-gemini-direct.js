const axios = require('axios');
require('dotenv').config();

async function testGeminiDirect() {
  console.log('🧪 直接测试Gemini API连接...\n');
  
  const apiKey = process.env.GEMINI_API_KEY;
  
  if (!apiKey) {
    console.log('❌ Gemini API密钥未配置');
    return;
  }
  
  console.log('✅ API密钥已配置');
  console.log(`🔑 密钥: ${apiKey.substring(0, 10)}...${apiKey.substring(apiKey.length - 4)}`);
  
  try {
    console.log('\n🌐 测试网络连接...');
    
    // 测试1: 简单的文本生成
    console.log('1️⃣ 测试基本文本生成...');
    
    const response = await axios.post(
      `https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${apiKey}`,
      {
        contents: [{
          parts: [{
            text: '你好，请用中文简单介绍一下你自己'
          }]
        }]
      },
      {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 15000 // 增加超时时间
      }
    );
    
    if (response.data && response.data.candidates && response.data.candidates[0]) {
      const text = response.data.candidates[0].content.parts[0].text;
      console.log('✅ Gemini API连接成功！');
      console.log(`🤖 回答: ${text}`);
      console.log(`📊 使用情况: ${JSON.stringify(response.data.usageMetadata || {})}`);
      
      // 测试2: 测试中文对话
      console.log('\n2️⃣ 测试中文对话...');
      
      const chatResponse = await axios.post(
        `https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${apiKey}`,
        {
          contents: [{
            parts: [{
              text: '请介绍一下新能源汽车的主要优势，用中文回答，不超过100字'
            }]
          }]
        },
        {
          headers: {
            'Content-Type': 'application/json'
          },
          timeout: 15000
        }
      );
      
      if (chatResponse.data && chatResponse.data.candidates && chatResponse.data.candidates[0]) {
        const chatText = chatResponse.data.candidates[0].content.parts[0].text;
        console.log('✅ 中文对话测试成功！');
        console.log(`🤖 回答: ${chatText}`);
      }
      
      // 测试3: 测试系统集成
      console.log('\n3️⃣ 测试系统集成...');
      
      try {
        const systemResponse = await axios.post('http://localhost:3001/api/ai-enhanced/chat', {
          message: '你好，请介绍一下你的功能'
        });
        
        if (systemResponse.data.success) {
          console.log('✅ 系统集成测试成功！');
          console.log(`🤖 使用服务: ${systemResponse.data.service}`);
          console.log(`📝 回答: ${systemResponse.data.response.substring(0, 100)}...`);
        } else {
          console.log('❌ 系统集成测试失败');
        }
      } catch (error) {
        console.log('❌ 系统集成测试失败:', error.message);
      }
      
    } else {
      console.log('❌ Gemini API响应格式异常');
      console.log('响应数据:', JSON.stringify(response.data, null, 2));
    }
    
  } catch (error) {
    console.log('❌ Gemini API连接失败');
    
    if (error.response) {
      console.log(`状态码: ${error.response.status}`);
      console.log(`错误信息: ${JSON.stringify(error.response.data, null, 2)}`);
      
      if (error.response.status === 400) {
        console.log('\n🔧 可能的解决方案:');
        console.log('- 检查API密钥是否正确');
        console.log('- 检查请求格式是否正确');
        console.log('- 确认API密钥有相应权限');
      } else if (error.response.status === 403) {
        console.log('\n🔧 可能的解决方案:');
        console.log('- API密钥可能无效或过期');
        console.log('- 检查API配额是否用完');
        console.log('- 确认地区限制');
      } else if (error.response.status === 429) {
        console.log('\n🔧 可能的解决方案:');
        console.log('- API调用频率过高，请稍后再试');
        console.log('- 检查免费配额限制（每分钟15次）');
      }
    } else if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
      console.log('⏱️ 请求超时');
      console.log('\n🔧 可能的解决方案:');
      console.log('- 检查网络连接');
      console.log('- 尝试使用VPN');
      console.log('- 检查防火墙设置');
      console.log('- 稍后再试（可能是服务器繁忙）');
    } else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      console.log('🌐 网络连接问题');
      console.log('\n🔧 解决方案:');
      console.log('- 检查网络连接');
      console.log('- 尝试使用VPN（推荐）');
      console.log('- 检查DNS设置');
    } else {
      console.log(`网络错误: ${error.message}`);
      console.log('错误代码:', error.code);
    }
  }
  
  console.log('\n📋 总结:');
  console.log('- Gemini API密钥已正确配置');
  console.log('- 如果连接失败，主要是网络问题');
  console.log('- 建议使用VPN访问Google服务');
  console.log('- 系统会自动降级到本地知识库');
}

testGeminiDirect();
