<template>
  <el-dialog
    v-model="dialogVisible"
    title="维护记录详情"
    width="700px"
    @close="$emit('close')"
    class="maintenance-detail-dialog"
  >
    <div v-if="record" class="maintenance-detail" v-loading="loading" element-loading-text="加载中...">
      <!-- 记录头部 -->
      <div class="record-header">
        <div class="header-left">
          <h3 class="record-title">{{ record.title }}</h3>
          <div class="record-meta">
            <el-tag :type="getStatusType(record.status)" size="small">
              {{ getStatusText(record.status) }}
            </el-tag>
            <el-tag size="small">{{ getTypeText(record.type) }}</el-tag>
          </div>
        </div>
        <div class="header-right">
          <div class="record-cost">
            <span class="cost-label">维护费用</span>
            <span class="cost-value">¥{{ record.cost }}</span>
          </div>
        </div>
      </div>

      <!-- 基本信息 -->
      <div class="detail-section">
        <h4 class="section-title">基本信息</h4>
        <div class="info-grid">
          <div class="info-item">
            <label>电池ID:</label>
            <span>{{ record.battery_id }}</span>
          </div>
          <div class="info-item">
            <label>维护日期:</label>
            <span>{{ formatDateTime(record.date) }}</span>
          </div>
          <div class="info-item">
            <label>技师:</label>
            <span>{{ record.technician }}</span>
          </div>
          <div class="info-item">
            <label>创建时间:</label>
            <span>{{ formatDateTime(record.created_at) }}</span>
          </div>
        </div>
      </div>

      <!-- 维护内容 -->
      <div class="detail-section">
        <h4 class="section-title">维护内容</h4>
        <div class="content-card">
          <div class="content-item">
            <label>维护描述:</label>
            <p>{{ record.description }}</p>
          </div>
          <div v-if="record.replaced_parts" class="content-item">
            <label>更换部件:</label>
            <p>{{ record.replaced_parts }}</p>
          </div>
          <div v-if="record.notes" class="content-item">
            <label>备注信息:</label>
            <p>{{ record.notes }}</p>
          </div>
        </div>
      </div>

      <!-- 关联电池信息 -->
      <div v-if="batteryInfo" class="detail-section">
        <h4 class="section-title">关联电池信息</h4>
        <div class="battery-info-card">
          <div class="battery-header">
            <div class="battery-basic">
              <span class="battery-id">{{ batteryInfo.id }}</span>
              <span class="battery-model">{{ batteryInfo.manufacturer }} {{ batteryInfo.model }}</span>
            </div>
            <el-button size="small" @click="viewBattery">查看电池</el-button>
          </div>
          <div class="battery-status">
            <div class="status-item">
              <span class="status-label">健康度:</span>
              <span class="status-value">{{ batteryInfo.soh }}%</span>
            </div>
            <div class="status-item">
              <span class="status-label">当前温度:</span>
              <span class="status-value">{{ batteryInfo.temperature }}°C</span>
            </div>
            <div class="status-item">
              <span class="status-label">循环次数:</span>
              <span class="status-value">{{ batteryInfo.cycle_count }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 维护历史 -->
      <div class="detail-section">
        <h4 class="section-title">相关维护历史</h4>
        <div class="history-list">
          <div 
            v-for="historyRecord in relatedHistory" 
            :key="historyRecord.id"
            class="history-item"
          >
            <div class="history-date">{{ formatDate(historyRecord.date) }}</div>
            <div class="history-content">
              <div class="history-title">{{ historyRecord.title }}</div>
              <div class="history-type">{{ getTypeText(historyRecord.type) }}</div>
            </div>
            <div class="history-cost">¥{{ historyRecord.cost }}</div>
          </div>
        </div>
      </div>

      <!-- 下次维护建议 -->
      <div v-if="record.next_maintenance" class="detail-section">
        <h4 class="section-title">下次维护建议</h4>
        <div class="next-maintenance-card">
          <div class="next-date">
            <el-icon><Calendar /></el-icon>
            <span>建议日期: {{ formatDate(record.next_maintenance) }}</span>
          </div>
          <div class="next-actions">
            <el-button size="small" type="primary">
              <el-icon><Plus /></el-icon>
              创建维护计划
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="$emit('close')">关闭</el-button>
        <el-button type="primary" @click="editRecord">编辑记录</el-button>
        <el-button @click="printRecord">
          <el-icon><Printer /></el-icon>
          打印
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Calendar, Plus, Printer } from '@element-plus/icons-vue'

// Props
const props = defineProps<{
  modelValue: boolean
  recordId: string
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'close': []
}>()

// 响应式数据
const loading = ref(false)
const record = ref<any>(null)
const batteryInfo = ref<any>(null)
const relatedHistory = ref<any[]>([])

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 监听器
watch(() => props.recordId, async (newId) => {
  if (newId && props.modelValue) {
    await loadRecordDetail(newId)
  }
}, { immediate: true })

watch(() => props.modelValue, async (visible) => {
  if (visible && props.recordId) {
    await loadRecordDetail(props.recordId)
  }
})

// 方法
const loadRecordDetail = async (recordId: string) => {
  loading.value = true
  try {
    // 模拟加载记录详情
    record.value = {
      id: recordId,
      battery_id: 'battery-001',
      type: 'inspection',
      title: '定期健康检查',
      description: '对电池进行全面的健康检查，包括电压测试、容量测试、温度监控等。检查结果显示电池状态良好，各项参数均在正常范围内。',
      date: '2024-01-15 10:30:00',
      technician: '李师傅',
      cost: 200,
      status: 'completed',
      replaced_parts: '',
      next_maintenance: '2024-07-15',
      notes: '建议定期监控温度变化，保持良好的散热环境。',
      created_at: '2024-01-15T10:00:00Z'
    }
    
    // 模拟加载电池信息
    batteryInfo.value = {
      id: 'battery-001',
      manufacturer: '宁德时代',
      model: 'NCM811-75kWh',
      soh: 96,
      temperature: 25,
      cycle_count: 245
    }
    
    // 模拟加载相关历史
    relatedHistory.value = [
      {
        id: '1',
        date: '2023-07-15',
        title: '定期检查',
        type: 'inspection',
        cost: 180
      },
      {
        id: '2',
        date: '2023-01-15',
        title: '初始安装检查',
        type: 'inspection',
        cost: 150
      }
    ]
  } catch (error) {
    ElMessage.error('加载维护记录详情失败')
    record.value = null
  } finally {
    loading.value = false
  }
}

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    'completed': 'success',
    'pending': 'warning',
    'cancelled': 'danger',
    'scheduled': 'info'
  }
  return types[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    'completed': '已完成',
    'pending': '进行中',
    'cancelled': '已取消',
    'scheduled': '计划中'
  }
  return texts[status] || '未知'
}

const getTypeText = (type: string) => {
  const texts: Record<string, string> = {
    'inspection': '定期检查',
    'replacement': '部件更换',
    'calibration': '系统校准',
    'repair': '故障维修',
    'preventive': '预防性维护',
    'emergency': '紧急维修'
  }
  return texts[type] || '其他'
}

const formatDate = (dateString: string) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const formatDateTime = (dateString: string) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN')
}

const viewBattery = () => {
  ElMessage.info('跳转到电池详情页面')
}

const editRecord = () => {
  ElMessage.info('编辑维护记录')
}

const printRecord = () => {
  ElMessage.info('打印维护记录')
}
</script>

<style scoped>
.maintenance-detail-dialog :deep(.el-dialog__body) {
  padding: 0;
}

.maintenance-detail {
  padding: 24px;
}

/* 记录头部 */
.record-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding-bottom: 20px;
  border-bottom: 2px solid #f3f4f6;
}

.header-left {
  flex: 1;
}

.record-title {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 12px 0;
}

.record-meta {
  display: flex;
  gap: 8px;
}

.header-right {
  text-align: right;
}

.record-cost {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.cost-label {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
}

.cost-value {
  font-size: 24px;
  font-weight: 700;
  color: #10b981;
}

/* 详情区域 */
.detail-section {
  margin-bottom: 32px;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

/* 信息网格 */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-item label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.info-item span {
  font-size: 14px;
  color: #111827;
  font-weight: 500;
}

/* 内容卡片 */
.content-card {
  background: #f9fafb;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e5e7eb;
}

.content-item {
  margin-bottom: 16px;
}

.content-item:last-child {
  margin-bottom: 0;
}

.content-item label {
  font-size: 14px;
  color: #374151;
  font-weight: 600;
  display: block;
  margin-bottom: 8px;
}

.content-item p {
  font-size: 14px;
  color: #111827;
  line-height: 1.6;
  margin: 0;
}

/* 电池信息卡片 */
.battery-info-card {
  background: #f0f9ff;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #bae6fd;
}

.battery-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.battery-basic {
  display: flex;
  flex-direction: column;
}

.battery-id {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

.battery-model {
  font-size: 14px;
  color: #6b7280;
}

.battery-status {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-label {
  font-size: 12px;
  color: #6b7280;
}

.status-value {
  font-size: 14px;
  color: #111827;
  font-weight: 600;
}

/* 历史记录列表 */
.history-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.history-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.history-date {
  font-size: 12px;
  color: #6b7280;
  min-width: 80px;
}

.history-content {
  flex: 1;
}

.history-title {
  font-size: 14px;
  font-weight: 500;
  color: #111827;
}

.history-type {
  font-size: 12px;
  color: #6b7280;
}

.history-cost {
  font-size: 14px;
  font-weight: 600;
  color: #10b981;
}

/* 下次维护卡片 */
.next-maintenance-card {
  background: #fef3c7;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #fde68a;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.next-date {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #92400e;
  font-weight: 500;
}

/* 对话框底部 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .record-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .battery-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .battery-status {
    grid-template-columns: 1fr;
  }

  .history-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .next-maintenance-card {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
}
</style>
