<template>
  <div class="battery-list">
    <!-- 列表头部 -->
    <div class="list-header">
      <div class="list-title">
        <h3>电池列表</h3>
        <span class="battery-count">共 {{ batteries.length }} 块电池</span>
      </div>
      <div class="list-actions">
        <el-button-group>
          <el-button 
            :type="viewMode === 'grid' ? 'primary' : ''"
            @click="viewMode = 'grid'"
            size="small"
          >
            <el-icon><Grid /></el-icon>
            网格
          </el-button>
          <el-button 
            :type="viewMode === 'table' ? 'primary' : ''"
            @click="viewMode = 'table'"
            size="small"
          >
            <el-icon><List /></el-icon>
            列表
          </el-button>
        </el-button-group>
      </div>
    </div>

    <!-- 网格视图 -->
    <div v-if="viewMode === 'grid'" class="grid-view">
      <div class="battery-grid">
        <div 
          v-for="battery in batteries" 
          :key="battery.id" 
          class="battery-card"
          @click="$emit('view-detail', battery.id)"
        >
          <div class="battery-header">
            <div class="battery-brand">
              <span class="brand-name">{{ battery.manufacturer }}</span>
              <span class="battery-model">{{ battery.model }}</span>
            </div>
            <div class="battery-status">
              <el-tag :type="getStatusType(battery.health_status)" size="small">
                {{ getStatusText(battery.health_status) }}
              </el-tag>
            </div>
          </div>
          
          <div class="battery-info">
            <h4 class="battery-id">{{ battery.id }}</h4>
            <p class="battery-vehicle" v-if="battery.vehicle">
              {{ battery.vehicle.brand }} {{ battery.vehicle.model }}
            </p>
            <p class="battery-serial">SN: {{ battery.serial_number }}</p>
          </div>
          
          <div class="battery-metrics">
            <div class="metric-item">
              <span class="metric-label">健康度</span>
              <div class="metric-value">
                <el-progress
                  :percentage="battery.soh"
                  :color="getHealthColor(battery.soh)"
                  :stroke-width="6"
                  :show-text="false"
                />
                <span class="metric-text">{{ battery.soh }}%</span>
              </div>
            </div>
            <div class="metric-item">
              <span class="metric-label">电量</span>
              <div class="metric-value">
                <el-progress
                  :percentage="battery.soc"
                  color="#3b82f6"
                  :stroke-width="6"
                  :show-text="false"
                />
                <span class="metric-text">{{ battery.soc }}%</span>
              </div>
            </div>
            <div class="metric-row">
              <div class="metric-small">
                <span class="metric-label">温度</span>
                <span class="metric-text" :class="getTemperatureClass(battery.temperature)">
                  {{ battery.temperature }}°C
                </span>
              </div>
              <div class="metric-small">
                <span class="metric-label">电压</span>
                <span class="metric-text">{{ battery.voltage }}V</span>
              </div>
            </div>
          </div>
          
          <div class="battery-actions" @click.stop>
            <el-button size="small" @click="$emit('view-detail', battery.id)">
              详情
            </el-button>
            <el-button size="small" @click="$emit('edit-battery', battery.id)">
              编辑
            </el-button>
            <el-dropdown @command="handleAction">
              <el-button size="small">
                更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="{action: 'maintenance', id: battery.id}">
                    <el-icon><Tools /></el-icon>维护记录
                  </el-dropdown-item>
                  <el-dropdown-item :command="{action: 'trace', id: battery.id}">
                    <el-icon><Location /></el-icon>溯源追踪
                  </el-dropdown-item>
                  <el-dropdown-item :command="{action: 'analysis', id: battery.id}">
                    <el-icon><TrendCharts /></el-icon>健康分析
                  </el-dropdown-item>
                  <el-dropdown-item :command="{action: 'delete', id: battery.id}" divided>
                    <el-icon><Delete /></el-icon>删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>
    </div>

    <!-- 表格视图 -->
    <div v-else class="table-view">
      <el-table 
        :data="batteries" 
        style="width: 100%" 
        @row-click="handleRowClick"
        class="battery-table"
        v-loading="loading"
        element-loading-text="加载中..."
      >
        <el-table-column prop="id" label="电池ID" width="120" />
        <el-table-column prop="manufacturer" label="制造商" width="100" />
        <el-table-column prop="model" label="型号" width="100" />
        <el-table-column label="健康度" width="120">
          <template #default="{ row }">
            <el-progress
              :percentage="row.soh"
              :color="getHealthColor(row.soh)"
              :stroke-width="8"
            />
          </template>
        </el-table-column>
        <el-table-column label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.health_status)" size="small">
              {{ getStatusText(row.health_status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="车辆信息" width="150">
          <template #default="{ row }">
            <div v-if="row.vehicle" class="vehicle-info">
              <div class="vehicle-brand">{{ row.vehicle.brand }} {{ row.vehicle.model }}</div>
              <div class="vehicle-vin">{{ row.vehicle.vin?.slice(-8) }}</div>
            </div>
            <span v-else class="no-vehicle">未绑定车辆</span>
          </template>
        </el-table-column>
        <el-table-column prop="temperature" label="温度(°C)" width="100">
          <template #default="{ row }">
            <span :class="getTemperatureClass(row.temperature)">
              {{ row.temperature }}°C
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="voltage" label="电压(V)" width="100" />
        <el-table-column prop="capacity" label="容量(kWh)" width="100" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="table-actions">
              <el-button size="small" @click.stop="$emit('view-detail', row.id)">
                详情
              </el-button>
              <el-button size="small" @click.stop="$emit('edit-battery', row.id)">
                编辑
              </el-button>
              <el-button size="small" @click.stop="$emit('view-maintenance', row.id)">
                维护
              </el-button>
              <el-button 
                size="small" 
                type="danger" 
                @click.stop="$emit('delete-battery', row.id)"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 空状态 -->
    <div v-if="batteries.length === 0 && !loading" class="empty-state">
      <el-empty description="暂无电池数据">
        <el-button type="primary" @click="$emit('add-battery')">添加第一块电池</el-button>
      </el-empty>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  Grid, List, ArrowDown, Tools, Location, TrendCharts, Delete
} from '@element-plus/icons-vue'
import type { Battery } from '@/stores/battery'

// Props
defineProps<{
  batteries: Battery[]
  loading?: boolean
}>()

// Emits
defineEmits<{
  'view-detail': [batteryId: string]
  'edit-battery': [batteryId: string]
  'delete-battery': [batteryId: string]
  'view-maintenance': [batteryId: string]
  'add-battery': []
}>()

// 响应式数据
const viewMode = ref<'grid' | 'table'>('grid')

// 方法
const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    'good': 'success',
    'warning': 'warning',
    'critical': 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    'good': '健康',
    'warning': '预警',
    'critical': '异常'
  }
  return texts[status] || '未知'
}

const getHealthColor = (health: number) => {
  if (health >= 80) return '#67C23A'
  if (health >= 60) return '#E6A23C'
  return '#F56C6C'
}

const getTemperatureClass = (temperature: number) => {
  if (temperature > 35) return 'temperature-high'
  if (temperature > 30) return 'temperature-medium'
  return 'temperature-normal'
}

const handleRowClick = (row: Battery) => {
  // 表格行点击事件
}

const handleAction = (command: {action: string, id: string}) => {
  const { action, id } = command
  switch (action) {
    case 'maintenance':
      // 维护记录
      break
    case 'trace':
      // 溯源追踪
      break
    case 'analysis':
      // 健康分析
      break
    case 'delete':
      // 删除电池
      break
  }
}
</script>

<style scoped>
.battery-list {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  overflow: hidden;
}

/* 列表头部 */
.list-header {
  padding: 24px;
  border-bottom: 1px solid #f3f4f6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.list-title h3 {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 4px 0;
}

.battery-count {
  font-size: 14px;
  color: #6b7280;
}

/* 网格视图 */
.grid-view {
  padding: 24px;
}

.battery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
}

.battery-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  cursor: pointer;
}

.battery-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #3b82f6;
}

.battery-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.battery-brand {
  display: flex;
  flex-direction: column;
}

.brand-name {
  font-size: 14px;
  font-weight: 600;
  color: #111827;
}

.battery-model {
  font-size: 12px;
  color: #6b7280;
}

.battery-info {
  margin-bottom: 16px;
}

.battery-id {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 8px 0;
}

.battery-vehicle {
  font-size: 13px;
  color: #3b82f6;
  margin: 0 0 4px 0;
}

.battery-serial {
  font-size: 12px;
  color: #9ca3af;
  margin: 0;
}

.battery-metrics {
  margin-bottom: 16px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.metric-item:last-child {
  margin-bottom: 0;
}

.metric-label {
  font-size: 12px;
  color: #6b7280;
  min-width: 50px;
}

.metric-value {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.metric-text {
  font-size: 12px;
  font-weight: 500;
  color: #111827;
  min-width: 35px;
  text-align: right;
}

.metric-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.metric-small {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.battery-actions {
  display: flex;
  gap: 8px;
}

/* 表格视图 */
.table-view {
  padding: 0;
}

.battery-table {
  background: transparent;
}

.battery-table :deep(.el-table__header) {
  background: #f9fafb;
}

.battery-table :deep(.el-table__row:hover) {
  background: #f0f9ff;
}

.vehicle-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.vehicle-brand {
  font-size: 13px;
  font-weight: 500;
  color: #111827;
}

.vehicle-vin {
  font-size: 12px;
  color: #6b7280;
}

.no-vehicle {
  font-size: 12px;
  color: #9ca3af;
  font-style: italic;
}

.table-actions {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.temperature-normal {
  color: #10b981;
  font-weight: 500;
}

.temperature-medium {
  color: #f59e0b;
  font-weight: 500;
}

.temperature-high {
  color: #ef4444;
  font-weight: 600;
}

/* 空状态 */
.empty-state {
  padding: 60px 24px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .list-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .battery-grid {
    grid-template-columns: 1fr;
  }

  .table-actions {
    flex-direction: column;
    gap: 4px;
  }
}
</style>
