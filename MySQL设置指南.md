# MySQL数据库设置指南

## 快速设置步骤

### 1. 启动MySQL服务
确保你的MySQL服务正在运行。

### 2. 连接到MySQL
打开命令行或MySQL客户端，使用以下命令连接：
```bash
mysql -u root -p123456
```

### 3. 执行数据库初始化脚本
在MySQL命令行中执行：
```sql
source E:/指尖消除小游戏脚本/mysql_setup.sql
```

或者直接复制粘贴 `mysql_setup.sql` 文件中的内容到MySQL命令行执行。

### 4. 验证数据库创建
执行以下命令验证：
```sql
USE ev_management;
SHOW TABLES;
SELECT COUNT(*) FROM users;
```

## 当前配置信息

- **数据库主机**: localhost
- **端口**: 3306
- **用户名**: root
- **密码**: 123456
- **数据库名**: ev_management

## 完成后

数据库设置完成后，项目将自动连接到MySQL数据库，不再使用模拟数据。

## 如果遇到问题

1. **连接被拒绝**: 检查MySQL服务是否启动
2. **密码错误**: 确认root用户密码是否为123456
3. **数据库不存在**: 确保执行了完整的初始化脚本

## 数据库表结构

初始化脚本将创建以下表：
- users (用户表)
- vehicles (车辆表)
- batteries (电池表)
- charging_stations (充电站表)
- charging_sessions (充电会话表)
- edge_devices (边缘设备表)
- 以及其他相关表和视图

每个表都包含示例数据，可以立即开始测试。