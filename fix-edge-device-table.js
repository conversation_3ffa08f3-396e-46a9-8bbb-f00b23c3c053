const mysql = require('mysql2/promise');

async function fixEdgeDeviceTable() {
  let connection;
  
  try {
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '123456',
      database: 'ev_management',
      multipleStatements: true
    });

    console.log('✅ 数据库连接成功');

    // 添加缺少的字段到 edge_device_data 表
    console.log('🔧 修复 edge_device_data 表结构...');
    
    const fieldsToAdd = [
      { name: 'unit', sql: 'ADD COLUMN unit VARCHAR(20) AFTER json_value' },
      { name: 'quality', sql: 'ADD COLUMN quality DECIMAL(5,2) DEFAULT 100.00 AFTER unit' }
    ];

    for (const field of fieldsToAdd) {
      try {
        await connection.execute(`ALTER TABLE edge_device_data ${field.sql}`);
        console.log(`✅ 添加 ${field.name} 字段成功`);
      } catch (error) {
        if (error.code === 'ER_DUP_FIELDNAME') {
          console.log(`ℹ️  ${field.name} 字段已存在`);
        } else {
          console.log(`⚠️  添加 ${field.name} 字段失败:`, error.message);
        }
      }
    }

    // 验证表结构
    console.log('🔍 验证 edge_device_data 表结构...');
    const [fields] = await connection.execute('DESCRIBE edge_device_data');
    console.log('edge_device_data 表字段:');
    fields.forEach(field => {
      console.log(`  - ${field.Field}: ${field.Type} ${field.Null === 'YES' ? '(可空)' : '(非空)'} ${field.Default ? `默认: ${field.Default}` : ''}`);
    });

    console.log('🎉 edge_device_data 表修复完成！');

  } catch (error) {
    console.error('❌ 修复失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 执行修复
fixEdgeDeviceTable();