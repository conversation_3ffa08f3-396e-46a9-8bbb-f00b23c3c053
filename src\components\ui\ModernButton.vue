<template>
  <button
    ref="buttonRef"
    :class="[
      'modern-btn',
      `modern-btn--${variant}`,
      `modern-btn--${responsiveSize}`,
      {
        'modern-btn--loading': loading,
        'modern-btn--disabled': disabled,
        'modern-btn--full-width': fullWidth,
        'modern-btn--icon-only': iconOnly
      },
      buttonClasses
    ]"
    :style="buttonStyles"
    :disabled="disabled || loading"
    v-modern-interaction="interactionConfig"
    @click="handleClick"
  >
    <span class="modern-btn__content">
      <span v-if="loading" class="modern-btn__spinner"></span>
      <template v-else>
        <slot name="icon" v-if="$slots.icon"></slot>
        <span v-if="!iconOnly" class="modern-btn__text">
          <slot></slot>
        </span>
      </template>
    </span>
    <div class="modern-btn__ripple"></div>
  </button>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useBreakpoint, useResponsiveValue, useDeviceFeatures, type ResponsiveValue } from '@/composables/useResponsive'
import { interactionPresets, type ModernInteractionBinding } from '@/directives/modernInteraction'

interface Props {
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'ghost' | 'neon'
  size?: 'small' | 'medium' | 'large' | ResponsiveValue<'small' | 'medium' | 'large'>
  loading?: boolean
  disabled?: boolean
  fullWidth?: boolean
  iconOnly?: boolean
  customStyles?: Record<string, string>
  interaction?: 'button' | 'subtle' | 'intense' | ModernInteractionBinding
}

interface Emits {
  click: [event: MouseEvent]
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'primary',
  size: 'medium',
  loading: false,
  disabled: false,
  fullWidth: false,
  iconOnly: false,
  interaction: 'button'
})

const emit = defineEmits<Emits>()

// 响应式功能
const { isMobileDevice, isTabletDevice, isTouchDevice } = useBreakpoint()
const { reducedMotion, highContrast } = useDeviceFeatures()

// 响应式尺寸
const responsiveSize = useResponsiveValue(
  typeof props.size === 'string' ? props.size : props.size,
  'medium'
)

// 响应式边框圆角
const responsiveBorderRadius = useResponsiveValue(
  {
    mobile: '10px',
    tablet: '11px',
    desktop: '12px'
  },
  '12px'
)

// 按钮引用
const buttonRef = ref<HTMLButtonElement>()

// 交互配置
const interactionConfig = computed(() => {
  if (props.disabled || props.loading) {
    return { disabled: true }
  }
  
  if (typeof props.interaction === 'string') {
    return interactionPresets[props.interaction] || interactionPresets.button
  }
  
  return props.interaction
})

// 按钮样式
const buttonStyles = computed(() => {
  const styles: Record<string, string> = {}
  
  // CSS变量
  styles['--btn-border-radius'] = responsiveBorderRadius.value
  
  // 全宽样式
  if (props.fullWidth) {
    styles.width = '100%'
  }
  
  // 图标按钮样式
  if (props.iconOnly) {
    const sizeMap = {
      small: '36px',
      medium: '44px',
      large: '52px'
    }
    const size = sizeMap[responsiveSize as keyof typeof sizeMap] || sizeMap.medium
    styles.width = size
    styles.height = size
    styles.padding = '0'
  }
  
  // 减少动画
  if (reducedMotion) {
    styles.transition = 'none'
    styles.animation = 'none'
  }
  
  // 自定义样式
  if (props.customStyles) {
    Object.assign(styles, props.customStyles)
  }
  
  return styles
})

// 按钮类名
const buttonClasses = computed(() => {
  const classes: string[] = []
  
  // 设备特定类名
  if (isMobileDevice) {
    classes.push('modern-btn--mobile')
  }
  if (isTabletDevice) {
    classes.push('modern-btn--tablet')
  }
  if (isTouchDevice) {
    classes.push('modern-btn--touch')
  }
  
  // 无障碍类名
  if (reducedMotion) {
    classes.push('modern-btn--reduced-motion')
  }
  if (highContrast) {
    classes.push('modern-btn--high-contrast')
  }
  
  return classes
})

// 点击处理
const handleClick = (event: MouseEvent) => {
  if (!props.loading && !props.disabled) {
    emit('click', event)
  }
}
</script>

<style scoped>
.modern-btn {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: var(--btn-border-radius, 12px);
  font-weight: 600;
  font-family: inherit;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  outline: none;
  user-select: none;
  text-decoration: none;
  white-space: nowrap;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transform-style: preserve-3d;
  will-change: transform, box-shadow;
  
  /* CSS变量 */
  --btn-padding: 12px 24px;
  --btn-font-size: 16px;
  --btn-min-height: 44px;
  --btn-gap: 8px;
  --btn-border-radius: 12px;
}

.modern-btn:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.modern-btn--full-width {
  width: 100%;
  display: flex;
}

.modern-btn--icon-only {
  border-radius: 50%;
  aspect-ratio: 1;
}

.modern-btn:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s;
}

.modern-btn:hover:before {
  transform: translateX(100%);
}

/* 响应式尺寸修饰符 */
.modern-btn--small {
  --btn-padding: 8px 16px;
  --btn-font-size: 14px;
  --btn-min-height: 36px;
  --btn-gap: 6px;
  --btn-border-radius: 10px;
}

.modern-btn--medium {
  --btn-padding: 12px 24px;
  --btn-font-size: 16px;
  --btn-min-height: 44px;
  --btn-gap: 8px;
  --btn-border-radius: 12px;
}

.modern-btn--large {
  --btn-padding: 16px 32px;
  --btn-font-size: 18px;
  --btn-min-height: 52px;
  --btn-gap: 10px;
  --btn-border-radius: 14px;
}

.modern-btn--mobile {
  --btn-padding: 8px 16px;
  --btn-font-size: 14px;
  --btn-min-height: 36px;
  --btn-gap: 6px;
  --btn-border-radius: 10px;
}

.modern-btn--tablet {
  --btn-padding: 10px 20px;
  --btn-font-size: 15px;
  --btn-min-height: 40px;
  --btn-gap: 7px;
  --btn-border-radius: 11px;
}

.modern-btn--touch {
  --btn-min-height: 44px; /* iOS 推荐的最小触摸目标 */
}

.modern-btn--reduced-motion {
  transition: none;
  animation: none;
}

.modern-btn--reduced-motion:hover {
  transform: none;
}

.modern-btn--reduced-motion .modern-btn__content {
  transition: none;
}

.modern-btn--reduced-motion .modern-btn__ripple {
  transition: none;
}

/* 尺寸变体应用CSS变量 */
.modern-btn--small,
.modern-btn--medium,
.modern-btn--large,
.modern-btn--mobile,
.modern-btn--tablet {
  padding: var(--btn-padding);
  font-size: var(--btn-font-size);
  min-height: var(--btn-min-height);
  gap: var(--btn-gap);
  border-radius: var(--btn-border-radius);
}

/* 图标按钮尺寸调整 */
.modern-btn--icon-only.modern-btn--small {
  width: 36px;
  height: 36px;
  padding: 0;
}

.modern-btn--icon-only.modern-btn--medium {
  width: 44px;
  height: 44px;
  padding: 0;
}

.modern-btn--icon-only.modern-btn--large {
  width: 52px;
  height: 52px;
  padding: 0;
}

/* 主要按钮 */
.modern-btn--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: var(--text-on-primary);
  box-shadow: 0 4px 15px 0 rgba(102, 126, 234, 0.4);
}

.modern-btn--primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px 0 rgba(102, 126, 234, 0.6);
}

.modern-btn--primary:active {
  transform: translateY(0);
  box-shadow: 0 4px 15px 0 rgba(102, 126, 234, 0.4);
}

/* 次要按钮 */
.modern-btn--secondary {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: var(--text-on-secondary);
    box-shadow: 0 4px 15px 0 rgba(240, 147, 251, 0.4);
  }

.modern-btn--secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px 0 rgba(240, 147, 251, 0.6);
}

/* 成功按钮 */
.modern-btn--success {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: var(--text-on-primary);
    box-shadow: 0 4px 15px 0 rgba(79, 172, 254, 0.4);
  }

.modern-btn--success:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px 0 rgba(79, 172, 254, 0.6);
}

/* 警告按钮 */
.modern-btn--warning {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    color: var(--text-on-primary);
    box-shadow: 0 4px 15px 0 rgba(250, 112, 154, 0.4);
  }

.modern-btn--warning:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px 0 rgba(250, 112, 154, 0.6);
}

/* 危险按钮 */
.modern-btn--danger {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: var(--text-on-primary);
    box-shadow: 0 4px 15px 0 rgba(255, 107, 107, 0.4);
  }

.modern-btn--danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px 0 rgba(255, 107, 107, 0.6);
}

/* 幽灵按钮 */
.modern-btn--ghost {
  background: rgba(255, 255, 255, 0.1);
  color: #667eea;
  border: 2px solid #667eea;
  backdrop-filter: blur(10px);
}

.modern-btn--ghost:hover {
  background: rgba(102, 126, 234, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px 0 rgba(102, 126, 234, 0.3);
}

/* 霓虹按钮 */
.modern-btn--neon {
  background: transparent;
  color: #00f5ff;
  border: 2px solid #00f5ff;
  text-shadow: 0 0 10px #00f5ff;
  box-shadow: 
    0 0 10px #00f5ff,
    inset 0 0 10px rgba(0, 245, 255, 0.1);
}

.modern-btn--neon:hover {
  background: rgba(0, 245, 255, 0.1);
  box-shadow: 
    0 0 20px #00f5ff,
    0 0 40px #00f5ff,
    inset 0 0 20px rgba(0, 245, 255, 0.2);
  transform: translateY(-2px);
}

/* 加载状态 */
.modern-btn--loading {
  pointer-events: none;
}

.modern-btn--loading .modern-btn__content {
  opacity: 0.7;
}

/* 禁用状态 */
.modern-btn--disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.modern-btn--disabled:hover {
  transform: none;
  box-shadow: none;
}

/* 内容容器 */
.modern-btn__content {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: inherit;
  z-index: 2;
  transition: transform 0.2s ease;
  width: 100%;
  height: 100%;
}

.modern-btn__text {
  display: flex;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.modern-btn--icon-only .modern-btn__content {
  gap: 0;
}

.modern-btn:active .modern-btn__content {
  transform: scale(0.98);
}

@media (prefers-reduced-motion: reduce) {
  .modern-btn:not(.modern-btn--reduced-motion) {
    transition: none;
    animation: none;
  }
  
  .modern-btn:not(.modern-btn--reduced-motion):hover {
    transform: none;
  }
  
  .modern-btn:not(.modern-btn--reduced-motion):active .modern-btn__content {
    transform: none;
  }
  
  .modern-btn:not(.modern-btn--reduced-motion) .modern-btn__content {
    transition: none;
  }
  
  .modern-btn:not(.modern-btn--reduced-motion) .modern-btn__ripple {
    transition: none;
  }
}

/* 加载动画 */
.modern-btn__spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 波纹效果 */
.modern-btn__ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.3s, height 0.3s;
}

.modern-btn:active .modern-btn__ripple {
  width: 200px;
  height: 200px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modern-btn:not(.modern-btn--mobile):not(.modern-btn--tablet) {
    --btn-border-radius: 10px;
  }
  
  .modern-btn--small:not(.modern-btn--mobile):not(.modern-btn--tablet) {
    --btn-padding: 6px 12px;
    --btn-font-size: 13px;
    --btn-min-height: 32px;
    --btn-gap: 4px;
  }
  
  .modern-btn--medium:not(.modern-btn--mobile):not(.modern-btn--tablet) {
    --btn-padding: 8px 16px;
    --btn-font-size: 14px;
    --btn-min-height: 36px;
    --btn-gap: 6px;
  }
  
  .modern-btn--large:not(.modern-btn--mobile):not(.modern-btn--tablet) {
    --btn-padding: 10px 20px;
    --btn-font-size: 16px;
    --btn-min-height: 40px;
    --btn-gap: 8px;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .modern-btn:not(.modern-btn--mobile):not(.modern-btn--tablet) {
    --btn-border-radius: 11px;
  }
  
  .modern-btn--small:not(.modern-btn--mobile):not(.modern-btn--tablet) {
    --btn-padding: 7px 14px;
    --btn-font-size: 13px;
    --btn-min-height: 34px;
    --btn-gap: 5px;
  }
  
  .modern-btn--medium:not(.modern-btn--mobile):not(.modern-btn--tablet) {
    --btn-padding: 10px 20px;
    --btn-font-size: 15px;
    --btn-min-height: 40px;
    --btn-gap: 7px;
  }
  
  .modern-btn--large:not(.modern-btn--mobile):not(.modern-btn--tablet) {
    --btn-padding: 14px 28px;
    --btn-font-size: 17px;
    --btn-min-height: 48px;
    --btn-gap: 9px;
  }
}

@media (max-width: 480px) {
  .modern-btn:not(.modern-btn--mobile):not(.modern-btn--tablet) {
    --btn-border-radius: 8px;
  }
  
  .modern-btn--small:not(.modern-btn--mobile):not(.modern-btn--tablet) {
    --btn-padding: 4px 8px;
    --btn-font-size: 12px;
    --btn-min-height: 28px;
  }
  
  .modern-btn--medium:not(.modern-btn--mobile):not(.modern-btn--tablet) {
    --btn-padding: 6px 12px;
    --btn-font-size: 13px;
    --btn-min-height: 32px;
  }
  
  .modern-btn--large:not(.modern-btn--mobile):not(.modern-btn--tablet) {
    --btn-padding: 8px 16px;
    --btn-font-size: 14px;
    --btn-min-height: 36px;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .modern-btn:not(.modern-btn--touch) {
    --btn-min-height: 44px; /* iOS 推荐的最小触摸目标 */
  }
  
  .modern-btn--small:not(.modern-btn--touch) {
    --btn-min-height: 36px;
  }
  
  .modern-btn:not(.modern-btn--reduced-motion):hover {
    transform: none;
  }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .modern-btn {
    border-width: 0.5px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .modern-btn--ghost {
    background: rgba(0, 0, 0, 0.2);
    color: #a78bfa;
    border-color: #a78bfa;
  }
  
  .modern-btn--ghost:hover {
    background: rgba(167, 139, 250, 0.1);
  }
}
</style>