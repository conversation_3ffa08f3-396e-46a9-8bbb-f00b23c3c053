<template>
  <el-dialog
    v-model="visible"
    title="支付确认"
    width="500px"
    :before-close="handleClose"
    class="payment-dialog"
  >
    <div class="payment-content">
      <!-- 订单信息 -->
      <div class="order-info">
        <h3 class="order-title">{{ orderInfo.title }}</h3>
        <p class="order-desc">{{ orderInfo.description }}</p>
        <div class="amount-section">
          <div class="original-amount" v-if="orderInfo.originalAmount && orderInfo.originalAmount > orderInfo.amount">
            原价：<span class="original-price">¥{{ orderInfo.originalAmount.toFixed(2) }}</span>
          </div>
          <div class="final-amount">
            实付金额：<span class="final-price">¥{{ orderInfo.amount.toFixed(2) }}</span>
          </div>
          <div class="discount" v-if="orderInfo.discount && orderInfo.discount > 0">
            优惠：<span class="discount-amount">-¥{{ orderInfo.discount.toFixed(2) }}</span>
          </div>
        </div>
      </div>

      <!-- 支付方式选择 -->
      <div class="payment-methods">
        <h4>选择支付方式</h4>
        <div class="method-list">
          <div 
            v-for="method in paymentMethods" 
            :key="method.value"
            class="method-item"
            :class="{ active: selectedMethod === method.value }"
            @click="selectedMethod = method.value"
          >
            <div class="method-icon">
              <component :is="method.icon" :size="24" />
            </div>
            <div class="method-info">
              <div class="method-name">{{ method.name }}</div>
              <div class="method-desc">{{ method.description }}</div>
            </div>
            <div class="method-radio">
              <el-radio 
                v-model="selectedMethod" 
                :label="method.value"
                size="large"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 余额信息 -->
      <div class="balance-info" v-if="selectedMethod === 'balance'">
        <div class="balance-item">
          <span>当前余额：</span>
          <span class="balance-amount">¥{{ userBalance.toFixed(2) }}</span>
        </div>
        <div class="balance-insufficient" v-if="userBalance < orderInfo.amount">
          <el-alert
            title="余额不足"
            type="warning"
            description="当前余额不足以完成支付，请选择其他支付方式或充值"
            show-icon
            :closable="false"
          />
        </div>
      </div>

      <!-- 支付协议 -->
      <div class="payment-agreement">
        <el-checkbox v-model="agreeTerms" size="large">
          我已阅读并同意
          <el-link type="primary" @click="showTerms">《支付服务协议》</el-link>
        </el-checkbox>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <ModernButton @click="handleClose" variant="ghost" size="large">取消</ModernButton>
        <div style="position: relative;">
          <ModernLoader v-if="paymentLoading" />
          <ModernButton 
            variant="primary" 
            @click="handlePayment"
            :disabled="!canPay || paymentLoading"
            size="large"
          >
            {{ paymentLoading ? '支付中...' : `确认支付 ¥${orderInfo.amount.toFixed(2)}` }}
          </ModernButton>
        </div>
      </div>
    </template>
  </el-dialog>

  <!-- 支付结果对话框 -->
  <el-dialog
    v-model="resultVisible"
    :title="paymentResult.success ? '支付成功' : '支付失败'"
    width="400px"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div class="payment-result">
      <div class="result-icon">
        <el-icon v-if="paymentResult.success" color="#67C23A" :size="60">
          <CircleCheck />
        </el-icon>
        <el-icon v-else color="#F56C6C" :size="60">
          <CircleClose />
        </el-icon>
      </div>
      <div class="result-message">
        {{ paymentResult.message }}
      </div>
      <div class="result-details" v-if="paymentResult.success">
        <p>支付金额：¥{{ orderInfo.amount.toFixed(2) }}</p>
        <p>支付方式：{{ getPaymentMethodName(selectedMethod) }}</p>
        <p>支付时间：{{ new Date().toLocaleString() }}</p>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <ModernButton variant="primary" @click="handleResultClose" size="large">
          确定
        </ModernButton>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  CreditCard, 
  Wallet, 
  CircleCheck, 
  CircleClose 
} from '@element-plus/icons-vue'
import { 
  paymentService, 
  PaymentMethod
} from '@/services/paymentService'
import type { PaymentOrder, PaymentRequest } from '@/services/paymentService'
import { ModernButton, ModernLoader } from '@/components/ui'

// Props
interface Props {
  modelValue: boolean
  orderInfo: Partial<PaymentOrder>
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  orderInfo: () => ({
    title: '',
    description: '',
    amount: 0
  })
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'payment-success': [order: PaymentOrder]
  'payment-failed': [error: string]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const selectedMethod = ref<PaymentMethod>(PaymentMethod.WECHAT)
const agreeTerms = ref(false)
const paymentLoading = ref(false)
const userBalance = ref(156.80) // 模拟用户余额
const resultVisible = ref(false)
const paymentResult = ref({
  success: false,
  message: ''
})

// 支付方式配置
const paymentMethods = [
  {
    value: PaymentMethod.WECHAT,
    name: '微信支付',
    description: '使用微信快捷支付',
    icon: 'CreditCard'
  },
  {
    value: PaymentMethod.ALIPAY,
    name: '支付宝',
    description: '使用支付宝安全支付',
    icon: 'CreditCard'
  },
  {
    value: PaymentMethod.BALANCE,
    name: '余额支付',
    description: '使用账户余额支付',
    icon: 'Wallet'
  }
]

// 计算属性
const canPay = computed(() => {
  if (!agreeTerms.value) return false
  if (selectedMethod.value === PaymentMethod.BALANCE) {
    return userBalance.value >= props.orderInfo.amount!
  }
  return true
})

// 监听对话框显示状态
watch(visible, (newVal) => {
  if (newVal) {
    // 重置状态
    selectedMethod.value = PaymentMethod.WECHAT
    agreeTerms.value = false
    paymentLoading.value = false
  }
})

// 处理关闭
const handleClose = () => {
  if (paymentLoading.value) {
    ElMessage.warning('支付进行中，请稍候...')
    return
  }
  visible.value = false
}

// 处理支付
const handlePayment = async () => {
  if (!canPay.value) {
    ElMessage.warning('请检查支付条件')
    return
  }

  try {
    paymentLoading.value = true

    // 创建订单
    const order = await paymentService.createOrder({
      ...props.orderInfo,
      amount: props.orderInfo.amount || 0
    })

    // 发起支付
    const paymentRequest: PaymentRequest = {
      orderId: order.id,
      amount: order.amount,
      paymentMethod: selectedMethod.value
    }

    const response = await paymentService.initiatePayment(paymentRequest)

    if (response.success) {
      paymentResult.value = {
        success: true,
        message: '支付成功！'
      }
      
      // 更新用户余额（如果是余额支付）
      if (selectedMethod.value === PaymentMethod.BALANCE) {
        userBalance.value -= order.amount
      }

      emit('payment-success', order)
      ElMessage.success('支付成功！')
    } else {
      paymentResult.value = {
        success: false,
        message: response.message || '支付失败，请重试'
      }
      emit('payment-failed', response.message)
    }

    visible.value = false
    resultVisible.value = true

  } catch (error) {
    console.error('支付异常:', error)
    const errorMessage = error instanceof Error ? error.message : '支付异常，请重试'
    paymentResult.value = {
      success: false,
      message: errorMessage
    }
    emit('payment-failed', errorMessage)
    ElMessage.error(errorMessage)
    resultVisible.value = true
  } finally {
    paymentLoading.value = false
  }
}

// 处理结果对话框关闭
const handleResultClose = () => {
  resultVisible.value = false
}

// 显示支付协议
const showTerms = () => {
  ElMessageBox.alert(
    '这里是支付服务协议的内容...',
    '支付服务协议',
    {
      confirmButtonText: '我知道了',
      type: 'info'
    }
  )
}

// 获取支付方式名称
const getPaymentMethodName = (method: PaymentMethod): string => {
  return paymentService.getPaymentMethodName(method)
}
</script>

<style scoped>
.payment-dialog {
  .payment-content {
    padding: 0;
  }

  .order-info {
    padding: 20px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 8px;
    margin-bottom: 20px;

    .order-title {
      font-size: 18px;
      font-weight: 600;
      color: #1f2937;
      margin: 0 0 8px 0;
    }

    .order-desc {
      color: #6b7280;
      margin: 0 0 16px 0;
      font-size: 14px;
    }

    .amount-section {
      .original-amount {
        font-size: 14px;
        color: #9ca3af;
        margin-bottom: 4px;

        .original-price {
          text-decoration: line-through;
        }
      }

      .final-amount {
        font-size: 16px;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 4px;

        .final-price {
          color: #dc2626;
          font-size: 20px;
        }
      }

      .discount {
        font-size: 14px;
        color: #059669;

        .discount-amount {
          font-weight: 600;
        }
      }
    }
  }

  .payment-methods {
    margin-bottom: 20px;

    h4 {
      font-size: 16px;
      font-weight: 600;
      color: #1f2937;
      margin: 0 0 16px 0;
    }

    .method-list {
      .method-item {
        display: flex;
        align-items: center;
        padding: 16px;
        border: 2px solid #e5e7eb;
        border-radius: 8px;
        margin-bottom: 12px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          border-color: #3b82f6;
          background-color: #f8fafc;
        }

        &.active {
          border-color: #3b82f6;
          background-color: #eff6ff;
        }

        .method-icon {
          margin-right: 12px;
          color: #6b7280;
        }

        .method-info {
          flex: 1;

          .method-name {
            font-size: 16px;
            font-weight: 500;
            color: #1f2937;
            margin-bottom: 4px;
          }

          .method-desc {
            font-size: 14px;
            color: #6b7280;
          }
        }

        .method-radio {
          :deep(.el-radio) {
            .el-radio__input {
              display: none;
            }
            .el-radio__label {
              display: none;
            }
          }
        }
      }
    }
  }

  .balance-info {
    padding: 16px;
    background-color: #f9fafb;
    border-radius: 8px;
    margin-bottom: 20px;

    .balance-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      .balance-amount {
        font-size: 18px;
        font-weight: 600;
        color: #059669;
      }
    }

    .balance-insufficient {
      margin-top: 12px;
    }
  }

  .payment-agreement {
    padding: 16px 0;
    border-top: 1px solid #e5e7eb;

    :deep(.el-checkbox) {
      .el-checkbox__label {
        font-size: 14px;
        color: #6b7280;
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;

    .modern-button {
      min-width: 100px;
    }
  }
}

.payment-result {
  text-align: center;
  padding: 20px 0;

  .result-icon {
    margin-bottom: 16px;
  }

  .result-message {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 16px;
  }

  .result-details {
    text-align: left;
    background-color: #f9fafb;
    padding: 16px;
    border-radius: 8px;
    margin-top: 16px;

    p {
      margin: 8px 0;
      font-size: 14px;
      color: #6b7280;

      &:first-child {
        margin-top: 0;
      }

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>