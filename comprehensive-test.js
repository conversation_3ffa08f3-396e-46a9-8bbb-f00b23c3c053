const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

async function comprehensiveTest() {
  console.log('🚀 EVAdmin Pro AI助手 - 综合功能测试\n');
  console.log('=' .repeat(60));
  
  const baseURL = 'http://localhost:3001/api/ai-enhanced';
  let passedTests = 0;
  let totalTests = 0;
  
  // 测试1: AI服务状态检查
  console.log('\n1️⃣ 测试AI服务状态...');
  totalTests++;
  try {
    const statusResponse = await axios.get(`${baseURL}/status`);
    if (statusResponse.data.success && statusResponse.data.status === 'online') {
      console.log('✅ AI服务状态正常');
      console.log(`   模式: ${statusResponse.data.mode || 'standard'}`);
      console.log(`   功能: ${statusResponse.data.capabilities.length} 项`);
      passedTests++;
    } else {
      console.log('❌ AI服务状态异常');
    }
  } catch (error) {
    console.log('❌ AI服务状态检查失败:', error.message);
  }
  
  // 测试2: AI助手信息获取
  console.log('\n2️⃣ 测试AI助手信息获取...');
  totalTests++;
  try {
    const infoResponse = await axios.get(`${baseURL}/info`);
    if (infoResponse.data.success && infoResponse.data.data.name) {
      console.log('✅ AI助手信息获取成功');
      console.log(`   名称: ${infoResponse.data.data.name}`);
      console.log(`   描述: ${infoResponse.data.data.description.substring(0, 50)}...`);
      passedTests++;
    } else {
      console.log('❌ AI助手信息获取失败');
    }
  } catch (error) {
    console.log('❌ AI助手信息获取失败:', error.message);
  }
  
  // 测试3: 知识库问答功能
  console.log('\n3️⃣ 测试知识库问答功能...');
  const knowledgeQuestions = [
    '电池健康度如何计算？',
    '如何查找充电站？',
    'ADAS功能如何开启？'
  ];
  
  for (const question of knowledgeQuestions) {
    totalTests++;
    try {
      const chatResponse = await axios.post(`${baseURL}/chat`, {
        message: question
      });
      
      if (chatResponse.data.success) {
        const hasKnowledge = chatResponse.data.response.includes('【') && chatResponse.data.response.includes('】');
        if (hasKnowledge) {
          console.log(`✅ 知识库问答: ${question}`);
          passedTests++;
        } else {
          console.log(`⚠️ 通用回答: ${question}`);
        }
      } else {
        console.log(`❌ 问答失败: ${question}`);
      }
    } catch (error) {
      console.log(`❌ 问答请求失败: ${question} - ${error.message}`);
    }
  }
  
  // 测试4: 通用问答功能
  console.log('\n4️⃣ 测试通用问答功能...');
  totalTests++;
  try {
    const generalResponse = await axios.post(`${baseURL}/chat`, {
      message: '你好，请介绍一下系统功能'
    });
    
    if (generalResponse.data.success && generalResponse.data.response.length > 50) {
      console.log('✅ 通用问答功能正常');
      passedTests++;
    } else {
      console.log('❌ 通用问答功能异常');
    }
  } catch (error) {
    console.log('❌ 通用问答测试失败:', error.message);
  }
  
  // 测试5: 文件上传和分析
  console.log('\n5️⃣ 测试文件上传和分析...');
  totalTests++;
  
  if (fs.existsSync('test-document.txt')) {
    try {
      const formData = new FormData();
      formData.append('file', fs.createReadStream('test-document.txt'));
      formData.append('question', '请分析这个文档的主要内容');
      
      const fileResponse = await axios.post(`${baseURL}/analyze-file`, formData, {
        headers: {
          ...formData.getHeaders()
        },
        timeout: 15000
      });
      
      if (fileResponse.data.success && fileResponse.data.response.length > 100) {
        console.log('✅ 文件上传和分析功能正常');
        console.log(`   文件: ${fileResponse.data.fileName}`);
        console.log(`   类型: ${fileResponse.data.fileType}`);
        passedTests++;
      } else {
        console.log('❌ 文件分析功能异常');
      }
    } catch (error) {
      console.log('❌ 文件上传测试失败:', error.message);
    }
  } else {
    console.log('⚠️ 测试文档不存在，跳过文件上传测试');
  }
  
  // 测试6: 对话上下文功能
  console.log('\n6️⃣ 测试对话上下文功能...');
  totalTests++;
  try {
    const contextResponse = await axios.post(`${baseURL}/chat`, {
      message: '刚才提到的电池健康度具体包含哪些指标？',
      context: [
        { type: 'user', content: '电池健康度如何计算？' },
        { type: 'ai', content: '电池健康度通过多个参数综合计算...' }
      ]
    });
    
    if (contextResponse.data.success && contextResponse.data.response.length > 50) {
      console.log('✅ 对话上下文功能正常');
      passedTests++;
    } else {
      console.log('❌ 对话上下文功能异常');
    }
  } catch (error) {
    console.log('❌ 对话上下文测试失败:', error.message);
  }
  
  // 测试结果汇总
  console.log('\n' + '=' .repeat(60));
  console.log('📊 测试结果汇总');
  console.log('=' .repeat(60));
  console.log(`✅ 通过测试: ${passedTests}/${totalTests}`);
  console.log(`📈 成功率: ${Math.round((passedTests / totalTests) * 100)}%`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 所有测试通过！AI助手功能完全正常。');
  } else if (passedTests >= totalTests * 0.8) {
    console.log('\n✅ 大部分测试通过，AI助手基本功能正常。');
  } else {
    console.log('\n⚠️ 部分测试失败，需要进一步检查。');
  }
  
  console.log('\n🌟 功能特性:');
  console.log('- ✅ 智能对话和问答');
  console.log('- ✅ 系统知识库集成');
  console.log('- ✅ 文件上传和分析');
  console.log('- ✅ 对话上下文记忆');
  console.log('- ✅ 多文件格式支持');
  console.log('- ✅ 本地模式降级处理');
  console.log('- ✅ 实时状态监控');
  
  console.log('\n📱 访问地址:');
  console.log('- 前端界面: http://localhost:5175/ai-assistant');
  console.log('- API文档: http://localhost:3001/api/ai-enhanced/status');
  
  console.log('\n🔧 下一步建议:');
  console.log('1. 配置OpenAI API密钥获得更智能的回答');
  console.log('2. 扩展知识库内容提升问答质量');
  console.log('3. 添加更多文件格式支持');
  console.log('4. 实现图片和视频分析功能');
  console.log('5. 添加语音交互功能');
}

comprehensiveTest();
