// 共享类型定义

export interface User {
  id: string
  email: string
  name: string
  phone?: string
  role: 'admin' | 'user' | 'fleet_manager'
  created_at: string
  updated_at: string
}

export interface Vehicle {
  id: string
  user_id: string
  make: string
  model: string
  year: number
  vin: string
  license_plate: string
  battery_capacity: number
  range: number
  status: 'active' | 'inactive' | 'maintenance'
  created_at: string
  updated_at: string
}

export interface Battery {
  id: string
  vehicle_id: string
  capacity: number
  current_charge: number
  health: number
  temperature: number
  voltage: number
  cycles: number
  status: 'normal' | 'warning' | 'critical'
  last_updated: string
  created_at: string
}

export interface BatteryTrace {
  id: string
  battery_id: string
  timestamp: string
  charge_level: number
  voltage: number
  current: number
  temperature: number
  location: string
  event_type: 'charge' | 'discharge' | 'maintenance' | 'diagnostic'
  created_at: string
}

export interface ChargingStation {
  id: string
  name: string
  location: string
  latitude: number
  longitude: number
  power_output: number
  connector_type: string
  status: 'available' | 'occupied' | 'maintenance' | 'offline'
  price_per_kwh: number
  created_at: string
  updated_at: string
}

export interface ChargingSession {
  id: string
  vehicle_id: string
  station_id: string
  user_id: string
  start_time: string
  end_time?: string
  energy_consumed: number
  cost: number
  status: 'active' | 'completed' | 'cancelled'
  created_at: string
}

export interface FleetManagement {
  id: string
  fleet_name: string
  manager_id: string
  vehicle_count: number
  total_mileage: number
  avg_efficiency: number
  maintenance_cost: number
  fuel_savings: number
  carbon_reduction: number
  status: 'active' | 'inactive'
  created_at: string
  updated_at: string
}

export interface DrivingBehavior {
  id: string
  vehicle_id: string
  user_id: string
  trip_date: string
  distance: number
  duration: number
  avg_speed: number
  max_speed: number
  harsh_acceleration: number
  harsh_braking: number
  rapid_turns: number
  efficiency_score: number
  safety_score: number
  eco_score: number
  created_at: string
}

export interface CarbonCredit {
  id: string
  user_id: string
  vehicle_id?: string
  amount: number
  transaction_type: 'earned' | 'spent' | 'transferred'
  source: 'driving' | 'charging' | 'referral' | 'bonus'
  description: string
  created_at: string
  expires_at?: string
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

export interface PaginatedResponse<T = any> {
  success: boolean
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
  message?: string
}