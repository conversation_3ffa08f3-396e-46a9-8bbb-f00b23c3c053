import { Router } from 'express'
import { pool } from '../config/mysql'
import type { User, ApiResponse, PaginatedResponse } from '../../shared/types'

const router = Router()

// 获取用户列表
router.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 10, user_type, status } = req.query
    const offset = (Number(page) - 1) * Number(limit)
    
    // 构建查询条件
    const conditions = []
    const params = []
    
    if (user_type) {
      conditions.push('user_type = ?')
      params.push(user_type)
    }
    
    if (status) {
      conditions.push('status = ?')
      params.push(status)
    }
    
    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : ''
    
    // 获取总数
    const [countResult] = await pool.execute(
      `SELECT COUNT(*) as total FROM users ${whereClause}`,
      params
    )
    const count = (countResult as any[])[0].total
    
    // 获取数据
    const [dataResult] = await pool.execute(
      `SELECT * FROM users ${whereClause} ORDER BY created_at DESC LIMIT ? OFFSET ?`,
      [...params, Number(limit), Number(offset)]
    )
    const data = dataResult as any[]
    
    const response: PaginatedResponse<User> = {
      success: true,
      data,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: count,
        totalPages: Math.ceil(count / Number(limit))
      }
    }
    
    res.json(response)
  } catch (error) {
    console.error('获取用户列表异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取单个用户信息
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params
    
    const [result] = await pool.execute(
      'SELECT * FROM users WHERE id = ?',
      [id]
    )
    const data = (result as any[])[0]
    
    if (!data) {
      return res.status(404).json({
        success: false,
        error: '用户不存在'
      } as ApiResponse)
    }
    
    res.json({
      success: true,
      data
    } as ApiResponse<User>)
  } catch (error) {
    console.error('获取用户信息异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 创建用户
router.post('/', async (req, res) => {
  try {
    const { email, phone, name, avatar_url, user_type = 'individual' } = req.body
    
    // 验证必填字段
    if (!email || !name) {
      return res.status(400).json({
        success: false,
        error: '邮箱和姓名为必填字段'
      } as ApiResponse)
    }
    
    // 检查邮箱是否已存在
    const [existingResult] = await pool.execute(
      'SELECT id FROM users WHERE email = ?',
      [email]
    )
    const existingUser = (existingResult as any[])[0]
    
    if (existingUser) {
      return res.status(400).json({
        success: false,
        error: '邮箱已存在'
      } as ApiResponse)
    }
    
    const userData = {
      email,
      phone,
      name,
      avatar_url,
      user_type,
      status: 'active'
    }
    
    const [insertResult] = await pool.execute(
      'INSERT INTO users (email, phone, name, avatar_url, user_type, status) VALUES (?, ?, ?, ?, ?, ?)',
      [userData.email, userData.phone, userData.name, userData.avatar_url, userData.user_type, userData.status]
    )
    
    const insertId = (insertResult as any).insertId
    
    // 获取插入的记录
    const [newUserResult] = await pool.execute(
      'SELECT * FROM users WHERE id = ?',
      [insertId]
    )
    const data = (newUserResult as any[])[0]
    
    res.status(201).json({
      success: true,
      data,
      message: '用户创建成功'
    } as ApiResponse<User>)
  } catch (error) {
    console.error('创建用户异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 更新用户信息
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params
    const { email, phone, name, avatar_url, user_type, status } = req.body
    
    // 构建更新字段和参数
    const updateFields = []
    const params = []
    
    if (email !== undefined) {
      updateFields.push('email = ?')
      params.push(email)
    }
    if (phone !== undefined) {
      updateFields.push('phone = ?')
      params.push(phone)
    }
    if (name !== undefined) {
      updateFields.push('name = ?')
      params.push(name)
    }
    if (avatar_url !== undefined) {
      updateFields.push('avatar_url = ?')
      params.push(avatar_url)
    }
    if (user_type !== undefined) {
      updateFields.push('user_type = ?')
      params.push(user_type)
    }
    if (status !== undefined) {
      updateFields.push('status = ?')
      params.push(status)
    }
    
    updateFields.push('updated_at = NOW()')
    params.push(id)
    
    if (updateFields.length === 1) {
      return res.status(400).json({
        success: false,
        error: '没有提供要更新的字段'
      } as ApiResponse)
    }
    
    await pool.execute(
      `UPDATE users SET ${updateFields.join(', ')} WHERE id = ?`,
      params
    )
    
    // 获取更新后的记录
    const [updatedResult] = await pool.execute(
      'SELECT * FROM users WHERE id = ?',
      [id]
    )
    const data = (updatedResult as any[])[0]
    
    res.json({
      success: true,
      data,
      message: '用户信息更新成功'
    } as ApiResponse<User>)
  } catch (error) {
    console.error('更新用户异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 删除用户
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params
    
    // 检查用户是否存在
    const [checkResult] = await pool.execute(
      'SELECT id FROM users WHERE id = ?',
      [id]
    )
    
    if ((checkResult as any[]).length === 0) {
      return res.status(404).json({
        success: false,
        error: '用户不存在'
      } as ApiResponse)
    }
    
    await pool.execute(
      'DELETE FROM users WHERE id = ?',
      [id]
    )
    
    res.json({
      success: true,
      message: '用户删除成功'
    } as ApiResponse)
  } catch (error) {
    console.error('删除用户异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取用户的车辆列表
router.get('/:id/vehicles', async (req, res) => {
  try {
    const { id } = req.params
    
    const [result] = await pool.execute(
      'SELECT * FROM vehicles WHERE user_id = ? ORDER BY created_at DESC',
      [id]
    )
    const data = result as any[]
    
    res.json({
      success: true,
      data
    } as ApiResponse)
  } catch (error) {
    console.error('获取用户车辆列表异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取用户详细档案
router.get('/profile/detailed', async (req, res) => {
  try {
    const { user_id = 1 } = req.query // 默认用户ID为1
    
    // 获取用户基本信息
    const [userResult] = await pool.execute(
      'SELECT * FROM users WHERE id = ?',
      [user_id]
    )
    const user = (userResult as any[])[0]
    
    if (!user) {
      return res.status(404).json({
        success: false,
        error: '用户不存在'
      } as ApiResponse)
    }
    
    // 获取用户统计信息
    const [statsResult] = await pool.execute(
      `SELECT 
        COUNT(DISTINCT v.id) as vehicle_count,
        COALESCE(SUM(cs.energy_consumed), 0) as total_energy,
        COALESCE(SUM(cs.distance), 0) as total_distance,
        COALESCE(AVG(cs.efficiency), 0) as avg_efficiency
       FROM users u
       LEFT JOIN vehicles v ON u.id = v.user_id
       LEFT JOIN charging_sessions cs ON v.id = cs.vehicle_id
       WHERE u.id = ?`,
      [user_id]
    )
    const stats = (statsResult as any[])[0]
    
    // 获取碳积分信息
    const [carbonResult] = await pool.execute(
      'SELECT COALESCE(SUM(amount), 0) as total_credits FROM carbon_credits WHERE user_id = ? AND type = "earned"',
      [user_id]
    )
    const carbonCredits = (carbonResult as any[])[0].total_credits
    
    const data = {
      id: user.id,
      name: user.name,
      email: user.email,
      phone: user.phone,
      avatar: user.avatar_url || '/default-avatar.png',
      level: Math.floor(carbonCredits / 1000) + 1, // 每1000积分升一级
      carbonCredits,
      totalMileage: Math.round(stats.total_distance || 0),
      ecoScore: Math.min(100, Math.round((stats.avg_efficiency || 0) * 10)), // 转换为0-100分
      memberDays: Math.floor((Date.now() - new Date(user.created_at).getTime()) / (1000 * 60 * 60 * 24)),
      vehicleCount: stats.vehicle_count || 0,
      totalEnergy: Math.round(stats.total_energy || 0),
      joinDate: user.created_at
    }
    
    res.json({
      success: true,
      data
    } as ApiResponse)
  } catch (error) {
    console.error('获取用户详细档案异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取用户驾驶档案
router.get('/driving-profile', async (req, res) => {
  try {
    const { user_id = 1 } = req.query
    
    // 获取驾驶行为数据
    const [behaviorResult] = await pool.execute(
      `SELECT 
        AVG(speed_score) as avg_speed_score,
        AVG(acceleration_score) as avg_acceleration_score,
        AVG(braking_score) as avg_braking_score,
        AVG(cornering_score) as avg_cornering_score,
        COUNT(*) as total_trips
       FROM driving_behavior db
       JOIN vehicles v ON db.vehicle_id = v.id
       WHERE v.user_id = ? AND db.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)`,
      [user_id]
    )
    const behavior = (behaviorResult as any[])[0]
    
    // 获取最近充电会话数据
    const [sessionResult] = await pool.execute(
      `SELECT 
        AVG(efficiency) as avg_efficiency,
        SUM(distance) as total_distance,
        COUNT(*) as session_count
       FROM charging_sessions cs
       JOIN vehicles v ON cs.vehicle_id = v.id
       WHERE v.user_id = ? AND cs.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)`,
      [user_id]
    )
    const sessions = (sessionResult as any[])[0]
    
    const data = {
      averageSpeed: Math.round((behavior.avg_speed_score || 0) * 10), // 转换为0-100分
      fuelEfficiency: Math.round((sessions.avg_efficiency || 0) * 100), // 转换为百分比
      safetyScore: Math.round((
        (behavior.avg_speed_score || 0) +
        (behavior.avg_acceleration_score || 0) +
        (behavior.avg_braking_score || 0) +
        (behavior.avg_cornering_score || 0)
      ) / 4 * 100),
      totalTrips: behavior.total_trips || 0,
      totalDistance: Math.round(sessions.total_distance || 0),
      drivingStyle: behavior.avg_speed_score > 0.8 ? '稳健型' : behavior.avg_speed_score > 0.6 ? '平衡型' : '激进型'
    }
    
    res.json({
      success: true,
      data
    } as ApiResponse)
  } catch (error) {
    console.error('获取用户驾驶档案异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取用户环保档案
router.get('/eco-profile', async (req, res) => {
  try {
    const { user_id = 1 } = req.query
    
    // 获取碳积分统计
    const [carbonResult] = await pool.execute(
      `SELECT 
        COALESCE(SUM(CASE WHEN type = 'earned' THEN amount ELSE 0 END), 0) as total_earned,
        COALESCE(SUM(CASE WHEN type = 'used' THEN amount ELSE 0 END), 0) as total_used
       FROM carbon_credits WHERE user_id = ?`,
      [user_id]
    )
    const carbon = (carbonResult as any[])[0]
    
    // 获取充电统计
    const [chargingResult] = await pool.execute(
      `SELECT 
        COUNT(*) as charging_count,
        COALESCE(SUM(energy_consumed), 0) as total_energy,
        COALESCE(AVG(efficiency), 0) as avg_efficiency
       FROM charging_sessions cs
       JOIN vehicles v ON cs.vehicle_id = v.id
       WHERE v.user_id = ?`,
      [user_id]
    )
    const charging = (chargingResult as any[])[0]
    
    const data = {
      carbonSaved: Math.round((carbon.total_earned || 0) * 0.1), // 假设每积分代表0.1kg碳减排
      energyEfficiency: Math.round((charging.avg_efficiency || 0) * 100),
      greenMiles: Math.round((charging.total_energy || 0) * 5), // 假设每kWh可行驶5公里
      ecoRanking: Math.min(100, Math.round((carbon.total_earned || 0) / 100)), // 基于积分的排名
      renewableEnergy: Math.round(Math.random() * 30 + 70), // 模拟可再生能源使用比例
      co2Reduction: Math.round((carbon.total_earned || 0) * 0.05) // 假设每积分减少0.05kg CO2
    }
    
    res.json({
      success: true,
      data
    } as ApiResponse)
  } catch (error) {
    console.error('获取用户环保档案异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取用户偏好设置
router.get('/preferences', async (req, res) => {
  try {
    const { user_id = 1 } = req.query
    
    // 这里可以从数据库获取用户偏好设置，暂时返回默认值
    const data = {
      notifications: {
        email: true,
        sms: false,
        push: true
      },
      privacy: {
        shareData: false,
        publicProfile: true,
        trackLocation: true
      },
      display: {
        theme: 'light',
        language: 'zh-CN',
        units: 'metric'
      },
      charging: {
        autoSchedule: true,
        preferredTime: '22:00',
        maxCharge: 80
      }
    }
    
    res.json({
      success: true,
      data
    } as ApiResponse)
  } catch (error) {
    console.error('获取用户偏好设置异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取用户成就
router.get('/achievements', async (req, res) => {
  try {
    const { user_id = 1 } = req.query
    
    // 获取用户统计数据来计算成就
    const [statsResult] = await pool.execute(
      `SELECT 
        COALESCE(SUM(CASE WHEN cc.type = 'earned' THEN cc.amount ELSE 0 END), 0) as total_credits,
        COUNT(DISTINCT v.id) as vehicle_count,
        COALESCE(SUM(cs.distance), 0) as total_distance,
        COUNT(DISTINCT cs.id) as charging_count
       FROM users u
       LEFT JOIN carbon_credits cc ON u.id = cc.user_id
       LEFT JOIN vehicles v ON u.id = v.user_id
       LEFT JOIN charging_sessions cs ON v.id = cs.vehicle_id
       WHERE u.id = ?`,
      [user_id]
    )
    const stats = (statsResult as any[])[0]
    
    const achievements = [
      {
        id: 'first_vehicle',
        title: '首辆座驾',
        description: '添加第一辆车辆',
        icon: '🚗',
        progress: Math.min(100, (stats.vehicle_count || 0) * 100),
        completed: (stats.vehicle_count || 0) >= 1,
        reward: 100
      },
      {
        id: 'eco_warrior',
        title: '环保战士',
        description: '累计获得1000碳积分',
        icon: '🌱',
        progress: Math.min(100, (stats.total_credits || 0) / 10),
        completed: (stats.total_credits || 0) >= 1000,
        reward: 500
      },
      {
        id: 'long_distance',
        title: '长途旅行者',
        description: '累计行驶10000公里',
        icon: '🛣️',
        progress: Math.min(100, (stats.total_distance || 0) / 100),
        completed: (stats.total_distance || 0) >= 10000,
        reward: 300
      },
      {
        id: 'charging_master',
        title: '充电大师',
        description: '完成100次充电',
        icon: '⚡',
        progress: Math.min(100, (stats.charging_count || 0)),
        completed: (stats.charging_count || 0) >= 100,
        reward: 200
      },
      {
        id: 'fleet_manager',
        title: '车队管理员',
        description: '拥有5辆车辆',
        icon: '🚛',
        progress: Math.min(100, (stats.vehicle_count || 0) * 20),
        completed: (stats.vehicle_count || 0) >= 5,
        reward: 1000
      }
    ]
    
    res.json({
      success: true,
      data: achievements
    } as ApiResponse)
  } catch (error) {
    console.error('获取用户成就异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

export default router