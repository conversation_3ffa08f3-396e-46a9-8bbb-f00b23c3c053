const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'ev_management',
  charset: 'utf8mb4'
};

async function importData() {
  let connection;
  
  try {
    console.log('🔌 连接到MySQL数据库...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 读取SQL文件
    const sqlFilePath = path.join(__dirname, 'simple_data.sql');
    const sqlContent = await fs.readFile(sqlFilePath, 'utf8');
    
    // 分割SQL语句（简单分割，按分号分割）
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--') && !stmt.startsWith('USE'));
    
    console.log(`📝 准备执行 ${statements.length} 条SQL语句`);
    
    // 执行每条SQL语句
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      
      try {
        if (statement.includes('INSERT INTO')) {
          await connection.execute(statement);
          const tableName = statement.match(/INSERT INTO (\w+)/)[1];
          console.log(`✅ 成功插入数据到表: ${tableName}`);
        } else if (statement.includes('SELECT')) {
          const [rows] = await connection.execute(statement);
          if (rows.length > 0) {
            console.log(`📊 ${rows[0].message || '查询完成'}`);
          }
        }
      } catch (error) {
        console.log(`⚠️  语句执行警告: ${error.message}`);
      }
    }
    
    // 验证数据导入结果
    console.log('\n📊 数据导入统计:');
    console.log('==================');
    
    const tables = [
      'users', 'vehicles', 'batteries', 'charging_stations', 
      'charging_sessions', 'carbon_credit', 'driving_behavior', 
      'fleet_management', 'edge_devices', 'edge_device_data', 'edge_tasks'
    ];
    
    for (const table of tables) {
      try {
        const [rows] = await connection.execute(`SELECT COUNT(*) as count FROM ${table}`);
        const count = rows[0].count;
        console.log(`${table}: ${count} 条记录`);
      } catch (error) {
        console.log(`${table}: 查询失败 - ${error.message}`);
      }
    }
    
    console.log('\n🎉 简化示例数据导入完成！');
    console.log('💡 现在可以访问各个模块查看真实数据了');
    
  } catch (error) {
    console.error('❌ 数据导入失败:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 运行导入脚本
importData();