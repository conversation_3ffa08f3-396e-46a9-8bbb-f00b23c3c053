<template>
  <div class="charging-maintenance">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <el-icon class="title-icon"><Tools /></el-icon>
            设备维护
          </h1>
          <p class="page-description">充电设备维护管理、故障处理</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="addMaintenance">
            <el-icon><Plus /></el-icon>
            新增维护任务
          </el-button>
        </div>
      </div>
    </div>

    <!-- 维护概览 -->
    <div class="maintenance-overview">
      <div class="overview-cards">
        <div class="overview-card">
          <div class="card-icon pending">
            <el-icon><Clock /></el-icon>
          </div>
          <div class="card-content">
            <h3>待维护设备</h3>
            <div class="card-value">{{ pendingMaintenance }}</div>
            <div class="card-change warning">需要关注</div>
          </div>
        </div>

        <div class="overview-card">
          <div class="card-icon progress">
            <el-icon><Tools /></el-icon>
          </div>
          <div class="card-content">
            <h3>维护中</h3>
            <div class="card-value">{{ inProgressMaintenance }}</div>
            <div class="card-change">{{ totalMaintenanceToday }} 今日任务</div>
          </div>
        </div>

        <div class="overview-card">
          <div class="card-icon completed">
            <el-icon><CircleCheck /></el-icon>
          </div>
          <div class="card-content">
            <h3>本月完成</h3>
            <div class="card-value">{{ completedMaintenance }}</div>
            <div class="card-change positive">+15% 效率提升</div>
          </div>
        </div>

        <div class="overview-card">
          <div class="card-icon health">
            <el-icon><Monitor /></el-icon>
          </div>
          <div class="card-content">
            <h3>设备健康度</h3>
            <div class="card-value">{{ deviceHealth }}%</div>
            <div class="card-change positive">良好状态</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 维护任务表格 -->
    <div class="maintenance-table-section">
      <div class="section-header">
        <h2>维护任务</h2>
        <div class="section-actions">
          <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 120px">
            <el-option label="全部状态" value="" />
            <el-option label="待处理" value="pending" />
            <el-option label="进行中" value="in_progress" />
            <el-option label="已完成" value="completed" />
          </el-select>
        </div>
      </div>

      <el-table :data="maintenanceTasks" style="width: 100%">
        <el-table-column prop="taskId" label="任务ID" width="120" />
        <el-table-column prop="stationName" label="充电站" width="150" />
        <el-table-column prop="deviceId" label="设备编号" width="120" />
        <el-table-column prop="type" label="维护类型" width="120">
          <template #default="scope">
            <el-tag :type="getMaintenanceTypeColor(scope.row.type)" size="small">
              {{ getMaintenanceTypeText(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="priority" label="优先级" width="100">
          <template #default="scope">
            <el-tag :type="getPriorityColor(scope.row.priority)" size="small">
              {{ getPriorityText(scope.row.priority) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusColor(scope.row.status)" size="small">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="assignee" label="负责人" width="100" />
        <el-table-column prop="scheduledDate" label="计划日期" width="120" />
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button size="small" @click="viewTask(scope.row)">查看</el-button>
            <el-button size="small" type="primary" @click="editTask(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="deleteTask(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Tools,
  Plus,
  Clock,
  CircleCheck,
  Monitor,
  Search
} from '@element-plus/icons-vue'

// 响应式数据
const searchQuery = ref('')
const statusFilter = ref('')
const pendingMaintenance = ref(5)
const inProgressMaintenance = ref(2)
const totalMaintenanceToday = ref(3)
const completedMaintenance = ref(28)
const deviceHealth = ref(95)

// 维护任务数据
const maintenanceTasks = ref([
  {
    taskId: 'MT001',
    stationName: '中央广场站',
    deviceId: 'CP001',
    type: 'routine',
    priority: 'medium',
    status: 'pending',
    assignee: '张工',
    scheduledDate: '2024-01-20',
    description: '定期检查充电桩连接器'
  },
  {
    taskId: 'MT002',
    stationName: '商业中心站',
    deviceId: 'CP005',
    type: 'repair',
    priority: 'high',
    status: 'in_progress',
    assignee: '李工',
    scheduledDate: '2024-01-18',
    description: '修复充电桩显示屏故障'
  },
  {
    taskId: 'MT003',
    stationName: '住宅区站',
    deviceId: 'CP012',
    type: 'upgrade',
    priority: 'low',
    status: 'completed',
    assignee: '王工',
    scheduledDate: '2024-01-15',
    description: '升级充电桩固件版本'
  }
])

// 方法
const getMaintenanceTypeColor = (type: string) => {
  const colors = {
    routine: '',
    repair: 'warning',
    upgrade: 'primary',
    emergency: 'danger'
  }
  return colors[type] || ''
}

const getMaintenanceTypeText = (type: string) => {
  const texts = {
    routine: '例行检查',
    repair: '故障维修',
    upgrade: '设备升级',
    emergency: '紧急维护'
  }
  return texts[type] || type
}

const getPriorityColor = (priority: string) => {
  const colors = {
    low: 'info',
    medium: '',
    high: 'warning',
    urgent: 'danger'
  }
  return colors[priority] || ''
}

const getPriorityText = (priority: string) => {
  const texts = {
    low: '低',
    medium: '中',
    high: '高',
    urgent: '紧急'
  }
  return texts[priority] || priority
}

const getStatusColor = (status: string) => {
  const colors = {
    pending: 'warning',
    in_progress: 'primary',
    completed: 'success',
    cancelled: 'info'
  }
  return colors[status] || ''
}

const getStatusText = (status: string) => {
  const texts = {
    pending: '待处理',
    in_progress: '进行中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return texts[status] || status
}

const addMaintenance = () => {
  ElMessage.info('新增维护任务功能开发中...')
}

const viewTask = (task: any) => {
  ElMessage.info(`查看维护任务: ${task.taskId}`)
}

const editTask = (task: any) => {
  ElMessage.info(`编辑维护任务: ${task.taskId}`)
}

const deleteTask = (task: any) => {
  ElMessage.warning(`删除维护任务: ${task.taskId}`)
}
</script>

<style scoped>
.charging-maintenance {
  padding: 24px;
  background: #f8fafc;
  min-height: 100vh;
}

/* 页面头部样式 */
.page-header {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 32px;
  color: white;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 32px;
}

.page-description {
  font-size: 16px;
  opacity: 0.9;
  margin: 0;
}

/* 维护概览样式 */
.maintenance-overview {
  margin-bottom: 32px;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
}

.overview-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.card-icon.pending {
  background: linear-gradient(135deg, #ffecd2, #fcb69f);
}

.card-icon.progress {
  background: linear-gradient(135deg, #a8edea, #fed6e3);
}

.card-icon.completed {
  background: linear-gradient(135deg, #d299c2, #fef9d7);
}

.card-icon.health {
  background: linear-gradient(135deg, #89f7fe, #66a6ff);
}

.card-content h3 {
  font-size: 14px;
  color: #718096;
  margin: 0 0 8px 0;
  font-weight: 500;
}

.card-value {
  font-size: 24px;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 4px 0;
}

.card-change {
  font-size: 12px;
  color: #718096;
}

.card-change.positive {
  color: #38a169;
}

.card-change.warning {
  color: #d69e2e;
}

/* 表格区域样式 */
.maintenance-table-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.section-header h2 {
  font-size: 20px;
  font-weight: 600;
  color: #1a202c;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .overview-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .overview-cards {
    grid-template-columns: 1fr;
  }
  
  .header-content {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }
}
</style>
