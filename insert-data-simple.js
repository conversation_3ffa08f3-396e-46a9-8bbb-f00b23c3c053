const mysql = require('mysql2/promise');

async function insertTestData() {
  let connection;
  
  try {
    console.log('🔌 连接到MySQL数据库...');
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '123456',
      database: 'ev_management'
    });
    console.log('✅ 数据库连接成功');
    
    // 插入用户数据
    console.log('👥 插入用户数据...');
    const users = [
      ['user_001', '张三', 'zhang<PERSON>@example.com', '$2b$10$rOzJqKqQQxQQxQQxQQxQQeJ7J7J7J7J7J7J7J7J7J7J7J7J7J7J7J7', '13800138001', 'user', 'active', 'https://example.com/avatar1.jpg'],
      ['user_002', '李四', '<EMAIL>', '$2b$10$rOzJqKqQQxQQxQQxQQxQQeJ7J7J7J7J7J7J7J7J7J7J7J7J7J7J7J7', '13800138002', 'user', 'active', 'https://example.com/avatar2.jpg'],
      ['user_003', '王五', '<EMAIL>', '$2b$10$rOzJqKqQQxQQxQQxQQxQQeJ7J7J7J7J7J7J7J7J7J7J7J7J7J7J7J7', '13800138003', 'user', 'active', 'https://example.com/avatar3.jpg'],
      ['user_004', '赵六', '<EMAIL>', '$2b$10$rOzJqKqQQxQQxQQxQQxQQeJ7J7J7J7J7J7J7J7J7J7J7J7J7J7J7J7', '13800138004', 'operator', 'active', 'https://example.com/avatar4.jpg'],
      ['user_005', '管理员', '<EMAIL>', '$2b$10$rOzJqKqQQxQQxQQxQQxQQeJ7J7J7J7J7J7J7J7J7J7J7J7J7J7J7J7', '13800138000', 'admin', 'active', 'https://example.com/avatar5.jpg']
    ];
    
    for (const user of users) {
      try {
        await connection.execute(
          'INSERT IGNORE INTO users (user_id, username, email, password_hash, phone, role, status, avatar_url) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
          user
        );
        console.log(`✅ 用户 ${user[1]} 插入成功`);
      } catch (err) {
        console.log(`⚠️ 用户 ${user[1]} 插入失败: ${err.message}`);
      }
    }
    
    // 插入车辆数据
    console.log('🚗 插入车辆数据...');
    const vehicles = [
      ['vehicle_001', 'user_001', '特斯拉', 'Model 3', 2023, 'TSLA123456789001', '京A12345', '白色', 75.0, 550, 'active', JSON.stringify({"address": "北京市朝阳区", "lat": 39.9042, "lng": 116.4074})],
      ['vehicle_002', 'user_002', '比亚迪', '汉EV', 2023, 'BYD1234567890002', '京B67890', '黑色', 85.4, 605, 'active', JSON.stringify({"address": "上海市浦东新区", "lat": 31.2397, "lng": 121.4997})],
      ['vehicle_003', 'user_003', '蔚来', 'ES8', 2023, 'NIO1234567890003', '京C11111', '蓝色', 100.0, 500, 'active', JSON.stringify({"address": "深圳市南山区", "lat": 22.5431, "lng": 113.9344})]
    ];
    
    for (const vehicle of vehicles) {
      try {
        await connection.execute(
          'INSERT IGNORE INTO vehicles (vehicle_id, owner_id, make, model, year, vin, license_plate, color, battery_capacity, range_km, status, current_location) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
          vehicle
        );
        console.log(`✅ 车辆 ${vehicle[2]} ${vehicle[3]} 插入成功`);
      } catch (err) {
        console.log(`⚠️ 车辆插入失败: ${err.message}`);
      }
    }
    
    // 插入充电站数据
    console.log('⚡ 插入充电站数据...');
    const stations = [
      ['station_001', '北京国贸充电站', '国家电网', '北京市朝阳区建国门外大街1号', 39.9088, 116.4317, 8, 6, 120.0, 1.2, JSON.stringify(["餐厅", "WiFi", "休息区"]), 'active'],
      ['station_002', '上海陆家嘴充电站', '特来电', '上海市浦东新区陆家嘴环路1000号', 31.2397, 121.4997, 12, 8, 150.0, 1.1, JSON.stringify(["便利店", "咖啡厅"]), 'active'],
      ['station_003', '深圳科技园充电站', '星星充电', '深圳市南山区科技园南区', 22.5431, 113.9344, 6, 4, 60.0, 1.0, JSON.stringify(["WiFi", "洗手间"]), 'active']
    ];
    
    for (const station of stations) {
      try {
        await connection.execute(
          'INSERT IGNORE INTO charging_stations (station_id, name, operator, address, latitude, longitude, total_ports, available_ports, power_output_kw, pricing_per_kwh, amenities, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
          station
        );
        console.log(`✅ 充电站 ${station[1]} 插入成功`);
      } catch (err) {
        console.log(`⚠️ 充电站插入失败: ${err.message}`);
      }
    }
    
    // 检查插入结果
    console.log('\n📊 数据统计:');
    const tables = ['users', 'vehicles', 'charging_stations'];
    for (const table of tables) {
      const [rows] = await connection.execute(`SELECT COUNT(*) as count FROM ${table}`);
      console.log(`${table}: ${rows[0].count} 条记录`);
    }
    
  } catch (error) {
    console.error('❌ 错误:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 数据库连接已关闭');
    }
  }
}

// 运行插入脚本
insertTestData();