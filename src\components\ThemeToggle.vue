<template>
  <label class="relative inline-flex items-center cursor-pointer theme-toggle">
    <input 
      class="sr-only peer" 
      type="checkbox" 
      :checked="isDark"
      @change="toggleTheme"
    />
    <div 
      class="w-24 h-12 rounded-full ring-0 peer duration-500 outline-none bg-gray-200 overflow-hidden before:flex before:items-center before:justify-center after:flex after:items-center after:justify-center before:content-['☀️'] before:absolute before:h-10 before:w-10 before:top-1/2 before:bg-white before:rounded-full before:left-1 before:-translate-y-1/2 before:transition-all before:duration-700 peer-checked:before:opacity-0 peer-checked:before:rotate-90 peer-checked:before:-translate-y-full shadow-lg shadow-gray-400 peer-checked:shadow-lg peer-checked:shadow-gray-700 peer-checked:bg-[#383838] after:content-['🌑'] after:absolute after:bg-[#1d1d1d] after:rounded-full after:top-[4px] after:right-1 after:translate-y-full after:w-10 after:h-10 after:opacity-0 after:transition-all after:duration-700 peer-checked:after:opacity-100 peer-checked:after:rotate-180 peer-checked:after:translate-y-0" 
    ></div>
  </label>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useTheme } from '@/composables/useTheme'

// 使用主题组合式函数
const { isDark, toggleTheme } = useTheme()
</script>

<style scoped>
.theme-toggle {
  transition: all 0.3s ease;
}

.theme-toggle:hover {
  transform: scale(1.05);
}

/* 确保在不同主题下的显示效果 */
:global(.dark) .theme-toggle .peer:not(:checked) {
  background-color: #374151;
}

:global(.light) .theme-toggle .peer:checked {
  background-color: #383838;
}
</style>