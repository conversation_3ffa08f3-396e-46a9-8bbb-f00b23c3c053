# 新能源汽车智能综合管理系统 - MySQL数据库迁移指南

## 概述

本指南将帮助您将项目数据库从 Supabase 迁移到 MySQL，实现完全的本地化部署。

## 🎯 迁移优势

- **完全控制**：拥有数据库的完全控制权
- **成本优化**：避免云服务费用
- **性能提升**：本地部署减少网络延迟
- **数据安全**：数据完全在本地环境
- **离线运行**：无需互联网连接即可运行

## 📋 迁移前准备

### 1. 安装 MySQL

#### Windows 用户：
1. 下载 MySQL Installer：https://dev.mysql.com/downloads/installer/
2. 选择 "MySQL Installer for Windows"
3. 运行安装程序，选择 "Developer Default" 安装类型
4. 设置 root 用户密码（请记住此密码）
5. 完成安装

#### 验证安装：
```bash
mysql --version
```

### 2. 启动 MySQL 服务

#### Windows：
```bash
# 启动服务
net start mysql80

# 或者通过服务管理器启动 MySQL80 服务
```

### 3. 配置数据库连接

编辑项目根目录的 `.env` 文件：

```env
# MySQL Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_mysql_password  # 替换为您的MySQL密码
DB_NAME=ev_management
```

## 🚀 迁移步骤

### 步骤 1：测试数据库连接

```bash
node test-mysql.js
```

如果连接失败，请检查：
- MySQL 服务是否启动
- 用户名和密码是否正确
- 端口 3306 是否可用

### 步骤 2：创建数据库和表结构

#### 方法 A：使用 MySQL 命令行

```bash
# 登录 MySQL
mysql -u root -p

# 执行初始化脚本
source mysql_init.sql
```

#### 方法 B：使用 MySQL Workbench

1. 打开 MySQL Workbench
2. 连接到本地 MySQL 服务器
3. 打开 `mysql_init.sql` 文件
4. 执行脚本

### 步骤 3：验证表结构

```bash
node migrate-to-mysql.js
```

此脚本将：
- 测试数据库连接
- 检查所有表是否创建成功
- 插入示例数据
- 显示迁移状态

### 步骤 4：更新 API 路由

以下文件已完成 MySQL 迁移：
- ✅ `api/config/mysql.ts` - MySQL 配置文件
- ✅ `api/routes/edge-devices.ts` - 边缘设备 API
- ✅ `api/routes/batteries.ts` - 电池管理 API

需要手动更新的文件：
- ⚠️ `api/routes/users.ts` - 用户管理
- ⚠️ `api/routes/vehicles.ts` - 车辆管理
- ⚠️ `api/routes/charging-stations.ts` - 充电站管理
- ⚠️ `api/routes/charging-sessions.ts` - 充电会话
- ⚠️ `api/routes/carbon-credits.ts` - 碳积分
- ⚠️ `api/routes/driving-behavior.ts` - 驾驶行为
- ⚠️ `api/routes/fleet-management.ts` - 车队管理
- ⚠️ `api/routes/auth.ts` - 身份认证

### 步骤 5：启动服务测试

```bash
npm run dev
```

访问 http://localhost:5173 测试应用功能。

## 📊 数据库表结构说明

### 核心表

| 表名 | 说明 | 主要字段 |
|------|------|----------|
| `users` | 用户信息 | id, email, name, user_type |
| `vehicles` | 车辆信息 | id, user_id, vin, brand, model |
| `batteries` | 电池信息 | id, vehicle_id, capacity, current_soh |
| `charging_stations` | 充电站 | id, name, location, total_ports |
| `charging_sessions` | 充电会话 | id, user_id, vehicle_id, station_id |
| `edge_devices` | 边缘设备 | id, name, type, status |

### 关系说明

```
users (1) -----> (n) vehicles
vehicles (1) --> (n) batteries
vehicles (1) --> (n) charging_sessions
charging_stations (1) --> (n) charging_sessions
edge_devices (1) -----> (n) edge_device_data
```

## 🔧 常见问题解决

### 问题 1：MySQL 连接被拒绝

**错误信息：** `Access denied for user 'root'@'localhost'`

**解决方案：**
```bash
# 重置 root 密码
mysql -u root
ALTER USER 'root'@'localhost' IDENTIFIED BY 'new_password';
FLUSH PRIVILEGES;
```

### 问题 2：MySQL 服务未启动

**错误信息：** `ECONNREFUSED`

**解决方案：**
```bash
# Windows
net start mysql80

# 或通过服务管理器启动 MySQL80 服务
```

### 问题 3：端口被占用

**解决方案：**
```bash
# 检查端口占用
netstat -an | findstr 3306

# 修改 .env 中的 DB_PORT 为其他端口
```

### 问题 4：数据库不存在

**解决方案：**
```sql
CREATE DATABASE ev_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

## 📈 性能优化建议

### 1. 索引优化

```sql
-- 为常用查询字段添加索引
CREATE INDEX idx_vehicles_user_id ON vehicles(user_id);
CREATE INDEX idx_batteries_vehicle_id ON batteries(vehicle_id);
CREATE INDEX idx_charging_sessions_user_id ON charging_sessions(user_id);
```

### 2. 连接池配置

在 `api/config/mysql.ts` 中调整连接池参数：

```typescript
const pool = mysql.createPool({
  // ... 其他配置
  connectionLimit: 10,      // 最大连接数
  acquireTimeout: 60000,    // 获取连接超时时间
  timeout: 60000,           // 查询超时时间
  reconnect: true           // 自动重连
});
```

### 3. 查询优化

- 使用 `LIMIT` 限制结果集大小
- 避免 `SELECT *`，只查询需要的字段
- 使用 `JOIN` 代替多次查询
- 合理使用事务

## 🔒 安全配置

### 1. 数据库用户权限

```sql
-- 创建专用数据库用户
CREATE USER 'ev_app'@'localhost' IDENTIFIED BY 'secure_password';
GRANT SELECT, INSERT, UPDATE, DELETE ON ev_management.* TO 'ev_app'@'localhost';
FLUSH PRIVILEGES;
```

### 2. 环境变量安全

- 不要将 `.env` 文件提交到版本控制
- 生产环境使用强密码
- 定期更换数据库密码

## 📝 迁移检查清单

- [ ] MySQL 服务已安装并启动
- [ ] 数据库连接测试通过
- [ ] 数据库和表结构创建成功
- [ ] 示例数据插入成功
- [ ] API 路由已更新为 MySQL
- [ ] 前端应用正常访问
- [ ] 所有功能模块测试通过
- [ ] 性能和安全配置完成

## 🎉 迁移完成

恭喜！您已成功将项目从 Supabase 迁移到 MySQL。现在您拥有：

- 完全本地化的数据库解决方案
- 更好的性能和控制能力
- 降低的运营成本
- 增强的数据安全性

## 📞 技术支持

如果在迁移过程中遇到问题，请：

1. 检查本指南的常见问题部分
2. 查看项目日志文件
3. 验证配置文件设置
4. 测试数据库连接

---

**注意：** 在生产环境部署前，请务必进行充分的测试，并备份重要数据。