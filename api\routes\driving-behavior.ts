import { Router } from 'express'
import { db } from '../config/mysql'
import type { ApiResponse, PaginatedResponse, DrivingBehavior, Vehicle, User } from '../../shared/types'

const router = Router()

// 获取驾驶行为记录列表
router.get('/', async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      user_id, 
      vehicle_id,
      start_date,
      end_date,
      min_score,
      max_score
    } = req.query
    const offset = (Number(page) - 1) * Number(limit)
    
    // 构建查询条件
    const conditions = []
    const params = []
    
    if (user_id) {
      conditions.push('db.user_id = ?')
      params.push(user_id)
    }
    
    if (vehicle_id) {
      conditions.push('db.vehicle_id = ?')
      params.push(vehicle_id)
    }
    
    if (start_date) {
      conditions.push('db.trip_start_time >= ?')
      params.push(start_date)
    }
    
    if (end_date) {
      conditions.push('db.trip_start_time <= ?')
      params.push(end_date)
    }
    
    if (min_score) {
      conditions.push('db.driving_score >= ?')
      params.push(Number(min_score))
    }
    
    if (max_score) {
      conditions.push('db.driving_score <= ?')
      params.push(Number(max_score))
    }
    
    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : ''
    
    // 获取总数
    const [countResult] = await db.execute(
      `SELECT COUNT(*) as total FROM driving_behavior db ${whereClause}`,
      params
    )
    const total = (countResult as any[])[0].total
    
    // 获取数据
    const [result] = await db.execute(
      `SELECT 
        db.*,
        u.username as user_name,
        u.email as user_email,
        v.brand as vehicle_brand,
        v.model as vehicle_model,
        v.license_plate as vehicle_license_plate
      FROM driving_behavior db
      LEFT JOIN users u ON db.user_id = u.user_id
      LEFT JOIN vehicles v ON db.vehicle_id = v.id
      ${whereClause}
      ORDER BY db.trip_start_time DESC
      LIMIT ? OFFSET ?`,
      [...params, Number(limit), offset]
    )
    const data = result as any[]
    
    // 格式化数据，将用户和车辆信息嵌套
    const formattedData = data.map(record => ({
      ...record,
      users: {
        name: record.user_name,
        email: record.user_email
      },
      vehicles: {
        brand: record.vehicle_brand,
        model: record.vehicle_model,
        license_plate: record.vehicle_license_plate
      }
    }))
    
    const response: PaginatedResponse<DrivingBehavior> = {
      success: true,
      data: formattedData,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: total,
        totalPages: Math.ceil(total / Number(limit))
      }
    }
    
    res.json(response)
  } catch (error) {
    console.error('获取驾驶行为记录异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取单个驾驶行为记录
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params
    
    const [result] = await db.execute(
      `SELECT 
        db.*,
        u.user_id as user_id_full,
        u.username as user_name,
        u.email as user_email,
        u.phone as user_phone,
        v.vehicle_id as vehicle_id_full,
        v.make as vehicle_brand,
        v.model as vehicle_model,
        v.license_plate as vehicle_license_plate,
        v.vin as vehicle_vin
      FROM driving_behavior db
      LEFT JOIN users u ON db.user_id = u.user_id
      LEFT JOIN vehicles v ON db.vehicle_id = v.vehicle_id
      WHERE db.id = ?`,
      [id]
    )
    const data = (result as any[])[0]
    
    if (!data) {
      return res.status(404).json({
        success: false,
        error: '驾驶行为记录不存在'
      } as ApiResponse)
    }
    
    // 格式化数据，将用户和车辆信息嵌套
    const formattedData = {
      ...data,
      users: {
        id: data.user_id_full,
        name: data.user_name,
        email: data.user_email,
        phone: data.user_phone
      },
      vehicles: {
        id: data.vehicle_id_full,
        brand: data.vehicle_brand,
        model: data.vehicle_model,
        license_plate: data.vehicle_license_plate,
        vin: data.vehicle_vin
      }
    }
    
    res.json({
      success: true,
      data: formattedData
    } as ApiResponse<DrivingBehavior>)
  } catch (error) {
    console.error('获取驾驶行为记录异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 创建驾驶行为记录
router.post('/', async (req, res) => {
  try {
    const {
      user_id,
      vehicle_id,
      trip_start_time,
      trip_end_time,
      distance_km,
      duration_minutes,
      avg_speed,
      max_speed,
      harsh_acceleration_count,
      harsh_braking_count,
      sharp_turn_count,
      speeding_duration_minutes,
      idle_duration_minutes,
      fuel_consumption,
      route_data,
      weather_conditions
    } = req.body
    
    // 验证必填字段
    if (!user_id || !vehicle_id || !trip_start_time || !trip_end_time) {
      return res.status(400).json({
        success: false,
        error: '用户ID、车辆ID、行程开始时间和结束时间为必填字段'
      } as ApiResponse)
    }
    
    // 验证用户和车辆是否存在
    const [userResult] = await db.execute(
      'SELECT id FROM users WHERE id = ?',
      [user_id]
    )
    const userData = (userResult as any[])[0]
    
    if (!userData) {
      return res.status(400).json({
        success: false,
        error: '用户不存在'
      } as ApiResponse)
    }
    
    const [vehicleResult] = await db.execute(
      'SELECT id, user_id FROM vehicles WHERE id = ?',
      [vehicle_id]
    )
    const vehicleData = (vehicleResult as any[])[0]
    
    if (!vehicleData) {
      return res.status(400).json({
        success: false,
        error: '车辆不存在'
      } as ApiResponse)
    }
    
    if (vehicleData.user_id !== user_id) {
      return res.status(400).json({
        success: false,
        error: '车辆不属于该用户'
      } as ApiResponse)
    }
    
    // 计算驾驶评分
    const drivingScore = calculateDrivingScore({
      distance_km: distance_km || 0,
      duration_minutes: duration_minutes || 0,
      avg_speed: avg_speed || 0,
      max_speed: max_speed || 0,
      harsh_acceleration_count: harsh_acceleration_count || 0,
      harsh_braking_count: harsh_braking_count || 0,
      sharp_turn_count: sharp_turn_count || 0,
      speeding_duration_minutes: speeding_duration_minutes || 0,
      idle_duration_minutes: idle_duration_minutes || 0
    })
    
    const behaviorData = {
      user_id,
      vehicle_id,
      trip_start_time,
      trip_end_time,
      distance_km: distance_km ? Number(distance_km) : null,
      duration_minutes: duration_minutes ? Number(duration_minutes) : null,
      avg_speed: avg_speed ? Number(avg_speed) : null,
      max_speed: max_speed ? Number(max_speed) : null,
      harsh_acceleration_count: harsh_acceleration_count || 0,
      harsh_braking_count: harsh_braking_count || 0,
      sharp_turn_count: sharp_turn_count || 0,
      speeding_duration_minutes: speeding_duration_minutes || 0,
      idle_duration_minutes: idle_duration_minutes || 0,
      fuel_consumption: fuel_consumption ? Number(fuel_consumption) : null,
      driving_score: drivingScore,
      route_data,
      weather_conditions
    }
    
    const [insertResult] = await db.execute(
      `INSERT INTO driving_behavior (
        user_id, vehicle_id, trip_start_time, trip_end_time, distance_km,
        duration_minutes, avg_speed, max_speed, harsh_acceleration_count,
        harsh_braking_count, sharp_turn_count, speeding_duration_minutes,
        idle_duration_minutes, fuel_consumption, driving_score, route_data,
        weather_conditions
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        behaviorData.user_id,
        behaviorData.vehicle_id,
        behaviorData.trip_start_time,
        behaviorData.trip_end_time,
        behaviorData.distance_km,
        behaviorData.duration_minutes,
        behaviorData.avg_speed,
        behaviorData.max_speed,
        behaviorData.harsh_acceleration_count,
        behaviorData.harsh_braking_count,
        behaviorData.sharp_turn_count,
        behaviorData.speeding_duration_minutes,
        behaviorData.idle_duration_minutes,
        behaviorData.fuel_consumption,
        behaviorData.driving_score,
        behaviorData.route_data,
        behaviorData.weather_conditions
      ]
    )
    
    const insertId = (insertResult as any).insertId
    
    // 获取插入的记录
    const [selectResult] = await db.execute(
      'SELECT * FROM driving_behavior WHERE id = ?',
      [insertId]
    )
    const data = (selectResult as any[])[0]
    
    res.status(201).json({
      success: true,
      data,
      message: '驾驶行为记录创建成功'
    } as ApiResponse<DrivingBehavior>)
  } catch (error) {
    console.error('创建驾驶行为记录异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 更新驾驶行为记录
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params
    const updateData = { ...req.body }
    
    // 移除不允许更新的字段
    delete updateData.id
    delete updateData.user_id
    delete updateData.vehicle_id
    delete updateData.created_at
    
    // 如果更新了影响评分的字段，重新计算驾驶评分
    if (updateData.distance_km || updateData.duration_minutes || updateData.avg_speed || 
        updateData.max_speed || updateData.harsh_acceleration_count || updateData.harsh_braking_count ||
        updateData.sharp_turn_count || updateData.speeding_duration_minutes || updateData.idle_duration_minutes) {
      
      // 获取当前记录
      const [currentResult] = await db.execute(
        'SELECT * FROM driving_behavior WHERE id = ?',
        [id]
      )
      const currentRecord = (currentResult as any[])[0]
      
      if (!currentRecord) {
        return res.status(404).json({
          success: false,
          error: '驾驶行为记录不存在'
        } as ApiResponse)
      }
      
      // 合并数据并重新计算评分
      const mergedData = { ...currentRecord, ...updateData }
      updateData.driving_score = calculateDrivingScore({
        distance_km: mergedData.distance_km || 0,
        duration_minutes: mergedData.duration_minutes || 0,
        avg_speed: mergedData.avg_speed || 0,
        max_speed: mergedData.max_speed || 0,
        harsh_acceleration_count: mergedData.harsh_acceleration_count || 0,
        harsh_braking_count: mergedData.harsh_braking_count || 0,
        sharp_turn_count: mergedData.sharp_turn_count || 0,
        speeding_duration_minutes: mergedData.speeding_duration_minutes || 0,
        idle_duration_minutes: mergedData.idle_duration_minutes || 0
      })
    }
    
    updateData.updated_at = new Date().toISOString()
    
    // 构建更新语句
    const updateFields = Object.keys(updateData).map(key => `${key} = ?`).join(', ')
    const updateValues = Object.values(updateData)
    
    await db.execute(
      `UPDATE driving_behavior SET ${updateFields} WHERE id = ?`,
      [...updateValues, id]
    )
    
    // 获取更新后的记录
    const [selectResult] = await db.execute(
      'SELECT * FROM driving_behavior WHERE id = ?',
      [id]
    )
    const data = (selectResult as any[])[0]
    
    res.json({
      success: true,
      data,
      message: '驾驶行为记录更新成功'
    } as ApiResponse<DrivingBehavior>)
  } catch (error) {
    console.error('更新驾驶行为记录异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 删除驾驶行为记录
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params
    
    const [result] = await db.execute(
      'DELETE FROM driving_behavior WHERE id = ?',
      [id]
    )
    
    const affectedRows = (result as any).affectedRows
    
    if (affectedRows === 0) {
      return res.status(404).json({
        success: false,
        error: '驾驶行为记录不存在'
      } as ApiResponse)
    }
    
    res.json({
      success: true,
      message: '驾驶行为记录删除成功'
    } as ApiResponse)
  } catch (error) {
    console.error('删除驾驶行为记录异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取用户驾驶行为统计
router.get('/statistics/:user_id', async (req, res) => {
  try {
    const { user_id } = req.params
    const { days = 30 } = req.query
    
    // 验证用户是否存在
    const [userResult] = await db.execute(
      'SELECT id, name FROM users WHERE id = ?',
      [user_id]
    )
    const user = (userResult as any[])[0]
    
    if (!user) {
      return res.status(404).json({
        success: false,
        error: '用户不存在'
      } as ApiResponse)
    }
    
    // 获取指定时间范围内的驾驶记录
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - Number(days))
    
    const [recordsResult] = await db.execute(
      'SELECT * FROM driving_behavior WHERE user_id = ? AND trip_start_time >= ? ORDER BY trip_start_time ASC',
      [user_id, startDate.toISOString()]
    )
    const drivingRecords = recordsResult as any[]
    
    // 计算统计数据
    const totalTrips = drivingRecords.length
    const totalDistance = drivingRecords.reduce((sum, r) => sum + (r.distance_km || 0), 0)
    const totalDuration = drivingRecords.reduce((sum, r) => sum + (r.duration_minutes || 0), 0)
    const totalFuelConsumption = drivingRecords.reduce((sum, r) => sum + (r.fuel_consumption || 0), 0)
    
    const avgSpeed = totalDuration > 0 ? (totalDistance / (totalDuration / 60)) : 0
    const avgDrivingScore = totalTrips > 0 ? 
      drivingRecords.reduce((sum, r) => sum + (r.driving_score || 0), 0) / totalTrips : 0
    
    // 安全事件统计
    const totalHarshAcceleration = drivingRecords.reduce((sum, r) => sum + (r.harsh_acceleration_count || 0), 0)
    const totalHarshBraking = drivingRecords.reduce((sum, r) => sum + (r.harsh_braking_count || 0), 0)
    const totalSharpTurns = drivingRecords.reduce((sum, r) => sum + (r.sharp_turn_count || 0), 0)
    const totalSpeedingTime = drivingRecords.reduce((sum, r) => sum + (r.speeding_duration_minutes || 0), 0)
    const totalIdleTime = drivingRecords.reduce((sum, r) => sum + (r.idle_duration_minutes || 0), 0)
    
    // 评分分布
    const scoreDistribution = {
      excellent: drivingRecords.filter(r => (r.driving_score || 0) >= 90).length,
      good: drivingRecords.filter(r => (r.driving_score || 0) >= 80 && (r.driving_score || 0) < 90).length,
      average: drivingRecords.filter(r => (r.driving_score || 0) >= 70 && (r.driving_score || 0) < 80).length,
      poor: drivingRecords.filter(r => (r.driving_score || 0) < 70).length
    }
    
    const statistics = {
      userInfo: {
        userId: user_id,
        userName: user.name,
        period: `最近${days}天`
      },
      tripSummary: {
        totalTrips,
        totalDistance: totalDistance.toFixed(2),
        totalDuration: Math.round(totalDuration),
        avgSpeed: avgSpeed.toFixed(2),
        avgDrivingScore: avgDrivingScore.toFixed(1),
        totalFuelConsumption: totalFuelConsumption.toFixed(2)
      },
      safetyMetrics: {
        totalHarshAcceleration,
        totalHarshBraking,
        totalSharpTurns,
        totalSpeedingTime,
        totalIdleTime,
        safetyScore: calculateSafetyScore({
          totalTrips,
          totalHarshAcceleration,
          totalHarshBraking,
          totalSharpTurns,
          totalSpeedingTime
        })
      },
      scoreDistribution,
      efficiency: {
        fuelEfficiency: totalDistance > 0 ? (totalFuelConsumption / totalDistance).toFixed(2) : '0',
        idleTimePercentage: totalDuration > 0 ? ((totalIdleTime / totalDuration) * 100).toFixed(1) : '0',
        speedingTimePercentage: totalDuration > 0 ? ((totalSpeedingTime / totalDuration) * 100).toFixed(1) : '0'
      }
    }
    
    res.json({
      success: true,
      data: statistics
    } as ApiResponse)
  } catch (error) {
    console.error('获取驾驶行为统计异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取驾驶行为趋势
router.get('/trends/:user_id', async (req, res) => {
  try {
    const { user_id } = req.params
    const { period = 'week' } = req.query // week, month, quarter
    
    let days: number
    switch (period) {
      case 'week':
        days = 7
        break
      case 'month':
        days = 30
        break
      case 'quarter':
        days = 90
        break
      default:
        days = 30
    }
    
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - days)
    
    const [recordsResult] = await db.execute(
      'SELECT * FROM driving_behavior WHERE user_id = ? AND trip_start_time >= ? ORDER BY trip_start_time ASC',
      [user_id, startDate.toISOString()]
    )
    const drivingRecords = recordsResult as any[]
    
    // 按日期分组
    const dailyStats = drivingRecords.reduce((acc, record) => {
      const date = new Date(record.trip_start_time).toISOString().split('T')[0]
      
      if (!acc[date]) {
        acc[date] = {
          date,
          tripCount: 0,
          totalDistance: 0,
          totalDuration: 0,
          avgScore: 0,
          scores: [],
          safetyEvents: 0
        }
      }
      
      acc[date].tripCount++
      acc[date].totalDistance += record.distance_km || 0
      acc[date].totalDuration += record.duration_minutes || 0
      acc[date].scores.push(record.driving_score || 0)
      acc[date].safetyEvents += (record.harsh_acceleration_count || 0) + 
                               (record.harsh_braking_count || 0) + 
                               (record.sharp_turn_count || 0)
      
      return acc
    }, {} as Record<string, any>)
    
    // 计算每日平均分数
    Object.values(dailyStats).forEach((day: any) => {
      day.avgScore = day.scores.length > 0 ? 
        day.scores.reduce((sum: number, score: number) => sum + score, 0) / day.scores.length : 0
      delete day.scores // 移除临时数组
    })
    
    const trends = Object.values(dailyStats).sort((a: any, b: any) => 
      new Date(a.date).getTime() - new Date(b.date).getTime()
    )
    
    res.json({
      success: true,
      data: {
        period,
        trends,
        summary: {
          totalDays: trends.length,
          avgDailyTrips: trends.length > 0 ? 
            (Number(trends.reduce((sum: number, day: any) => sum + (day.tripCount || 0), 0)) / Number(trends.length)).toFixed(1) : '0',
          avgDailyDistance: trends.length > 0 ? 
            (Number(trends.reduce((sum: number, day: any) => sum + (day.totalDistance || 0), 0)) / Number(trends.length)).toFixed(1) : '0',
          avgDailyScore: trends.length > 0 ? 
            (Number(trends.reduce((sum: number, day: any) => sum + (day.avgScore || 0), 0)) / Number(trends.length)).toFixed(1) : '0'
        }
      }
    } as ApiResponse)
  } catch (error) {
    console.error('获取驾驶行为趋势异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 驾驶评分计算函数
function calculateDrivingScore(data: {
  distance_km: number
  duration_minutes: number
  avg_speed: number
  max_speed: number
  harsh_acceleration_count: number
  harsh_braking_count: number
  sharp_turn_count: number
  speeding_duration_minutes: number
  idle_duration_minutes: number
}): number {
  let score = 100
  
  // 急加速扣分 (每次扣2分)
  score -= data.harsh_acceleration_count * 2
  
  // 急刹车扣分 (每次扣2分)
  score -= data.harsh_braking_count * 2
  
  // 急转弯扣分 (每次扣1分)
  score -= data.sharp_turn_count * 1
  
  // 超速时间扣分 (每分钟扣0.5分)
  score -= data.speeding_duration_minutes * 0.5
  
  // 怠速时间扣分 (超过总时间20%开始扣分)
  if (data.duration_minutes > 0) {
    const idlePercentage = (data.idle_duration_minutes / data.duration_minutes) * 100
    if (idlePercentage > 20) {
      score -= (idlePercentage - 20) * 0.5
    }
  }
  
  // 平均速度评分 (过快或过慢都扣分)
  if (data.avg_speed > 80) {
    score -= (data.avg_speed - 80) * 0.2
  } else if (data.avg_speed < 20 && data.distance_km > 5) {
    score -= (20 - data.avg_speed) * 0.1
  }
  
  // 最高速度评分
  if (data.max_speed > 120) {
    score -= (data.max_speed - 120) * 0.3
  }
  
  // 确保分数在0-100范围内
  return Math.max(0, Math.min(100, Math.round(score)))
}

// 安全评分计算函数
function calculateSafetyScore(data: {
  totalTrips: number
  totalHarshAcceleration: number
  totalHarshBraking: number
  totalSharpTurns: number
  totalSpeedingTime: number
}): number {
  if (data.totalTrips === 0) return 100
  
  let score = 100
  
  // 计算每次行程的平均安全事件
  const avgHarshAcceleration = data.totalHarshAcceleration / data.totalTrips
  const avgHarshBraking = data.totalHarshBraking / data.totalTrips
  const avgSharpTurns = data.totalSharpTurns / data.totalTrips
  const avgSpeedingTime = data.totalSpeedingTime / data.totalTrips
  
  // 根据平均值扣分
  score -= avgHarshAcceleration * 10
  score -= avgHarshBraking * 10
  score -= avgSharpTurns * 5
  score -= avgSpeedingTime * 0.5
  
  return Math.max(0, Math.min(100, Math.round(score)))
}

export default router