<template>
  <div class="charging-station-list">
    <div class="list-header">
      <h3>充电站列表</h3>
      <div class="list-controls">
        <el-input
          v-model="searchQuery"
          placeholder="搜索充电站"
          style="width: 200px"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <el-select v-model="statusFilter" placeholder="状态" style="width: 120px">
          <el-option label="全部" value="" />
          <el-option label="可用" value="available" />
          <el-option label="使用中" value="busy" />
          <el-option label="维护中" value="maintenance" />
        </el-select>
      </div>
    </div>

    <div class="stations-grid">
      <div
        v-for="station in filteredStations"
        :key="station.id"
        class="station-card"
        :class="{ selected: selectedStation?.id === station.id }"
        @click="selectStation(station)"
      >
        <div class="station-header">
          <div class="station-name">{{ station.name }}</div>
          <el-tag :type="getStatusType(station.status)" size="small">
            {{ getStatusText(station.status) }}
          </el-tag>
        </div>

        <div class="station-info">
          <div class="info-item">
            <el-icon><Location /></el-icon>
            <span>{{ station.address }}</span>
          </div>
          <div class="info-item">
            <el-icon><User /></el-icon>
            <span>{{ station.operator }}</span>
          </div>
          <div class="info-item">
            <el-icon><Connection /></el-icon>
            <span>{{ station.available_ports }}/{{ station.total_ports }} 可用</span>
          </div>
          <div class="info-item">
            <el-icon><Lightning /></el-icon>
            <span>{{ station.power }} kW</span>
          </div>
          <div class="info-item">
            <el-icon><Money /></el-icon>
            <span>¥{{ station.price }}/kWh</span>
          </div>
        </div>

        <div class="station-actions">
          <el-button size="small" @click.stop="$emit('navigate', station)">
            导航
          </el-button>
          <el-button size="small" type="primary" @click.stop="$emit('reserve', station)">
            预约
          </el-button>
        </div>
      </div>
    </div>

    <div v-if="filteredStations.length === 0" class="empty-state">
      <el-icon class="empty-icon"><OfficeBuilding /></el-icon>
      <p>暂无符合条件的充电站</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, defineEmits } from 'vue'
import {
  Search,
  Location,
  User,
  Connection,
  Lightning,
  Money,
  OfficeBuilding
} from '@element-plus/icons-vue'

interface Station {
  id: string
  name: string
  address: string
  operator: string
  status: string
  available_ports: number
  total_ports: number
  power: number
  price: number
}

const props = defineProps<{
  stations: Station[]
  selectedStation?: Station | null
}>()

const emit = defineEmits<{
  select: [station: Station]
  navigate: [station: Station]
  reserve: [station: Station]
}>()

// 响应式数据
const searchQuery = ref('')
const statusFilter = ref('')

// 计算属性
const filteredStations = computed(() => {
  let filtered = props.stations

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(station =>
      station.name.toLowerCase().includes(query) ||
      station.address.toLowerCase().includes(query) ||
      station.operator.toLowerCase().includes(query)
    )
  }

  if (statusFilter.value) {
    if (statusFilter.value === 'available') {
      filtered = filtered.filter(s => s.status === 'active' && s.available_ports > 0)
    } else if (statusFilter.value === 'busy') {
      filtered = filtered.filter(s => s.status === 'active' && s.available_ports === 0)
    } else {
      filtered = filtered.filter(s => s.status === statusFilter.value)
    }
  }

  return filtered
})

// 方法
const selectStation = (station: Station) => {
  emit('select', station)
}

const getStatusType = (status: string) => {
  const types = {
    active: 'success',
    maintenance: 'warning',
    offline: 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts = {
    active: '正常',
    maintenance: '维护中',
    offline: '离线'
  }
  return texts[status] || '未知'
}
</script>

<style scoped>
.charging-station-list {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e2e8f0;
}

.list-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
  margin: 0;
}

.list-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.stations-grid {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.station-card {
  background: #f7fafc;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.station-card:hover {
  background: #edf2f7;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.station-card.selected {
  border-color: #667eea;
  background: #e6f3ff;
}

.station-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.station-name {
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
}

.station-info {
  margin-bottom: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 14px;
  color: #4a5568;
}

.station-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #718096;
  padding: 40px;
}

.empty-icon {
  font-size: 48px;
  color: #cbd5e1;
  margin-bottom: 16px;
}

.empty-state p {
  font-size: 16px;
  margin: 0;
}

@media (max-width: 768px) {
  .list-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .list-controls {
    width: 100%;
    justify-content: center;
  }
  
  .station-card {
    padding: 12px;
  }
  
  .station-actions {
    justify-content: center;
  }
}
</style>
