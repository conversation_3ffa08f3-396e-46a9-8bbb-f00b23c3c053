<template>
  <div class="tech-support-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">技术支持</h1>
      <p class="page-subtitle">为开发者提供全面的技术文档和支持服务</p>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 快速导航 -->
      <div class="quick-nav">
        <el-card class="nav-card">
          <template #header>
            <div class="card-header">
              <el-icon><Guide /></el-icon>
              <span>快速导航</span>
            </div>
          </template>
          <div class="nav-grid">
            <div class="nav-item" @click="scrollToSection('docs')">
              <el-icon><Document /></el-icon>
              <span>技术文档</span>
            </div>
            <div class="nav-item" @click="scrollToSection('api')">
              <el-icon><Connection /></el-icon>
              <span>API接口</span>
            </div>
            <div class="nav-item" @click="scrollToSection('guide')">
              <el-icon><Reading /></el-icon>
              <span>开发指南</span>
            </div>
            <div class="nav-item" @click="scrollToSection('faq')">
              <el-icon><QuestionFilled /></el-icon>
              <span>常见问题</span>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 技术文档 -->
      <div id="docs" class="section">
        <el-card class="content-card">
          <template #header>
            <div class="card-header">
              <el-icon><Document /></el-icon>
              <span>技术文档</span>
            </div>
          </template>
          <div class="docs-content">
            <div class="doc-category">
              <h3>系统架构</h3>
              <div class="doc-list">
                <div class="doc-item">
                  <el-icon><Files /></el-icon>
                  <div class="doc-info">
                    <h4>整体架构设计</h4>
                    <p>介绍新能源汽车智能综合管理系统的整体架构设计理念和技术选型</p>
                    <el-tag type="success">最新版本</el-tag>
                  </div>
                  <el-button type="primary" link @click="viewDoc('architecture')">
                    查看文档
                  </el-button>
                </div>
                <div class="doc-item">
                  <el-icon><Files /></el-icon>
                  <div class="doc-info">
                    <h4>微服务架构</h4>
                    <p>详细说明系统的微服务拆分策略和服务间通信机制</p>
                    <el-tag type="info">v2.0</el-tag>
                  </div>
                  <el-button type="primary" link @click="viewDoc('microservices')">
                    查看文档
                  </el-button>
                </div>
              </div>
            </div>

            <div class="doc-category">
              <h3>前端开发</h3>
              <div class="doc-list">
                <div class="doc-item">
                  <el-icon><Files /></el-icon>
                  <div class="doc-info">
                    <h4>Vue 3 + TypeScript 开发规范</h4>
                    <p>前端开发的代码规范、组件设计原则和最佳实践</p>
                    <el-tag type="success">推荐</el-tag>
                  </div>
                  <el-button type="primary" link @click="viewDoc('frontend')">
                    查看文档
                  </el-button>
                </div>
                <div class="doc-item">
                  <el-icon><Files /></el-icon>
                  <div class="doc-info">
                    <h4>UI组件库使用指南</h4>
                    <p>Element Plus组件的使用方法和自定义主题配置</p>
                    <el-tag type="info">基础</el-tag>
                  </div>
                  <el-button type="primary" link @click="viewDoc('ui-components')">
                    查看文档
                  </el-button>
                </div>
              </div>
            </div>

            <div class="doc-category">
              <h3>后端开发</h3>
              <div class="doc-list">
                <div class="doc-item">
                  <el-icon><Files /></el-icon>
                  <div class="doc-info">
                    <h4>Node.js + Express 开发指南</h4>
                    <p>后端API开发规范、数据库设计和安全最佳实践</p>
                    <el-tag type="success">核心</el-tag>
                  </div>
                  <el-button type="primary" link @click="viewDoc('backend')">
                    查看文档
                  </el-button>
                </div>
                <div class="doc-item">
                  <el-icon><Files /></el-icon>
                  <div class="doc-info">
                    <h4>数据库设计文档</h4>
                    <p>MySQL数据库表结构设计和数据模型说明</p>
                    <el-tag type="warning">重要</el-tag>
                  </div>
                  <el-button type="primary" link @click="viewDoc('database')">
                    查看文档
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- API接口 -->
      <div id="api" class="section">
        <el-card class="content-card">
          <template #header>
            <div class="card-header">
              <el-icon><Connection /></el-icon>
              <span>API接口文档</span>
            </div>
          </template>
          <div class="api-content">
            <div class="api-overview">
              <h3>接口概览</h3>
              <p>系统提供RESTful API接口，支持JSON格式数据交换，所有接口都需要进行身份验证。</p>
              <div class="api-stats">
                <div class="stat-item">
                  <div class="stat-number">50+</div>
                  <div class="stat-label">API接口</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">8</div>
                  <div class="stat-label">核心模块</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">99.9%</div>
                  <div class="stat-label">可用性</div>
                </div>
              </div>
            </div>

            <div class="api-modules">
              <div class="api-module" v-for="module in apiModules" :key="module.name">
                <div class="module-header">
                  <el-icon :class="module.icon"></el-icon>
                  <h4>{{ module.name }}</h4>
                  <el-tag :type="module.status === 'stable' ? 'success' : 'warning'">
                    {{ module.status === 'stable' ? '稳定' : '开发中' }}
                  </el-tag>
                </div>
                <p class="module-desc">{{ module.description }}</p>
                <div class="module-endpoints">
                  <div class="endpoint" v-for="endpoint in module.endpoints" :key="endpoint.path">
                    <el-tag :type="getMethodType(endpoint.method)" size="small">
                      {{ endpoint.method }}
                    </el-tag>
                    <code class="endpoint-path">{{ endpoint.path }}</code>
                    <span class="endpoint-desc">{{ endpoint.description }}</span>
                  </div>
                </div>
                <el-button type="primary" size="small" @click="viewApiDoc(module.key)">
                  查看详细文档
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 开发指南 -->
      <div id="guide" class="section">
        <el-card class="content-card">
          <template #header>
            <div class="card-header">
              <el-icon><Reading /></el-icon>
              <span>开发指南</span>
            </div>
          </template>
          <div class="guide-content">
            <div class="guide-steps">
              <h3>快速开始</h3>
              <div class="step" v-for="(step, index) in developmentSteps" :key="index">
                <div class="step-number">{{ index + 1 }}</div>
                <div class="step-content">
                  <h4>{{ step.title }}</h4>
                  <p>{{ step.description }}</p>
                  <div class="step-code" v-if="step.code">
                    <pre><code>{{ step.code }}</code></pre>
                  </div>
                </div>
              </div>
            </div>

            <div class="guide-resources">
              <h3>开发资源</h3>
              <div class="resource-grid">
                <div class="resource-item" v-for="resource in developmentResources" :key="resource.name">
                  <el-icon :class="resource.icon"></el-icon>
                  <h4>{{ resource.name }}</h4>
                  <p>{{ resource.description }}</p>
                  <el-button type="primary" link @click="openResource(resource.url)">
                    访问资源
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 常见问题 -->
      <div id="faq" class="section">
        <el-card class="content-card">
          <template #header>
            <div class="card-header">
              <el-icon><QuestionFilled /></el-icon>
              <span>常见问题</span>
            </div>
          </template>
          <div class="faq-content">
            <el-collapse v-model="activeFaq">
              <el-collapse-item 
                v-for="(faq, index) in faqList" 
                :key="index" 
                :title="faq.question" 
                :name="index"
              >
                <div class="faq-answer">
                  <p>{{ faq.answer }}</p>
                  <div class="faq-code" v-if="faq.code">
                    <pre><code>{{ faq.code }}</code></pre>
                  </div>
                  <div class="faq-tags" v-if="faq.tags">
                    <el-tag v-for="tag in faq.tags" :key="tag" size="small">{{ tag }}</el-tag>
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
        </el-card>
      </div>

      <!-- 联系支持 -->
      <div class="support-contact">
        <el-card class="contact-card">
          <template #header>
            <div class="card-header">
              <el-icon><Service /></el-icon>
              <span>需要更多帮助？</span>
            </div>
          </template>
          <div class="contact-content">
            <p>如果您在开发过程中遇到问题，可以通过以下方式联系我们：</p>
            <div class="contact-methods">
              <div class="contact-method">
                <el-icon><Message /></el-icon>
                <div class="method-info">
                  <h4>技术支持邮箱</h4>
                  <p><EMAIL></p>
                </div>
              </div>
              <div class="contact-method">
                <el-icon><ChatDotRound /></el-icon>
                <div class="method-info">
                  <h4>在线客服</h4>
                  <p>工作日 9:00-18:00</p>
                </div>
              </div>
              <div class="contact-method">
                <el-icon><Phone /></el-icon>
                <div class="method-info">
                  <h4>技术热线</h4>
                  <p>************</p>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Document,
  Guide,
  Connection,
  Reading,
  QuestionFilled,
  Files,
  Service,
  Message,
  ChatDotRound,
  Phone
} from '@element-plus/icons-vue'

// 响应式数据
const activeFaq = ref<number[]>([])

// API模块数据
const apiModules = ref([
  {
    key: 'auth',
    name: '用户认证',
    icon: 'User',
    status: 'stable',
    description: '用户注册、登录、权限验证等接口',
    endpoints: [
      { method: 'POST', path: '/api/auth/login', description: '用户登录' },
      { method: 'POST', path: '/api/auth/register', description: '用户注册' },
      { method: 'POST', path: '/api/auth/logout', description: '用户登出' },
      { method: 'GET', path: '/api/auth/profile', description: '获取用户信息' }
    ]
  },
  {
    key: 'vehicles',
    name: '车辆管理',
    icon: 'Truck',
    status: 'stable',
    description: '车辆信息管理、状态监控等接口',
    endpoints: [
      { method: 'GET', path: '/api/vehicles', description: '获取车辆列表' },
      { method: 'POST', path: '/api/vehicles', description: '添加车辆' },
      { method: 'PUT', path: '/api/vehicles/:id', description: '更新车辆信息' },
      { method: 'DELETE', path: '/api/vehicles/:id', description: '删除车辆' }
    ]
  },
  {
    key: 'charging',
    name: '充电管理',
    icon: 'Lightning',
    status: 'stable',
    description: '充电站管理、充电会话等接口',
    endpoints: [
      { method: 'GET', path: '/api/charging-stations', description: '获取充电站列表' },
      { method: 'POST', path: '/api/charging-sessions', description: '开始充电会话' },
      { method: 'PUT', path: '/api/charging-sessions/:id', description: '结束充电会话' }
    ]
  },
  {
    key: 'battery',
    name: '电池管理',
    icon: 'Lightning',
    status: 'stable',
    description: '电池状态监控、健康度分析等接口',
    endpoints: [
      { method: 'GET', path: '/api/batteries', description: '获取电池列表' },
      { method: 'GET', path: '/api/batteries/:id/status', description: '获取电池状态' },
      { method: 'GET', path: '/api/batteries/:id/health', description: '获取电池健康度' }
    ]
  }
])

// 开发步骤
const developmentSteps = ref([
  {
    title: '环境准备',
    description: '安装Node.js、MySQL数据库，配置开发环境',
    code: 'npm install\nnpm run dev'
  },
  {
    title: '数据库初始化',
    description: '创建数据库，导入初始数据',
    code: 'mysql -u root -p < mysql_setup.sql'
  },
  {
    title: '配置环境变量',
    description: '复制.env.example为.env，配置数据库连接等信息',
    code: 'cp .env.example .env'
  },
  {
    title: '启动开发服务器',
    description: '启动前端和后端开发服务器',
    code: 'npm run dev'
  }
])

// 开发资源
const developmentResources = ref([
  {
    name: 'Vue 3 官方文档',
    description: 'Vue 3 框架的官方文档和API参考',
    icon: 'Document',
    url: 'https://vuejs.org/'
  },
  {
    name: 'Element Plus',
    description: 'Vue 3 UI组件库文档',
    icon: 'Grid',
    url: 'https://element-plus.org/'
  },
  {
    name: 'TypeScript 手册',
    description: 'TypeScript 语言官方手册',
    icon: 'Document',
    url: 'https://www.typescriptlang.org/docs/'
  },
  {
    name: 'Node.js 文档',
    description: 'Node.js 运行时环境文档',
    icon: 'Document',
    url: 'https://nodejs.org/docs/'
  }
])

// 常见问题列表
const faqList = ref([
  {
    question: '如何启动开发环境？',
    answer: '首先确保已安装Node.js和MySQL，然后运行npm install安装依赖，配置.env文件，最后运行npm run dev启动开发服务器。',
    code: 'npm install\nnpm run dev',
    tags: ['环境配置', '启动']
  },
  {
    question: '数据库连接失败怎么办？',
    answer: '检查MySQL服务是否启动，确认.env文件中的数据库配置信息是否正确，包括主机地址、端口、用户名和密码。',
    tags: ['数据库', '连接问题']
  },
  {
    question: '如何添加新的API接口？',
    answer: '在api/routes目录下创建或修改路由文件，定义新的路由处理函数，确保包含适当的错误处理和参数验证。',
    code: 'router.get(\'/new-endpoint\', async (req, res) => {\n  // 处理逻辑\n})',
    tags: ['API开发', '路由']
  },
  {
    question: '前端组件如何与后端API交互？',
    answer: '使用axios或fetch发送HTTP请求，建议在services目录下创建API服务文件，统一管理API调用。',
    code: 'const response = await axios.get(\'/api/endpoint\')\nconst data = response.data',
    tags: ['前端开发', 'API调用']
  },
  {
    question: '如何处理用户认证？',
    answer: '系统使用JWT token进行用户认证，登录成功后将token存储在localStorage中，每次API请求时在请求头中携带token。',
    tags: ['认证', 'JWT', '安全']
  },
  {
    question: '如何自定义主题样式？',
    answer: '可以通过修改CSS变量来自定义主题，或者在组件中使用:deep()选择器覆盖Element Plus的默认样式。',
    code: ':deep(.el-button) {\n  background-color: #custom-color;\n}',
    tags: ['样式', '主题', 'CSS']
  }
])

// 方法
const scrollToSection = (sectionId: string) => {
  const element = document.getElementById(sectionId)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth', block: 'start' })
  }
}

const viewDoc = (docType: string) => {
  ElMessage.info(`正在打开${docType}文档...`)
  // 这里可以实现实际的文档查看逻辑
}

const viewApiDoc = (moduleKey: string) => {
  ElMessage.info(`正在打开${moduleKey}模块API文档...`)
  // 这里可以实现实际的API文档查看逻辑
}

const getMethodType = (method: string) => {
  const types: Record<string, string> = {
    'GET': 'success',
    'POST': 'primary',
    'PUT': 'warning',
    'DELETE': 'danger'
  }
  return types[method] || 'info'
}

const openResource = (url: string) => {
  window.open(url, '_blank')
}
</script>

<style scoped>
.tech-support-container {
  min-height: 100vh;
  background: #ffffff;
  padding: 24px;
}

.page-header {
  text-align: center;
  margin-bottom: 32px;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 8px;
}

.page-subtitle {
  font-size: 16px;
  color: #718096;
  margin: 0;
}

.main-content {
  max-width: 1200px;
  margin: 0 auto;
}

.quick-nav {
  margin-bottom: 32px;
}

.nav-card {
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #1a202c;
}

.nav-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px;
  border-radius: 8px;
  background: #f7fafc;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #1a202c;
}

.nav-item:hover {
  background: #3182ce;
  color: white;
  transform: translateY(-2px);
}

.section {
  margin-bottom: 32px;
}

.content-card {
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}

.docs-content {
  padding: 16px 0;
}

.doc-category {
  margin-bottom: 24px;
}

.doc-category h3 {
  color: #1a202c;
  margin-bottom: 16px;
  font-size: 20px;
  font-weight: 600;
}

.doc-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.doc-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  transition: all 0.3s ease;
  background: #ffffff;
}

.doc-item:hover {
  border-color: #3182ce;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.doc-info {
  flex: 1;
}

.doc-info h4 {
  margin: 0 0 8px 0;
  color: #1a202c;
  font-weight: 600;
}

.doc-info p {
  margin: 0 0 8px 0;
  color: #718096;
  font-size: 14px;
}

.api-content {
  padding: 16px 0;
}

.api-overview {
  margin-bottom: 24px;
  text-align: center;
}

.api-overview h3 {
  color: #1a202c;
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 8px;
}

.api-overview p {
  color: #718096;
  margin-bottom: 16px;
}

.api-stats {
  display: flex;
  justify-content: center;
  gap: 32px;
  margin-top: 20px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #3182ce;
}

.stat-label {
  font-size: 14px;
  color: #718096;
}

.api-modules {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.api-module {
  padding: 20px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: #ffffff;
}

.module-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.module-header h4 {
  flex: 1;
  margin: 0;
  color: #1a202c;
  font-weight: 600;
}

.module-desc {
  color: #718096;
  margin-bottom: 16px;
}

.module-endpoints {
  margin-bottom: 16px;
}

.endpoint {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 14px;
}

.endpoint-path {
  background: #f7fafc;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  color: #1a202c;
}

.endpoint-desc {
  color: #718096;
}

.guide-content {
  padding: 16px 0;
}

.guide-steps {
  margin-bottom: 32px;
}

.guide-steps h3 {
  color: #1a202c;
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 16px;
}

.step {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
}

.step-number {
  width: 40px;
  height: 40px;
  background: #3182ce;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-content h4 {
  margin: 0 0 8px 0;
  color: #1a202c;
  font-weight: 600;
}

.step-content p {
  margin: 0 0 12px 0;
  color: #718096;
}

.step-code {
  background: #f7fafc;
  border-radius: 6px;
  padding: 16px;
  overflow-x: auto;
}

.step-code pre {
  margin: 0;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  color: #1a202c;
}

.guide-resources h3 {
  color: #1a202c;
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 16px;
}

.resource-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.resource-item {
  padding: 20px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  text-align: center;
  transition: all 0.3s ease;
  background: #ffffff;
}

.resource-item:hover {
  border-color: #3182ce;
  transform: translateY(-2px);
}

.resource-item h4 {
  margin: 12px 0 8px 0;
  color: #1a202c;
  font-weight: 600;
}

.resource-item p {
  margin: 0 0 12px 0;
  color: #718096;
  font-size: 14px;
}

.faq-content {
  padding: 16px 0;
}

.faq-answer {
  padding: 16px 0;
}

.faq-answer p {
  margin-bottom: 12px;
  line-height: 1.6;
  color: #1a202c;
}

.faq-code {
  background: #f7fafc;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 12px;
  overflow-x: auto;
}

.faq-code pre {
  margin: 0;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  color: #1a202c;
}

.faq-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.support-contact {
  margin-top: 32px;
}

.contact-card {
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}

.contact-content {
  text-align: center;
  padding: 16px 0;
}

.contact-content p {
  color: #718096;
  margin-bottom: 16px;
}

.contact-methods {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-top: 24px;
}

.contact-method {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.method-info h4 {
  margin: 0 0 4px 0;
  color: #1a202c;
  font-weight: 600;
}

.method-info p {
  margin: 0;
  color: #718096;
  font-size: 14px;
}

@media (max-width: 768px) {
  .page-title {
    font-size: 24px;
  }
  
  .tech-support-container {
    padding: 16px;
  }
  
  .nav-grid {
    grid-template-columns: 1fr;
  }
  
  .api-modules {
    grid-template-columns: 1fr;
  }
  
  .resource-grid {
    grid-template-columns: 1fr;
  }
  
  .contact-methods {
    grid-template-columns: 1fr;
  }
  
  .step {
    flex-direction: column;
    gap: 12px;
  }
  
  .step-number {
    align-self: flex-start;
  }
}
</style>
