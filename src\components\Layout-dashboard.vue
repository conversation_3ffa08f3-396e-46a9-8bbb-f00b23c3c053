<template>
  <div class="layout-container">
    <el-container>
      <!-- 侧边栏 -->
      <el-aside :width="isCollapsed ? '64px' : '240px'" class="sidebar">
        <div class="logo-container">
          <div class="logo">
            <span class="logo-text" v-if="!isCollapsed">EVAdmin Pro</span>
            <span class="logo-icon" v-else>EV</span>
          </div>
        </div>
        
        <el-menu
          :default-active="$route.path"
          :collapse="isCollapsed"
          :unique-opened="true"
          router
          class="sidebar-menu"
        >
          <el-menu-item index="/">
            <el-icon><Odometer /></el-icon>
            <span>首页</span>
          </el-menu-item>
          
          <el-sub-menu index="vehicle">
            <template #title>
              <el-icon><Odometer /></el-icon>
              <span>车辆管理</span>
            </template>
            <el-menu-item index="/vehicle-network">
              <el-icon><DataBoard /></el-icon>
              <span>车辆网络</span>
            </el-menu-item>
            <el-menu-item index="/vehicle-monitoring">
              <el-icon><Monitor /></el-icon>
              <span>实时监控</span>
            </el-menu-item>
          </el-sub-menu>

          <el-sub-menu index="battery">
            <template #title>
              <el-icon><Lightning /></el-icon>
              <span>电池管理</span>
            </template>
            <el-menu-item index="/battery-management">
              <el-icon><Lightning /></el-icon>
              <span>电池监控</span>
            </el-menu-item>
          </el-sub-menu>

          <el-sub-menu index="charging">
            <template #title>
              <el-icon><Lightning /></el-icon>
              <span>充电服务</span>
            </template>
            <el-menu-item index="/charging-service">
              <el-icon><Lightning /></el-icon>
              <span>充电管理</span>
            </el-menu-item>
          </el-sub-menu>

          <el-sub-menu index="ai">
            <template #title>
              <el-icon><ChatDotRound /></el-icon>
              <span>AI助手</span>
            </template>
            <el-menu-item index="/ai-assistant">
              <el-icon><ChatDotRound /></el-icon>
              <span>智能助手</span>
            </el-menu-item>
          </el-sub-menu>

          <el-menu-item index="/adas">
            <el-icon><Lock /></el-icon>
            <span>ADAS系统</span>
          </el-menu-item>

          <el-menu-item index="/system-security">
            <el-icon><Lock /></el-icon>
            <span>系统安全</span>
          </el-menu-item>

          <el-menu-item index="/user-ecosystem">
            <el-icon><UserFilled /></el-icon>
            <span>用户生态</span>
          </el-menu-item>

          <el-menu-item index="/tech-support">
            <el-icon><Tools /></el-icon>
            <span>技术支持</span>
          </el-menu-item>

          <el-menu-item index="/about-us">
            <el-icon><InfoFilled /></el-icon>
            <span>关于我们</span>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <!-- 主内容区域 -->
      <el-container>
        <!-- 顶部导航栏 -->
        <el-header class="header">
          <div class="header-left">
            <el-button 
              text 
              @click="toggleSidebar"
              class="collapse-btn"
            >
              <el-icon><Expand v-if="isCollapsed" /><Fold v-else /></el-icon>
            </el-button>
            <span class="page-title">电动车智能管理平台</span>
          </div>
          
          <div class="header-right">
            <el-button text class="header-btn">
              <el-icon><Search /></el-icon>
            </el-button>
            <el-button text class="header-btn">
              <el-icon><FullScreen /></el-icon>
            </el-button>
            <el-button text class="header-btn">
              <el-icon><Setting /></el-icon>
            </el-button>
            <el-button text class="header-btn">
              <el-icon><Message /></el-icon>
            </el-button>
            
            <el-dropdown class="user-dropdown">
              <div class="user-info">
                <el-avatar size="small" class="user-avatar">超</el-avatar>
                <span class="username" v-if="!isCollapsed">超级管理员</span>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item>个人中心</el-dropdown-item>
                  <el-dropdown-item>系统设置</el-dropdown-item>
                  <el-dropdown-item divided @click="logout">退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>

        <!-- 主内容 - 使用router-view显示不同页面 -->
        <el-main class="main-content">
          <router-view />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import {
  Expand, Fold, Search, FullScreen, Setting, Message, Plus, Refresh,
  Odometer, DataBoard, Monitor, Lightning, TrendCharts, Sunny,
  MapLocation, ChatDotRound, DataAnalysis, Lock,
  UserFilled, Tools, InfoFilled
} from '@element-plus/icons-vue'

const router = useRouter()
const authStore = useAuthStore()

const isCollapsed = ref(false)

const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value
}

const logout = () => {
  authStore.logout()
  router.push('/login')
}
</script>

<style scoped>
.layout-container {
  height: 100vh;
  background: #f5f7fa;
}

.sidebar {
  background: #ffffff;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  transition: width 0.3s ease;
}

.logo-container {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 20px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo-text {
  font-size: 20px;
  font-weight: 700;
  color: #1a202c;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.logo-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 700;
  font-size: 14px;
}

.sidebar-menu {
  border: none;
  padding: 0 12px;
}

.sidebar-menu .el-menu-item,
.sidebar-menu .el-sub-menu__title {
  height: 48px;
  line-height: 48px;
  margin-bottom: 4px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.sidebar-menu .el-menu-item:hover,
.sidebar-menu .el-sub-menu__title:hover {
  background: #f0f2ff;
  color: #667eea;
}

.sidebar-menu .el-menu-item.is-active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.header {
  background: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  height: 60px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.collapse-btn {
  font-size: 18px;
  color: #606266;
}

.page-title {
  font-size: 16px;
  font-weight: 500;
  color: #1a202c;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-btn {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  color: #606266;
  transition: all 0.3s ease;
}

.header-btn:hover {
  background: #f0f2ff;
  color: #667eea;
}

.user-dropdown {
  margin-left: 12px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.user-info:hover {
  background: #f0f2ff;
}

.user-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.username {
  font-size: 14px;
  color: #1a202c;
  font-weight: 500;
}

.main-content {
  padding: 0;
  background: #f5f7fa;
  overflow-y: auto;
}

.dashboard-container {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 8px 0;
}

.page-subtitle {
  font-size: 16px;
  color: #718096;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.stat-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.stat-label {
  font-size: 14px;
  color: #718096;
  margin: 0 0 8px 0;
}

.stat-value {
  font-size: 32px;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.blue { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.stat-icon.green { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.stat-icon.purple { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.stat-icon.orange { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }

.stat-footer {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stat-change {
  font-size: 14px;
  font-weight: 600;
}

.stat-change.positive {
  color: #38a169;
}

.stat-period {
  font-size: 14px;
  color: #718096;
}

.modules-section {
  margin-bottom: 40px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 24px 0;
}

.modules-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.module-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.module-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.module-icon {
  width: 56px;
  height: 56px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  color: white;
  flex-shrink: 0;
}

.module-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 8px 0;
}

.module-desc {
  font-size: 14px;
  color: #718096;
  margin: 0;
}

.realtime-section {
  margin-bottom: 40px;
}

.realtime-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

.realtime-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 20px 0;
}

.chart-placeholder {
  display: flex;
  align-items: end;
  justify-content: space-around;
  height: 200px;
  padding: 20px 0;
}

.chart-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.chart-bar {
  width: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px 4px 0 0;
  transition: height 0.3s ease;
}

.chart-label {
  font-size: 12px;
  color: #718096;
}

.usage-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.usage-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.station-name {
  font-size: 14px;
  color: #1a202c;
  min-width: 100px;
}

.usage-bar {
  flex: 1;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
}

.usage-fill {
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.usage-percent {
  font-size: 14px;
  font-weight: 600;
  color: #1a202c;
  min-width: 40px;
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .modules-grid {
    grid-template-columns: 1fr;
  }
  
  .realtime-grid {
    grid-template-columns: 1fr;
  }
}
</style>