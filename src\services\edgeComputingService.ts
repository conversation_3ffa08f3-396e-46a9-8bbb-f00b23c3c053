import { ref, reactive } from 'vue'
import { apiWithRetry } from '../utils/api'

// 边缘设备接口
export interface EdgeDevice {
  id: string
  name: string
  type: 'sensor' | 'controller' | 'gateway' | 'camera'
  status: 'online' | 'offline' | 'error'
  location: {
    lat: number
    lng: number
    address: string
  }
  capabilities: string[]
  lastHeartbeat: Date
  data?: any
}

// 边缘计算任务接口
export interface EdgeTask {
  id: string
  name: string
  type: 'data_processing' | 'ai_inference' | 'real_time_analysis' | 'alert_detection'
  priority: 'low' | 'medium' | 'high' | 'critical'
  status: 'pending' | 'running' | 'completed' | 'failed'
  deviceId: string
  config: any
  result?: any
  createdAt: Date
  completedAt?: Date
}

// 边缘数据接口
export interface EdgeData {
  id: string
  deviceId: string
  type: string
  payload: any
  timestamp: Date
  processed: boolean
  metadata?: any
}

// 边缘计算配置
export interface EdgeConfig {
  maxConcurrentTasks: number
  dataRetentionDays: number
  syncInterval: number
  compressionEnabled: boolean
  encryptionEnabled: boolean
}

// 边缘计算服务类
export class EdgeComputingService {
  private devices = reactive<Map<string, EdgeDevice>>(new Map())
  private tasks = reactive<Map<string, EdgeTask>>(new Map())
  private dataBuffer = reactive<EdgeData[]>([])
  private config = reactive<EdgeConfig>({
    maxConcurrentTasks: 10,
    dataRetentionDays: 7,
    syncInterval: 30000, // 30秒
    compressionEnabled: true,
    encryptionEnabled: true
  })
  
  private syncTimer: NodeJS.Timeout | null = null
  private isRunning = ref(false)
  private lastSyncTime = ref(new Date())
  
  constructor() {
    this.initializeService()
  }
  
  // 初始化服务
  private async initializeService() {
    this.loadConfiguration()
    this.startSyncTimer()
    await this.loadRealDevices()
  }
  
  // 加载配置
  private loadConfiguration() {
    const savedConfig = localStorage.getItem('edge_computing_config')
    if (savedConfig) {
      Object.assign(this.config, JSON.parse(savedConfig))
    }
  }
  
  // 保存配置
  public saveConfiguration() {
    localStorage.setItem('edge_computing_config', JSON.stringify(this.config))
  }
  
  // 启动同步定时器
  private startSyncTimer() {
    if (this.syncTimer) {
      clearInterval(this.syncTimer)
    }
    
    this.syncTimer = setInterval(() => {
      this.syncWithCloud()
      this.cleanupOldData()
    }, this.config.syncInterval)
    
    this.isRunning.value = true
  }
  
  // 停止服务
  public stopService() {
    if (this.syncTimer) {
      clearInterval(this.syncTimer)
      this.syncTimer = null
    }
    this.isRunning.value = false
  }
  
  // 加载真实设备数据
  private async loadRealDevices() {
    try {
      const response = await apiWithRetry.get('/edge-devices')
      
      if (response.data?.success && Array.isArray(response.data.data)) {
        const realDevices: EdgeDevice[] = response.data.data.map((device: any) => ({
          id: device.id,
          name: device.name,
          type: device.type,
          status: device.status,
          location: {
            lat: device.latitude || 0,
            lng: device.longitude || 0,
            address: device.address || '未知位置'
          },
          capabilities: device.capabilities || [],
          lastHeartbeat: new Date(device.last_heartbeat || Date.now())
        }))
        
        // 清空现有设备并添加新设备
        this.devices.clear()
        realDevices.forEach(device => {
          this.devices.set(device.id, device)
        })
        
        console.log(`已加载${realDevices.length}个边缘设备`)
      } else {
        console.error('加载边缘设备失败:', response.data?.error || '未知错误')
        this.loadFallbackDevices()
      }
    } catch (error) {
      console.error('加载边缘设备异常:', error)
      this.loadFallbackDevices()
    }
    
    // 启动实时数据获取
    this.startRealDataFetching()
  }
  
  // 加载备用设备数据（当API请求失败时）
  private loadFallbackDevices() {
    console.warn('无法加载设备数据，请检查API连接')
    // 不再使用备用设备数据，完全依赖真实API
  }
  
  // 启动实时数据获取
  private startRealDataFetching() {
    // 设置定时器，定期从API获取最新数据
    setInterval(async () => {
      try {
        // 获取所有在线设备的最新数据
        const onlineDevices = Array.from(this.devices.values())
          .filter(device => device.status === 'online')
          .map(device => device.id)
        
        if (onlineDevices.length === 0) return
        
        const response = await apiWithRetry.get('/edge-devices/data', {
          params: { deviceIds: onlineDevices.join(',') }
        })
        
        if (response.data?.success && Array.isArray(response.data.data)) {
          // 处理每个设备的数据
          response.data.data.forEach((deviceData: any) => {
            this.processRealDeviceData(deviceData)
          })
        } else {
          console.warn('获取设备数据失败', response.data?.error)
          // 不再使用备用数据，记录错误日志
        }
      } catch (error) {
        console.error('获取设备数据异常:', error)
        // 不再使用备用数据，仅记录错误
      }
    }, 5000) // 每5秒获取一次数据
  }
  
  // 处理从API获取的真实设备数据
  private processRealDeviceData(deviceData: any) {
    const device = this.devices.get(deviceData.device_id)
    if (!device) return
    
    // 更新设备状态
    device.status = deviceData.status || device.status
    device.lastHeartbeat = new Date()
    
    // 创建边缘数据对象
    const edgeData: EdgeData = {
      id: deviceData.id || `data_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      deviceId: device.id,
      type: device.type,
      payload: deviceData.payload || {},
      timestamp: new Date(deviceData.timestamp || Date.now()),
      processed: false,
      metadata: deviceData.metadata
    }
    
    // 添加数据到缓冲区
    this.addData(edgeData)
  }
  
  // 已移除备用数据生成逻辑，完全依赖真实API数据
  
  // 添加数据到缓冲区
  public addData(data: EdgeData) {
    this.dataBuffer.push(data)
    
    // 自动处理数据
    this.processData(data)
    
    // 限制缓冲区大小
    if (this.dataBuffer.length > 1000) {
      this.dataBuffer.splice(0, this.dataBuffer.length - 1000)
    }
  }
  
  // 处理数据
  private async processData(data: EdgeData) {
    try {
      // 创建处理任务
      const task: EdgeTask = {
        id: `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        name: `处理${data.type}数据`,
        type: 'data_processing',
        priority: 'medium',
        status: 'running',
        deviceId: data.deviceId,
        config: { dataId: data.id },
        createdAt: new Date()
      }
      
      this.tasks.set(task.id, task)
      
      // 模拟数据处理
      await this.simulateDataProcessing(data, task)
      
      // 标记数据为已处理
      data.processed = true
      
      // 更新任务状态
      task.status = 'completed'
      task.completedAt = new Date()
      task.result = {
        processed: true,
        insights: this.generateInsights(data),
        alerts: this.checkForAlerts(data)
      }
      
    } catch (error) {
      console.error('数据处理失败:', error)
    }
  }
  
  // 模拟数据处理
  private async simulateDataProcessing(data: EdgeData, task: EdgeTask): Promise<void> {
    return new Promise(resolve => {
      // 模拟处理时间
      const processingTime = Math.random() * 2000 + 500 // 0.5-2.5秒
      setTimeout(resolve, processingTime)
    })
  }
  
  // 生成数据洞察
  private generateInsights(data: EdgeData): any {
    const insights: any = {
      timestamp: new Date(),
      deviceId: data.deviceId,
      type: data.type
    }
    
    switch (data.type) {
      case 'sensor':
        insights.temperatureTrend = data.payload.temperature > 30 ? 'high' : 'normal'
        insights.humidityLevel = data.payload.humidity > 70 ? 'high' : 'normal'
        break
      case 'controller':
        insights.powerEfficiency = data.payload.power > 80 ? 'high' : 'normal'
        insights.systemHealth = data.payload.status === 'normal' ? 'good' : 'warning'
        break
      case 'gateway':
        insights.networkLoad = data.payload.dataRate > 500 ? 'high' : 'normal'
        insights.resourceUsage = (data.payload.cpuUsage + data.payload.memoryUsage) / 2
        break
      case 'camera':
        insights.activityLevel = data.payload.motionDetected ? 'high' : 'low'
        insights.storageStatus = data.payload.storageUsed > 80 ? 'warning' : 'normal'
        break
    }
    
    return insights
  }
  
  // 检查告警
  private checkForAlerts(data: EdgeData): any[] {
    const alerts: any[] = []
    
    switch (data.type) {
      case 'sensor':
        if (data.payload.temperature > 35) {
          alerts.push({
            level: 'warning',
            message: `设备${data.deviceId}温度过高: ${data.payload.temperature.toFixed(1)}°C`,
            timestamp: new Date()
          })
        }
        break
      case 'controller':
        if (data.payload.status !== 'normal') {
          alerts.push({
            level: 'error',
            message: `控制器${data.deviceId}状态异常`,
            timestamp: new Date()
          })
        }
        break
      case 'gateway':
        if (data.payload.cpuUsage > 90) {
          alerts.push({
            level: 'warning',
            message: `网关${data.deviceId}CPU使用率过高: ${data.payload.cpuUsage.toFixed(1)}%`,
            timestamp: new Date()
          })
        }
        break
      case 'camera':
        if (data.payload.storageUsed > 90) {
          alerts.push({
            level: 'warning',
            message: `摄像头${data.deviceId}存储空间不足`,
            timestamp: new Date()
          })
        }
        break
    }
    
    return alerts
  }
  
  // 与云端同步
  private async syncWithCloud() {
    try {
      // 获取未同步的数据
      const unsyncedData = this.dataBuffer.filter(data => data.processed)
      
      if (unsyncedData.length > 0) {
        console.log(`同步${unsyncedData.length}条数据到云端`)
        
        // 调用API同步数据到云端
        const response = await apiWithRetry.post('/edge-devices/sync', {
          data: unsyncedData.map(data => ({
            id: data.id,
            device_id: data.deviceId,
            type: data.type,
            payload: data.payload,
            timestamp: data.timestamp,
            metadata: data.metadata
          }))
        })
        
        if (response.data?.success) {
          // 移除已同步的数据
          this.dataBuffer = this.dataBuffer.filter(data => !data.processed)
          console.log('数据同步成功')
        } else {
          console.warn('数据同步失败:', response.data?.error || '未知错误')
          // 失败时静默处理，保留数据待下次同步
        }
      }
      
      // 更新最后同步时间
      this.lastSyncTime.value = new Date()
      
    } catch (error) {
      console.warn('云端同步失败:', error)
      // 异常时静默处理，保留数据待下次同步
    }
  }
  
  // 备用云端同步方法（当API请求失败时）
  private async fallbackCloudSync(data: EdgeData[]): Promise<void> {
    console.warn('云端同步失败，无法连接到API服务，数据将保留待下次同步')
    // 静默处理，不抛出错误，保留数据待下次同步
  }
  
  // 清理旧数据
  private cleanupOldData() {
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - this.config.dataRetentionDays)
    
    // 清理旧的数据缓冲
    this.dataBuffer = this.dataBuffer.filter(data => data.timestamp > cutoffDate)
    
    // 清理旧的任务
    this.tasks.forEach((task, id) => {
      if (task.createdAt < cutoffDate) {
        this.tasks.delete(id)
      }
    })
  }
  
  // 获取设备列表
  public getDevices(): EdgeDevice[] {
    return Array.from(this.devices.values())
  }
  
  // 获取任务列表
  public getTasks(): EdgeTask[] {
    return Array.from(this.tasks.values())
  }
  
  // 获取数据缓冲
  public getDataBuffer(): EdgeData[] {
    return [...this.dataBuffer]
  }
  
  // 获取配置
  public getConfig(): EdgeConfig {
    return { ...this.config }
  }
  
  // 更新配置
  public updateConfig(newConfig: Partial<EdgeConfig>) {
    Object.assign(this.config, newConfig)
    this.saveConfiguration()
    
    // 重启同步定时器
    this.startSyncTimer()
  }
  
  // 获取服务状态
  public getServiceStatus() {
    return {
      isRunning: this.isRunning.value,
      deviceCount: this.devices.size,
      activeTaskCount: Array.from(this.tasks.values()).filter(task => task.status === 'running').length,
      dataBufferSize: this.dataBuffer.length,
      lastSyncTime: this.lastSyncTime.value
    }
  }
  
  // 手动执行任务
  public async executeTask(taskConfig: Partial<EdgeTask>): Promise<EdgeTask> {
    // 创建任务对象
    const task: EdgeTask = {
      id: `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: taskConfig.name || '手动任务',
      type: taskConfig.type || 'data_processing',
      priority: taskConfig.priority || 'medium',
      status: 'running',
      deviceId: taskConfig.deviceId || '',
      config: taskConfig.config || {},
      createdAt: new Date()
    }
    
    // 添加到任务列表
    this.tasks.set(task.id, task)
    
    try {
      // 调用API执行任务
      const response = await apiWithRetry.post('/edge-devices/tasks', {
        task: {
          id: task.id,
          name: task.name,
          type: task.type,
          priority: task.priority,
          device_id: task.deviceId,
          config: task.config
        }
      })
      
      if (response.data?.success) {
        // 更新任务状态
        task.status = 'completed'
        task.completedAt = new Date()
        task.result = response.data.data || {
          success: true,
          message: '任务执行成功',
          data: { processed: true }
        }
        console.log('任务执行成功:', task.id)
      } else {
        // 任务执行失败
        task.status = 'failed'
        task.result = {
          success: false,
          error: response.data?.error || '任务执行失败'
        }
        console.error('任务执行失败:', response.data?.error)
        // 失败时尝试备用执行方法
        await this.fallbackTaskExecution(task)
      }
    } catch (error) {
      console.error('任务执行异常:', error)
      task.status = 'failed'
      task.result = {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
      // 异常时尝试备用执行方法
      await this.fallbackTaskExecution(task)
    }
    
    return task
  }
  
  // 备用任务执行方法（当API请求失败时）
  private async fallbackTaskExecution(task: EdgeTask): Promise<void> {
    console.error('任务执行失败，无法连接到API:', task.id)
    task.status = 'failed'
    task.completedAt = new Date()
    task.result = {
      success: false,
      message: '任务执行失败：无法连接到API服务',
      error: 'API_CONNECTION_FAILED'
    }
  }
}

// 创建全局边缘计算服务实例
export const edgeComputingService = new EdgeComputingService()

// 边缘计算管理器
export class EdgeComputingManager {
  private static instance: EdgeComputingManager
  private service: EdgeComputingService
  
  private constructor() {
    this.service = edgeComputingService
  }
  
  public static getInstance(): EdgeComputingManager {
    if (!EdgeComputingManager.instance) {
      EdgeComputingManager.instance = new EdgeComputingManager()
    }
    return EdgeComputingManager.instance
  }
  
  public getService(): EdgeComputingService {
    return this.service
  }
  
  // 初始化边缘计算
  public async initialize(): Promise<void> {
    console.log('边缘计算服务初始化完成')
  }
  
  // 销毁服务
  public destroy(): void {
    this.service.stopService()
  }
}

// 导出默认实例
export default EdgeComputingManager.getInstance()