-- 修复数据库结构问题
USE ev_management;

-- 检查并修复 edge_device_data 表结构
-- 如果表存在但缺少字段，则添加缺少的字段

-- 添加缺少的字段到 edge_device_data 表
ALTER TABLE edge_device_data 
ADD COLUMN IF NOT EXISTS text_value TEXT AFTER value,
ADD COLUMN IF NOT EXISTS json_value JSON AFTER text_value;

-- 检查并修复 users 表结构，确保与认证代码兼容
-- 添加认证相关字段
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS user_id VARCHAR(50) UNIQUE AFTER id,
ADD COLUMN IF NOT EXISTS username VARCHAR(100) AFTER name,
ADD COLUMN IF NOT EXISTS password_hash VARCHAR(255) AFTER username,
ADD COLUMN IF NOT EXISTS role ENUM('admin', 'user', 'fleet_manager') DEFAULT 'user' AFTER user_type,
ADD COLUMN IF NOT EXISTS last_login TIMESTAMP NULL AFTER status;

-- 更新现有用户数据，确保兼容性
UPDATE users SET 
    user_id = CONCAT('user_', UNIX_TIMESTAMP(), '_', SUBSTRING(MD5(RAND()), 1, 8)),
    username = name,
    password_hash = '$2b$10$defaulthashforexistingusers',
    role = CASE 
        WHEN user_type = 'enterprise' THEN 'admin'
        WHEN user_type = 'fleet' THEN 'fleet_manager'
        ELSE 'user'
    END
WHERE user_id IS NULL;

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_user_id ON users(user_id);
CREATE INDEX IF NOT EXISTS idx_username ON users(username);

-- 显示修复结果
SELECT 'Database structure fixed successfully!' as message;

-- 显示表结构确认
DESCRIBE edge_device_data;
DESCRIBE users;