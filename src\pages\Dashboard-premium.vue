<template>
  <div class="dashboard-container">
    <!-- 顶部欢迎区域 -->
    <div class="welcome-section">
      <div class="welcome-content">
        <div class="welcome-text">
          <h1 class="welcome-title">
            <span class="gradient-text">EVAdmin Pro</span>
            <span class="title-badge">智能管理平台</span>
          </h1>
          <p class="welcome-subtitle">
            欢迎回来，{{ currentUser }}！今天是 {{ currentDate }}
          </p>
          <div class="quick-stats">
            <div class="quick-stat-item">
              <span class="quick-stat-value">{{ totalVehicles }}</span>
              <span class="quick-stat-label">在线车辆</span>
            </div>
            <div class="quick-stat-item">
              <span class="quick-stat-value">{{ totalStations }}</span>
              <span class="quick-stat-label">充电站</span>
            </div>
            <div class="quick-stat-item">
              <span class="quick-stat-value">{{ systemHealth }}%</span>
              <span class="quick-stat-label">系统健康度</span>
            </div>
          </div>
        </div>
        <div class="welcome-actions">
          <el-button type="primary" size="large" class="action-btn">
            <el-icon><Plus /></el-icon>
            快速添加
          </el-button>
          <el-button size="large" class="action-btn" @click="refreshData">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </div>
      <div class="welcome-visual">
        <div class="floating-cards">
          <div class="floating-card card-1">
            <el-icon><Odometer /></el-icon>
          </div>
          <div class="floating-card card-2">
            <el-icon><Lightning /></el-icon>
          </div>
          <div class="floating-card card-3">
            <el-icon><TrendCharts /></el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 核心指标卡片 -->
    <div class="metrics-section">
      <div class="metrics-grid">
        <div class="metric-card primary">
          <div class="metric-header">
            <div class="metric-icon">
              <el-icon><Odometer /></el-icon>
            </div>
            <div class="metric-trend">
              <el-icon class="trend-icon up"><TrendCharts /></el-icon>
              <span class="trend-value">+12.5%</span>
            </div>
          </div>
          <div class="metric-content">
            <h3 class="metric-title">在线车辆</h3>
            <div class="metric-value">{{ formatNumber(totalVehicles) }}</div>
            <p class="metric-subtitle">较昨日增长 {{ vehicleGrowth }}%</p>
          </div>
          <div class="metric-chart">
            <div class="mini-chart">
              <div class="chart-bar" v-for="(value, index) in vehicleChartData" :key="index" 
                   :style="{ height: value + '%' }"></div>
            </div>
          </div>
        </div>

        <div class="metric-card success">
          <div class="metric-header">
            <div class="metric-icon">
              <el-icon><Lightning /></el-icon>
            </div>
            <div class="metric-trend">
              <el-icon class="trend-icon up"><TrendCharts /></el-icon>
              <span class="trend-value">+8.3%</span>
            </div>
          </div>
          <div class="metric-content">
            <h3 class="metric-title">充电站点</h3>
            <div class="metric-value">{{ totalStations }}</div>
            <p class="metric-subtitle">平均使用率 {{ stationUsage }}%</p>
          </div>
          <div class="metric-progress">
            <el-progress :percentage="stationUsage" :show-text="false" 
                        stroke-width="6" color="#10b981" />
          </div>
        </div>

        <div class="metric-card warning">
          <div class="metric-header">
            <div class="metric-icon">
              <el-icon><Odometer /></el-icon>
            </div>
            <div class="metric-trend">
              <el-icon class="trend-icon up"><TrendCharts /></el-icon>
              <span class="trend-value">+15.2%</span>
            </div>
          </div>
          <div class="metric-content">
            <h3 class="metric-title">总行驶里程</h3>
            <div class="metric-value">{{ formatMileage(totalMileage) }}</div>
            <p class="metric-subtitle">本月新增 {{ monthlyMileage }} 万公里</p>
          </div>
          <div class="metric-chart">
            <div class="progress-ring">
              <svg class="progress-ring-svg" width="60" height="60">
                <circle class="progress-ring-circle-bg" cx="30" cy="30" r="25" />
                <circle class="progress-ring-circle" cx="30" cy="30" r="25" 
                        :stroke-dasharray="circumference" 
                        :stroke-dashoffset="circumference - (mileageProgress / 100) * circumference" />
              </svg>
              <div class="progress-text">{{ mileageProgress }}%</div>
            </div>
          </div>
        </div>

        <div class="metric-card info">
          <div class="metric-header">
            <div class="metric-icon">
              <el-icon><Sunny /></el-icon>
            </div>
            <div class="metric-trend">
              <el-icon class="trend-icon up"><TrendCharts /></el-icon>
              <span class="trend-value">+22.1%</span>
            </div>
          </div>
          <div class="metric-content">
            <h3 class="metric-title">碳减排量</h3>
            <div class="metric-value">{{ carbonReduction }}</div>
            <p class="metric-subtitle">相当于种植 {{ treesEquivalent }} 棵树</p>
          </div>
          <div class="metric-visual">
            <div class="carbon-visual">
              <el-icon class="carbon-icon"><Sunny /></el-icon>
              <div class="carbon-particles">
                <div class="particle" v-for="i in 6" :key="i"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 智能功能模块 -->
    <div class="features-section">
      <div class="section-header">
        <h2 class="section-title">
          <el-icon class="title-icon"><Grid /></el-icon>
          智能功能中心
        </h2>
        <el-button text type="primary">查看全部</el-button>
      </div>
      
      <div class="features-grid">
        <router-link to="/vehicle-network" class="feature-card vehicle">
          <div class="feature-background">
            <div class="feature-pattern"></div>
          </div>
          <div class="feature-content">
            <div class="feature-header">
              <div class="feature-icon">
                <el-icon><DataBoard /></el-icon>
              </div>
              <div class="feature-status">
                <el-badge :value="activeVehicles" class="status-badge" />
              </div>
            </div>
            <h3 class="feature-title">车辆网络监控</h3>
            <p class="feature-desc">实时监控车辆状态、位置追踪、性能分析</p>
            <div class="feature-stats">
              <div class="stat-item">
                <span class="stat-value">{{ activeVehicles }}</span>
                <span class="stat-label">在线</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ offlineVehicles }}</span>
                <span class="stat-label">离线</span>
              </div>
            </div>
          </div>
          <div class="feature-hover-effect"></div>
        </router-link>

        <router-link to="/battery-management" class="feature-card battery">
          <div class="feature-background">
            <div class="feature-pattern"></div>
          </div>
          <div class="feature-content">
            <div class="feature-header">
              <div class="feature-icon">
                <el-icon><Lightning /></el-icon>
              </div>
              <div class="feature-status">
                <el-badge :value="batteryAlerts" type="warning" class="status-badge" />
              </div>
            </div>
            <h3 class="feature-title">电池健康管理</h3>
            <p class="feature-desc">电池健康度监控、寿命预测、维护提醒</p>
            <div class="feature-stats">
              <div class="stat-item">
                <span class="stat-value">{{ avgBatteryHealth }}%</span>
                <span class="stat-label">平均健康度</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ batteryAlerts }}</span>
                <span class="stat-label">预警</span>
              </div>
            </div>
          </div>
          <div class="feature-hover-effect"></div>
        </router-link>

        <router-link to="/charging-service" class="feature-card charging">
          <div class="feature-background">
            <div class="feature-pattern"></div>
          </div>
          <div class="feature-content">
            <div class="feature-header">
              <div class="feature-icon">
                <el-icon><Lightning /></el-icon>
              </div>
              <div class="feature-status">
                <el-badge :value="chargingStations" type="success" class="status-badge" />
              </div>
            </div>
            <h3 class="feature-title">充电服务网络</h3>
            <p class="feature-desc">充电站管理、预约服务、收费统计</p>
            <div class="feature-stats">
              <div class="stat-item">
                <span class="stat-value">{{ chargingStations }}</span>
                <span class="stat-label">站点</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ avgUsageRate }}%</span>
                <span class="stat-label">使用率</span>
              </div>
            </div>
          </div>
          <div class="feature-hover-effect"></div>
        </router-link>

        <router-link to="/ai-assistant" class="feature-card ai">
          <div class="feature-background">
            <div class="feature-pattern"></div>
          </div>
          <div class="feature-content">
            <div class="feature-header">
              <div class="feature-icon">
                <el-icon><ChatDotRound /></el-icon>
              </div>
              <div class="feature-status">
                <el-badge value="AI" type="info" class="status-badge" />
              </div>
            </div>
            <h3 class="feature-title">AI智能助手</h3>
            <p class="feature-desc">智能分析、预测维护、优化建议</p>
            <div class="feature-stats">
              <div class="stat-item">
                <span class="stat-value">{{ aiRecommendations }}</span>
                <span class="stat-label">建议</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ aiAccuracy }}%</span>
                <span class="stat-label">准确率</span>
              </div>
            </div>
          </div>
          <div class="feature-hover-effect"></div>
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import {
  Plus, Refresh, Odometer, Lightning, Sunny, TrendCharts,
  DataBoard, ChatDotRound, Grid, MoreFilled, Warning,
  SuccessFilled, InfoFilled
} from '@element-plus/icons-vue'

// 响应式数据
const currentUser = ref('管理员')
const currentDate = ref('')
const timeRange = ref('today')

// 核心指标数据
const totalVehicles = ref(1234)
const totalStations = ref(89)
const systemHealth = ref(96)
const vehicleGrowth = ref(12.5)
const stationUsage = ref(78)
const totalMileage = ref(456000)
const monthlyMileage = ref(12.8)
const carbonReduction = ref('2.3吨')
const treesEquivalent = ref(156)

// 功能模块数据
const activeVehicles = ref(1156)
const offlineVehicles = ref(78)
const batteryAlerts = ref(3)
const avgBatteryHealth = ref(94)
const chargingStations = ref(89)
const avgUsageRate = ref(78)
const aiRecommendations = ref(12)
const aiAccuracy = ref(96)

// 系统性能数据
const cpuUsage = ref(45)
const memoryUsage = ref(62)
const networkLatency = ref(28)

// 计算属性
const circumference = computed(() => 2 * Math.PI * 25)
const mileageProgress = computed(() => Math.round((monthlyMileage.value / 20) * 100))

const vehicleChartData = ref([60, 75, 45, 80, 65, 90, 70])

const systemStatus = computed(() => {
  const health = systemHealth.value
  if (health >= 90) return { type: 'success', text: '运行良好' }
  if (health >= 70) return { type: 'warning', text: '需要关注' }
  return { type: 'danger', text: '需要维护' }
})

// 方法
const formatNumber = (num: number) => {
  return num.toLocaleString()
}

const formatMileage = (num: number) => {
  return (num / 10000).toFixed(1) + '万公里'
}

const refreshData = () => {
  // 模拟数据刷新
  totalVehicles.value = Math.floor(Math.random() * 100) + 1200
  systemHealth.value = Math.floor(Math.random() * 10) + 90
  cpuUsage.value = Math.floor(Math.random() * 30) + 30
  memoryUsage.value = Math.floor(Math.random() * 40) + 40
  networkLatency.value = Math.floor(Math.random() * 20) + 20
}

const updateDateTime = () => {
  const now = new Date()
  currentDate.value = now.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  })
}

// 生命周期
onMounted(() => {
  updateDateTime()
  const timer = setInterval(updateDateTime, 60000) // 每分钟更新时间

  onUnmounted(() => {
    clearInterval(timer)
  })
})
</script>

<style scoped>
/* 全局容器样式 */
.dashboard-container {
  padding: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
}

.dashboard-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
  pointer-events: none;
}

/* 欢迎区域样式 */
.welcome-section {
  padding: 40px 32px 60px;
  position: relative;
  z-index: 1;
}

.welcome-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.welcome-text {
  flex: 1;
  color: white;
}

.welcome-title {
  font-size: 48px;
  font-weight: 800;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
  gap: 16px;
}

.gradient-text {
  background: linear-gradient(45deg, #fff, #e0e7ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.title-badge {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.welcome-subtitle {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0 0 32px 0;
  font-weight: 300;
}

.quick-stats {
  display: flex;
  gap: 32px;
}

.quick-stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.quick-stat-value {
  font-size: 32px;
  font-weight: 700;
  color: white;
  margin-bottom: 4px;
}

.quick-stat-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
}

.welcome-actions {
  display: flex;
  gap: 16px;
  margin-top: 32px;
}

.action-btn {
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 600;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.welcome-visual {
  position: relative;
  width: 300px;
  height: 300px;
}

.floating-cards {
  position: relative;
  width: 100%;
  height: 100%;
}

.floating-card {
  position: absolute;
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: float 6s ease-in-out infinite;
}

.floating-card.card-1 {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.floating-card.card-2 {
  top: 50%;
  right: 10%;
  animation-delay: 2s;
}

.floating-card.card-3 {
  bottom: 20%;
  left: 30%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(5deg); }
}

/* 指标卡片样式 */
.metrics-section {
  padding: 0 32px 40px;
  position: relative;
  z-index: 1;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.metric-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 32px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2);
}

.metric-card.primary::before { background: linear-gradient(90deg, #667eea, #764ba2); }
.metric-card.success::before { background: linear-gradient(90deg, #10b981, #059669); }
.metric-card.warning::before { background: linear-gradient(90deg, #f59e0b, #d97706); }
.metric-card.info::before { background: linear-gradient(90deg, #3b82f6, #1d4ed8); }

.metric-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.metric-icon {
  width: 56px;
  height: 56px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  color: white;
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.metric-card.success .metric-icon { background: linear-gradient(135deg, #10b981, #059669); }
.metric-card.warning .metric-icon { background: linear-gradient(135deg, #f59e0b, #d97706); }
.metric-card.info .metric-icon { background: linear-gradient(135deg, #3b82f6, #1d4ed8); }

.metric-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  background: rgba(16, 185, 129, 0.1);
  border-radius: 20px;
  color: #059669;
  font-size: 14px;
  font-weight: 600;
}

.trend-icon {
  font-size: 16px;
}

.trend-icon.up {
  color: #059669;
}

.metric-content {
  margin-bottom: 24px;
}

.metric-title {
  font-size: 16px;
  color: #6b7280;
  margin: 0 0 8px 0;
  font-weight: 500;
}

.metric-value {
  font-size: 36px;
  font-weight: 800;
  color: #111827;
  margin: 0 0 8px 0;
}

.metric-subtitle {
  font-size: 14px;
  color: #9ca3af;
  margin: 0;
}

.metric-chart {
  height: 60px;
  display: flex;
  align-items: end;
}

.mini-chart {
  display: flex;
  align-items: end;
  gap: 4px;
  width: 100%;
  height: 100%;
}

.chart-bar {
  flex: 1;
  background: linear-gradient(to top, #e5e7eb, #667eea);
  border-radius: 2px;
  min-height: 8px;
  transition: all 0.3s ease;
}

.chart-bar:hover {
  background: linear-gradient(to top, #667eea, #764ba2);
}

.metric-progress {
  margin-top: 16px;
}

.progress-ring {
  position: relative;
  width: 60px;
  height: 60px;
}

.progress-ring-svg {
  transform: rotate(-90deg);
}

.progress-ring-circle-bg {
  fill: none;
  stroke: #e5e7eb;
  stroke-width: 4;
}

.progress-ring-circle {
  fill: none;
  stroke: #667eea;
  stroke-width: 4;
  stroke-linecap: round;
  transition: stroke-dashoffset 0.5s ease;
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 12px;
  font-weight: 600;
  color: #374151;
}

.carbon-visual {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60px;
}

.carbon-icon {
  font-size: 32px;
  color: #f59e0b;
  z-index: 2;
}

.carbon-particles {
  position: absolute;
  width: 100%;
  height: 100%;
}

.particle {
  position: absolute;
  width: 6px;
  height: 6px;
  background: #fbbf24;
  border-radius: 50%;
  animation: particle-float 3s ease-in-out infinite;
}

.particle:nth-child(1) { top: 10%; left: 20%; animation-delay: 0s; }
.particle:nth-child(2) { top: 30%; right: 15%; animation-delay: 0.5s; }
.particle:nth-child(3) { bottom: 20%; left: 30%; animation-delay: 1s; }
.particle:nth-child(4) { bottom: 30%; right: 25%; animation-delay: 1.5s; }
.particle:nth-child(5) { top: 50%; left: 10%; animation-delay: 2s; }
.particle:nth-child(6) { top: 70%; right: 10%; animation-delay: 2.5s; }

@keyframes particle-float {
  0%, 100% { transform: translateY(0px) scale(1); opacity: 0.7; }
  50% { transform: translateY(-15px) scale(1.2); opacity: 1; }
}

/* 功能模块样式 */
.features-section {
  padding: 40px 32px;
  position: relative;
  z-index: 1;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  max-width: 1400px;
  margin-left: auto;
  margin-right: auto;
}

.section-title {
  font-size: 28px;
  font-weight: 700;
  color: white;
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0;
}

.title-icon {
  font-size: 32px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.feature-card {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 32px;
  text-decoration: none;
  color: inherit;
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.4s ease;
  overflow: hidden;
  min-height: 280px;
}

.feature-card:hover {
  transform: translateY(-12px) scale(1.02);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  text-decoration: none;
  color: inherit;
}

.feature-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.1;
  transition: opacity 0.3s ease;
}

.feature-card:hover .feature-background {
  opacity: 0.2;
}

.feature-pattern {
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 25% 25%, #667eea 2px, transparent 2px),
    radial-gradient(circle at 75% 75%, #764ba2 2px, transparent 2px);
  background-size: 40px 40px;
}

.feature-content {
  position: relative;
  z-index: 2;
}

.feature-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.feature-icon {
  width: 64px;
  height: 64px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  color: white;
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.feature-card.vehicle .feature-icon { background: linear-gradient(135deg, #667eea, #764ba2); }
.feature-card.battery .feature-icon { background: linear-gradient(135deg, #10b981, #059669); }
.feature-card.charging .feature-icon { background: linear-gradient(135deg, #f59e0b, #d97706); }
.feature-card.ai .feature-icon { background: linear-gradient(135deg, #3b82f6, #1d4ed8); }

.status-badge {
  transform: scale(0.9);
}

.feature-title {
  font-size: 20px;
  font-weight: 700;
  color: #111827;
  margin: 0 0 12px 0;
}

.feature-desc {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 24px 0;
  line-height: 1.6;
}

.feature-stats {
  display: flex;
  gap: 24px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #111827;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #9ca3af;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.feature-hover-effect {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.feature-card:hover .feature-hover-effect {
  opacity: 1;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .welcome-content {
    flex-direction: column;
    text-align: center;
    gap: 32px;
  }

  .welcome-visual {
    width: 200px;
    height: 200px;
  }

  .floating-card {
    width: 60px;
    height: 60px;
    font-size: 24px;
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: 0;
  }

  .welcome-section {
    padding: 24px 16px 40px;
  }

  .welcome-title {
    font-size: 32px;
    flex-direction: column;
    gap: 8px;
  }

  .quick-stats {
    flex-direction: column;
    gap: 16px;
  }

  .metrics-section {
    padding: 0 16px 24px;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .features-section {
    padding: 24px 16px;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .section-title {
    font-size: 24px;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.metric-card,
.feature-card {
  animation: fadeInUp 0.6s ease forwards;
}

.metric-card:nth-child(1) { animation-delay: 0.1s; }
.metric-card:nth-child(2) { animation-delay: 0.2s; }
.metric-card:nth-child(3) { animation-delay: 0.3s; }
.metric-card:nth-child(4) { animation-delay: 0.4s; }

.feature-card:nth-child(1) { animation-delay: 0.5s; }
.feature-card:nth-child(2) { animation-delay: 0.6s; }
.feature-card:nth-child(3) { animation-delay: 0.7s; }
.feature-card:nth-child(4) { animation-delay: 0.8s; }
</style>
