const mysql = require('mysql2/promise');

async function testDirectQuery() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: '123456',
    database: 'ev_management'
  });
  
  try {
    const query = '电池健康度';
    console.log('测试查询:', query);
    
    const [rows] = await connection.execute(`
      SELECT question, answer, category, keywords
      FROM ai_knowledge_base 
      WHERE is_active = TRUE 
      AND (
        question LIKE CONCAT('%', ?, '%')
        OR answer LIKE CONCAT('%', ?, '%')
        OR keywords LIKE CONCAT('%', ?, '%')
      )
      ORDER BY priority DESC, id DESC
      LIMIT 3
    `, [query, query, query]);
    
    console.log('查询结果数量:', rows.length);
    rows.forEach((row, index) => {
      console.log(`${index + 1}. [${row.category}] ${row.question}`);
      console.log(`   关键词: ${row.keywords}`);
      console.log(`   答案: ${row.answer.substring(0, 50)}...\n`);
    });
    
    // 测试更简单的查询
    console.log('\n测试简单查询...');
    const [allRows] = await connection.execute('SELECT COUNT(*) as total FROM ai_knowledge_base WHERE is_active = TRUE');
    console.log('知识库总数:', allRows[0].total);
    
    // 查看所有知识库条目
    console.log('\n所有知识库条目:');
    const [allKnowledge] = await connection.execute('SELECT question, keywords FROM ai_knowledge_base WHERE is_active = TRUE LIMIT 5');
    allKnowledge.forEach((row, index) => {
      console.log(`${index + 1}. ${row.question}`);
      console.log(`   关键词: ${row.keywords}\n`);
    });
    
  } catch (error) {
    console.error('查询失败:', error.message);
  } finally {
    await connection.end();
  }
}

testDirectQuery();
