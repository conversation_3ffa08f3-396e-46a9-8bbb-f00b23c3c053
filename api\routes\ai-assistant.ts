import express from 'express';
import type { Request, Response } from 'express';
import { db } from '../config/mysql';

const router = express.Router();

// AI助手知识库数据
const systemKnowledge = {
  // 系统功能介绍
  functions: {
    '电池管理': {
      description: '智能电池管理系统可以实时监控电池状态、健康度、温度等关键参数',
      features: ['实时监控', '健康度分析', '预警提醒', '充电优化建议'],
      usage: '在仪表板或电池管理页面查看详细信息'
    },
    '充电服务': {
      description: '智能充电服务提供充电站查找、预约、支付等一站式服务',
      features: ['充电站地图', '实时状态', '预约充电', '智能支付'],
      usage: '通过充电服务页面查找附近充电站并进行预约'
    },
    '驾驶辅助': {
      description: 'ADAS高级驾驶辅助系统提供安全驾驶支持',
      features: ['碰撞预警', '车道保持', '自适应巡航', '盲点监测'],
      usage: '在ADAS系统页面配置和查看辅助功能状态'
    },
    '车联网': {
      description: '车联网交互中心实现车辆与云端的智能连接',
      features: ['远程控制', '实时定位', '故障诊断', 'OTA升级'],
      usage: '通过车联网页面远程监控和控制车辆'
    },
    '用户生态': {
      description: '用户生态服务提供个性化体验和社区互动',
      features: ['个人档案', '驾驶行为分析', '碳积分系统', '社区互动'],
      usage: '在用户生态页面管理个人信息和查看积分'
    },
    '系统安全': {
      description: '系统安全运维确保数据安全和系统稳定',
      features: ['安全监控', '访问控制', '数据加密', '审计日志'],
      usage: '管理员可在系统安全页面查看安全状态'
    }
  },
  
  // 常见问题解答
  faq: {
    '如何查看电池状态': {
      answer: '您可以通过以下方式查看电池状态：\n1. 在仪表板页面查看电池健康度卡片\n2. 进入"智能电池管理"页面查看详细信息\n3. 电池状态包括：当前电量、健康度、温度、充电状态等',
      category: '电池管理'
    },
    '充电站在哪里': {
      answer: '查找充电站的方法：\n1. 点击"智能充电服务"进入充电页面\n2. 使用地图功能查看附近充电站\n3. 可以按距离、价格、充电速度筛选\n4. 支持实时查看充电桩状态和预约功能',
      category: '充电服务'
    },
    '如何设置充电计划': {
      answer: '设置充电计划步骤：\n1. 进入"智能充电服务"页面\n2. 点击"充电计划"功能\n3. 设置充电时间、目标电量\n4. 选择充电模式（快充/慢充）\n5. 系统会根据电价和电池状态优化充电策略',
      category: '充电服务'
    },
    '车辆保养提醒': {
      answer: '车辆保养提醒功能：\n1. 系统会根据里程和时间自动提醒保养\n2. 在仪表板可查看下次保养时间\n3. 支持设置保养提醒周期\n4. 可预约附近的服务网点',
      category: '车辆管理'
    },
    '碳积分如何获得': {
      answer: '获得碳积分的方式：\n1. 绿色出行：使用电动车代替燃油车\n2. 节能驾驶：保持良好的驾驶习惯\n3. 充电优化：在低碳时段充电\n4. 参与活动：完成系统推荐的环保任务\n5. 积分可用于兑换优惠券和礼品',
      category: '用户生态'
    },
    '系统功能介绍': {
      answer: '新能源汽车智能综合管理系统主要功能：\n1. 智能电池管理 - 电池监控和优化\n2. 智能充电服务 - 充电站查找和预约\n3. 高级驾驶辅助 - ADAS安全功能\n4. 车联网交互 - 远程控制和监控\n5. 用户生态服务 - 个性化体验\n6. 系统安全运维 - 数据安全保障',
      category: '系统介绍'
    }
  },
  
  // 操作指南
  guides: {
    '登录系统': '使用注册的用户名和密码登录，支持记住登录状态',
    '查看仪表板': '登录后默认进入仪表板，显示车辆状态、电池信息等关键数据',
    '切换主题': '点击右上角的主题切换按钮，支持明暗主题切换',
    '个人设置': '点击用户头像进入个人中心，可修改个人信息和偏好设置'
  }
};

// 智能问答逻辑
class AIAssistant {
  private findBestMatch(question: string): any {
    const lowerQuestion = question.toLowerCase();
    
    // 检查FAQ中的匹配
    for (const [key, value] of Object.entries(systemKnowledge.faq)) {
      if (lowerQuestion.includes(key.toLowerCase()) || 
          this.calculateSimilarity(lowerQuestion, key.toLowerCase()) > 0.6) {
        return {
          type: 'faq',
          content: value,
          confidence: 0.9
        };
      }
    }
    
    // 检查功能介绍中的匹配
    for (const [key, value] of Object.entries(systemKnowledge.functions)) {
      if (lowerQuestion.includes(key) || 
          lowerQuestion.includes(value.description.toLowerCase())) {
        return {
          type: 'function',
          content: value,
          functionName: key,
          confidence: 0.8
        };
      }
    }
    
    // 检查操作指南中的匹配
    for (const [key, value] of Object.entries(systemKnowledge.guides)) {
      if (lowerQuestion.includes(key.toLowerCase())) {
        return {
          type: 'guide',
          content: value,
          confidence: 0.7
        };
      }
    }
    
    return null;
  }
  
  private calculateSimilarity(str1: string, str2: string): number {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;
    
    if (longer.length === 0) return 1.0;
    
    const editDistance = this.levenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }
  
  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = [];
    
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }
    
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }
    
    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }
    
    return matrix[str2.length][str1.length];
  }
  
  public generateResponse(question: string, context: any[] = []): string {
    // 处理问候语
    const greetings = ['你好', '您好', 'hello', 'hi', '嗨'];
    if (greetings.some(greeting => question.toLowerCase().includes(greeting))) {
      return '您好！我是您的专属AI助手，很高兴为您服务！我可以帮您解答关于新能源汽车管理系统的任何问题。您可以询问系统功能、操作方法或者遇到的问题。';
    }
    
    // 处理感谢语
    const thanks = ['谢谢', '感谢', 'thank'];
    if (thanks.some(thank => question.toLowerCase().includes(thank))) {
      return '不客气！很高兴能帮助到您。如果您还有其他问题，随时可以问我哦！';
    }
    
    // 查找最佳匹配
    const match = this.findBestMatch(question);
    
    if (match && match.confidence > 0.6) {
      switch (match.type) {
        case 'faq':
          return `${match.content.answer}\n\n💡 如果您需要更多帮助，可以继续询问相关问题。`;
          
        case 'function':
          return `**${match.functionName}功能介绍**\n\n${match.content.description}\n\n**主要特性：**\n${match.content.features.map((f: string) => `• ${f}`).join('\n')}\n\n**使用方法：**\n${match.content.usage}\n\n💡 您可以直接点击导航菜单中的"${match.functionName}"来使用此功能。`;
          
        case 'guide':
          return `**操作指南**\n\n${match.content}\n\n💡 如果您在操作过程中遇到问题，可以随时询问我。`;
      }
    }
    
    // 处理系统状态查询
    if (question.includes('状态') || question.includes('运行') || question.includes('正常')) {
      return '系统当前运行状态良好！\n\n**系统概况：**\n• 所有核心服务正常运行\n• 数据库连接稳定\n• API响应正常\n• 用户体验良好\n\n如果您遇到任何问题，请具体描述，我会帮您解决。';
    }
    
    // 处理帮助请求
    if (question.includes('帮助') || question.includes('help') || question.includes('怎么') || question.includes('如何')) {
      return '我可以帮助您解决以下问题：\n\n**🔋 电池管理**\n• 电池状态查询\n• 健康度分析\n• 充电建议\n\n**⚡ 充电服务**\n• 充电站查找\n• 预约充电\n• 充电计划设置\n\n**🚗 车辆管理**\n• 车辆状态监控\n• 保养提醒\n• 远程控制\n\n**🌱 用户生态**\n• 碳积分查询\n• 个人档案管理\n• 驾驶行为分析\n\n请告诉我您具体需要什么帮助？';
    }
    
    // 默认回复
    return '抱歉，我没有完全理解您的问题。不过我可以帮您解答关于新能源汽车管理系统的各种问题！\n\n您可以询问：\n• 系统功能使用方法\n• 电池和充电相关问题\n• 车辆管理和监控\n• 个人账户和设置\n• 碳积分和生态服务\n\n请尝试更具体地描述您的问题，我会尽力为您解答。';
  }
}

const aiAssistant = new AIAssistant();

// AI聊天接口
router.post('/chat', async (req: Request, res: Response) => {
  try {
    const { message, context = [] } = req.body;
    
    if (!message || typeof message !== 'string') {
      return res.status(400).json({
        success: false,
        message: '消息内容不能为空'
      });
    }
    
    // 记录用户问题（可选，用于改进AI）
    try {
      await db.query(
        'INSERT INTO ai_chat_logs (user_id, question, timestamp) VALUES (?, ?, NOW())',
        [(req as any).user?.id || 'anonymous', message]
      );
    } catch (error) {
      console.log('记录聊天日志失败:', error);
    }
    
    // 生成AI回复
    const response = aiAssistant.generateResponse(message, context);
    
    // 模拟思考时间（让用户感觉更真实）
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
    
    res.json({
      success: true,
      response: response,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('AI聊天错误:', error);
    res.status(500).json({
      success: false,
      message: 'AI服务暂时不可用，请稍后再试'
    });
  }
});

// AI服务状态检查
router.get('/status', async (req: Request, res: Response) => {
  try {
    // 检查数据库连接
    await db.query('SELECT 1');
    
    res.json({
      success: true,
      status: 'online',
      message: 'AI助手服务正常运行',
      timestamp: new Date().toISOString(),
      capabilities: [
        '系统功能咨询',
        '操作指导',
        '问题解答',
        '智能建议'
      ]
    });
    
  } catch (error) {
    console.error('AI状态检查错误:', error);
    res.status(500).json({
      success: false,
      status: 'offline',
      message: 'AI助手服务暂时不可用'
    });
  }
});

// 获取知识库信息
router.get('/knowledge', async (req: Request, res: Response) => {
  try {
    const { category } = req.query;
    
    if (category) {
      // 返回特定分类的知识
      const categoryData = systemKnowledge[category as keyof typeof systemKnowledge];
      if (categoryData) {
        res.json({
          success: true,
          data: categoryData
        });
      } else {
        res.status(404).json({
          success: false,
          message: '未找到指定分类的知识'
        });
      }
    } else {
      // 返回所有知识库概览
      res.json({
        success: true,
        data: {
          functions: Object.keys(systemKnowledge.functions),
          faqCount: Object.keys(systemKnowledge.faq).length,
          guideCount: Object.keys(systemKnowledge.guides).length
        }
      });
    }
    
  } catch (error) {
    console.error('获取知识库错误:', error);
    res.status(500).json({
      success: false,
      message: '获取知识库信息失败'
    });
  }
});

// 获取聊天历史
router.get('/history', async (req: Request, res: Response) => {
  try {
    const userId = (req as any).user?.id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '用户未登录'
      });
    }
    
    const rows = await db.query(
      'SELECT question, timestamp FROM ai_chat_logs WHERE user_id = ? ORDER BY timestamp DESC LIMIT 50',
      [userId]
    );
    
    res.json({
      success: true,
      data: rows
    });
    
  } catch (error) {
    console.error('获取聊天历史错误:', error);
    res.status(500).json({
      success: false,
      message: '获取聊天历史失败'
    });
  }
});

export default router;