<template>
  <div id="app">
    <!-- 直接渲染路由视图，不再嵌套额外的Layout -->
    <router-view />
  </div>
  
  <!-- 全局路由加载动画 -->
  <BikeLoader 
    v-if="loadingStore.isRouteLoading" 
    :fullscreen="true" 
    :text="loadingStore.routeLoadingText" 
  />
</template>

<script setup lang="ts">
import { BikeLoader } from '@/components'
import { useLoadingStore } from '@/stores/loading'

const loadingStore = useLoadingStore()
</script>

<style>
#app {
  min-height: 100vh;
  overflow-x: hidden;
  overflow-y: auto;
}
</style>