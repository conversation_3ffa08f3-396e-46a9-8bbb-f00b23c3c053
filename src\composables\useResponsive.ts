import { ref, computed, onMounted, onUnmounted, readonly, type Ref } from 'vue'
import {
  type Breakpoint,
  type ResponsiveValue,
  type MediaQuery,
  breakpoints,
  mediaQueries,
  getCurrentBreakpoint,
  matchesBreakpoint,
  isMobile,
  isTablet,
  isDesktop,
  getResponsiveValue,
  createMediaQueryListener,
  getOrientation,
  isTouchDevice,
  isHighDPI,
  prefersDarkMode,
  prefersReducedMotion,
  prefersHighContrast
} from '@/utils/responsive'

// 响应式断点组合式函数
export function useBreakpoint() {
  const currentBreakpoint = ref<Breakpoint>(getCurrentBreakpoint())
  const windowWidth = ref(window.innerWidth)
  const windowHeight = ref(window.innerHeight)
  
  let cleanup: (() => void) | null = null
  
  const updateBreakpoint = () => {
    currentBreakpoint.value = getCurrentBreakpoint()
    windowWidth.value = window.innerWidth
    windowHeight.value = window.innerHeight
  }
  
  onMounted(() => {
    const handleResize = () => updateBreakpoint()
    window.addEventListener('resize', handleResize)
    
    cleanup = () => {
      window.removeEventListener('resize', handleResize)
    }
  })
  
  onUnmounted(() => {
    cleanup?.()
  })
  
  // 计算属性
  const isXs = computed(() => currentBreakpoint.value === 'xs')
  const isSm = computed(() => currentBreakpoint.value === 'sm')
  const isMd = computed(() => currentBreakpoint.value === 'md')
  const isLg = computed(() => currentBreakpoint.value === 'lg')
  const isXl = computed(() => currentBreakpoint.value === 'xl')
  const isXxl = computed(() => currentBreakpoint.value === 'xxl')
  
  const isSmUp = computed(() => matchesBreakpoint('sm'))
  const isMdUp = computed(() => matchesBreakpoint('md'))
  const isLgUp = computed(() => matchesBreakpoint('lg'))
  const isXlUp = computed(() => matchesBreakpoint('xl'))
  const isXxlUp = computed(() => matchesBreakpoint('xxl'))
  
  const isMobileDevice = computed(() => isMobile())
  const isTabletDevice = computed(() => isTablet())
  const isDesktopDevice = computed(() => isDesktop())
  
  return {
    // 当前状态
    currentBreakpoint: readonly(currentBreakpoint),
    windowWidth: readonly(windowWidth),
    windowHeight: readonly(windowHeight),
    
    // 精确断点
    isXs,
    isSm,
    isMd,
    isLg,
    isXl,
    isXxl,
    
    // 向上断点
    isSmUp,
    isMdUp,
    isLgUp,
    isXlUp,
    isXxlUp,
    
    // 设备类型
    isMobileDevice,
    isTabletDevice,
    isDesktopDevice
  }
}

// 媒体查询组合式函数
export function useMediaQuery(query: string | MediaQuery) {
  const matches = ref(false)
  let cleanup: (() => void) | null = null
  
  onMounted(() => {
    const queryString = typeof query === 'string' ? query : mediaQueries[query]
    cleanup = createMediaQueryListener(queryString, (isMatch) => {
      matches.value = isMatch
    })
  })
  
  onUnmounted(() => {
    cleanup?.()
  })
  
  return readonly(matches)
}

// 响应式值组合式函数
export function useResponsiveValue<T>(value: ResponsiveValue<T> | T, fallback?: T) {
  const { currentBreakpoint } = useBreakpoint()
  
  return computed(() => {
    if (typeof value !== 'object' || value === null) {
      return value as T
    }
    
    return getResponsiveValue(value as ResponsiveValue<T>, fallback)
  })
}

// 设备特性组合式函数
export function useDeviceFeatures() {
  const orientation = ref(getOrientation())
  const isTouch = ref(isTouchDevice())
  const isHighDPIDevice = ref(isHighDPI())
  const darkMode = ref(prefersDarkMode())
  const reducedMotion = ref(prefersReducedMotion())
  const highContrast = ref(prefersHighContrast())
  
  let cleanupFunctions: Array<() => void> = []
  
  onMounted(() => {
    // 监听方向变化
    const handleOrientationChange = () => {
      orientation.value = getOrientation()
    }
    window.addEventListener('resize', handleOrientationChange)
    cleanupFunctions.push(() => {
      window.removeEventListener('resize', handleOrientationChange)
    })
    
    // 监听暗色模式变化
    cleanupFunctions.push(
      createMediaQueryListener('(prefers-color-scheme: dark)', (matches) => {
        darkMode.value = matches
      })
    )
    
    // 监听减少动画偏好变化
    cleanupFunctions.push(
      createMediaQueryListener('(prefers-reduced-motion: reduce)', (matches) => {
        reducedMotion.value = matches
      })
    )
    
    // 监听高对比度偏好变化
    cleanupFunctions.push(
      createMediaQueryListener('(prefers-contrast: high)', (matches) => {
        highContrast.value = matches
      })
    )
  })
  
  onUnmounted(() => {
    cleanupFunctions.forEach(cleanup => cleanup())
    cleanupFunctions = []
  })
  
  return {
    orientation: readonly(orientation),
    isTouch: readonly(isTouch),
    isHighDPIDevice: readonly(isHighDPIDevice),
    darkMode: readonly(darkMode),
    reducedMotion: readonly(reducedMotion),
    highContrast: readonly(highContrast),
    
    // 便捷计算属性
    isPortrait: computed(() => orientation.value === 'portrait'),
    isLandscape: computed(() => orientation.value === 'landscape')
  }
}

// 响应式网格组合式函数
export function useResponsiveGrid(config: {
  columns?: ResponsiveValue<number>
  gap?: ResponsiveValue<string | number>
  minItemWidth?: ResponsiveValue<string | number>
}) {
  const { currentBreakpoint } = useBreakpoint()
  
  const columns = useResponsiveValue(config.columns || { xs: 1, sm: 2, md: 3, lg: 4 })
  const gap = useResponsiveValue(config.gap || { xs: 16, sm: 20, md: 24 })
  const minItemWidth = useResponsiveValue(config.minItemWidth)
  
  const gridStyles = computed(() => {
    const styles: Record<string, string> = {
      display: 'grid'
    }
    
    if (minItemWidth.value) {
      styles.gridTemplateColumns = `repeat(auto-fit, minmax(${minItemWidth.value}px, 1fr))`
    } else if (columns.value) {
      styles.gridTemplateColumns = `repeat(${columns.value}, 1fr)`
    }
    
    if (gap.value) {
      styles.gap = typeof gap.value === 'number' ? `${gap.value}px` : gap.value
    }
    
    return styles
  })
  
  return {
    columns,
    gap,
    minItemWidth,
    gridStyles
  }
}

// 响应式容器组合式函数
export function useResponsiveContainer(maxWidths?: ResponsiveValue<number>) {
  const { currentBreakpoint } = useBreakpoint()
  
  const defaultMaxWidths = {
    sm: 540,
    md: 720,
    lg: 960,
    xl: 1140,
    xxl: 1320
  }
  
  const maxWidth = useResponsiveValue(maxWidths || defaultMaxWidths)
  
  const containerStyles = computed(() => ({
    width: '100%',
    maxWidth: maxWidth.value ? `${maxWidth.value}px` : 'none',
    marginLeft: 'auto',
    marginRight: 'auto',
    paddingLeft: '16px',
    paddingRight: '16px'
  }))
  
  return {
    maxWidth,
    containerStyles
  }
}

// 响应式字体大小组合式函数
export function useResponsiveFontSize(sizes: ResponsiveValue<number>) {
  const fontSize = useResponsiveValue(sizes)
  
  const fontStyles = computed(() => ({
    fontSize: fontSize.value ? `${fontSize.value}px` : undefined
  }))
  
  return {
    fontSize,
    fontStyles
  }
}

// 响应式间距组合式函数
export function useResponsiveSpacing(spacing: ResponsiveValue<number>) {
  const space = useResponsiveValue(spacing)
  
  const spacingStyles = computed(() => ({
    padding: space.value ? `${space.value}px` : undefined
  }))
  
  const marginStyles = computed(() => ({
    margin: space.value ? `${space.value}px` : undefined
  }))
  
  const gapStyles = computed(() => ({
    gap: space.value ? `${space.value}px` : undefined
  }))
  
  return {
    space,
    spacingStyles,
    marginStyles,
    gapStyles
  }
}

// 响应式可见性组合式函数
export function useResponsiveVisibility(visibility: ResponsiveValue<boolean>) {
  const isVisible = useResponsiveValue(visibility, true)
  
  const visibilityStyles = computed(() => ({
    display: isVisible.value ? undefined : 'none'
  }))
  
  return {
    isVisible,
    visibilityStyles
  }
}

// 响应式类名组合式函数
export function useResponsiveClasses(classes: ResponsiveValue<string | string[]>) {
  const { currentBreakpoint } = useBreakpoint()
  
  const activeClasses = computed(() => {
    const currentClasses = getResponsiveValue(classes)
    if (!currentClasses) return []
    
    return Array.isArray(currentClasses) ? currentClasses : [currentClasses]
  })
  
  return {
    activeClasses
  }
}

// 响应式图片组合式函数
export function useResponsiveImage(sources: ResponsiveValue<{ src: string; width?: number; height?: number }>) {
  const { currentBreakpoint, isHighDPIDevice } = useBreakpoint()
  const { isHighDPIDevice: isHighDPI } = useDeviceFeatures()
  
  const currentSource = useResponsiveValue(sources)
  
  const imageProps = computed(() => {
    if (!currentSource.value) return {}
    
    const { src, width, height } = currentSource.value
    const dpiMultiplier = isHighDPI.value ? 2 : 1
    
    return {
      src,
      width: width ? width * dpiMultiplier : undefined,
      height: height ? height * dpiMultiplier : undefined,
      style: {
        width: width ? `${width}px` : undefined,
        height: height ? `${height}px` : undefined
      }
    }
  })
  
  return {
    currentSource,
    imageProps
  }
}

// 响应式动画组合式函数
export function useResponsiveAnimation() {
  const { reducedMotion } = useDeviceFeatures()
  
  const shouldAnimate = computed(() => !reducedMotion.value)
  
  const animationStyles = computed(() => ({
    transition: shouldAnimate.value ? undefined : 'none',
    animation: shouldAnimate.value ? undefined : 'none'
  }))
  
  return {
    shouldAnimate,
    reducedMotion,
    animationStyles
  }
}

// 导出工具函数
export {
  breakpoints,
  mediaQueries,
  getCurrentBreakpoint,
  matchesBreakpoint,
  isMobile,
  isTablet,
  isDesktop,
  getResponsiveValue
}

// 导出类型
export type {
  Breakpoint,
  ResponsiveValue,
  MediaQuery
}