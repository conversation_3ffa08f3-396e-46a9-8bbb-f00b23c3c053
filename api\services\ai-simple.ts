import fs from 'fs'
import { db } from '../config/mysql'

// 简化的AI服务类 - 仅使用本地知识库
export class SimpleAIService {
  
  // 从知识库搜索相关信息
  async searchKnowledge(query: string): Promise<string> {
    try {
      // 先尝试精确匹配，然后尝试关键词匹配
      let result = await db.query(`
        SELECT question, answer, category
        FROM ai_knowledge_base
        WHERE is_active = TRUE
        AND (
          question LIKE CONCAT('%', ?, '%')
          OR answer LIKE CONCAT('%', ?, '%')
          OR keywords LIKE CONCAT('%', ?, '%')
        )
        ORDER BY priority DESC, id DESC
        LIMIT 3
      `, [query, query, query])

      // 如果没有结果，尝试拆分查询词进行搜索
      if ((!result || (Array.isArray(result) && result.length === 0))) {
        const keywords = query.split(/[？?，,。.\s]+/).filter(word => word.length > 1)

        if (keywords.length > 0) {
          const keywordConditions = keywords.map(() =>
            '(question LIKE CONCAT(\'%\', ?, \'%\') OR answer LIKE CONCAT(\'%\', ?, \'%\') OR keywords LIKE CONCAT(\'%\', ?, \'%\'))'
          ).join(' OR ')

          const keywordParams = keywords.flatMap(keyword => [keyword, keyword, keyword])

          result = await db.query(`
            SELECT question, answer, category
            FROM ai_knowledge_base
            WHERE is_active = TRUE
            AND (${keywordConditions})
            ORDER BY priority DESC, id DESC
            LIMIT 3
          `, keywordParams)
        }
      }

      // MySQL2返回[rows, fields]格式
      let rows;
      if (Array.isArray(result) && result.length >= 2) {
        rows = result[0]; // [rows, fields]格式
      } else if (Array.isArray(result)) {
        rows = result; // 直接是rows数组
      } else {
        rows = [];
      }

      if (Array.isArray(rows) && rows.length > 0) {
        return rows.map((row: any) =>
          `【${row.category}】${row.question}: ${row.answer}`
        ).join('\n\n');
      }

      return ''
    } catch (error) {
      console.error('搜索知识库失败:', error)
      return ''
    }
  }

  // 获取AI助手配置
  async getAIConfig() {
    try {
      const result = await db.query(
        'SELECT * FROM ai_assistant_config WHERE is_active = TRUE ORDER BY id DESC LIMIT 1'
      )
      
      const [rows, fields] = result as any
      
      if (Array.isArray(rows) && rows.length > 0) {
        const config = rows[0]
        return {
          name: config.name,
          fullName: config.full_name,
          description: config.description,
          avatar: config.avatar_url,
          model: config.model,
          temperature: config.temperature,
          maxTokens: config.max_tokens,
          capabilities: config.capabilities ? (
            typeof config.capabilities === 'string' ?
            JSON.parse(config.capabilities) :
            config.capabilities
          ) : [],
          systemPrompt: config.system_prompt
        }
      }
      
      // 返回默认配置
      return {
        name: '小E',
        fullName: 'EVAdmin Pro AI Assistant',
        description: '我是EVAdmin Pro的专属AI助手，专门为新能源汽车管理系统提供智能服务',
        avatar: '/images/ai-avatar.svg',
        model: 'local',
        temperature: 0.7,
        maxTokens: 2000,
        capabilities: [
          '文本对话和问答',
          '文件内容分析',
          '系统功能指导',
          '数据分析建议',
          '故障诊断帮助'
        ],
        systemPrompt: '你是EVAdmin Pro的AI助手'
      }
    } catch (error) {
      console.error('获取AI配置失败:', error)
      return {
        name: '小E',
        fullName: 'EVAdmin Pro AI Assistant',
        description: '我是EVAdmin Pro的专属AI助手',
        avatar: '/images/ai-avatar.svg',
        model: 'local',
        temperature: 0.7,
        maxTokens: 2000,
        capabilities: [],
        systemPrompt: '你是EVAdmin Pro的AI助手'
      }
    }
  }

  // 文本对话 - 使用本地知识库
  async chat(message: string, context: any[] = []): Promise<string> {
    try {
      // 搜索相关知识
      const knowledgeResult = await this.searchKnowledge(message)

      if (knowledgeResult) {
        return `${knowledgeResult}\n\n💡 提示：当前使用本地知识库，如需更智能的回答，请配置OpenAI API密钥。`
      }

      // 如果没有找到相关知识，返回通用回答
      return this.getGenericResponse(message)
    } catch (error) {
      console.error('本地聊天错误:', error)
      return '抱歉，我暂时无法回答您的问题。请稍后再试或联系技术支持。'
    }
  }

  // 分析文件
  async analyzeFile(filePath: string, fileName: string, mimeType: string, question: string): Promise<string> {
    try {
      let fileContent = ''
      
      // 根据文件类型读取内容
      if (mimeType === 'application/pdf') {
        return '抱歉，PDF文件分析功能正在开发中，请上传文本文件。'
      } else if (mimeType.startsWith('text/') || mimeType === 'application/json') {
        fileContent = fs.readFileSync(filePath, 'utf-8')
      } else {
        return `抱歉，暂时不支持分析 ${mimeType} 类型的文件。目前支持的文件类型包括：文本文件(.txt)、JSON文件(.json)。`
      }

      // 搜索相关知识
      const knowledgeResult = await this.searchKnowledge(question)
      
      // 基本文件信息
      const lines = fileContent.split('\n').length
      const words = fileContent.split(/\s+/).length
      
      let analysis = `文件分析结果：

📄 文件信息：
- 文件名：${fileName}
- 文件类型：${mimeType}
- 总行数：${lines} 行
- 总字数：约 ${words} 个词

📝 内容预览：
${fileContent.substring(0, 500)}${fileContent.length > 500 ? '...\n\n（内容已截断，显示前500字符）' : ''}
`

      // 检查是否包含系统相关关键词
      const keywords = ['电池', '充电', '车辆', 'ADAS', '新能源', '管理系统', 'EVAdmin', '系统', '功能', '模块']
      const foundKeywords = keywords.filter(keyword => fileContent.includes(keyword))
      
      if (foundKeywords.length > 0) {
        analysis += `\n🔍 检测到相关关键词：${foundKeywords.join(', ')}`
        analysis += '\n这个文件似乎与新能源汽车管理系统相关。'
      }

      if (knowledgeResult) {
        analysis += `\n\n📚 相关系统知识：\n${knowledgeResult}`
      }
      
      analysis += `\n\n💡 提示：当前使用本地文件分析，如需更智能的分析，请配置OpenAI API密钥。`
      
      return analysis
    } catch (error) {
      console.error('本地文件分析失败:', error)
      return `文件信息：${fileName} (${mimeType})\n\n抱歉，文件分析功能暂时不可用。请稍后再试或联系技术支持。`
    }
  }

  // 分析图片 - 简化版本
  async analyzeImage(imageBase64: string, question: string): Promise<string> {
    return `图片分析功能需要OpenAI API支持。\n\n当前使用本地模式，无法分析图片内容。\n\n如需图片分析功能，请：\n1. 配置OpenAI API密钥\n2. 确保网络连接正常\n3. 重新上传图片进行分析`
  }

  // 通用回答生成
  private getGenericResponse(query: string): string {
    const lowerQuery = query.toLowerCase()
    
    if (lowerQuery.includes('电池') || lowerQuery.includes('battery')) {
      return '关于电池管理，您可以在"智能电池管理"页面查看电池状态、健康度、温度等信息。系统提供实时监控、健康度分析、充电优化建议等功能。如需详细帮助，请查看系统帮助文档或联系技术支持。'
    }
    
    if (lowerQuery.includes('充电') || lowerQuery.includes('charge')) {
      return '关于充电服务，您可以在"智能充电服务"页面查找充电站、预约充电、查看充电记录等。系统提供充电站地图导航、实时状态查看、费用管理等功能。如需详细帮助，请查看系统帮助文档。'
    }
    
    if (lowerQuery.includes('车辆') || lowerQuery.includes('vehicle')) {
      return '关于车辆管理，您可以在"车辆管理"页面查看车辆信息、状态监控、维护记录等。系统提供车辆档案管理、实时监控、性能分析等功能。如需详细帮助，请查看系统帮助文档。'
    }
    
    if (lowerQuery.includes('adas') || lowerQuery.includes('驾驶辅助')) {
      return '关于ADAS高级驾驶辅助系统，您可以在"ADAS系统"页面配置和使用各种辅助功能，包括碰撞预警、车道保持、自适应巡航等。请在安全环境下测试功能。'
    }
    
    if (lowerQuery.includes('系统') || lowerQuery.includes('功能')) {
      return 'EVAdmin Pro是新能源汽车智能综合管理系统，包含以下核心功能：\n\n1. 智能电池管理 - 电池监控和优化\n2. 智能充电服务 - 充电站管理和预约\n3. 车辆管理 - 车辆信息和状态监控\n4. ADAS系统 - 高级驾驶辅助\n5. 车联网交互 - 远程控制和诊断\n6. 用户生态服务 - 个性化服务和社区\n\n您可以通过左侧导航菜单访问各个功能模块。'
    }
    
    return '您好！我是EVAdmin Pro的AI助手小E。我可以帮您解答关于新能源汽车管理系统的问题。请尝试询问关于电池管理、充电服务、车辆管理、ADAS系统等方面的问题，或者查看系统帮助文档获取更多信息。'
  }

  // 检查服务状态
  async checkStatus(): Promise<boolean> {
    try {
      // 检查数据库连接
      await db.query('SELECT 1')
      return true
    } catch (error) {
      console.error('AI服务状态检查失败:', error)
      return false
    }
  }
}

export const simpleAIService = new SimpleAIService()
