-- 创建AI聊天日志表
CREATE TABLE IF NOT EXISTS ai_chat_logs (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id VARCHAR(255) NOT NULL,
  question TEXT NOT NULL,
  response TEXT,
  timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
  session_id VARCHAR(255),
  ip_address VARCHAR(45),
  user_agent TEXT,
  INDEX idx_user_id (user_id),
  INDEX idx_timestamp (timestamp),
  INDEX idx_session_id (session_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入一些示例数据（可选）
INSERT INTO ai_chat_logs (user_id, question, response, timestamp) VALUES
('admin', '系统功能介绍', '新能源汽车智能综合管理系统主要功能包括：智能电池管理、智能充电服务、高级驾驶辅助、车联网交互、用户生态服务、系统安全运维等六大核心模块。', NOW()),
('admin', '如何查看电池状态', '您可以通过仪表板页面查看电池健康度卡片，或进入"智能电池管理"页面查看详细信息，包括当前电量、健康度、温度、充电状态等。', NOW()),
('admin', '充电站在哪里', '您可以点击"智能充电服务"进入充电页面，使用地图功能查看附近充电站，支持按距离、价格、充电速度筛选，并可实时查看充电桩状态。', NOW());

-- 创建AI知识库表（用于存储更复杂的知识）
CREATE TABLE IF NOT EXISTS ai_knowledge_base (
  id INT AUTO_INCREMENT PRIMARY KEY,
  category VARCHAR(100) NOT NULL,
  question VARCHAR(500) NOT NULL,
  answer TEXT NOT NULL,
  keywords TEXT,
  priority INT DEFAULT 1,
  is_active BOOLEAN DEFAULT TRUE,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_category (category),
  INDEX idx_keywords (keywords(255)),
  INDEX idx_priority (priority),
  INDEX idx_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入知识库数据
INSERT INTO ai_knowledge_base (category, question, answer, keywords, priority) VALUES
('电池管理', '电池健康度如何计算', '电池健康度是通过多个参数综合计算得出的，包括：1. 电池容量衰减率 2. 充放电循环次数 3. 电池内阻变化 4. 温度影响因素 5. 充电效率等。系统会实时监控这些参数并给出健康度评分。', '电池,健康度,计算,容量,循环', 5),
('电池管理', '电池温度过高怎么办', '当电池温度过高时，请立即：1. 停止快速充电 2. 将车辆停放在阴凉处 3. 开启电池冷却系统 4. 避免高负荷驾驶 5. 如温度持续过高，请联系服务中心检查。', '电池,温度,过热,冷却,安全', 5),
('充电服务', '充电费用如何计算', '充电费用包括：1. 电费：按实际用电量计算 2. 服务费：充电站运营费用 3. 停车费：部分充电站收取 4. 会员优惠：VIP用户享受折扣。具体费用在充电前会显示预估金额。', '充电,费用,计算,电费,服务费', 4),
('充电服务', '预约充电如何操作', '预约充电步骤：1. 在充电服务页面选择充电站 2. 查看可用时段 3. 选择预约时间和充电时长 4. 确认预约信息 5. 支付预约费用 6. 按时到达充电站即可。', '预约,充电,操作,时间,费用', 4),
('驾驶辅助', 'ADAS功能有哪些', 'ADAS高级驾驶辅助系统包括：1. 自适应巡航控制(ACC) 2. 车道保持辅助(LKA) 3. 前碰撞预警(FCW) 4. 自动紧急制动(AEB) 5. 盲点监测(BSM) 6. 交通标志识别(TSR) 7. 疲劳驾驶提醒等。', 'ADAS,驾驶辅助,功能,安全,自动', 4),
('车联网', '远程控制有什么功能', '远程控制功能包括：1. 远程启动/熄火 2. 空调预热/制冷 3. 车门锁定/解锁 4. 车窗控制 5. 充电控制 6. 车辆定位 7. 防盗报警 8. 故障诊断等。所有操作都通过加密通道确保安全。', '远程控制,功能,启动,空调,车门', 4),
('用户生态', '碳积分有什么用', '碳积分用途：1. 兑换充电优惠券 2. 购买车辆配件折扣 3. 参与环保活动 4. 兑换生活用品 5. 捐赠公益项目 6. 升级会员等级。积分越多，享受的优惠和服务越丰富。', '碳积分,用途,兑换,优惠,环保', 3),
('系统安全', '数据安全如何保障', '数据安全保障措施：1. 端到端加密传输 2. 多重身份验证 3. 访问权限控制 4. 数据备份机制 5. 安全审计日志 6. 定期安全检测 7. 隐私保护合规。确保用户数据安全可靠。', '数据安全,加密,权限,备份,隐私', 5),
('系统使用', '忘记密码怎么办', '忘记密码解决方法：1. 点击登录页面"忘记密码" 2. 输入注册邮箱或手机号 3. 接收验证码 4. 设置新密码 5. 重新登录。如仍有问题，请联系客服：400-123-4567。', '忘记密码,重置,验证码,邮箱,客服', 3),
('系统使用', '如何切换主题', '切换主题方法：1. 点击页面右上角的主题切换按钮 2. 选择明亮或暗黑主题 3. 系统会自动保存您的偏好 4. 下次登录时会自动应用您选择的主题。', '主题,切换,明亮,暗黑,偏好', 2);

-- 创建AI会话表（用于跟踪用户会话）
CREATE TABLE IF NOT EXISTS ai_sessions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  session_id VARCHAR(255) UNIQUE NOT NULL,
  user_id VARCHAR(255) NOT NULL,
  start_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  last_activity DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  message_count INT DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  INDEX idx_session_id (session_id),
  INDEX idx_user_id (user_id),
  INDEX idx_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

COMMIT;