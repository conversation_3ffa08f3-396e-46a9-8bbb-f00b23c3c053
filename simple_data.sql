-- 新能源汽车智能综合管理系统 - 简化示例数据
USE ev_management;

-- 1. 用户数据
INSERT INTO users (id, email, phone, name, user_type, status) VALUES 
('user-001', '<EMAIL>', '13800138001', '张伟', 'enterprise', 'active'),
('user-002', '<EMAIL>', '13800138002', '李明', 'fleet', 'active'),
('user-003', '<EMAIL>', '13800138003', '王芳', 'individual', 'active'),
('user-004', '<EMAIL>', '13800138004', '刘强', 'individual', 'active'),
('user-005', '<EMAIL>', '13800138005', '陈静', 'individual', 'active');

-- 2. 车辆数据
INSERT INTO vehicles (id, user_id, vin, license_plate, brand, model, year, color, vehicle_type, battery_capacity, max_range, status) VALUES 
('vehicle-001', 'user-001', 'WVWZZZ1JZXW123456', '京A12345', '特斯拉', 'Model 3', 2023, '珍珠白', 'bev', 75.0, 500, 'active'),
('vehicle-002', 'user-002', 'WVWZZZ1JZXW123457', '京B67890', '比亚迪', '汉EV', 2023, '汉宫红', 'bev', 85.4, 605, 'active'),
('vehicle-003', 'user-003', 'WVWZZZ1JZXW123458', '京C11111', '蔚来', 'ES6', 2023, '星空蓝', 'bev', 100.0, 610, 'active'),
('vehicle-004', 'user-004', 'WVWZZZ1JZXW123459', '沪A88888', '小鹏', 'P7', 2023, '机甲灰', 'bev', 80.9, 586, 'active'),
('vehicle-005', 'user-005', 'WVWZZZ1JZXW123460', '粤B99999', '理想', 'ONE', 2023, '珠光白', 'phev', 40.5, 800, 'active');

-- 3. 电池数据
INSERT INTO batteries (id, vehicle_id, battery_id, manufacturer, model, capacity, voltage, chemistry, current_soh, current_soc, temperature, cycle_count, status) VALUES 
('battery-001', 'vehicle-001', 'BAT001-TS-2023', '宁德时代', 'NCM811-75kWh', 75.0, 400.0, 'NCM811', 98.5, 85.2, 28.5, 245, 'normal'),
('battery-002', 'vehicle-002', 'BAT002-BYD-2023', '比亚迪', 'LFP-85kWh', 85.4, 403.2, 'LFP', 99.1, 72.8, 26.8, 189, 'normal'),
('battery-003', 'vehicle-003', 'BAT003-CATL-2023', '宁德时代', 'NCM523-100kWh', 100.0, 408.0, 'NCM523', 97.8, 90.5, 29.2, 312, 'normal'),
('battery-004', 'vehicle-004', 'BAT004-XP-2023', '宁德时代', 'NCM811-81kWh', 80.9, 355.2, 'NCM811', 96.7, 68.3, 31.1, 278, 'warning'),
('battery-005', 'vehicle-005', 'BAT005-LI-2023', '宁德时代', 'NCM622-40kWh', 40.5, 288.0, 'NCM622', 98.9, 45.7, 27.3, 156, 'normal');

-- 4. 充电站数据
INSERT INTO charging_stations (id, station_code, name, operator, address, latitude, longitude, total_ports, available_ports, power_type, max_power, status) VALUES 
('station-001', 'CS001-BJ-GMT', '北京国贸充电站', '国家电网', '北京市朝阳区国贸中心地下停车场B2层', 39.9075, 116.4574, 8, 6, 'dc', 120.0, 'active'),
('station-002', 'CS002-SH-LJZ', '上海陆家嘴充电站', '特来电', '上海市浦东新区陆家嘴金融区世纪大道88号', 31.2397, 121.4999, 12, 10, 'mixed', 180.0, 'active'),
('station-003', 'CS003-SZ-KJY', '深圳科技园充电站', '星星充电', '深圳市南山区科技园南区深南大道9988号', 22.5431, 113.9344, 6, 4, 'dc', 150.0, 'active'),
('station-004', 'CS004-BJ-ZGC', '北京中关村充电站', '小桔充电', '北京市海淀区中关村大街1号', 39.9388, 116.3974, 10, 8, 'mixed', 200.0, 'active'),
('station-005', 'CS005-GZ-ZJX', '广州珠江新城充电站', '南方电网', '广州市天河区珠江新城花城大道85号', 23.1291, 113.2644, 15, 12, 'dc', 160.0, 'active');

-- 5. 充电会话数据
INSERT INTO charging_sessions (id, user_id, vehicle_id, station_id, session_code, start_time, end_time, start_soc, end_soc, energy_delivered, peak_power, total_cost, payment_method, payment_status, session_status) VALUES 
('session-001', 'user-001', 'vehicle-001', 'station-001', 'CHG20240101001', '2024-01-15 08:30:00', '2024-01-15 09:45:00', 25.5, 85.2, 44.8, 118.5, 53.76, '微信支付', 'completed', 'completed'),
('session-002', 'user-002', 'vehicle-002', 'station-002', 'CHG20240101002', '2024-01-15 14:20:00', '2024-01-15 15:50:00', 15.3, 72.8, 49.1, 175.2, 63.83, '支付宝', 'completed', 'completed'),
('session-003', 'user-003', 'vehicle-003', 'station-003', 'CHG20240101003', '2024-01-15 19:10:00', '2024-01-15 21:05:00', 12.8, 90.5, 77.7, 148.9, 97.13, '银行卡', 'completed', 'completed'),
('session-004', 'user-004', 'vehicle-004', 'station-004', 'CHG20240102001', '2024-01-16 07:45:00', '2024-01-16 09:20:00', 8.2, 68.3, 48.7, 195.6, 57.24, '微信支付', 'completed', 'completed'),
('session-005', 'user-005', 'vehicle-005', 'station-005', 'CHG20240102002', '2024-01-16 16:30:00', '2024-01-16 17:15:00', 22.1, 45.7, 9.6, 158.3, 16.8, '支付宝', 'completed', 'completed');

-- 6. 碳积分数据
INSERT INTO carbon_credit (id, user_id, vehicle_id, credit_type, amount, description, reference_id, status, earned_at) VALUES 
('credit-001', 'user-001', 'vehicle-001', 'driving', 15.6, '绿色驾驶行为奖励', 'session-001', 'approved', '2024-01-15 09:45:00'),
('credit-002', 'user-002', 'vehicle-002', 'charging', 24.5, '使用清洁能源充电奖励', 'session-002', 'approved', '2024-01-15 15:50:00'),
('credit-003', 'user-003', 'vehicle-003', 'driving', 38.9, '长距离绿色出行奖励', NULL, 'approved', '2024-01-15 21:05:00'),
('credit-004', 'user-004', 'vehicle-004', 'sharing', 12.3, '车辆共享使用奖励', NULL, 'approved', '2024-01-16 09:20:00'),
('credit-005', 'user-005', 'vehicle-005', 'maintenance', 8.7, '定期保养维护奖励', NULL, 'approved', '2024-01-16 17:15:00');

-- 7. 驾驶行为数据
INSERT INTO driving_behavior (id, user_id, vehicle_id, trip_id, start_time, end_time, distance, duration, avg_speed, max_speed, acceleration_score, braking_score, cornering_score, eco_score, safety_score, energy_efficiency) VALUES 
('trip-001', 'user-001', 'vehicle-001', 'TRIP20240115001', '2024-01-15 08:00:00', '2024-01-15 08:30:00', 25.6, 1800, 51.2, 78.5, 8.5, 9.2, 8.8, 9.1, 8.9, 15.2),
('trip-002', 'user-002', 'vehicle-002', 'TRIP20240115002', '2024-01-15 14:00:00', '2024-01-15 14:20:00', 12.3, 1200, 36.9, 65.2, 7.8, 8.5, 9.1, 8.7, 8.5, 18.7),
('trip-003', 'user-003', 'vehicle-003', 'TRIP20240115003', '2024-01-15 18:30:00', '2024-01-15 19:10:00', 35.8, 2400, 53.7, 95.3, 9.3, 8.9, 9.5, 9.2, 9.2, 14.8),
('trip-004', 'user-004', 'vehicle-004', 'TRIP20240116001', '2024-01-16 07:15:00', '2024-01-16 07:45:00', 18.9, 1800, 37.8, 72.1, 8.1, 7.9, 8.6, 8.4, 8.3, 16.9),
('trip-005', 'user-005', 'vehicle-005', 'TRIP20240116002', '2024-01-16 16:00:00', '2024-01-16 16:30:00', 22.1, 1800, 44.2, 88.7, 7.5, 8.2, 7.8, 8.0, 7.9, 12.3);

-- 8. 车队管理数据
INSERT INTO fleet_management (id, user_id, fleet_name, fleet_code, description, manager_name, manager_phone, manager_email, total_vehicles, active_vehicles, status) VALUES 
('fleet-001', 'user-002', '京东物流车队', 'JD-FLEET-001', '京东物流北京配送车队', '李明', '13800138002', '<EMAIL>', 2, 2, 'active'),
('fleet-002', 'user-002', '顺丰速运车队', 'SF-FLEET-002', '顺丰速运华南区域车队', '李明', '13800138002', '<EMAIL>', 1, 1, 'active');

-- 车队车辆关联数据
INSERT INTO fleet_vehicles (fleet_id, vehicle_id, assigned_at, status) VALUES 
('fleet-001', 'vehicle-002', '2024-01-01 00:00:00', 'active'),
('fleet-001', 'vehicle-004', '2024-01-01 00:00:00', 'active'),
('fleet-002', 'vehicle-005', '2024-01-01 00:00:00', 'active');

-- 9. 边缘设备数据
INSERT INTO edge_devices (id, name, type, status, last_heartbeat) VALUES 
('edge-001', '温度传感器-北京国贸-01', 'sensor', 'online', NOW() - INTERVAL 30 SECOND),
('edge-002', '智能控制器-上海陆家嘴-01', 'controller', 'online', NOW() - INTERVAL 45 SECOND),
('edge-003', '网关设备-深圳科技园-01', 'gateway', 'online', NOW() - INTERVAL 1 MINUTE),
('edge-004', '监控摄像头-北京中关村-01', 'camera', 'online', NOW() - INTERVAL 20 SECOND),
('edge-005', '环境传感器-广州珠江新城-01', 'sensor', 'online', NOW() - INTERVAL 1 MINUTE);

-- 边缘设备数据
INSERT INTO edge_device_data (device_id, data_type, value, unit, quality, timestamp) VALUES 
('edge-001', 'temperature', 25.6, '°C', 98.5, NOW() - INTERVAL 1 MINUTE),
('edge-001', 'humidity', 65.2, '%', 97.8, NOW() - INTERVAL 1 MINUTE),
('edge-002', 'power_consumption', 1250.5, 'kW', 99.8, NOW() - INTERVAL 2 MINUTE),
('edge-002', 'load_factor', 0.85, '', 98.9, NOW() - INTERVAL 2 MINUTE),
('edge-003', 'data_throughput', 1024.0, 'MB/s', 97.5, NOW() - INTERVAL 3 MINUTE),
('edge-003', 'connection_count', 156, '', 100.0, NOW() - INTERVAL 3 MINUTE),
('edge-004', 'vehicle_count', 23, '', 95.6, NOW() - INTERVAL 1 MINUTE),
('edge-005', 'air_quality', 85.3, 'AQI', 99.2, NOW() - INTERVAL 1 MINUTE);

-- 边缘任务数据
INSERT INTO edge_tasks (id, name, type, priority, status, device_id, created_at) VALUES 
('task-001', '实时温度监控', 'real_time_analysis', 'high', 'running', 'edge-001', NOW() - INTERVAL 2 HOUR),
('task-002', '负载均衡优化', 'data_processing', 'medium', 'completed', 'edge-002', NOW() - INTERVAL 1 HOUR),
('task-003', '数据聚合处理', 'data_processing', 'medium', 'running', 'edge-003', NOW() - INTERVAL 3 HOUR),
('task-004', '车牌识别分析', 'ai_inference', 'high', 'completed', 'edge-004', NOW() - INTERVAL 4 HOUR),
('task-005', '环境质量评估', 'real_time_analysis', 'low', 'pending', 'edge-005', NOW() - INTERVAL 30 MINUTE);

SELECT '简化示例数据导入完成！' as message;