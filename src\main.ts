import { createApp } from 'vue'
import { createPinia } from 'pinia'
import './style.css'
import './styles/mobile.css'
import App from './App.vue'
import router from './router'
import { initMobileApp } from './utils/mobile'
import { vModernInteraction } from './directives/modernInteraction'

// Element Plus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// 初始化移动端适配
initMobileApp()

// 创建Vue应用实例
const app = createApp(App)

// 创建Pinia实例
const pinia = createPinia()

// 使用Pinia状态管理
app.use(pinia)

// 使用Element Plus
app.use(ElementPlus)

// 注册所有Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 使用路由
app.use(router)

// 注册全局指令
app.directive('modern-interaction', vModernInteraction)

// 挂载应用
app.mount('#app')
