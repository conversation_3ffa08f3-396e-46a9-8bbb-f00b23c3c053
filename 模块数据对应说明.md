# 新能源汽车智能综合管理系统 - 模块数据对应说明

本文档详细说明了系统各个前端模块与数据库表的对应关系，以及如何使用示例数据。

## 📋 目录

1. [数据库表概览](#数据库表概览)
2. [前端模块与数据表对应](#前端模块与数据表对应)
3. [示例数据说明](#示例数据说明)
4. [数据导入方法](#数据导入方法)
5. [模块测试指南](#模块测试指南)

## 🗄️ 数据库表概览

### 核心业务表

| 表名 | 用途 | 记录数 | 主要字段 |
|------|------|--------|----------|
| `users` | 用户信息 | 10条 | id, email, name, user_type, status |
| `vehicles` | 车辆信息 | 10条 | id, user_id, vin, brand, model, battery_capacity |
| `batteries` | 电池信息 | 10条 | id, vehicle_id, capacity, current_soh, current_soc |
| `charging_stations` | 充电站 | 10条 | id, name, address, total_ports, power_type |
| `charging_sessions` | 充电会话 | 10条 | id, user_id, vehicle_id, station_id, energy_delivered |
| `carbon_credit` | 碳积分 | 10条 | id, user_id, credit_type, amount, status |
| `driving_behavior` | 驾驶行为 | 10条 | id, user_id, vehicle_id, distance, eco_score |
| `fleet_management` | 车队管理 | 5条 | id, fleet_name, total_vehicles, status |
| `edge_devices` | 边缘设备 | 10条 | id, name, type, status, capabilities |

### 关联和追踪表

| 表名 | 用途 | 记录数 | 说明 |
|------|------|--------|---------|
| `battery_trace` | 电池追踪 | 6条 | 电池历史数据记录 |
| `fleet_vehicles` | 车队车辆关联 | 10条 | 车队与车辆的多对多关系 |
| `edge_device_data` | 设备数据 | 12条 | 边缘设备实时数据 |
| `edge_tasks` | 边缘任务 | 10条 | 边缘计算任务记录 |

## 🎯 前端模块与数据表对应

### 1. Dashboard (仪表板)
**路由**: `/dashboard`
**对应表**: 所有表的统计数据
```sql
-- 获取仪表板统计数据
SELECT 
    (SELECT COUNT(*) FROM users WHERE status = 'active') as active_users,
    (SELECT COUNT(*) FROM vehicles WHERE status = 'active') as active_vehicles,
    (SELECT COUNT(*) FROM charging_stations WHERE status = 'active') as active_stations,
    (SELECT SUM(energy_delivered) FROM charging_sessions WHERE session_status = 'completed') as total_energy;
```

### 2. UserEcosystem (用户生态)
**路由**: `/user-ecosystem`
**主要表**: `users`, `carbon_credit`
```sql
-- 获取用户列表
SELECT * FROM users ORDER BY created_at DESC;

-- 获取用户碳积分
SELECT 
    u.name,
    SUM(CASE WHEN cc.status = 'approved' THEN cc.amount ELSE 0 END) as total_credits
FROM users u
LEFT JOIN carbon_credit cc ON u.id = cc.user_id
GROUP BY u.id, u.name;
```

### 3. VehicleNetwork (车辆网络)
**路由**: `/vehicle-network`
**主要表**: `vehicles`, `fleet_management`, `fleet_vehicles`
```sql
-- 获取车辆列表及车主信息
SELECT 
    v.*,
    u.name as owner_name,
    u.email as owner_email
FROM vehicles v
JOIN users u ON v.user_id = u.id
ORDER BY v.created_at DESC;

-- 获取车队管理信息
SELECT 
    fm.*,
    COUNT(fv.vehicle_id) as vehicle_count
FROM fleet_management fm
LEFT JOIN fleet_vehicles fv ON fm.id = fv.fleet_id AND fv.status = 'active'
GROUP BY fm.id;
```

### 4. BatteryManagement (电池管理)
**路由**: `/battery-management`
**主要表**: `batteries`, `battery_trace`, `vehicles`
```sql
-- 获取电池列表及车辆信息
SELECT 
    b.*,
    v.brand,
    v.model,
    v.license_plate
FROM batteries b
JOIN vehicles v ON b.vehicle_id = v.id
ORDER BY b.current_soh DESC;

-- 获取电池追踪数据
SELECT 
    bt.*,
    b.battery_id
FROM battery_trace bt
JOIN batteries b ON bt.battery_id = b.id
WHERE bt.timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY)
ORDER BY bt.timestamp DESC;
```

### 5. ChargingService (充电服务)
**路由**: `/charging-service`
**主要表**: `charging_stations`, `charging_sessions`
```sql
-- 获取充电站列表
SELECT 
    cs.*,
    COUNT(css.id) as session_count
FROM charging_stations cs
LEFT JOIN charging_sessions css ON cs.id = css.station_id
GROUP BY cs.id
ORDER BY cs.created_at DESC;

-- 获取充电会话记录
SELECT 
    css.*,
    u.name as user_name,
    v.license_plate,
    cs.name as station_name
FROM charging_sessions css
JOIN users u ON css.user_id = u.id
JOIN vehicles v ON css.vehicle_id = v.id
JOIN charging_stations cs ON css.station_id = cs.id
ORDER BY css.start_time DESC;
```

### 6. ADAS / AdasSystem (高级驾驶辅助)
**路由**: `/adas`, `/adas-system`
**主要表**: `driving_behavior`, `vehicles`, `users`
```sql
-- 获取驾驶行为数据
SELECT 
    db.*,
    u.name as driver_name,
    v.license_plate,
    v.brand,
    v.model
FROM driving_behavior db
JOIN users u ON db.user_id = u.id
JOIN vehicles v ON db.vehicle_id = v.id
ORDER BY db.start_time DESC;

-- 获取驾驶评分统计
SELECT 
    u.name,
    AVG(db.safety_score) as avg_safety_score,
    AVG(db.eco_score) as avg_eco_score,
    AVG(db.energy_efficiency) as avg_efficiency
FROM driving_behavior db
JOIN users u ON db.user_id = u.id
GROUP BY u.id, u.name;
```

### 7. SystemSecurity (系统安全)
**路由**: `/system-security`
**主要表**: `edge_devices`, `edge_tasks`
```sql
-- 获取边缘设备安全状态
SELECT 
    ed.*,
    COUNT(et.id) as task_count,
    MAX(et.created_at) as last_task_time
FROM edge_devices ed
LEFT JOIN edge_tasks et ON ed.id = et.device_id
GROUP BY ed.id
ORDER BY ed.last_heartbeat DESC;

-- 获取安全相关任务
SELECT * FROM edge_tasks 
WHERE type IN ('alert_detection', 'ai_inference') 
AND name LIKE '%安防%' OR name LIKE '%监控%'
ORDER BY created_at DESC;
```

### 8. EdgeComputingMonitor (边缘计算监控)
**路由**: 在 RealtimeMonitor 组件中
**主要表**: `edge_devices`, `edge_device_data`, `edge_tasks`
```sql
-- 获取边缘设备实时状态
SELECT 
    ed.*,
    edd.data_type,
    edd.value,
    edd.unit,
    edd.timestamp
FROM edge_devices ed
LEFT JOIN edge_device_data edd ON ed.id = edd.device_id
WHERE edd.timestamp >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
ORDER BY edd.timestamp DESC;

-- 获取设备性能数据
SELECT 
    device_id,
    data_type,
    AVG(value) as avg_value,
    MAX(value) as max_value,
    MIN(value) as min_value
FROM edge_device_data
WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
GROUP BY device_id, data_type;
```

## 📊 示例数据说明

### 用户数据特点
- **10个用户**: 包含个人用户、企业用户、车队用户
- **真实邮箱格式**: 使用常见邮箱后缀
- **头像链接**: 使用 DiceBear API 生成的 SVG 头像
- **用户状态**: 9个活跃用户，1个非活跃用户

### 车辆数据特点
- **10辆车**: 覆盖主流新能源汽车品牌
- **车辆类型**: BEV (纯电动)、PHEV (插电混动)
- **地理分布**: 北京、上海、深圳、广州等主要城市
- **车辆状态**: 8辆正常、1辆维护中、1辆已退役

### 电池数据特点
- **电池厂商**: 宁德时代、比亚迪、中航锂电等
- **电池化学**: NCM811、LFP、NCM523、NCM622
- **健康状态**: SOH 92.1% - 99.2%
- **电量状态**: SOC 5.3% - 90.5%

### 充电站数据特点
- **10个充电站**: 分布在全国主要城市
- **运营商**: 国家电网、特来电、星星充电等
- **功率类型**: AC、DC、Mixed
- **最大功率**: 120kW - 200kW

### 边缘设备数据特点
- **设备类型**: 传感器、控制器、网关、摄像头
- **设备状态**: 8个在线、1个错误、1个离线
- **功能能力**: 温度监测、负载控制、数据聚合、AI推理

## 🚀 数据导入方法

### 方法1: 使用 MySQL 命令行
```bash
# 进入项目目录
cd e:\指尖消除小游戏脚本

# 连接到 MySQL 并执行脚本
mysql -u root -p ev_management < sample_data.sql
```

### 方法2: 使用 MySQL Workbench
1. 打开 MySQL Workbench
2. 连接到数据库
3. 打开 `sample_data.sql` 文件
4. 执行整个脚本

### 方法3: 使用项目脚本
```bash
# 在项目根目录执行
node -e "const mysql = require('mysql2/promise'); const fs = require('fs'); (async () => { const connection = await mysql.createConnection({host: 'localhost', user: 'root', password: 'your_password', database: 'ev_management'}); const sql = fs.readFileSync('sample_data.sql', 'utf8'); await connection.execute(sql); console.log('数据导入完成'); await connection.end(); })();"
```

## 🧪 模块测试指南

### 1. 启动项目
```bash
npm run dev
```

### 2. 测试各个模块

#### Dashboard 测试
- 访问 `http://localhost:5173/dashboard`
- 检查统计卡片是否显示正确数据
- 验证图表是否正常渲染

#### UserEcosystem 测试
- 访问 `http://localhost:5173/user-ecosystem`
- 检查用户列表是否显示10个用户
- 验证碳积分数据是否正确

#### VehicleNetwork 测试
- 访问 `http://localhost:5173/vehicle-network`
- 检查车辆列表是否显示10辆车
- 验证车队管理功能

#### BatteryManagement 测试
- 访问 `http://localhost:5173/battery-management`
- 检查电池列表和健康状态
- 验证电池追踪图表

#### ChargingService 测试
- 访问 `http://localhost:5173/charging-service`
- 检查充电站地图显示
- 验证充电会话记录

#### ADAS 测试
- 访问 `http://localhost:5173/adas`
- 检查驾驶行为数据
- 验证评分统计

#### SystemSecurity 测试
- 访问 `http://localhost:5173/system-security`
- 检查安全设备状态
- 验证告警信息

### 3. 数据验证查询

```sql
-- 验证数据完整性
SELECT 
    '用户数据' as table_name, COUNT(*) as count FROM users
UNION ALL
SELECT '车辆数据', COUNT(*) FROM vehicles
UNION ALL
SELECT '电池数据', COUNT(*) FROM batteries
UNION ALL
SELECT '充电站数据', COUNT(*) FROM charging_stations
UNION ALL
SELECT '充电会话', COUNT(*) FROM charging_sessions
UNION ALL
SELECT '碳积分记录', COUNT(*) FROM carbon_credit
UNION ALL
SELECT '驾驶行为', COUNT(*) FROM driving_behavior
UNION ALL
SELECT '车队管理', COUNT(*) FROM fleet_management
UNION ALL
SELECT '边缘设备', COUNT(*) FROM edge_devices;
```

## 📝 注意事项

1. **数据一致性**: 所有外键关系都已正确设置
2. **时间数据**: 使用相对时间确保数据的时效性
3. **地理位置**: 使用真实的经纬度坐标
4. **数据量**: 每个表都有足够的测试数据
5. **状态多样性**: 包含各种状态的数据用于测试不同场景

## 🔧 故障排除

### 常见问题

1. **外键约束错误**
   - 确保按照脚本顺序执行
   - 检查父表数据是否存在

2. **字符编码问题**
   - 确保数据库使用 utf8mb4 编码
   - 检查连接字符集设置

3. **时间戳问题**
   - 确保 MySQL 时区设置正确
   - 检查 NOW() 函数返回值

4. **JSON 数据问题**
   - 确保 MySQL 版本支持 JSON 类型
   - 检查 JSON 格式是否正确

### 重置数据

如需重置所有数据，可以执行：

```sql
-- 清空所有数据（注意：这会删除所有数据）
SET FOREIGN_KEY_CHECKS = 0;
TRUNCATE TABLE edge_device_data;
TRUNCATE TABLE edge_tasks;
TRUNCATE TABLE edge_devices;
TRUNCATE TABLE fleet_vehicles;
TRUNCATE TABLE fleet_management;
TRUNCATE TABLE driving_behavior;
TRUNCATE TABLE carbon_credit;
TRUNCATE TABLE charging_sessions;
TRUNCATE TABLE battery_trace;
TRUNCATE TABLE batteries;
TRUNCATE TABLE charging_stations;
TRUNCATE TABLE vehicles;
TRUNCATE TABLE users;
SET FOREIGN_KEY_CHECKS = 1;

-- 然后重新执行 sample_data.sql
```

---

**文档版本**: v1.0.0  
**更新日期**: 2024年1月  
**维护者**: 系统开发团队