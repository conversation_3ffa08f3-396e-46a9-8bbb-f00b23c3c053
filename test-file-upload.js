const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

async function testFileUpload() {
  console.log('📁 测试文件上传和分析功能...\n');
  
  try {
    // 测试文本文件上传
    console.log('1. 测试文本文件分析...');
    
    const formData = new FormData();
    formData.append('file', fs.createReadStream('test-document.txt'));
    formData.append('question', '这个文档介绍了什么系统？有哪些主要功能？');
    
    const response = await axios.post('http://localhost:3001/api/ai-enhanced/analyze-file', formData, {
      headers: {
        ...formData.getHeaders()
      }
    });
    
    if (response.data.success) {
      console.log('✅ 文本文件分析成功');
      console.log('文件名:', response.data.fileName);
      console.log('文件类型:', response.data.fileType);
      console.log('AI分析结果:', response.data.response.substring(0, 200) + '...');
    } else {
      console.log('❌ 文本文件分析失败:', response.data.message);
    }
    
    console.log('\n2. 测试图片文件分析...');
    
    // 测试图片文件上传
    const imageFormData = new FormData();
    imageFormData.append('file', fs.createReadStream('test-chart.svg'));
    imageFormData.append('question', '这张图表显示了什么数据？请分析图表内容。');
    
    const imageResponse = await axios.post('http://localhost:3001/api/ai-enhanced/analyze-file', imageFormData, {
      headers: {
        ...imageFormData.getHeaders()
      }
    });
    
    if (imageResponse.data.success) {
      console.log('✅ 图片文件分析成功');
      console.log('文件名:', imageResponse.data.fileName);
      console.log('文件类型:', imageResponse.data.fileType);
      console.log('AI分析结果:', imageResponse.data.response.substring(0, 200) + '...');
    } else {
      console.log('❌ 图片文件分析失败:', imageResponse.data.message);
    }
    
    console.log('\n🎉 文件上传测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  }
}

testFileUpload();
