import express from 'express';
import type { Request, Response } from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { db } from '../config/mysql';
import { simpleAIService } from '../services/ai-simple';

const router = express.Router();

// 配置文件上传
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(process.cwd(), 'api/uploads');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ 
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
  },
  fileFilter: (req, file, cb) => {
    // 允许的文件类型
    const allowedTypes = [
      'text/plain',
      'text/csv',
      'application/json',
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp'
    ];
    
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('不支持的文件类型'));
    }
  }
});

// AI聊天接口 - 简化版
router.post('/chat', async (req: Request, res: Response) => {
  try {
    const { message, context = [] } = req.body;
    
    if (!message || typeof message !== 'string') {
      return res.status(400).json({
        success: false,
        message: '消息内容不能为空'
      });
    }

    console.log('收到聊天请求:', message);

    // 记录用户问题
    try {
      await db.query(
        'INSERT INTO ai_chat_logs (user_id, question, timestamp) VALUES (?, ?, NOW())',
        [(req as any).user?.id || 'anonymous', message]
      );
    } catch (error) {
      console.log('记录聊天日志失败:', error);
    }

    // 使用简化AI服务生成回复
    const response = await simpleAIService.chat(message, context);
    
    console.log('AI回复生成成功');
    
    res.json({
      success: true,
      response: response,
      timestamp: new Date().toISOString(),
      model: 'local-knowledge-base'
    });
    
  } catch (error) {
    console.error('AI聊天错误:', error);
    res.status(500).json({
      success: false,
      message: 'AI服务暂时不可用，请稍后再试'
    });
  }
});

// 文件上传和分析接口 - 简化版
router.post('/analyze-file', upload.single('file'), async (req: Request, res: Response) => {
  try {
    const { question = '请分析这个文件的内容' } = req.body;
    const file = req.file;

    console.log('收到文件分析请求:', file?.originalname, file?.mimetype);

    if (!file) {
      return res.status(400).json({
        success: false,
        message: '请上传文件'
      });
    }

    let analysisResult: string;

    // 根据文件类型进行不同的处理
    if (file.mimetype.startsWith('text/') || file.mimetype === 'application/json') {
      // 文本文件处理
      console.log('开始分析文本文件...');
      analysisResult = await simpleAIService.analyzeFile(file.path, file.originalname, file.mimetype, question);
    } else if (file.mimetype.startsWith('image/')) {
      // 图片文件处理
      console.log('开始分析图片文件...');
      const imageBuffer = fs.readFileSync(file.path);
      const imageBase64 = imageBuffer.toString('base64');
      analysisResult = await simpleAIService.analyzeImage(imageBase64, question);
    } else {
      // 其他文件类型
      analysisResult = `抱歉，暂时不支持分析 ${file.mimetype} 类型的文件。目前支持的文件类型包括：文本文件(.txt)、JSON文件(.json)、图片文件(.jpg, .png, .gif, .webp)。`;
    }

    console.log('文件分析完成');

    // 记录文件分析日志
    try {
      await db.query(
        'INSERT INTO ai_chat_logs (user_id, question, file_name, file_type, timestamp) VALUES (?, ?, ?, ?, NOW())',
        [(req as any).user?.id || 'anonymous', question, file.originalname, file.mimetype]
      );
    } catch (error) {
      console.log('记录文件分析日志失败:', error);
    }

    // 清理上传的文件
    fs.unlinkSync(file.path);

    res.json({
      success: true,
      response: analysisResult,
      fileName: file.originalname,
      fileType: file.mimetype,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('文件分析错误:', error);
    
    // 清理上传的文件
    if (req.file) {
      try {
        fs.unlinkSync(req.file.path);
      } catch (cleanupError) {
        console.error('清理文件失败:', cleanupError);
      }
    }

    res.status(500).json({
      success: false,
      message: '文件分析失败，请稍后再试'
    });
  }
});

// AI服务状态检查
router.get('/status', async (req: Request, res: Response) => {
  try {
    // 检查本地AI服务状态
    const localStatus = await simpleAIService.checkStatus();
    
    res.json({
      success: true,
      status: localStatus ? 'online' : 'offline',
      message: localStatus ? 'AI助手服务正常运行（本地模式）' : 'AI服务暂时不可用',
      timestamp: new Date().toISOString(),
      capabilities: [
        '智能对话',
        '文件分析',
        '系统咨询',
        '操作指导',
        '问题解答'
      ],
      openaiConnected: false,
      mode: 'local'
    });
    
  } catch (error) {
    console.error('AI状态检查错误:', error);
    res.status(500).json({
      success: false,
      status: 'offline',
      message: 'AI助手服务暂时不可用'
    });
  }
});

// 获取AI助手信息
router.get('/info', async (req: Request, res: Response) => {
  try {
    const aiConfig = await simpleAIService.getAIConfig();
    
    res.json({
      success: true,
      data: {
        ...aiConfig,
        version: '2.0.0-local'
      }
    });
  } catch (error) {
    console.error('获取AI信息错误:', error);
    res.status(500).json({
      success: false,
      message: '获取AI信息失败'
    });
  }
});

export default router;
