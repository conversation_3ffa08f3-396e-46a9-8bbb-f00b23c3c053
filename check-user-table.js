const mysql = require('mysql2/promise');

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'ev_management',
  charset: 'utf8mb4'
};

async function checkUserTable() {
  let connection;
  
  try {
    console.log('🔌 连接到MySQL数据库...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 检查用户表结构
    console.log('\n📋 用户表字段信息:');
    console.log('==================');
    const [columns] = await connection.execute('DESCRIBE users');
    
    columns.forEach((col, index) => {
      console.log(`${index + 1}. ${col.Field} (${col.Type}) - ${col.Null === 'YES' ? '可空' : '非空'} ${col.Key ? '- ' + col.Key : ''}`);
    });
    
    // 检查现有数据
    console.log('\n📊 用户表现有数据:');
    console.log('==================');
    const [users] = await connection.execute('SELECT * FROM users LIMIT 5');
    
    if (users.length > 0) {
      console.log(`共有 ${users.length} 条记录（显示前5条）:`);
      users.forEach((user, index) => {
        console.log(`${index + 1}. ID: ${user.id}, Email: ${user.email}`);
      });
    } else {
      console.log('用户表为空');
    }
    
  } catch (error) {
    console.error('❌ 检查失败:', error.message);
    console.error('错误详情:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 数据库连接已关闭');
    }
  }
}

// 运行检查脚本
checkUserTable();