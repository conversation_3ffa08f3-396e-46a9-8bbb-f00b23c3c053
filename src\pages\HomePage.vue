<template>
  <div class="home-container">
    <!-- 全新顶部导航 -->
    <header class="modern-header">
      <div class="header-content">
        <div class="brand-section">
          <div class="brand-logo">
            <div class="logo-icon">
              <el-icon><Car /></el-icon>
            </div>
            <div class="brand-text">
              <h1>EV智控</h1>
              <span>新能源汽车管理平台</span>
            </div>
          </div>
        </div>
        <nav class="nav-menu">
          <a @click="handleNavClick('#features')" class="nav-link">功能特色</a>
          <a @click="handleNavClick('#modules')" class="nav-link">核心模块</a>
          <a @click="handleNavClick('#about')" class="nav-link">关于我们</a>
        </nav>
        <div class="header-actions">
          <!-- 主题切换按钮 -->
          <div class="theme-toggle-wrapper">
            <ThemeToggle />
          </div>
          <ModernButton type="outline" size="small" @click="learnMore">了解更多</ModernButton>
          <ModernButton type="primary" @click="goToDashboard">
            <el-icon><Monitor /></el-icon>
            立即体验
          </ModernButton>
        </div>
      </div>
    </header>

    <!-- 全新主视觉区域 -->
    <section class="hero-banner">
      <div class="hero-background">
        <div class="gradient-orb orb-1"></div>
        <div class="gradient-orb orb-2"></div>
        <div class="gradient-orb orb-3"></div>
      </div>
      <div class="hero-container">
        <div class="hero-text">
          <h1 class="main-title">
            <span class="title-line">智能驱动未来</span>
            <span class="title-line highlight">新能源汽车管理</span>
          </h1>
          <p class="hero-description">
            {{ displayedText }}<span class="typing-cursor" v-show="typewriterActive">|</span>
          </p>
          <div class="hero-buttons">
            <ModernButton type="primary" size="large" @click="goToDashboard">
              <el-icon><Monitor /></el-icon>
              开始探索
            </ModernButton>
            <ModernButton type="outline" size="large" @click="watchDemo">
              <el-icon><DataBoard /></el-icon>
              观看演示
            </ModernButton>
          </div>
        </div>
        <div class="hero-visual">
          <div class="car-showcase">
            <div class="car-icon">
              <el-icon><Car /></el-icon>
            </div>
            <div class="tech-rings">
              <div class="tech-ring ring-1"></div>
              <div class="tech-ring ring-2"></div>
              <div class="tech-ring ring-3"></div>
            </div>
            <div class="floating-tech">
              <div class="tech-item item-1">
                <el-icon><Lightning /></el-icon>
              </div>
              <div class="tech-item item-2">
                <el-icon><Shield /></el-icon>
              </div>
              <div class="tech-item item-3">
                <el-icon><Connection /></el-icon>
              </div>
              <div class="tech-item item-4">
                <el-icon><DataAnalysis /></el-icon>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

      <!-- 功能模块导航 -->
      <section id="modules" class="modules-section">
        <div class="section-header">
          <h2 class="section-title">核心功能模块</h2>
          <p class="section-subtitle">全方位智能化管理解决方案</p>
        </div>
        <div class="modules-grid">
          <!-- 首页仪表板 -->
          <div class="flip-card" @click="navigateTo('/dashboard')">
            <div class="flip-card-inner">
              <div class="flip-card-front">
                <div class="module-icon dashboard">
                  <el-icon><DataBoard /></el-icon>
                </div>
                <p class="title">首页仪表板</p>
                <p>系统概览</p>
              </div>
              <div class="flip-card-back">
                <p class="title">功能详情</p>
                <p>实时数据统计、快速操作入口、数据可视化、实时监控</p>
              </div>
            </div>
          </div>

          <!-- 智能电池管理 -->
          <div class="flip-card" @click="navigateTo('/battery')">
            <div class="flip-card-inner">
              <div class="flip-card-front">
                <div class="module-icon battery">
                  <el-icon><Lightning /></el-icon>
                </div>
                <p class="title">智能电池管理</p>
                <p>电池监控</p>
              </div>
              <div class="flip-card-back">
                <p class="title">功能详情</p>
                <p>电池状态监控、健康度分析、溯源追踪、预警系统</p>
              </div>
            </div>
          </div>

          <!-- 智能充电服务 -->
          <div class="flip-card" @click="navigateTo('/charging')">
            <div class="flip-card-inner">
              <div class="flip-card-front">
                <div class="module-icon charging">
                  <el-icon><Position /></el-icon>
                </div>
                <p class="title">智能充电服务</p>
                <p>充电管理</p>
              </div>
              <div class="flip-card-back">
                <p class="title">功能详情</p>
                <p>充电站地图、充电记录、费用管理、预约功能</p>
              </div>
            </div>
          </div>

          <!-- 高级驾驶辅助 -->
          <div class="flip-card" @click="navigateTo('/adas')">
            <div class="flip-card-inner">
              <div class="flip-card-front">
                <div class="module-icon adas">
                  <el-icon><Aim /></el-icon>
                </div>
                <p class="title">高级驾驶辅助</p>
                <p>驾驶辅助</p>
              </div>
              <div class="flip-card-back">
                <p class="title">功能详情</p>
                <p>驾驶行为分析、安全评分、路线优化</p>
              </div>
            </div>
          </div>

          <!-- 车联网交互中心 -->
          <div class="flip-card" @click="navigateTo('/vehicle-network')">
            <div class="flip-card-inner">
              <div class="flip-card-front">
                <div class="module-icon network">
                  <el-icon><Connection /></el-icon>
                </div>
                <p class="title">车联网交互中心</p>
                <p>车联网服务</p>
              </div>
              <div class="flip-card-back">
                <p class="title">功能详情</p>
                <p>车辆远程控制、故障诊断、OTA升级</p>
              </div>
            </div>
          </div>

          <!-- 用户生态服务 -->
          <div class="flip-card" @click="navigateTo('/user-ecosystem')">
            <div class="flip-card-inner">
              <div class="flip-card-front">
                <div class="module-icon ecosystem">
                  <el-icon><UserFilled /></el-icon>
                </div>
                <p class="title">用户生态服务</p>
                <p>生态管理</p>
              </div>
              <div class="flip-card-back">
                <p class="title">功能详情</p>
                <p>碳积分管理、用户画像、车队管理</p>
              </div>
            </div>
          </div>

          <!-- 系统安全运维 -->
          <div class="flip-card" @click="navigateTo('/system-security')">
            <div class="flip-card-inner">
              <div class="flip-card-front">
                <div class="module-icon security">
                  <el-icon><Lock /></el-icon>
                </div>
                <p class="title">系统安全运维</p>
                <p>安全管理</p>
              </div>
              <div class="flip-card-back">
                <p class="title">功能详情</p>
                <p>用户权限管理、系统监控、审计日志</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 技术优势展示 -->
      <section id="features" class="features-section">
        <div class="section-header">
          <h2 class="section-title">技术优势</h2>
          <p class="section-subtitle">领先的智能化技术架构</p>
        </div>
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">
              <el-icon><CloudSun /></el-icon>
            </div>
            <h3>云端智能</h3>
            <p>基于云计算的智能分析平台，提供强大的数据处理能力</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">
              <el-icon><DataAnalysis /></el-icon>
            </div>
            <h3>AI驱动</h3>
            <p>人工智能算法驱动，实现智能预测和自动化决策</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">
              <el-icon><Timer /></el-icon>
            </div>
            <h3>实时监控</h3>
            <p>7x24小时实时监控，确保系统稳定运行和数据安全</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">
              <el-icon><Shield /></el-icon>
            </div>
            <h3>安全防护</h3>
            <p>多层次安全防护体系，保障数据和系统的绝对安全</p>
          </div>
        </div>
      </section>

    <!-- 现代化底部 -->
    <footer id="about" class="modern-footer">
      <div class="footer-container">
        <div class="footer-main">
          <div class="footer-brand">
            <div class="footer-logo">
              <div class="logo-icon">
                <el-icon><Car /></el-icon>
              </div>
              <h3>EV智控</h3>
            </div>
            <p>新能源汽车智能综合管理系统</p>
            <p>基于云-边-端架构的智能化管理平台</p>
          </div>
          <div class="footer-links">
            <div class="link-group">
              <h4>产品功能</h4>
              <a @click="handleFooterLink('battery')">智能电池管理</a>
              <a @click="handleFooterLink('charging')">充电服务</a>
              <a @click="handleFooterLink('adas')">驾驶辅助</a>
              <a @click="handleFooterLink('network')">车联网服务</a>
            </div>
            <div class="link-group">
              <h4>技术支持</h4>
              <a @click="handleFooterLink('docs')">技术文档</a>
              <a @click="handleFooterLink('api')">API接口</a>
              <a @click="handleFooterLink('guide')">开发指南</a>
              <a @click="handleFooterLink('faq')">常见问题</a>
            </div>
            <div class="link-group">
              <h4>关于我们</h4>
              <a @click="handleFooterLink('about')">公司介绍</a>
              <a @click="handleFooterLink('contact')">联系我们</a>
              <a @click="handleFooterLink('join')">加入我们</a>
              <a @click="handleFooterLink('partners')">合作伙伴</a>
            </div>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; 2025 EV智控. 保留所有权利.</p>
          <div class="footer-actions">
            <ModernButton type="primary" @click="goToDashboard">
              <el-icon><Monitor /></el-icon>
              立即体验
            </ModernButton>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Monitor,
  Lightning,
  Connection,
  DataBoard,
  Position,
  Aim,
  UserFilled,
  Lock,
  ArrowRight,
  DataAnalysis,
  Timer
} from '@element-plus/icons-vue'
import { Car, Shield, CloudSun } from 'lucide-vue-next'
import { ModernButton } from '@/components/ui'
import ThemeToggle from '@/components/ThemeToggle.vue'

const router = useRouter()

// 动态欢迎区域状态
const displayedText = ref('')
const fullText = '智能化管理，让新能源汽车更高效、更安全、更环保'
const typewriterActive = ref(false)

// 打字机效果
const startTypewriter = () => {
  if (typewriterActive.value) return
  typewriterActive.value = true
  displayedText.value = ''
  
  let index = 0
  const timer = setInterval(() => {
    if (index < fullText.length) {
      displayedText.value += fullText[index]
      index++
    } else {
      clearInterval(timer)
      setTimeout(() => {
        typewriterActive.value = false
      }, 2000)
    }
  }, 150)
}

// 导航方法
const navigateTo = (path: string) => {
  router.push(path)
}

const goToDashboard = () => {
  // 跳转到管理系统仪表盘
  router.push('/dashboard')
}

// 了解更多功能
const learnMore = () => {
  // 平滑滚动到功能特色区域
  const featuresSection = document.getElementById('features')
  if (featuresSection) {
    featuresSection.scrollIntoView({ 
      behavior: 'smooth',
      block: 'start'
    })
  }
}

// 观看演示功能
const watchDemo = () => {
  // 显示演示开始提示
  ElMessage({
    message: '🎬 正在启动系统演示模式，即将为您展示核心功能...',
    type: 'success',
    duration: 3000
  })
  
  // 延迟跳转到Dashboard页面进行演示
  setTimeout(() => {
    // 跳转到Dashboard页面并传递演示模式参数
    router.push({
      path: '/dashboard',
      query: { demo: 'true' }
    })
  }, 1000)
}

// 导航链接点击处理
const handleNavClick = (target: string) => {
  const element = document.getElementById(target.replace('#', ''))
  if (element) {
    element.scrollIntoView({ 
      behavior: 'smooth',
      block: 'start'
    })
  }
}

// 底部链接处理
const handleFooterLink = (type: string) => {
  switch (type) {
    case 'battery':
      router.push('/battery')
      break
    case 'charging':
      router.push('/charging')
      break
    case 'adas':
      router.push('/adas')
      break
    case 'network':
      router.push('/vehicle-network')
      break
    case 'docs':
    case 'api':
    case 'guide':
    case 'faq':
      // 技术支持相关页面统一跳转到技术支持页面
      router.push('/tech-support')
      break
    case 'about':
    case 'contact':
    case 'join':
    case 'partners':
      // 关于我们相关页面统一跳转到关于我们页面
      router.push('/about-us')
      break
    default:
      ElMessage.info('该功能正在开发中，敬请期待！')
  }
}

// 组件挂载时启动动画
onMounted(() => {
  // 启动打字机效果
  setTimeout(() => {
    startTypewriter()
  }, 1000)
  
  // 定期重启打字机效果
  setInterval(() => {
    if (!typewriterActive.value) {
      startTypewriter()
    }
  }, 8000)
})
</script>

<style scoped>
.home-container {
  min-height: 100vh;
  background: var(--bg-primary);
  color: var(--text-primary);
  position: relative;
  overflow-x: hidden;
  transition: all 0.3s ease;
}

/* 现代化顶部导航 */
.modern-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: var(--bg-header);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-primary);
  transition: all 0.3s ease;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.brand-section {
  display: flex;
  align-items: center;
}

.brand-logo {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logo-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #22c55e, #16a34a);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  box-shadow: 0 4px 15px rgba(34, 197, 94, 0.3);
}

.brand-text h1 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, #22c55e, #4ade80);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.brand-text span {
  font-size: 0.875rem;
  color: var(--text-secondary);
  display: block;
}

.nav-menu {
  display: flex;
  gap: 2rem;
}

.nav-link {
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
  cursor: pointer;
}

.nav-link:hover {
  color: #4ade80;
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(135deg, #22c55e, #4ade80);
  transition: width 0.3s ease;
}

.nav-link:hover::after {
  width: 100%;
}

.header-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

/* 主视觉区域 */
.hero-banner {
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  padding-top: 80px;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

.gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(60px);
  opacity: 0.6;
  animation: float 6s ease-in-out infinite;
}

.orb-1 {
  width: 300px;
  height: 300px;
  background: linear-gradient(135deg, #22c55e, #16a34a);
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.orb-2 {
  width: 200px;
  height: 200px;
  background: linear-gradient(135deg, #4ade80, #22c55e);
  top: 60%;
  right: 20%;
  animation-delay: 2s;
}

.orb-3 {
  width: 150px;
  height: 150px;
  background: linear-gradient(135deg, #10b981, #059669);
  bottom: 20%;
  left: 60%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  position: relative;
  z-index: 10;
}

.hero-text {
  max-width: 600px;
}

.main-title {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  display: flex;
  flex-direction: column;
}

.title-line {
  display: block;
  margin-bottom: 0.5rem;
}

.title-line.highlight {
  background: linear-gradient(135deg, #22c55e, #4ade80, #16a34a);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  background-size: 200% 200%;
  animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.hero-description {
  font-size: 1.25rem;
  color: var(--text-primary);
  line-height: 1.6;
  margin-bottom: 2rem;
  min-height: 2em;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.typing-cursor {
  color: #22c55e;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

.hero-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.car-showcase {
  position: relative;
  width: 400px;
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.car-icon {
  width: 120px;
  height: 120px;
  background: linear-gradient(135deg, #22c55e, #16a34a);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60px;
  position: relative;
  z-index: 10;
  box-shadow: 0 20px 40px rgba(34, 197, 94, 0.3);
}

.tech-rings {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.tech-ring {
  position: absolute;
  border: 2px solid rgba(34, 197, 94, 0.3);
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.ring-1 {
  width: 200px;
  height: 200px;
  animation: rotate 10s linear infinite;
}

.ring-2 {
  width: 280px;
  height: 280px;
  animation: rotate 15s linear infinite reverse;
  border-color: rgba(74, 222, 128, 0.3);
}

.ring-3 {
  width: 360px;
  height: 360px;
  animation: rotate 20s linear infinite;
  border-color: rgba(16, 185, 129, 0.3);
}

@keyframes rotate {
  from { transform: translate(-50%, -50%) rotate(0deg); }
  to { transform: translate(-50%, -50%) rotate(360deg); }
}

.floating-tech {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.tech-item {
  position: absolute;
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  animation: pulse 2s ease-in-out infinite;
}

.item-1 {
  top: 20%;
  left: 20%;
  color: #22c55e;
  animation-delay: 0s;
}

.item-2 {
  top: 20%;
  right: 20%;
  color: #4ade80;
  animation-delay: 0.5s;
}

.item-3 {
  bottom: 20%;
  left: 20%;
  color: #10b981;
  animation-delay: 1s;
}

.item-4 {
  bottom: 20%;
  right: 20%;
  color: #f59e0b;
  animation-delay: 1.5s;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.8; }
  50% { transform: scale(1.1); opacity: 1; }
}

/* 区域标题样式 */
.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #22c55e, #4ade80);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-subtitle {
  font-size: 1.125rem;
  color: var(--text-primary);
  max-width: 600px;
  margin: 0 auto;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* 模块区域 */
.modules-section {
  padding: 6rem 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.modules-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  justify-items: center;
}

/* 特色展示区域 */
.features-section {
  padding: 6rem 2rem;
  background: var(--bg-secondary);
  backdrop-filter: blur(10px);
  border-top: 1px solid var(--border-secondary);
  border-bottom: 1px solid var(--border-secondary);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  max-width: 1000px;
  margin: 0 auto;
}

.feature-card {
  background: var(--bg-card);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-primary);
  border-radius: 20px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(74, 222, 128, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-10px);
  border-color: rgba(34, 197, 94, 0.3);
  box-shadow: 0 20px 40px rgba(34, 197, 94, 0.2);
}

.feature-card:hover::before {
  opacity: 1;
}

.feature-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #22c55e, #16a34a);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  margin: 0 auto 1.5rem;
  position: relative;
  z-index: 10;
}

.feature-card h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-primary);
  position: relative;
  z-index: 10;
}

.feature-card p {
  color: var(--text-primary);
  line-height: 1.6;
  position: relative;
  z-index: 10;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* 现代化底部 */
.modern-footer {
  background: var(--bg-footer);
  border-top: 1px solid var(--border-primary);
  padding: 4rem 0 2rem;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.footer-main {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 4rem;
  margin-bottom: 3rem;
}

.footer-brand {
  max-width: 400px;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.footer-logo .logo-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #22c55e, #16a34a);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.footer-logo h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, #22c55e, #4ade80);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.footer-brand p {
  color: var(--text-primary);
  line-height: 1.6;
  margin-bottom: 0.5rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.footer-links {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
}

.link-group h4 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.link-group a {
  display: block;
  color: var(--text-secondary);
  text-decoration: none;
  margin-bottom: 0.5rem;
  transition: color 0.3s ease;
  cursor: pointer;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.link-group a:hover {
  color: #4ade80;
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom p {
  color: var(--text-secondary);
  margin: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.footer-actions {
  display: flex;
  gap: 1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
  }
  
  .nav-menu {
    display: none;
  }
  
  .hero-container {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }
  
  .main-title {
    font-size: 2.5rem;
  }
  
  .car-showcase {
    width: 300px;
    height: 300px;
  }
  
  .footer-main {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .footer-links {
    grid-template-columns: 1fr;
  }
  
  .footer-bottom {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
}

/* 翻转卡片样式 */
.flip-card {
  background-color: transparent;
  width: 190px;
  height: 254px;
  perspective: 1000px;
  font-family: sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
}

.flip-card:hover {
  transform: translateY(-5px);
}

.title {
  font-size: 1.2em;
  font-weight: 900;
  text-align: center;
  margin: 10px 0;
  line-height: 1.2;
}

.flip-card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  text-align: center;
  transition: transform 0.8s;
  transform-style: preserve-3d;
}

.flip-card:hover .flip-card-inner {
  transform: rotateY(180deg);
}

.flip-card-front, .flip-card-back {
  box-shadow: 0 8px 14px 0 rgba(0,0,0,0.2);
  position: absolute;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 1rem;
  padding: 20px;
  box-sizing: border-box;
}

.flip-card-front {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  color: var(--text-primary);
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px var(--shadow-primary);
}

.flip-card-back {
  background: var(--bg-card-hover);
  border: 1px solid var(--border-secondary);
  color: var(--text-primary);
  transform: rotateY(180deg);
  backdrop-filter: blur(25px);
  box-shadow: 0 8px 32px var(--shadow-secondary);
}

.flip-card-front p, .flip-card-back p {
  margin: 5px 0;
  font-size: 14px;
  line-height: 1.4;
}

.flip-card-back p:not(.title) {
  font-size: 12px;
  opacity: 0.9;
}

.module-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
  flex-shrink: 0;
  margin-bottom: 10px;
}

.module-icon.dashboard {
  background: linear-gradient(135deg, #22c55e 0%, #4ade80 100%);
  box-shadow: 0 4px 20px rgba(34, 197, 94, 0.3);
}

.module-icon.battery {
  background: linear-gradient(135deg, #16a34a 0%, #22c55e 100%);
  box-shadow: 0 4px 20px rgba(22, 163, 74, 0.3);
}

.module-icon.charging {
  background: linear-gradient(135deg, #4ade80 0%, #6ee7b7 100%);
  box-shadow: 0 4px 20px rgba(74, 222, 128, 0.3);
}

.module-icon.adas {
  background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
  box-shadow: 0 4px 20px rgba(16, 185, 129, 0.3);
}

.module-icon.network {
  background: linear-gradient(135deg, #059669 0%, #10b981 100%);
  box-shadow: 0 4px 20px rgba(5, 150, 105, 0.3);
}

.module-icon.ecosystem {
  background: linear-gradient(135deg, #065f46 0%, #047857 100%);
  box-shadow: 0 4px 20px rgba(6, 95, 70, 0.3);
}

.module-icon.security {
  background: linear-gradient(135deg, #14532d 0%, #166534 100%);
  box-shadow: 0 4px 20px rgba(20, 83, 45, 0.3);
}

.module-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.module-title {
  font-size: 18px;
  font-weight: bold;
  margin: 0;
  color: var(--text-primary);
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.module-description {
  font-size: 14px;
  opacity: 0.95;
  margin: 0;
  line-height: 1.4;
  color: var(--text-primary);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.module-features {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.feature-tag {
  padding: 4px 8px;
  background: var(--bg-tag);
  border-radius: 12px;
  font-size: 12px;
  color: var(--text-primary);
}

.module-arrow {
  font-size: 20px;
  opacity: 0.6;
  transition: all 0.3s ease;
}

.module-card:hover .module-arrow {
  opacity: 1;
  transform: translateX(5px);
}

/* 响应式设计补充 */
@media (max-width: 1024px) {
  .modules-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .hero-container {
    gap: 3rem;
  }
}

@media (max-width: 480px) {
  .modules-grid {
    grid-template-columns: 1fr;
  }
  
  .hero-banner {
    padding: 4rem 1rem;
  }
  
  .main-title {
    font-size: 2rem;
  }
  
  .hero-description {
    font-size: 1rem;
  }
  
  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .car-showcase {
    width: 250px;
    height: 250px;
  }
  
  .tech-rings {
    width: 200px;
    height: 200px;
  }
}

/* 主题切换按钮样式 */
.theme-toggle-wrapper {
  display: flex;
  align-items: center;
  margin-right: 16px;
}

.theme-toggle-wrapper :deep(.theme-toggle) {
  transform: scale(0.8);
}

@media (max-width: 768px) {
  .theme-toggle-wrapper {
    margin-right: 8px;
  }
  
  .theme-toggle-wrapper :deep(.theme-toggle) {
    transform: scale(0.7);
  }
}
</style>
