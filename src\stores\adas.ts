import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface DetectedObject {
  id: string
  type: 'vehicle' | 'pedestrian' | 'cyclist' | 'obstacle'
  zone: 'front' | 'left' | 'right' | 'rear'
  distance: number
  x: number
  y: number
}

export interface Warning {
  id: string
  type: string
  title: string
  description: string
  level: 'low' | 'medium' | 'high' | 'critical'
  timestamp: string
  active: boolean
}

export interface ParkingSpace {
  id: string
  type: 'parallel' | 'perpendicular' | 'angle'
  length: number
  width: number
  difficulty: number
  status: 'available' | 'occupied' | 'reserved'
}

export interface ParkingStatus {
  title: string
  description: string
  inProgress: boolean
  completed: boolean
  step: string
}

export interface BehaviorScores {
  safety: number
  economy: number
  comfort: number
  eco: number
}

export interface BehaviorMetrics {
  avgSpeed: number
  harshEvents: number
  drivingTime: number
  distance: number
}

export const useAdasStore = defineStore('adas', () => {
  // 状态
  const detectedObjects = ref<DetectedObject[]>([])
  const warnings = ref<Warning[]>([])
  const parkingSpaces = ref<ParkingSpace[]>([])
  const perceptionAccuracy = ref(95)
  const todayWarnings = ref(3)
  const weekWarnings = ref(15)
  const warningRate = ref(2.1)
  const drivingScore = ref(85)
  const behaviorScores = ref<BehaviorScores>({
    safety: 88,
    economy: 82,
    comfort: 85,
    eco: 90
  })
  const behaviorMetrics = ref<BehaviorMetrics>({
    avgSpeed: 45,
    harshEvents: 2,
    drivingTime: 2.5,
    distance: 125
  })
  const parkingStatus = ref<ParkingStatus>({
    title: '待机状态',
    description: '系统准备就绪，等待指令',
    inProgress: false,
    completed: false,
    step: ''
  })

  // 方法
  const fetchDetectedObjects = async () => {
    try {
      const response = await fetch('/api/vehicles/status')
      const data = await response.json()
      if (data.success && data.data.length > 0) {
        // 基于车辆状态生成检测对象
        detectedObjects.value = data.data.slice(0, 3).map((vehicle: any, index: number) => ({
          id: vehicle.id || `obj-${index}`,
          type: 'vehicle' as const,
          zone: ['front', 'left', 'right'][index % 3] as 'front' | 'left' | 'right' | 'rear',
          distance: Math.random() * 20 + 5,
          x: Math.random() * 100,
          y: Math.random() * 100
        }))
      } else {
        detectedObjects.value = []
      }
    } catch (error) {
      console.error('获取检测对象失败:', error)
      detectedObjects.value = []
    }
  }

  const fetchWarnings = async () => {
    try {
      const response = await fetch('/api/alerts/recent')
      const data = await response.json()
      if (data.success && data.data.length > 0) {
        warnings.value = data.data.map((alert: any) => ({
          id: alert.id || `warning-${Date.now()}`,
          type: alert.type || 'general',
          title: alert.message || '系统预警',
          description: alert.description || alert.message || '请注意安全',
          level: alert.priority || 'medium',
          timestamp: alert.timestamp || new Date().toISOString(),
          active: true
        }))
      } else {
        warnings.value = []
      }
    } catch (error) {
      console.error('获取预警信息失败:', error)
      warnings.value = []
    }
  }

  const fetchParkingSpaces = async () => {
    parkingSpaces.value = [
      {
        id: '1',
        type: 'parallel',
        length: 6.0,
        width: 2.5,
        difficulty: 3,
        status: 'available'
      },
      {
        id: '2',
        type: 'perpendicular',
        length: 5.5,
        width: 2.8,
        difficulty: 2,
        status: 'available'
      },
      {
        id: '3',
        type: 'angle',
        length: 5.8,
        width: 2.6,
        difficulty: 4,
        status: 'occupied'
      }
    ]
  }

  const fetchBehaviorData = async (userId?: string) => {
    try {
      const url = userId ? `/api/driving-behavior?user_id=${userId}` : '/api/driving-behavior'
      const response = await fetch(url)
      const data = await response.json()
      if (data.success && data.data.length > 0) {
        const latestBehavior = data.data[0]
        // 更新驾驶评分
        drivingScore.value = Math.round((latestBehavior.safety_score + latestBehavior.eco_score) / 2)
        // 更新行为评分
        behaviorScores.value = {
          safety: latestBehavior.safety_score || 85,
          economy: latestBehavior.eco_score || 80,
          comfort: latestBehavior.comfort_score || 88,
          eco: latestBehavior.energy_efficiency || 75
        }
        // 更新行为指标
        behaviorMetrics.value = {
          avgSpeed: latestBehavior.avg_speed || 45,
          harshEvents: latestBehavior.harsh_events || 2,
          drivingTime: latestBehavior.driving_time || 120,
          distance: latestBehavior.distance || 85
        }
      }
    } catch (error) {
      console.error('获取驾驶行为数据失败:', error)
    }
  }

  const refreshPerception = () => {
    perceptionAccuracy.value = Math.floor(Math.random() * 10) + 90
    fetchDetectedObjects()
  }

  const startParking = async (spaceId: string, settings: any) => {
    parkingStatus.value = {
      title: '泊车进行中',
      description: '正在执行自动泊车程序',
      inProgress: true,
      completed: false,
      step: '检测车位环境...'
    }
  }

  const stopParking = async () => {
    parkingStatus.value = {
      title: '泊车已停止',
      description: '用户手动停止泊车程序',
      inProgress: false,
      completed: false,
      step: ''
    }
  }

  const pauseParking = async () => {
    parkingStatus.value = {
      title: '泊车已暂停',
      description: '泊车程序暂停，等待继续指令',
      inProgress: false,
      completed: false,
      step: '暂停中...'
    }
  }

  return {
    // 状态
    detectedObjects,
    warnings,
    parkingSpaces,
    perceptionAccuracy,
    todayWarnings,
    weekWarnings,
    warningRate,
    drivingScore,
    behaviorScores,
    behaviorMetrics,
    parkingStatus,
    // 方法
    fetchDetectedObjects,
    fetchWarnings,
    fetchParkingSpaces,
    fetchBehaviorData,
    refreshPerception,
    startParking,
    stopParking,
    pauseParking
  }
})