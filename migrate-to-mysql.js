/**
 * 数据库迁移脚本：从 Supabase 迁移到 MySQL
 * 使用说明：
 * 1. 确保已安装 MySQL 并创建了数据库
 * 2. 运行 mysql_init.sql 初始化表结构
 * 3. 更新 .env 文件中的 MySQL 连接信息
 * 4. 运行此脚本进行数据迁移（可选）
 */

const mysql = require('mysql2/promise')
const { createClient } = require('@supabase/supabase-js')
require('dotenv').config()

// MySQL 连接配置
const mysqlConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME || 'ev_management',
  charset: 'utf8mb4'
}

// Supabase 配置（如果需要迁移现有数据）
const supabaseUrl = process.env.SUPABASE_URL
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY

async function testMySQLConnection() {
  try {
    console.log('🔍 测试 MySQL 连接...')
    const connection = await mysql.createConnection(mysqlConfig)
    await connection.execute('SELECT 1')
    await connection.end()
    console.log('✅ MySQL 连接成功！')
    return true
  } catch (error) {
    console.error('❌ MySQL 连接失败:', error.message)
    console.log('\n请检查以下配置：')
    console.log('1. MySQL 服务是否启动')
    console.log('2. .env 文件中的数据库配置是否正确')
    console.log('3. 数据库是否已创建')
    console.log('4. 用户权限是否正确')
    return false
  }
}

async function checkTableStructure() {
  try {
    console.log('\n🔍 检查数据库表结构...')
    const connection = await mysql.createConnection(mysqlConfig)
    
    const tables = [
      'users', 'vehicles', 'batteries', 'battery_trace',
      'charging_stations', 'charging_sessions', 'carbon_credit',
      'driving_behavior', 'fleet_management', 'fleet_vehicles',
      'edge_devices', 'edge_device_data', 'edge_tasks'
    ]
    
    for (const table of tables) {
      const [rows] = await connection.execute(
        'SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = ? AND table_name = ?',
        [mysqlConfig.database, table]
      )
      
      if (rows[0].count > 0) {
        console.log(`✅ 表 ${table} 存在`)
      } else {
        console.log(`❌ 表 ${table} 不存在`)
      }
    }
    
    await connection.end()
    console.log('\n📋 表结构检查完成')
  } catch (error) {
    console.error('❌ 检查表结构失败:', error.message)
  }
}

async function insertSampleData() {
  try {
    console.log('\n📝 插入示例数据...')
    const connection = await mysql.createConnection(mysqlConfig)
    
    // 检查是否已有数据
    const [userRows] = await connection.execute('SELECT COUNT(*) as count FROM users')
    if (userRows[0].count > 0) {
      console.log('⚠️  数据库中已有数据，跳过示例数据插入')
      await connection.end()
      return
    }
    
    // 插入示例用户
    await connection.execute(`
      INSERT INTO users (id, email, name, user_type) VALUES 
      ('user-001', '<EMAIL>', '系统管理员', 'enterprise'),
      ('user-002', '<EMAIL>', '车队管理员', 'fleet'),
      ('user-003', '<EMAIL>', '个人用户', 'individual')
    `)
    
    // 插入示例车辆
    await connection.execute(`
      INSERT INTO vehicles (id, user_id, vin, license_plate, brand, model, year, vehicle_type, battery_capacity, max_range) VALUES 
      ('vehicle-001', 'user-001', 'WVWZZZ1JZXW123456', '京A12345', '特斯拉', 'Model 3', 2023, 'bev', 75.0, 500),
      ('vehicle-002', 'user-002', 'WVWZZZ1JZXW123457', '京B67890', '比亚迪', '汉EV', 2023, 'bev', 85.4, 605),
      ('vehicle-003', 'user-003', 'WVWZZZ1JZXW123458', '京C11111', '蔚来', 'ES6', 2023, 'bev', 100.0, 610)
    `)
    
    // 插入示例电池
    await connection.execute(`
      INSERT INTO batteries (id, vehicle_id, battery_id, manufacturer, capacity, current_soh, current_soc) VALUES 
      ('battery-001', 'vehicle-001', 'BAT001', '宁德时代', 75.0, 98.5, 85.2),
      ('battery-002', 'vehicle-002', 'BAT002', '比亚迪', 85.4, 99.1, 72.8),
      ('battery-003', 'vehicle-003', 'BAT003', '宁德时代', 100.0, 97.8, 90.5)
    `)
    
    // 插入示例边缘设备
    await connection.execute(`
      INSERT INTO edge_devices (id, name, type, status, location, capabilities, last_heartbeat) VALUES 
      ('edge-001', '温度传感器-01', 'sensor', 'online', JSON_OBJECT('lat', 39.9075, 'lng', 116.4574, 'address', '北京国贸'), JSON_ARRAY('temperature', 'humidity'), NOW()),
      ('edge-002', '智能控制器-01', 'controller', 'online', JSON_OBJECT('lat', 31.2397, 'lng', 121.4999, 'address', '上海陆家嘴'), JSON_ARRAY('power_control', 'load_balancing'), NOW()),
      ('edge-003', '网关设备-01', 'gateway', 'online', JSON_OBJECT('lat', 22.5431, 'lng', 113.9344, 'address', '深圳科技园'), JSON_ARRAY('data_aggregation', 'protocol_conversion'), NOW())
    `)
    
    await connection.end()
    console.log('✅ 示例数据插入成功！')
  } catch (error) {
    console.error('❌ 插入示例数据失败:', error.message)
  }
}

async function migrateFromSupabase() {
  if (!supabaseUrl || !supabaseKey) {
    console.log('\n⚠️  未配置 Supabase 连接信息，跳过数据迁移')
    return
  }
  
  try {
    console.log('\n🔄 开始从 Supabase 迁移数据...')
    const supabase = createClient(supabaseUrl, supabaseKey)
    const connection = await mysql.createConnection(mysqlConfig)
    
    // 这里可以添加具体的数据迁移逻辑
    // 例如：从 Supabase 读取数据，然后插入到 MySQL
    
    console.log('✅ 数据迁移完成！')
    await connection.end()
  } catch (error) {
    console.error('❌ 数据迁移失败:', error.message)
  }
}

async function updateApiRoutes() {
  console.log('\n📝 API 路由更新说明：')
  console.log('✅ edge-devices.ts - 已更新为 MySQL')
  console.log('✅ batteries.ts - 已更新为 MySQL')
  console.log('⚠️  其他路由文件需要手动更新：')
  console.log('   - users.ts')
  console.log('   - vehicles.ts')
  console.log('   - charging-stations.ts')
  console.log('   - charging-sessions.ts')
  console.log('   - carbon-credits.ts')
  console.log('   - driving-behavior.ts')
  console.log('   - fleet-management.ts')
  console.log('   - auth.ts')
}

async function main() {
  console.log('🚀 开始 MySQL 数据库迁移流程\n')
  
  // 1. 测试 MySQL 连接
  const connected = await testMySQLConnection()
  if (!connected) {
    process.exit(1)
  }
  
  // 2. 检查表结构
  await checkTableStructure()
  
  // 3. 插入示例数据
  await insertSampleData()
  
  // 4. 从 Supabase 迁移数据（可选）
  await migrateFromSupabase()
  
  // 5. 显示 API 路由更新状态
  await updateApiRoutes()
  
  console.log('\n🎉 MySQL 数据库迁移流程完成！')
  console.log('\n📋 后续步骤：')
  console.log('1. 更新其余 API 路由文件以使用 MySQL')
  console.log('2. 测试所有 API 接口')
  console.log('3. 更新前端服务配置（如需要）')
  console.log('4. 部署到生产环境')
}

// 运行迁移
if (require.main === module) {
  main().catch(console.error)
}

module.exports = {
  testMySQLConnection,
  checkTableStructure,
  insertSampleData,
  migrateFromSupabase
}