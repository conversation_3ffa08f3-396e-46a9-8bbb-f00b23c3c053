<template>
  <div class="realtime-monitor">
    <!-- 连接状态指示器 -->
    <div class="connection-status" :class="connectionStatusClass">
      <div class="status-indicator">
        <div class="status-dot" :class="connectionStatusClass"></div>
        <span class="status-text">{{ connectionStatusText }}</span>
      </div>
      <div class="connection-info">
        <span class="reconnect-info" v-if="reconnectAttempts > 0">
          重连次数: {{ reconnectAttempts }}/{{ maxReconnectAttempts }}
        </span>
      </div>
    </div>

    <!-- 实时数据面板 -->
    <div class="data-panels">
      <!-- 电池状态 -->
      <div class="data-panel battery-panel">
        <div class="panel-header">
          <Battery class="panel-icon" />
          <h3>电池状态</h3>
          <div class="last-update">{{ formatTime(batteryLastUpdate) }}</div>
        </div>
        <div class="panel-content" v-if="batteryStatus">
          <div class="battery-level">
            <div class="level-bar">
              <div 
                class="level-fill" 
                :style="{ width: batteryStatus.level + '%' }"
                :class="getBatteryLevelClass(batteryStatus.level)"
              ></div>
            </div>
            <span class="level-text">{{ batteryStatus.level }}%</span>
          </div>
          <div class="battery-details">
            <div class="detail-item">
              <span class="label">温度:</span>
              <span class="value" :class="getTemperatureClass(batteryStatus.temperature)">
                {{ batteryStatus.temperature }}°C
              </span>
            </div>
            <div class="detail-item">
              <span class="label">电压:</span>
              <span class="value">{{ batteryStatus.voltage }}V</span>
            </div>
            <div class="detail-item">
              <span class="label">电流:</span>
              <span class="value">{{ batteryStatus.current }}A</span>
            </div>
            <div class="detail-item">
              <span class="label">健康度:</span>
              <span class="value" :class="getHealthClass(batteryStatus.health)">
                {{ batteryStatus.health }}
              </span>
            </div>
            <div class="detail-item">
              <span class="label">充电状态:</span>
              <span class="value">{{ batteryStatus.chargingStatus }}</span>
            </div>
          </div>
        </div>
        <div class="panel-content no-data" v-else>
          <span>暂无数据</span>
        </div>
      </div>

      <!-- 车辆状态 -->
      <div class="data-panel vehicle-panel">
        <div class="panel-header">
          <Car class="panel-icon" />
          <h3>车辆状态</h3>
          <div class="last-update">{{ formatTime(vehicleLastUpdate) }}</div>
        </div>
        <div class="panel-content" v-if="vehicleStatus">
          <div class="vehicle-details">
            <div class="detail-item">
              <span class="label">速度:</span>
              <span class="value speed">{{ vehicleStatus.speed }} km/h</span>
            </div>
            <div class="detail-item">
              <span class="label">位置:</span>
              <span class="value location">
                {{ vehicleStatus.location.lat.toFixed(6) }}, 
                {{ vehicleStatus.location.lng.toFixed(6) }}
              </span>
            </div>
            <div class="detail-item">
              <span class="label">方向:</span>
              <span class="value">{{ vehicleStatus.direction }}°</span>
            </div>
            <div class="detail-item">
              <span class="label">引擎:</span>
              <span class="value" :class="getEngineStatusClass(vehicleStatus.engineStatus)">
                {{ vehicleStatus.engineStatus }}
              </span>
            </div>
            <div class="detail-item">
              <span class="label">燃油:</span>
              <span class="value">{{ vehicleStatus.fuelLevel }}%</span>
            </div>
            <div class="detail-item">
              <span class="label">里程:</span>
              <span class="value">{{ vehicleStatus.mileage.toLocaleString() }} km</span>
            </div>
          </div>
        </div>
        <div class="panel-content no-data" v-else>
          <span>暂无数据</span>
        </div>
      </div>

      <!-- 充电站状态 -->
      <div class="data-panel stations-panel">
        <div class="panel-header">
          <Zap class="panel-icon" />
          <h3>充电站状态</h3>
          <div class="last-update">{{ formatTime(stationsLastUpdate) }}</div>
        </div>
        <div class="panel-content" v-if="chargingStations && chargingStations.length > 0">
          <div class="stations-summary">
            <div class="summary-item">
              <span class="count available">{{ availableStations }}</span>
              <span class="label">可用</span>
            </div>
            <div class="summary-item">
              <span class="count occupied">{{ occupiedStations }}</span>
              <span class="label">使用中</span>
            </div>
            <div class="summary-item">
              <span class="count maintenance">{{ maintenanceStations }}</span>
              <span class="label">维护中</span>
            </div>
            <div class="summary-item">
              <span class="count offline">{{ offlineStations }}</span>
              <span class="label">离线</span>
            </div>
          </div>
          <div class="stations-list">
            <div 
              v-for="station in chargingStations.slice(0, 5)" 
              :key="station.id"
              class="station-item"
            >
              <div class="station-status" :class="station.status"></div>
              <div class="station-info">
                <span class="station-id">{{ station.id }}</span>
                <span class="station-power">{{ station.power }}kW</span>
                <span class="station-queue" v-if="station.queue > 0">
                  排队: {{ station.queue }}
                </span>
              </div>
            </div>
          </div>
        </div>
        <div class="panel-content no-data" v-else>
          <span>暂无数据</span>
        </div>
      </div>
    </div>

    <!-- 实时告警 -->
    <div class="alerts-section">
      <div class="section-header">
        <AlertTriangle class="section-icon" />
        <h3>实时告警</h3>
        <ModernButton 
          size="small" 
          type="danger" 
          plain
          @click="clearAlerts"
          v-if="alerts.length > 0"
        >
          清除全部
        </ModernButton>
      </div>
      <div class="alerts-list" v-if="alerts.length > 0">
        <div 
          v-for="alert in alerts.slice(0, 10)" 
          :key="alert.id"
          class="alert-item"
          :class="[alert.type, alert.priority]"
        >
          <div class="alert-icon">
            <AlertTriangle v-if="alert.type === 'warning'" />
            <XCircle v-else-if="alert.type === 'error'" />
            <Info v-else />
          </div>
          <div class="alert-content">
            <div class="alert-message">{{ alert.message }}</div>
            <div class="alert-time">{{ formatTime(alert.timestamp) }}</div>
          </div>
          <div class="alert-priority" :class="alert.priority">
            {{ getPriorityText(alert.priority) }}
          </div>
        </div>
      </div>
      <div class="no-alerts" v-else>
        <span>暂无告警</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { Battery, Car, Zap, AlertTriangle, XCircle, Info } from 'lucide-vue-next'
import { realtimeDataManager, type RealtimeData } from '@/services/websocketService'
import { ElMessage } from 'element-plus'
import ModernButton from '@/components/ModernButton.vue'

// 响应式数据
const connectionState = ref('CLOSED')
const reconnectAttempts = ref(0)
const maxReconnectAttempts = ref(10)
const batteryStatus = ref<RealtimeData['batteryStatus'] | null>(null)
const vehicleStatus = ref<RealtimeData['vehicleStatus'] | null>(null)
const chargingStations = ref<RealtimeData['chargingStations']>([])
const alerts = ref<RealtimeData['alerts']>([])
const batteryLastUpdate = ref(0)
const vehicleLastUpdate = ref(0)
const stationsLastUpdate = ref(0)

// 计算属性
const connectionStatusClass = computed(() => {
  switch (connectionState.value) {
    case 'OPEN': return 'connected'
    case 'CONNECTING': return 'connecting'
    case 'CLOSING': return 'disconnecting'
    default: return 'disconnected'
  }
})

const connectionStatusText = computed(() => {
  switch (connectionState.value) {
    case 'OPEN': return '已连接'
    case 'CONNECTING': return '连接中'
    case 'CLOSING': return '断开中'
    default: return '已断开'
  }
})

const availableStations = computed(() => 
  chargingStations.value.filter(s => s.status === 'available').length
)

const occupiedStations = computed(() => 
  chargingStations.value.filter(s => s.status === 'occupied').length
)

const maintenanceStations = computed(() => 
  chargingStations.value.filter(s => s.status === 'maintenance').length
)

const offlineStations = computed(() => 
  chargingStations.value.filter(s => s.status === 'offline').length
)

// 方法
const formatTime = (timestamp: number) => {
  if (!timestamp) return '--'
  const date = new Date(timestamp)
  return date.toLocaleTimeString('zh-CN', { 
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const getBatteryLevelClass = (level: number) => {
  if (level <= 20) return 'low'
  if (level <= 50) return 'medium'
  return 'high'
}

const getTemperatureClass = (temp: number) => {
  if (temp > 45) return 'hot'
  if (temp < 0) return 'cold'
  return 'normal'
}

const getHealthClass = (health: string) => {
  switch (health.toLowerCase()) {
    case 'good': return 'good'
    case 'fair': return 'fair'
    case 'poor': return 'poor'
    default: return 'unknown'
  }
}

const getEngineStatusClass = (status: string) => {
  switch (status.toLowerCase()) {
    case 'running': return 'running'
    case 'idle': return 'idle'
    case 'off': return 'off'
    default: return 'unknown'
  }
}

const getPriorityText = (priority: string) => {
  switch (priority) {
    case 'critical': return '严重'
    case 'high': return '高'
    case 'medium': return '中'
    case 'low': return '低'
    default: return '未知'
  }
}

const clearAlerts = () => {
  realtimeDataManager.clearAlerts()
  ElMessage.success('已清除所有告警')
}

const updateConnectionState = () => {
  connectionState.value = realtimeDataManager.getConnectionState()
  const stats = realtimeDataManager.wsService?.getStats()
  if (stats) {
    reconnectAttempts.value = stats.reconnectAttempts
    maxReconnectAttempts.value = stats.maxReconnectAttempts
  }
}

// 生命周期
onMounted(async () => {
  // 订阅数据更新
  realtimeDataManager.subscribe('batteryStatus', (data: RealtimeData['batteryStatus']) => {
    batteryStatus.value = data
    batteryLastUpdate.value = Date.now()
  })

  realtimeDataManager.subscribe('vehicleStatus', (data: RealtimeData['vehicleStatus']) => {
    vehicleStatus.value = data
    vehicleLastUpdate.value = Date.now()
  })

  realtimeDataManager.subscribe('chargingStations', (data: RealtimeData['chargingStations']) => {
    chargingStations.value = data
    stationsLastUpdate.value = Date.now()
  })

  realtimeDataManager.subscribe('alerts', (data: RealtimeData['alerts']) => {
    alerts.value = data
  })

  // 监听连接状态变化
  const wsService = realtimeDataManager.wsService
  wsService.on('connected', updateConnectionState)
  wsService.on('disconnected', updateConnectionState)
  wsService.on('error', updateConnectionState)

  // 定期更新连接状态
  const stateInterval = setInterval(updateConnectionState, 1000)

  // 请求通知权限
  await realtimeDataManager.requestNotificationPermission()

  // 尝试连接
  try {
    await realtimeDataManager.connect()
  } catch (error) {
    console.error('Failed to connect to WebSocket:', error)
    ElMessage.error('WebSocket连接失败')
  }

  // 清理函数
  onUnmounted(() => {
    clearInterval(stateInterval)
  })
})

onUnmounted(() => {
  // 取消订阅
  realtimeDataManager.unsubscribe('batteryStatus')
  realtimeDataManager.unsubscribe('vehicleStatus')
  realtimeDataManager.unsubscribe('chargingStations')
  realtimeDataManager.unsubscribe('alerts')
})
</script>

<style scoped>
.realtime-monitor {
  padding: 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  min-height: 100vh;
}

/* 连接状态 */
.connection-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-dot.connected {
  background: #10b981;
}

.status-dot.connecting {
  background: #f59e0b;
}

.status-dot.disconnected {
  background: #ef4444;
  animation: none;
}

.status-text {
  font-weight: 600;
  color: #374151;
}

.reconnect-info {
  font-size: 12px;
  color: #6b7280;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 数据面板 */
.data-panels {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.data-panel {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.panel-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px 20px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

.panel-icon {
  width: 20px;
  height: 20px;
}

.panel-header h3 {
  margin: 0;
  flex: 1;
  font-size: 16px;
  font-weight: 600;
}

.last-update {
  font-size: 12px;
  opacity: 0.8;
}

.panel-content {
  padding: 20px;
}

.panel-content.no-data {
  text-align: center;
  color: #6b7280;
  padding: 40px 20px;
}

/* 电池状态 */
.battery-level {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.level-bar {
  flex: 1;
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
}

.level-fill {
  height: 100%;
  transition: width 0.3s ease;
}

.level-fill.high {
  background: linear-gradient(90deg, #10b981 0%, #059669 100%);
}

.level-fill.medium {
  background: linear-gradient(90deg, #f59e0b 0%, #d97706 100%);
}

.level-fill.low {
  background: linear-gradient(90deg, #ef4444 0%, #dc2626 100%);
}

.level-text {
  font-weight: 600;
  color: #374151;
}

.battery-details,
.vehicle-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f9fafb;
  border-radius: 6px;
}

.detail-item .label {
  font-size: 14px;
  color: #6b7280;
}

.detail-item .value {
  font-weight: 600;
  color: #374151;
}

.value.hot {
  color: #ef4444;
}

.value.cold {
  color: #3b82f6;
}

.value.good {
  color: #10b981;
}

.value.fair {
  color: #f59e0b;
}

.value.poor {
  color: #ef4444;
}

.value.running {
  color: #10b981;
}

.value.idle {
  color: #f59e0b;
}

.value.off {
  color: #6b7280;
}

.value.speed {
  font-size: 16px;
  color: #3b82f6;
}

.value.location {
  font-family: monospace;
  font-size: 12px;
}

/* 充电站状态 */
.stations-summary {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
  margin-bottom: 16px;
}

.summary-item {
  text-align: center;
  padding: 12px;
  background: #f9fafb;
  border-radius: 8px;
}

.summary-item .count {
  display: block;
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 4px;
}

.count.available {
  color: #10b981;
}

.count.occupied {
  color: #f59e0b;
}

.count.maintenance {
  color: #8b5cf6;
}

.count.offline {
  color: #ef4444;
}

.summary-item .label {
  font-size: 12px;
  color: #6b7280;
}

.stations-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.station-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f9fafb;
  border-radius: 8px;
}

.station-status {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.station-status.available {
  background: #10b981;
}

.station-status.occupied {
  background: #f59e0b;
}

.station-status.maintenance {
  background: #8b5cf6;
}

.station-status.offline {
  background: #ef4444;
}

.station-info {
  display: flex;
  gap: 12px;
  align-items: center;
  flex: 1;
}

.station-id {
  font-weight: 600;
  color: #374151;
}

.station-power {
  color: #6b7280;
  font-size: 14px;
}

.station-queue {
  color: #f59e0b;
  font-size: 12px;
}

/* 告警部分 */
.alerts-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px 20px;
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
}

.section-icon {
  width: 20px;
  height: 20px;
}

.section-header h3 {
  margin: 0;
  flex: 1;
  font-size: 16px;
  font-weight: 600;
}

.alerts-list {
  max-height: 400px;
  overflow-y: auto;
}

.alert-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  border-bottom: 1px solid #f3f4f6;
}

.alert-item:last-child {
  border-bottom: none;
}

.alert-item.error {
  background: #fef2f2;
}

.alert-item.warning {
  background: #fffbeb;
}

.alert-item.info {
  background: #eff6ff;
}

.alert-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.alert-item.error .alert-icon {
  color: #ef4444;
}

.alert-item.warning .alert-icon {
  color: #f59e0b;
}

.alert-item.info .alert-icon {
  color: #3b82f6;
}

.alert-content {
  flex: 1;
}

.alert-message {
  font-weight: 500;
  color: #374151;
  margin-bottom: 4px;
}

.alert-time {
  font-size: 12px;
  color: #6b7280;
}

.alert-priority {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.alert-priority.critical {
  background: #fecaca;
  color: #991b1b;
}

.alert-priority.high {
  background: #fed7aa;
  color: #9a3412;
}

.alert-priority.medium {
  background: #fef3c7;
  color: #92400e;
}

.alert-priority.low {
  background: #d1fae5;
  color: #065f46;
}

.no-alerts {
  text-align: center;
  color: #6b7280;
  padding: 40px 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .realtime-monitor {
    padding: 12px;
  }
  
  .data-panels {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .connection-status {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
  
  .battery-details,
  .vehicle-details {
    grid-template-columns: 1fr;
  }
  
  .stations-summary {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>