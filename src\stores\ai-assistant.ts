import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '@/utils/api'

// 消息接口
export interface Message {
  id: string
  type: 'user' | 'ai'
  content: string
  timestamp: Date
  isTyping?: boolean
  fileInfo?: {
    name: string
    type: string
    size: number
  }
}

// AI助手信息接口
export interface AIAssistantInfo {
  name: string
  fullName: string
  description: string
  avatar: string
  capabilities: string[]
  version: string
  model: string
}

export const useAIAssistantStore = defineStore('ai-assistant', () => {
  // 状态
  const messages = ref<Message[]>([])
  const isLoading = ref(false)
  const isOnline = ref(false)
  const aiInfo = ref<AIAssistantInfo | null>(null)
  const error = ref<string | null>(null)

  // 计算属性
  const conversationCount = computed(() => {
    return Math.ceil(messages.value.length / 2)
  })

  const lastMessage = computed(() => {
    return messages.value[messages.value.length - 1]
  })

  // 发送文本消息
  const sendMessage = async (message: string, context: Message[] = []) => {
    try {
      isLoading.value = true
      error.value = null

      // 添加用户消息
      const userMessage: Message = {
        id: Date.now().toString(),
        type: 'user',
        content: message,
        timestamp: new Date()
      }
      messages.value.push(userMessage)

      // 添加AI正在输入的消息
      const aiTypingMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: '',
        timestamp: new Date(),
        isTyping: true
      }
      messages.value.push(aiTypingMessage)

      // 调用API
      const response = await api.post('/ai-enhanced/chat', {
        message,
        context: context.slice(-10) // 最近10条消息作为上下文
      })

      // 移除正在输入的消息
      messages.value.pop()

      // 添加AI回复
      const aiMessage: Message = {
        id: (Date.now() + 2).toString(),
        type: 'ai',
        content: response.data.response,
        timestamp: new Date()
      }
      messages.value.push(aiMessage)

      return response.data.response
    } catch (err: any) {
      // 移除正在输入的消息
      if (messages.value[messages.value.length - 1]?.isTyping) {
        messages.value.pop()
      }

      // 添加错误消息
      const errorMessage: Message = {
        id: (Date.now() + 2).toString(),
        type: 'ai',
        content: '抱歉，我暂时无法回答您的问题，请稍后再试。',
        timestamp: new Date()
      }
      messages.value.push(errorMessage)

      error.value = err.message || '发送消息失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 上传文件并分析
  const analyzeFile = async (file: File, question: string = '请分析这个文件的内容') => {
    try {
      isLoading.value = true
      error.value = null

      // 添加用户消息（包含文件信息）
      const userMessage: Message = {
        id: Date.now().toString(),
        type: 'user',
        content: question,
        timestamp: new Date(),
        fileInfo: {
          name: file.name,
          type: file.type,
          size: file.size
        }
      }
      messages.value.push(userMessage)

      // 添加AI正在输入的消息
      const aiTypingMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: '',
        timestamp: new Date(),
        isTyping: true
      }
      messages.value.push(aiTypingMessage)

      // 创建FormData
      const formData = new FormData()
      formData.append('file', file)
      formData.append('question', question)

      // 调用文件分析API
      const response = await api.post('/ai-enhanced/analyze-file', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })

      // 移除正在输入的消息
      messages.value.pop()

      // 添加AI回复
      const aiMessage: Message = {
        id: (Date.now() + 2).toString(),
        type: 'ai',
        content: response.data.response,
        timestamp: new Date()
      }
      messages.value.push(aiMessage)

      return response.data.response
    } catch (err: any) {
      // 移除正在输入的消息
      if (messages.value[messages.value.length - 1]?.isTyping) {
        messages.value.pop()
      }

      // 添加错误消息
      const errorMessage: Message = {
        id: (Date.now() + 2).toString(),
        type: 'ai',
        content: '抱歉，文件分析失败，请检查文件格式或稍后再试。',
        timestamp: new Date()
      }
      messages.value.push(errorMessage)

      error.value = err.message || '文件分析失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 检查AI服务状态
  const checkStatus = async () => {
    try {
      const response = await api.get('/ai-enhanced/status')
      isOnline.value = response.data.success && response.data.openaiConnected
      return response.data
    } catch (err: any) {
      isOnline.value = false
      error.value = err.message || '检查AI状态失败'
      throw err
    }
  }

  // 获取AI助手信息
  const fetchAIInfo = async () => {
    try {
      const response = await api.get('/ai-enhanced/info')
      aiInfo.value = response.data.data
      return response.data.data
    } catch (err: any) {
      error.value = err.message || '获取AI信息失败'
      throw err
    }
  }

  // 清空对话
  const clearMessages = () => {
    messages.value = []
  }

  // 重置状态
  const resetState = () => {
    messages.value = []
    isLoading.value = false
    isOnline.value = false
    aiInfo.value = null
    error.value = null
  }

  return {
    // 状态
    messages,
    isLoading,
    isOnline,
    aiInfo,
    error,
    
    // 计算属性
    conversationCount,
    lastMessage,
    
    // 方法
    sendMessage,
    analyzeFile,
    checkStatus,
    fetchAIInfo,
    clearMessages,
    resetState
  }
})
