import { Router } from 'express'
import { db } from '../config/mysql'
import type { ApiResponse } from '../../shared/types'

const router = Router()

// 获取碳积分统计
router.get('/stats', async (req, res) => {
  try {
    const { user_id } = req.query
    
    // 模拟碳积分数据
    const stats = {
      totalCredits: 1250,
      monthlyCredits: 180,
      weeklyCredits: 45,
      dailyCredits: 8,
      rank: 'Gold',
      level: 5,
      nextLevelCredits: 1500,
      carbonSaved: 2.5, // 吨
      treesEquivalent: 12,
      recentActivities: [
        {
          id: 1,
          activity: '绿色出行',
          credits: 15,
          date: new Date().toISOString(),
          description: '使用电动车出行10公里'
        },
        {
          id: 2,
          activity: '节能充电',
          credits: 8,
          date: new Date(Date.now() - 86400000).toISOString(),
          description: '在低碳时段充电'
        },
        {
          id: 3,
          activity: '环保驾驶',
          credits: 12,
          date: new Date(Date.now() - 172800000).toISOString(),
          description: '保持节能驾驶习惯'
        }
      ]
    }
    
    res.json({
      success: true,
      data: stats
    } as ApiResponse)
  } catch (error) {
    console.error('获取碳积分统计异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取碳积分历史记录
router.get('/history', async (req, res) => {
  try {
    const { page = 1, limit = 20, user_id } = req.query
    
    // 模拟历史记录数据
    const history = Array.from({ length: Number(limit) }, (_, i) => ({
      id: i + 1,
      activity: ['绿色出行', '节能充电', '环保驾驶', '参与活动'][i % 4],
      credits: Math.floor(Math.random() * 20) + 5,
      date: new Date(Date.now() - i * 86400000).toISOString(),
      description: `活动描述 ${i + 1}`
    }))
    
    res.json({
      success: true,
      data: history,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: 100,
        totalPages: Math.ceil(100 / Number(limit))
      }
    } as ApiResponse)
  } catch (error) {
    console.error('获取碳积分历史异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 兑换碳积分
router.post('/redeem', async (req, res) => {
  try {
    const { item_id, credits_required } = req.body
    
    if (!item_id || !credits_required) {
      return res.status(400).json({
        success: false,
        error: '商品ID和所需积分为必填字段'
      } as ApiResponse)
    }
    
    // 模拟兑换成功
    res.json({
      success: true,
      message: '兑换成功',
      data: {
        transaction_id: `TXN${Date.now()}`,
        item_id,
        credits_used: credits_required,
        remaining_credits: 1250 - credits_required
      }
    } as ApiResponse)
  } catch (error) {
    console.error('兑换碳积分异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

export default router