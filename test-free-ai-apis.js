const axios = require('axios');
require('dotenv').config();

async function testFreeAIAPIs() {
  console.log('🆓 测试免费AI API连接...\n');
  console.log('=' .repeat(60));
  
  const geminiKey = process.env.GEMINI_API_KEY;
  const openaiKey = process.env.OPENAI_API_KEY;
  
  // 测试1: Google Gemini API
  console.log('\n1️⃣ 测试Google Gemini API...');
  
  if (!geminiKey || geminiKey === 'your-gemini-api-key-here') {
    console.log('❌ Gemini API密钥未配置');
    console.log('📋 申请步骤:');
    console.log('1. 访问 https://makersuite.google.com/app/apikey');
    console.log('2. 使用Google账号登录');
    console.log('3. 点击"Create API Key"');
    console.log('4. 复制API密钥到 .env 文件中的 GEMINI_API_KEY');
  } else {
    console.log('✅ Gemini API密钥已配置');
    console.log(`🔑 密钥前缀: ${geminiKey.substring(0, 10)}...`);
    
    try {
      // 测试Gemini API连接
      const response = await axios.post(
        `https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${geminiKey}`,
        {
          contents: [{
            parts: [{
              text: '你好，请简单介绍一下你自己'
            }]
          }]
        },
        {
          headers: {
            'Content-Type': 'application/json'
          },
          timeout: 10000
        }
      );
      
      if (response.data && response.data.candidates && response.data.candidates[0]) {
        const text = response.data.candidates[0].content.parts[0].text;
        console.log('✅ Gemini API连接成功！');
        console.log(`🤖 测试回答: ${text.substring(0, 100)}...`);
        console.log('💰 费用: 免费（每分钟15次请求）');
      } else {
        console.log('❌ Gemini API响应格式异常');
      }
      
    } catch (error) {
      console.log('❌ Gemini API连接失败');
      if (error.response) {
        console.log(`状态码: ${error.response.status}`);
        console.log(`错误信息: ${error.response.data?.error?.message || error.response.statusText}`);
      } else {
        console.log(`网络错误: ${error.message}`);
      }
    }
  }
  
  // 测试2: OpenAI API（如果配置了）
  console.log('\n2️⃣ 测试OpenAI API...');
  
  if (!openaiKey || openaiKey === 'your-openai-api-key-here') {
    console.log('⚠️ OpenAI API密钥未配置（可选）');
    console.log('💡 OpenAI提供更高质量的回答，但需要付费');
  } else {
    console.log('✅ OpenAI API密钥已配置');
    console.log(`🔑 密钥前缀: ${openaiKey.substring(0, 7)}...${openaiKey.substring(openaiKey.length - 4)}`);
    
    try {
      const response = await axios.post('https://api.openai.com/v1/chat/completions', {
        model: 'gpt-3.5-turbo',
        messages: [{ role: 'user', content: '你好' }],
        max_tokens: 50
      }, {
        headers: {
          'Authorization': `Bearer ${openaiKey}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });
      
      if (response.data && response.data.choices && response.data.choices[0]) {
        console.log('✅ OpenAI API连接成功！');
        console.log(`🤖 测试回答: ${response.data.choices[0].message.content}`);
        console.log('💰 费用: 付费（GPT-3.5约$0.002/1K tokens）');
      }
      
    } catch (error) {
      console.log('❌ OpenAI API连接失败');
      if (error.response) {
        console.log(`状态码: ${error.response.status}`);
        console.log(`错误信息: ${error.response.data?.error?.message || error.response.statusText}`);
      }
    }
  }
  
  // 测试3: 本地知识库
  console.log('\n3️⃣ 测试本地知识库...');
  
  try {
    const response = await axios.post('http://localhost:3001/api/ai-enhanced/status');
    
    if (response.data.success) {
      console.log('✅ 本地知识库服务正常');
      console.log(`🔄 当前服务: ${response.data.currentService}`);
      console.log('💰 费用: 完全免费');
      
      // 显示可用服务状态
      const services = response.data.services;
      console.log('\n📊 AI服务状态:');
      console.log(`- OpenAI: ${services.openai.available ? '✅ 可用' : '❌ 不可用'}`);
      console.log(`- Gemini: ${services.gemini.available ? '✅ 可用' : '❌ 不可用'}`);
      console.log(`- 本地库: ${services.local.available ? '✅ 可用' : '❌ 不可用'}`);
      
    } else {
      console.log('❌ 本地知识库服务异常');
    }
    
  } catch (error) {
    console.log('❌ 无法连接到本地服务');
    console.log('请确保后端服务器正在运行 (npm run server:dev)');
  }
  
  // 测试4: 实际对话测试
  console.log('\n4️⃣ 测试实际对话功能...');
  
  try {
    const chatResponse = await axios.post('http://localhost:3001/api/ai-enhanced/chat', {
      message: '你好，请介绍一下EVAdmin Pro系统的主要功能'
    });
    
    if (chatResponse.data.success) {
      console.log('✅ 对话功能测试成功');
      console.log(`🤖 使用服务: ${chatResponse.data.service}`);
      console.log(`📝 回答预览: ${chatResponse.data.response.substring(0, 100)}...`);
    } else {
      console.log('❌ 对话功能测试失败');
    }
    
  } catch (error) {
    console.log('❌ 对话功能测试失败:', error.message);
  }
  
  // 总结和建议
  console.log('\n' + '=' .repeat(60));
  console.log('📋 配置建议');
  console.log('=' .repeat(60));
  
  console.log('\n🆓 免费方案推荐:');
  console.log('1. Google Gemini API - 免费，性能优秀');
  console.log('   • 每分钟15次请求，每天1500次请求');
  console.log('   • 支持文本对话和图片分析');
  console.log('   • 申请地址: https://makersuite.google.com/app/apikey');
  
  console.log('\n💰 付费方案（可选）:');
  console.log('1. OpenAI GPT-4o - 最高质量，需要付费');
  console.log('   • 约$5-20/月（根据使用量）');
  console.log('   • 申请地址: https://platform.openai.com/api-keys');
  
  console.log('\n🔧 配置步骤:');
  console.log('1. 申请Gemini API密钥（推荐）');
  console.log('2. 在 .env 文件中设置 GEMINI_API_KEY');
  console.log('3. 重启服务器: rs');
  console.log('4. 访问 http://localhost:5175/ai-assistant 测试');
  
  console.log('\n🎯 功能对比:');
  console.log('- Gemini: 免费 + 智能回答 + 图片分析');
  console.log('- OpenAI: 付费 + 最佳质量 + 全功能');
  console.log('- 本地库: 免费 + 系统专业知识 + 离线可用');
}

testFreeAIAPIs();
