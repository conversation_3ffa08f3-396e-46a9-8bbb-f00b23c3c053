<template>
  <div 
    class="modern-loader"
    :class="[
      `loader-${type}`,
      `loader-${size}`,
      {
        'loader-overlay': overlay,
        'loader-with-text': text,
        'loader-responsive': responsive
      }
    ]"
    :style="{
      '--loader-color': color,
      '--loader-size': sizeValue,
      '--animation-duration': `${duration}ms`
    }"
    role="status"
    :aria-label="ariaLabel"
  >
    <!-- 覆盖层背景 -->
    <div v-if="overlay" class="modern-loader__backdrop" />
    
    <!-- 加载器容器 -->
    <div class="modern-loader__container">
      <!-- 脉冲圆环加载器 -->
      <div v-if="type === 'pulse'" class="loader-pulse">
        <div class="pulse-ring" v-for="i in 3" :key="i" />
      </div>
      
      <!-- 量子点加载器 -->
      <div v-else-if="type === 'quantum'" class="loader-quantum">
        <div class="quantum-dot" v-for="i in 5" :key="i" />
      </div>
      
      <!-- 霓虹螺旋加载器 -->
      <div v-else-if="type === 'neon-spiral'" class="loader-neon-spiral">
        <div class="spiral-line" v-for="i in 3" :key="i" />
      </div>
      
      <!-- 粒子波浪加载器 -->
      <div v-else-if="type === 'particle-wave'" class="loader-particle-wave">
        <div class="wave-particle" v-for="i in 5" :key="i" :style="{ animationDelay: `${i * 0.1}s` }" />
      </div>
      
      <!-- 全息立方体加载器 -->
      <div v-else-if="type === 'hologram'" class="loader-hologram">
        <div class="hologram-cube">
          <div class="cube-face cube-face--front" />
          <div class="cube-face cube-face--back" />
          <div class="cube-face cube-face--right" />
          <div class="cube-face cube-face--left" />
          <div class="cube-face cube-face--top" />
          <div class="cube-face cube-face--bottom" />
        </div>
      </div>
      
      <!-- 能量环加载器 -->
      <div v-else-if="type === 'energy-ring'" class="loader-energy-ring">
        <div class="energy-circle">
          <div class="energy-particle" v-for="i in 3" :key="i" :style="{ animationDelay: `${i * 0.5}s` }" />
        </div>
      </div>
      
      <!-- 现代旋转器（默认） -->
      <div v-else class="loader-modern">
        <div class="modern-spinner" />
      </div>
      
      <!-- 加载文本 -->
      <div v-if="text" class="modern-loader__text">
        {{ text }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useBreakpoint, useDeviceFeatures } from '@/composables/useResponsive'

interface Props {
  type?: 'pulse' | 'quantum' | 'neon-spiral' | 'particle-wave' | 'hologram' | 'energy-ring' | 'modern'
  size?: 'small' | 'medium' | 'large' | 'xl'
  color?: string
  text?: string
  overlay?: boolean
  duration?: number
  responsive?: boolean
  ariaLabel?: string
}

const props = withDefaults(defineProps<Props>(), {
  type: 'modern',
  size: 'medium',
  color: '#667eea',
  overlay: false,
  duration: 1000,
  responsive: true,
  ariaLabel: '加载中...'
})

const { isMobileDevice } = useBreakpoint()
const { reducedMotion } = useDeviceFeatures()

const sizeValue = computed(() => {
  const sizes = {
    small: props.responsive && isMobileDevice ? '32px' : '40px',
    medium: props.responsive && isMobileDevice ? '48px' : '60px',
    large: props.responsive && isMobileDevice ? '64px' : '80px',
    xl: props.responsive && isMobileDevice ? '80px' : '100px'
  }
  return sizes[props.size]
})
</script>

<style scoped>
.modern-loader {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 1000;
}

.modern-loader__backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(8px);
  z-index: -1;
}

.modern-loader__container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.modern-loader__text {
  font-size: 16px;
  font-weight: 500;
  color: #667eea;
  text-align: center;
  margin-top: 12px;
}

.loader-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
}

/* 尺寸变体 */
.loader-small {
  --loader-size: 40px;
}

.loader-medium {
  --loader-size: 60px;
}

.loader-large {
  --loader-size: 80px;
}

.loader-xl {
  --loader-size: 100px;
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .modern-loader * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 脉冲圆环加载器 */
.loader-pulse {
  position: relative;
  width: var(--loader-size);
  height: var(--loader-size);
}

.pulse-ring {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 2px solid var(--loader-color);
  border-radius: 50%;
  opacity: 0;
  animation: pulseRing var(--animation-duration) ease-out infinite;
}

.pulse-ring:nth-child(2) {
  animation-delay: 0.33s;
}

.pulse-ring:nth-child(3) {
  animation-delay: 0.66s;
}

@keyframes pulseRing {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.4);
    opacity: 0;
  }
}

/* 量子点加载器 */
.loader-quantum {
  display: flex;
  align-items: center;
  gap: 8px;
}

.quantum-dot {
  width: 12px;
  height: 12px;
  background: var(--loader-color);
  border-radius: 50%;
  animation: quantumBounce 1.4s ease-in-out infinite both;
  box-shadow: 0 0 20px var(--loader-color);
}

.quantum-dot:nth-child(1) { animation-delay: -0.32s; }
.quantum-dot:nth-child(2) { animation-delay: -0.16s; }
.quantum-dot:nth-child(3) { animation-delay: 0s; }
.quantum-dot:nth-child(4) { animation-delay: 0.16s; }
.quantum-dot:nth-child(5) { animation-delay: 0.32s; }

@keyframes quantumBounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* 霓虹螺旋加载器 */
.loader-neon-spiral {
  position: relative;
  width: var(--loader-size);
  height: var(--loader-size);
}

.spiral-line {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 2px solid transparent;
  border-top: 2px solid #00f5ff;
  border-radius: 50%;
  animation: neonSpin var(--animation-duration) linear infinite;
  box-shadow: 0 0 20px #00f5ff;
}

.spiral-line:nth-child(2) {
  animation-delay: 0.33s;
  border-top-color: #ff6b6b;
  box-shadow: 0 0 20px #ff6b6b;
}

.spiral-line:nth-child(3) {
  animation-delay: 0.66s;
  border-top-color: #4ecdc4;
  box-shadow: 0 0 20px #4ecdc4;
}

@keyframes neonSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 粒子波浪加载器 */
.loader-particle-wave {
  display: flex;
  align-items: center;
  gap: 4px;
}

.wave-particle {
  width: 8px;
  height: 8px;
  background: linear-gradient(45deg, #f093fb, #f5576c);
  border-radius: 50%;
  animation: particleWave 1.5s ease-in-out infinite;
  box-shadow: 0 0 15px rgba(240, 147, 251, 0.6);
}

@keyframes particleWave {
  0%, 40%, 100% {
    transform: scaleY(0.4) translateY(0);
  }
  20% {
    transform: scaleY(1) translateY(-15px);
  }
}

/* 全息立方体加载器 */
.loader-hologram {
  perspective: 200px;
}

.hologram-cube {
  position: relative;
  width: 40px;
  height: 40px;
  transform-style: preserve-3d;
  animation: hologramRotate 3s linear infinite;
}

.cube-face {
  position: absolute;
  width: 40px;
  height: 40px;
  background: rgba(102, 126, 234, 0.3);
  border: 1px solid #667eea;
  box-shadow: 0 0 20px rgba(102, 126, 234, 0.4);
}

.cube-face--front { transform: rotateY(0deg) translateZ(20px); }
.cube-face--back { transform: rotateY(180deg) translateZ(20px); }
.cube-face--right { transform: rotateY(90deg) translateZ(20px); }
.cube-face--left { transform: rotateY(-90deg) translateZ(20px); }
.cube-face--top { transform: rotateX(90deg) translateZ(20px); }
.cube-face--bottom { transform: rotateX(-90deg) translateZ(20px); }

@keyframes hologramRotate {
  0% { transform: rotateX(0deg) rotateY(0deg); }
  100% { transform: rotateX(360deg) rotateY(360deg); }
}

/* 能量环加载器 */
.loader-energy-ring {
  position: relative;
  width: 60px;
  height: 60px;
}

.energy-circle {
  position: relative;
  width: 100%;
  height: 100%;
  border: 2px solid rgba(102, 126, 234, 0.2);
  border-radius: 50%;
  animation: energyPulse 2s ease-in-out infinite;
}

.energy-particle {
  position: absolute;
  top: -4px;
  left: 50%;
  width: 8px;
  height: 8px;
  background: #667eea;
  border-radius: 50%;
  transform-origin: 0 34px;
  animation: energyOrbit 1.5s linear infinite;
  box-shadow: 0 0 15px #667eea;
}

@keyframes energyPulse {
  0%, 100% {
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.4);
  }
  50% {
    box-shadow: 0 0 40px rgba(102, 126, 234, 0.8);
  }
}

@keyframes energyOrbit {
  0% { transform: rotate(0deg) translateX(-4px); }
  100% { transform: rotate(360deg) translateX(-4px); }
}

/* 现代旋转器 */
.loader-modern {
  position: relative;
  width: var(--loader-size);
  height: var(--loader-size);
}

.modern-spinner {
  width: 100%;
  height: 100%;
  border: 3px solid rgba(102, 126, 234, 0.2);
  border-top: 3px solid var(--loader-color);
  border-radius: 50%;
  animation: modernSpin var(--animation-duration) linear infinite;
  box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
}

@keyframes modernSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modern-loader__text {
    font-size: 14px;
  }
  
  .loader-pulse,
  .loader-neon-spiral,
  .loader-energy-ring {
    width: 50px;
    height: 50px;
  }
  
  .quantum-dot {
    width: 10px;
    height: 10px;
  }
  
  .hologram-cube {
    width: 35px;
    height: 35px;
  }
  
  .cube-face {
    width: 35px;
    height: 35px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .modern-loader__text {
    color: #a78bfa;
  }
  
  .modern-loader__backdrop {
    background: rgba(0, 0, 0, 0.9);
  }
}
</style>