-- 新能源汽车智能综合管理系统 - 示例数据插入脚本
USE ev_management;

-- 1. 用户数据
INSERT INTO users (id, email, phone, name, avatar_url, user_type, status) VALUES 
('user-001', '<EMAIL>', '13800138001', '张伟', 'https://api.dicebear.com/7.x/avataaars/svg?seed=zhang', 'enterprise', 'active'),
('user-002', '<EMAIL>', '13800138002', '李明', 'https://api.dicebear.com/7.x/avataaars/svg?seed=li', 'fleet', 'active'),
('user-003', '<EMAIL>', '13800138003', '王芳', 'https://api.dicebear.com/7.x/avataaars/svg?seed=wang', 'individual', 'active'),
('user-004', '<EMAIL>', '13800138004', '刘强', 'https://api.dicebear.com/7.x/avataaars/svg?seed=liu', 'individual', 'active'),
('user-005', '<EMAIL>', '13800138005', '陈静', 'https://api.dicebear.com/7.x/avataaars/svg?seed=chen', 'individual', 'active'),
('user-006', '<EMAIL>', '13800138006', '赵军', 'https://api.dicebear.com/7.x/avataaars/svg?seed=zhao', 'fleet', 'active'),
('user-007', '<EMAIL>', '13800138007', '孙丽', 'https://api.dicebear.com/7.x/avataaars/svg?seed=sun', 'individual', 'active'),
('user-008', '<EMAIL>', '13800138008', '周涛', 'https://api.dicebear.com/7.x/avataaars/svg?seed=zhou', 'enterprise', 'active'),
('user-009', '<EMAIL>', '13800138009', '吴敏', 'https://api.dicebear.com/7.x/avataaars/svg?seed=wu', 'individual', 'active'),
('user-010', '<EMAIL>', '13800138010', '郑华', 'https://api.dicebear.com/7.x/avataaars/svg?seed=zheng', 'individual', 'inactive');

-- 2. 车辆数据
INSERT INTO vehicles (id, user_id, vin, license_plate, brand, model, year, color, vehicle_type, battery_capacity, max_range, status, location) VALUES 
('vehicle-001', 'user-001', 'WVWZZZ1JZXW123456', '京A12345', '特斯拉', 'Model 3', 2023, '珍珠白', 'bev', 75.0, 500, 'active', JSON_OBJECT('lat', 39.9075, 'lng', 116.4574, 'address', '北京市朝阳区国贸中心')),
('vehicle-002', 'user-002', 'WVWZZZ1JZXW123457', '京B67890', '比亚迪', '汉EV', 2023, '汉宫红', 'bev', 85.4, 605, 'active', JSON_OBJECT('lat', 31.2397, 'lng', 121.4999, 'address', '上海市浦东新区陆家嘴')),
('vehicle-003', 'user-003', 'WVWZZZ1JZXW123458', '京C11111', '蔚来', 'ES6', 2023, '星空蓝', 'bev', 100.0, 610, 'active', JSON_OBJECT('lat', 22.5431, 'lng', 113.9344, 'address', '深圳市南山区科技园')),
('vehicle-004', 'user-004', 'WVWZZZ1JZXW123459', '沪A88888', '小鹏', 'P7', 2023, '机甲灰', 'bev', 80.9, 586, 'active', JSON_OBJECT('lat', 31.2304, 'lng', 121.4737, 'address', '上海市黄浦区外滩')),
('vehicle-005', 'user-005', 'WVWZZZ1JZXW123460', '粤B99999', '理想', 'ONE', 2023, '珠光白', 'phev', 40.5, 800, 'active', JSON_OBJECT('lat', 22.5329, 'lng', 114.0577, 'address', '深圳市福田区中心区')),
('vehicle-006', 'user-006', 'WVWZZZ1JZXW123461', '京D55555', '广汽埃安', 'AION S', 2023, '极地白', 'bev', 58.8, 510, 'active', JSON_OBJECT('lat', 39.9042, 'lng', 116.4074, 'address', '北京市西城区金融街')),
('vehicle-007', 'user-007', 'WVWZZZ1JZXW123462', '沪B77777', '威马', 'EX5', 2022, '雪山白', 'bev', 69.0, 520, 'maintenance', JSON_OBJECT('lat', 31.2231, 'lng', 121.4581, 'address', '上海市徐汇区徐家汇')),
('vehicle-008', 'user-008', 'WVWZZZ1JZXW123463', '粤A66666', '哪吒', 'U Pro', 2023, '科技银', 'bev', 68.0, 500, 'active', JSON_OBJECT('lat', 23.1291, 'lng', 113.2644, 'address', '广州市天河区珠江新城')),
('vehicle-009', 'user-009', 'WVWZZZ1JZXW123464', '京E33333', '零跑', 'C11', 2023, '星河银', 'bev', 90.0, 610, 'active', JSON_OBJECT('lat', 39.9388, 'lng', 116.3974, 'address', '北京市海淀区中关村')),
('vehicle-010', 'user-010', 'WVWZZZ1JZXW123465', '沪C22222', '极氪', '001', 2023, '极夜黑', 'bev', 100.0, 712, 'retired', JSON_OBJECT('lat', 31.1993, 'lng', 121.4354, 'address', '上海市闵行区莘庄'));

-- 3. 电池数据
INSERT INTO batteries (id, vehicle_id, battery_id, manufacturer, model, capacity, voltage, chemistry, manufacture_date, warranty_end_date, current_soh, current_soc, temperature, cycle_count, status) VALUES 
('battery-001', 'vehicle-001', 'BAT001-TS-2023', '宁德时代', 'NCM811-75kWh', 75.0, 400.0, 'NCM811', '2023-01-15', '2031-01-15', 98.5, 85.2, 28.5, 245, 'normal'),
('battery-002', 'vehicle-002', 'BAT002-BYD-2023', '比亚迪', 'LFP-85kWh', 85.4, 403.2, 'LFP', '2023-02-20', '2031-02-20', 99.1, 72.8, 26.8, 189, 'normal'),
('battery-003', 'vehicle-003', 'BAT003-CATL-2023', '宁德时代', 'NCM523-100kWh', 100.0, 408.0, 'NCM523', '2023-03-10', '2031-03-10', 97.8, 90.5, 29.2, 312, 'normal'),
('battery-004', 'vehicle-004', 'BAT004-XP-2023', '宁德时代', 'NCM811-81kWh', 80.9, 355.2, 'NCM811', '2023-04-05', '2031-04-05', 96.7, 68.3, 31.1, 278, 'warning'),
('battery-005', 'vehicle-005', 'BAT005-LI-2023', '宁德时代', 'NCM622-40kWh', 40.5, 288.0, 'NCM622', '2023-05-12', '2031-05-12', 98.9, 45.7, 27.3, 156, 'normal'),
('battery-006', 'vehicle-006', 'BAT006-GAC-2023', '中航锂电', 'LFP-59kWh', 58.8, 345.6, 'LFP', '2023-06-18', '2031-06-18', 97.2, 82.1, 30.5, 203, 'normal'),
('battery-007', 'vehicle-007', 'BAT007-WM-2022', '谷神电池', 'NCM523-69kWh', 69.0, 350.4, 'NCM523', '2022-08-25', '2030-08-25', 94.5, 15.6, 35.2, 456, 'critical'),
('battery-008', 'vehicle-008', 'BAT008-NZ-2023', '宁德时代', 'NCM811-68kWh', 68.0, 345.6, 'NCM811', '2023-07-30', '2031-07-30', 98.3, 77.9, 28.9, 167, 'normal'),
('battery-009', 'vehicle-009', 'BAT009-LP-2023', '中创新航', 'NCM811-90kWh', 90.0, 403.2, 'NCM811', '2023-08-15', '2031-08-15', 99.2, 88.4, 26.7, 134, 'normal'),
('battery-010', 'vehicle-010', 'BAT010-ZK-2023', '宁德时代', 'NCM811-100kWh', 100.0, 408.0, 'NCM811', '2023-09-20', '2031-09-20', 92.1, 5.3, 22.1, 678, 'maintenance');

-- 4. 充电站数据
INSERT INTO charging_stations (id, station_code, name, operator, address, latitude, longitude, total_ports, available_ports, power_type, max_power, pricing, amenities, operating_hours, status) VALUES 
('station-001', 'CS001-BJ-GMT', '北京国贸充电站', '国家电网', '北京市朝阳区国贸中心地下停车场B2层', 39.9075, 116.4574, 8, 6, 'dc', 120.0, JSON_OBJECT('peak', 1.8, 'valley', 0.8, 'normal', 1.2), JSON_ARRAY('WiFi', '便利店', '洗手间', '休息区'), JSON_OBJECT('weekday', '06:00-22:00', 'weekend', '24小时'), 'active'),
('station-002', 'CS002-SH-LJZ', '上海陆家嘴充电站', '特来电', '上海市浦东新区陆家嘴金融区世纪大道88号', 31.2397, 121.4999, 12, 10, 'mixed', 180.0, JSON_OBJECT('peak', 2.0, 'valley', 0.9, 'normal', 1.3), JSON_ARRAY('WiFi', '咖啡厅', '洗手间', '儿童区'), JSON_OBJECT('weekday', '24小时', 'weekend', '24小时'), 'active'),
('station-003', 'CS003-SZ-KJY', '深圳科技园充电站', '星星充电', '深圳市南山区科技园南区深南大道9988号', 22.5431, 113.9344, 6, 4, 'dc', 150.0, JSON_OBJECT('peak', 1.9, 'valley', 0.85, 'normal', 1.25), JSON_ARRAY('WiFi', '餐厅', '洗手间'), JSON_OBJECT('weekday', '07:00-23:00', 'weekend', '07:00-23:00'), 'active'),
('station-004', 'CS004-BJ-ZGC', '北京中关村充电站', '小桔充电', '北京市海淀区中关村大街1号', 39.9388, 116.3974, 10, 8, 'mixed', 200.0, JSON_OBJECT('peak', 1.85, 'valley', 0.82, 'normal', 1.18), JSON_ARRAY('WiFi', '超市', '洗手间', '母婴室'), JSON_OBJECT('weekday', '24小时', 'weekend', '24小时'), 'active'),
('station-005', 'CS005-GZ-ZJX', '广州珠江新城充电站', '南方电网', '广州市天河区珠江新城花城大道85号', 23.1291, 113.2644, 15, 12, 'dc', 160.0, JSON_OBJECT('peak', 1.75, 'valley', 0.78, 'normal', 1.15), JSON_ARRAY('WiFi', '商场', '洗手间', '休息区'), JSON_OBJECT('weekday', '06:00-24:00', 'weekend', '24小时'), 'active'),
('station-006', 'CS006-SH-XJH', '上海徐家汇充电站', '蔚来充电', '上海市徐汇区徐家汇港汇恒隆广场', 31.2231, 121.4581, 8, 5, 'mixed', 175.0, JSON_OBJECT('peak', 1.95, 'valley', 0.88, 'normal', 1.28), JSON_ARRAY('WiFi', '购物中心', '洗手间', '餐饮'), JSON_OBJECT('weekday', '08:00-22:00', 'weekend', '08:00-22:00'), 'maintenance'),
('station-007', 'CS007-CD-TFS', '成都天府广场充电站', '国家电网', '四川省成都市锦江区天府广场地下停车场', 30.6598, 104.0657, 12, 9, 'dc', 140.0, JSON_OBJECT('peak', 1.7, 'valley', 0.75, 'normal', 1.1), JSON_ARRAY('WiFi', '地铁站', '洗手间'), JSON_OBJECT('weekday', '06:00-23:00', 'weekend', '24小时'), 'active'),
('station-008', 'CS008-HZ-XH', '杭州西湖充电站', '万马充电', '浙江省杭州市西湖区西湖文化广场', 30.2741, 120.1551, 6, 3, 'mixed', 130.0, JSON_OBJECT('peak', 1.8, 'valley', 0.8, 'normal', 1.2), JSON_ARRAY('WiFi', '景区', '洗手间', '观光'), JSON_OBJECT('weekday', '07:00-21:00', 'weekend', '07:00-21:00'), 'active'),
('station-009', 'CS009-NJ-XJK', '南京新街口充电站', '云快充', '江苏省南京市玄武区新街口中央商场', 32.0473, 118.7778, 9, 7, 'dc', 155.0, JSON_OBJECT('peak', 1.82, 'valley', 0.83, 'normal', 1.22), JSON_ARRAY('WiFi', '商业区', '洗手间', '美食'), JSON_OBJECT('weekday', '24小时', 'weekend', '24小时'), 'active'),
('station-010', 'CS010-WH-GS', '武汉光谷充电站', '普天充电', '湖北省武汉市洪山区光谷广场', 30.5078, 114.4147, 14, 11, 'mixed', 190.0, JSON_OBJECT('peak', 1.77, 'valley', 0.77, 'normal', 1.17), JSON_ARRAY('WiFi', '科技园', '洗手间', '创业咖啡'), JSON_OBJECT('weekday', '24小时', 'weekend', '24小时'), 'offline');

-- 5. 充电会话数据
INSERT INTO charging_sessions (id, user_id, vehicle_id, station_id, session_code, start_time, end_time, start_soc, end_soc, energy_delivered, peak_power, total_cost, payment_method, payment_status, session_status) VALUES 
('session-001', 'user-001', 'vehicle-001', 'station-001', 'CHG20240101001', '2024-01-15 08:30:00', '2024-01-15 09:45:00', 25.5, 85.2, 44.8, 118.5, 53.76, '微信支付', 'completed', 'completed'),
('session-002', 'user-002', 'vehicle-002', 'station-002', 'CHG20240101002', '2024-01-15 14:20:00', '2024-01-15 15:50:00', 15.3, 72.8, 49.1, 175.2, 63.83, '支付宝', 'completed', 'completed'),
('session-003', 'user-003', 'vehicle-003', 'station-003', 'CHG20240101003', '2024-01-15 19:10:00', '2024-01-15 21:05:00', 12.8, 90.5, 77.7, 148.9, 97.13, '银行卡', 'completed', 'completed'),
('session-004', 'user-004', 'vehicle-004', 'station-004', 'CHG20240102001', '2024-01-16 07:45:00', '2024-01-16 09:20:00', 8.2, 68.3, 48.7, 195.6, 57.24, '微信支付', 'completed', 'completed'),
('session-005', 'user-005', 'vehicle-005', 'station-005', 'CHG20240102002', '2024-01-16 16:30:00', '2024-01-16 17:15:00', 22.1, 45.7, 9.6, 158.3, 16.8, '支付宝', 'completed', 'completed'),
('session-006', 'user-006', 'vehicle-006', 'station-001', 'CHG20240102003', '2024-01-16 20:15:00', '2024-01-16 22:30:00', 35.6, 82.1, 27.4, 115.8, 32.88, '银行卡', 'completed', 'completed'),
('session-007', 'user-007', 'vehicle-007', 'station-007', 'CHG20240103001', '2024-01-17 09:00:00', NULL, 8.9, NULL, NULL, NULL, NULL, '微信支付', 'pending', 'active'),
('session-008', 'user-008', 'vehicle-008', 'station-008', 'CHG20240103002', '2024-01-17 13:25:00', '2024-01-17 15:10:00', 28.4, 77.9, 33.7, 125.4, 40.44, '支付宝', 'completed', 'completed'),
('session-009', 'user-009', 'vehicle-009', 'station-009', 'CHG20240103003', '2024-01-17 18:40:00', '2024-01-17 20:55:00', 45.2, 88.4, 38.9, 152.7, 47.47, '银行卡', 'completed', 'completed'),
('session-010', 'user-001', 'vehicle-001', 'station-002', 'CHG20240104001', '2024-01-18 11:20:00', '2024-01-18 12:10:00', 62.1, 85.2, 17.3, 168.9, 22.49, '微信支付', 'failed', 'interrupted');

-- 6. 碳积分数据
INSERT INTO carbon_credit (id, user_id, vehicle_id, credit_type, amount, description, reference_id, status, earned_at) VALUES 
('credit-001', 'user-001', 'vehicle-001', 'driving', 15.6, '绿色驾驶行为奖励 - 平稳加速减速', 'session-001', 'approved', '2024-01-15 09:45:00'),
('credit-002', 'user-002', 'vehicle-002', 'charging', 24.5, '使用清洁能源充电奖励', 'session-002', 'approved', '2024-01-15 15:50:00'),
('credit-003', 'user-003', 'vehicle-003', 'driving', 38.9, '长距离绿色出行奖励', NULL, 'approved', '2024-01-15 21:05:00'),
('credit-004', 'user-004', 'vehicle-004', 'sharing', 12.3, '车辆共享使用奖励', NULL, 'approved', '2024-01-16 09:20:00'),
('credit-005', 'user-005', 'vehicle-005', 'maintenance', 8.7, '定期保养维护奖励', NULL, 'approved', '2024-01-16 17:15:00'),
('credit-006', 'user-006', 'vehicle-006', 'charging', 16.8, '非高峰时段充电奖励', 'session-006', 'approved', '2024-01-16 22:30:00'),
('credit-007', 'user-007', 'vehicle-007', 'driving', 22.1, '节能驾驶模式使用奖励', NULL, 'pending', '2024-01-17 09:00:00'),
('credit-008', 'user-008', 'vehicle-008', 'charging', 20.2, '太阳能充电站使用奖励', 'session-008', 'approved', '2024-01-17 15:10:00'),
('credit-009', 'user-009', 'vehicle-009', 'driving', 31.4, '零排放里程达成奖励', NULL, 'approved', '2024-01-17 20:55:00'),
('credit-010', 'user-001', 'vehicle-001', 'sharing', 5.9, '参与拼车出行奖励', NULL, 'rejected', '2024-01-18 12:10:00');

-- 7. 驾驶行为数据
INSERT INTO driving_behavior (id, user_id, vehicle_id, trip_id, start_time, end_time, distance, duration, avg_speed, max_speed, acceleration_score, braking_score, cornering_score, eco_score, safety_score, energy_efficiency, route_data) VALUES 
('trip-001', 'user-001', 'vehicle-001', 'TRIP20240115001', '2024-01-15 08:00:00', '2024-01-15 08:30:00', 25.6, 1800, 51.2, 78.5, 8.5, 9.2, 8.8, 9.1, 8.9, 15.2, JSON_OBJECT('start', '北京国贸', 'end', '北京朝阳公园', 'waypoints', JSON_ARRAY(JSON_OBJECT('lat', 39.9075, 'lng', 116.4574), JSON_OBJECT('lat', 39.9342, 'lng', 116.4374)))),
('trip-002', 'user-002', 'vehicle-002', 'TRIP20240115002', '2024-01-15 14:00:00', '2024-01-15 14:20:00', 12.3, 1200, 36.9, 65.2, 7.8, 8.5, 9.1, 8.7, 8.5, 18.7, JSON_OBJECT('start', '上海陆家嘴', 'end', '上海外滩', 'waypoints', JSON_ARRAY(JSON_OBJECT('lat', 31.2397, 'lng', 121.4999), JSON_OBJECT('lat', 31.2304, 'lng', 121.4737)))),
('trip-003', 'user-003', 'vehicle-003', 'TRIP20240115003', '2024-01-15 18:30:00', '2024-01-15 19:10:00', 35.8, 2400, 53.7, 95.3, 9.3, 8.9, 9.5, 9.2, 9.2, 14.8, JSON_OBJECT('start', '深圳科技园', 'end', '深圳宝安机场', 'waypoints', JSON_ARRAY(JSON_OBJECT('lat', 22.5431, 'lng', 113.9344), JSON_OBJECT('lat', 22.6390, 'lng', 113.8107)))),
('trip-004', 'user-004', 'vehicle-004', 'TRIP20240116001', '2024-01-16 07:15:00', '2024-01-16 07:45:00', 18.9, 1800, 37.8, 72.1, 8.1, 7.9, 8.6, 8.4, 8.3, 16.9, JSON_OBJECT('start', '北京中关村', 'end', '北京西单', 'waypoints', JSON_ARRAY(JSON_OBJECT('lat', 39.9388, 'lng', 116.3974), JSON_OBJECT('lat', 39.9097, 'lng', 116.3668)))),
('trip-005', 'user-005', 'vehicle-005', 'TRIP20240116002', '2024-01-16 16:00:00', '2024-01-16 16:30:00', 22.1, 1800, 44.2, 88.7, 7.5, 8.2, 7.8, 8.0, 7.9, 12.3, JSON_OBJECT('start', '广州珠江新城', 'end', '广州白云机场', 'waypoints', JSON_ARRAY(JSON_OBJECT('lat', 23.1291, 'lng', 113.2644), JSON_OBJECT('lat', 23.3924, 'lng', 113.2988)))),
('trip-006', 'user-006', 'vehicle-006', 'TRIP20240116003', '2024-01-16 19:45:00', '2024-01-16 20:15:00', 15.7, 1800, 31.4, 58.9, 9.1, 9.4, 9.0, 9.3, 9.2, 19.8, JSON_OBJECT('start', '北京金融街', 'end', '北京国贸', 'waypoints', JSON_ARRAY(JSON_OBJECT('lat', 39.9042, 'lng', 116.4074), JSON_OBJECT('lat', 39.9075, 'lng', 116.4574)))),
('trip-007', 'user-007', 'vehicle-007', 'TRIP20240117001', '2024-01-17 08:30:00', '2024-01-17 09:00:00', 28.4, 1800, 56.8, 102.3, 6.8, 7.2, 7.5, 7.1, 7.2, 13.5, JSON_OBJECT('start', '上海徐家汇', 'end', '上海虹桥机场', 'waypoints', JSON_ARRAY(JSON_OBJECT('lat', 31.2231, 'lng', 121.4581), JSON_OBJECT('lat', 31.1979, 'lng', 121.3365)))),
('trip-008', 'user-008', 'vehicle-008', 'TRIP20240117002', '2024-01-17 13:00:00', '2024-01-17 13:25:00', 11.2, 1500, 26.9, 45.6, 8.7, 8.9, 8.5, 8.8, 8.7, 20.1, JSON_OBJECT('start', '杭州西湖', 'end', '杭州火车站', 'waypoints', JSON_ARRAY(JSON_OBJECT('lat', 30.2741, 'lng', 120.1551), JSON_OBJECT('lat', 30.2431, 'lng', 120.1814)))),
('trip-009', 'user-009', 'vehicle-009', 'TRIP20240117003', '2024-01-17 18:10:00', '2024-01-17 18:40:00', 19.6, 1800, 39.2, 68.4, 9.0, 8.7, 9.2, 8.9, 9.0, 17.3, JSON_OBJECT('start', '南京新街口', 'end', '南京南站', 'waypoints', JSON_ARRAY(JSON_OBJECT('lat', 32.0473, 'lng', 118.7778), JSON_OBJECT('lat', 31.9565, 'lng', 118.7134)))),
('trip-010', 'user-001', 'vehicle-001', 'TRIP20240118001', '2024-01-18 10:50:00', '2024-01-18 11:20:00', 16.8, 1800, 33.6, 62.1, 8.3, 8.6, 8.4, 8.5, 8.5, 18.9, JSON_OBJECT('start', '北京朝阳公园', 'end', '北京三里屯', 'waypoints', JSON_ARRAY(JSON_OBJECT('lat', 39.9342, 'lng', 116.4374), JSON_OBJECT('lat', 39.9368, 'lng', 116.4472))));

-- 8. 车队管理数据
INSERT INTO fleet_management (id, user_id, fleet_name, fleet_code, description, manager_name, manager_phone, manager_email, total_vehicles, active_vehicles, status) VALUES 
('fleet-001', 'user-002', '京东物流车队', 'JD-FLEET-001', '京东物流北京配送车队，主要负责北京地区的电商配送服务', '李明', '13800138002', '<EMAIL>', 25, 23, 'active'),
('fleet-002', 'user-006', '顺丰速运车队', 'SF-FLEET-002', '顺丰速运华南区域车队，覆盖广深地区快递配送', '赵军', '13800138006', '<EMAIL>', 18, 16, 'active'),
('fleet-003', 'user-008', '滴滴出行车队', 'DD-FLEET-003', '滴滴出行企业级车队，提供高端商务出行服务', '周涛', '13800138008', '<EMAIL>', 32, 28, 'active'),
('fleet-004', 'user-002', '美团配送车队', 'MT-FLEET-004', '美团外卖配送车队，专注于同城即时配送服务', '李明', '13800138002', '<EMAIL>', 15, 14, 'active'),
('fleet-005', 'user-006', '货拉拉货运车队', 'HLL-FLEET-005', '货拉拉同城货运车队，提供搬家和货运服务', '赵军', '13800138006', '<EMAIL>', 22, 20, 'inactive');

-- 车队车辆关联数据
INSERT INTO fleet_vehicles (fleet_id, vehicle_id, assigned_at, status) VALUES 
('fleet-001', 'vehicle-002', '2024-01-01 00:00:00', 'active'),
('fleet-001', 'vehicle-006', '2024-01-01 00:00:00', 'active'),
('fleet-002', 'vehicle-008', '2024-01-01 00:00:00', 'active'),
('fleet-003', 'vehicle-001', '2024-01-01 00:00:00', 'active'),
('fleet-003', 'vehicle-003', '2024-01-01 00:00:00', 'active'),
('fleet-003', 'vehicle-009', '2024-01-01 00:00:00', 'active'),
('fleet-004', 'vehicle-004', '2024-01-01 00:00:00', 'active'),
('fleet-004', 'vehicle-005', '2024-01-01 00:00:00', 'active'),
('fleet-005', 'vehicle-007', '2024-01-01 00:00:00', 'inactive'),
('fleet-005', 'vehicle-010', '2024-01-01 00:00:00', 'inactive');

-- 9. 边缘设备数据
INSERT INTO edge_devices (id, name, type, status, location, capabilities, last_heartbeat) VALUES 
('edge-001', '温度传感器-北京国贸-01', 'sensor', 'online', JSON_OBJECT('lat', 39.9075, 'lng', 116.4574, 'address', '北京市朝阳区国贸中心', 'floor', 'B2'), JSON_ARRAY('temperature', 'humidity', 'air_quality'), NOW() - INTERVAL 30 SECOND),
('edge-002', '智能控制器-上海陆家嘴-01', 'controller', 'online', JSON_OBJECT('lat', 31.2397, 'lng', 121.4999, 'address', '上海市浦东新区陆家嘴金融区', 'floor', '1F'), JSON_ARRAY('power_control', 'load_balancing', 'energy_optimization'), NOW() - INTERVAL 45 SECOND),
('edge-003', '网关设备-深圳科技园-01', 'gateway', 'online', JSON_OBJECT('lat', 22.5431, 'lng', 113.9344, 'address', '深圳市南山区科技园南区', 'floor', 'B1'), JSON_ARRAY('data_aggregation', 'protocol_conversion', 'edge_computing'), NOW() - INTERVAL 1 MINUTE),
('edge-004', '监控摄像头-北京中关村-01', 'camera', 'online', JSON_OBJECT('lat', 39.9388, 'lng', 116.3974, 'address', '北京市海淀区中关村大街', 'floor', '1F'), JSON_ARRAY('video_analytics', 'license_recognition', 'behavior_detection'), NOW() - INTERVAL 20 SECOND),
('edge-005', '环境传感器-广州珠江新城-01', 'sensor', 'online', JSON_OBJECT('lat', 23.1291, 'lng', 113.2644, 'address', '广州市天河区珠江新城', 'floor', 'B1'), JSON_ARRAY('temperature', 'humidity', 'co2', 'noise'), NOW() - INTERVAL 1 MINUTE),
('edge-006', '充电控制器-上海徐家汇-01', 'controller', 'error', JSON_OBJECT('lat', 31.2231, 'lng', 121.4581, 'address', '上海市徐汇区徐家汇', 'floor', 'B2'), JSON_ARRAY('charging_control', 'payment_processing', 'safety_monitoring'), NOW() - INTERVAL 5 MINUTE),
('edge-007', '数据网关-成都天府广场-01', 'gateway', 'online', JSON_OBJECT('lat', 30.6598, 'lng', 104.0657, 'address', '四川省成都市锦江区天府广场', 'floor', 'B1'), JSON_ARRAY('data_collection', 'real_time_processing', 'cloud_sync'), NOW() - INTERVAL 2 MINUTE),
('edge-008', '安防摄像头-杭州西湖-01', 'camera', 'offline', JSON_OBJECT('lat', 30.2741, 'lng', 120.1551, 'address', '浙江省杭州市西湖区', 'floor', '1F'), JSON_ARRAY('security_monitoring', 'crowd_detection', 'emergency_alert'), NOW() - INTERVAL 10 MINUTE),
('edge-009', '智能传感器-南京新街口-01', 'sensor', 'online', JSON_OBJECT('lat', 32.0473, 'lng', 118.7778, 'address', '江苏省南京市玄武区新街口', 'floor', 'B1'), JSON_ARRAY('motion_detection', 'occupancy_sensing', 'light_control'), NOW() - INTERVAL 40 SECOND),
('edge-010', '边缘计算节点-武汉光谷-01', 'controller', 'online', JSON_OBJECT('lat', 30.5078, 'lng', 114.4147, 'address', '湖北省武汉市洪山区光谷广场', 'floor', '2F'), JSON_ARRAY('ai_inference', 'data_analytics', 'predictive_maintenance'), NOW() - INTERVAL 1 MINUTE);

-- 边缘设备数据
INSERT INTO edge_device_data (device_id, data_type, value, text_value, json_value, unit, quality, timestamp) VALUES 
('edge-001', 'temperature', 25.6, NULL, NULL, '°C', 98.5, NOW() - INTERVAL 1 MINUTE),
('edge-001', 'humidity', 65.2, NULL, NULL, '%', 97.8, NOW() - INTERVAL 1 MINUTE),
('edge-001', 'air_quality', 85.3, 'Good', JSON_OBJECT('pm25', 12, 'pm10', 18, 'co2', 420), 'AQI', 99.2, NOW() - INTERVAL 1 MINUTE),
('edge-002', 'power_consumption', 1250.5, NULL, NULL, 'kW', 99.8, NOW() - INTERVAL 2 MINUTE),
('edge-002', 'load_factor', 0.85, NULL, NULL, '', 98.9, NOW() - INTERVAL 2 MINUTE),
('edge-002', 'energy_efficiency', 92.3, NULL, JSON_OBJECT('input_power', 1250.5, 'output_power', 1154.2, 'loss', 96.3), '%', 99.1, NOW() - INTERVAL 2 MINUTE),
('edge-003', 'data_throughput', 1024.0, NULL, NULL, 'MB/s', 97.5, NOW() - INTERVAL 3 MINUTE),
('edge-003', 'connection_count', 156, NULL, NULL, '', 100.0, NOW() - INTERVAL 3 MINUTE),
('edge-003', 'cpu_usage', 68.7, NULL, JSON_OBJECT('cores', 8, 'frequency', 2.4, 'temperature', 45.2), '%', 98.3, NOW() - INTERVAL 3 MINUTE),
('edge-004', 'vehicle_count', 23, NULL, NULL, '', 95.6, NOW() - INTERVAL 1 MINUTE),
('edge-004', 'license_detected', 18, NULL, JSON_ARRAY('京A12345', '京B67890', '京C11111'), '', 92.4, NOW() - INTERVAL 1 MINUTE),
('edge-004', 'anomaly_score', 0.12, 'Normal', NULL, '', 96.8, NOW() - INTERVAL 1 MINUTE);

-- 边缘任务数据
INSERT INTO edge_tasks (id, name, type, priority, status, device_id, config, result, created_at, completed_at) VALUES 
('task-001', '实时温度监控', 'real_time_analysis', 'high', 'running', 'edge-001', JSON_OBJECT('interval', 60, 'threshold', 30, 'alert_enabled', true), NULL, NOW() - INTERVAL 2 HOUR, NULL),
('task-002', '负载均衡优化', 'data_processing', 'medium', 'completed', 'edge-002', JSON_OBJECT('algorithm', 'round_robin', 'max_load', 0.9), JSON_OBJECT('optimization_rate', 15.3, 'energy_saved', 125.4), NOW() - INTERVAL 1 HOUR, NOW() - INTERVAL 30 MINUTE),
('task-003', '数据聚合处理', 'data_processing', 'medium', 'running', 'edge-003', JSON_OBJECT('batch_size', 1000, 'compression', true), NULL, NOW() - INTERVAL 3 HOUR, NULL),
('task-004', '车牌识别分析', 'ai_inference', 'high', 'completed', 'edge-004', JSON_OBJECT('model', 'yolo_v8', 'confidence', 0.85), JSON_OBJECT('accuracy', 94.2, 'processed_frames', 1580), NOW() - INTERVAL 4 HOUR, NOW() - INTERVAL 1 HOUR),
('task-005', '环境质量评估', 'real_time_analysis', 'low', 'pending', 'edge-005', JSON_OBJECT('metrics', JSON_ARRAY('temperature', 'humidity', 'co2'), 'report_interval', 300), NULL, NOW() - INTERVAL 30 MINUTE, NULL),
('task-006', '充电异常检测', 'alert_detection', 'critical', 'failed', 'edge-006', JSON_OBJECT('safety_threshold', 80, 'alert_channels', JSON_ARRAY('sms', 'email')), JSON_OBJECT('error', 'Device communication timeout'), NOW() - INTERVAL 6 HOUR, NOW() - INTERVAL 5 HOUR),
('task-007', '数据同步任务', 'data_processing', 'medium', 'completed', 'edge-007', JSON_OBJECT('sync_interval', 900, 'compression_ratio', 0.7), JSON_OBJECT('sync_success', true, 'data_size', 2.3), NOW() - INTERVAL 8 HOUR, NOW() - INTERVAL 7 HOUR),
('task-008', '安防监控分析', 'ai_inference', 'high', 'running', 'edge-009', JSON_OBJECT('detection_zones', 3, 'sensitivity', 0.75), NULL, NOW() - INTERVAL 1 HOUR, NULL),
('task-009', '预测性维护', 'ai_inference', 'medium', 'running', 'edge-010', JSON_OBJECT('model_type', 'lstm', 'prediction_window', 168), NULL, NOW() - INTERVAL 5 HOUR, NULL),
('task-010', '实时告警处理', 'alert_detection', 'critical', 'completed', 'edge-001', JSON_OBJECT('alert_types', JSON_ARRAY('temperature', 'humidity'), 'escalation_time', 300), JSON_OBJECT('alerts_processed', 3, 'false_positives', 0), NOW() - INTERVAL 12 HOUR, NOW() - INTERVAL 11 HOUR);

-- 更新车队车辆数量
UPDATE fleet_management fm SET 
    total_vehicles = (SELECT COUNT(*) FROM fleet_vehicles fv WHERE fv.fleet_id = fm.id),
    active_vehicles = (SELECT COUNT(*) FROM fleet_vehicles fv WHERE fv.fleet_id = fm.id AND fv.status = 'active');

-- 更新充电站可用端口数量
UPDATE charging_stations SET available_ports = total_ports - FLOOR(RAND() * 3) WHERE status = 'active';

SELECT '示例数据导入完成！' as message;