import { Router } from 'express'
import { db } from '../config/mysql'
import type { ApiResponse, PaginatedResponse, Battery, BatteryTrace, Vehicle, User } from '../../shared/types'

const router = Router()

// 获取电池列表
router.get('/', async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      vehicle_id, 
      manufacturer, 
      battery_type,
      health_status,
      min_capacity,
      max_capacity
    } = req.query
    const offset = (Number(page) - 1) * Number(limit)
    
    let query = `
      SELECT b.*, v.make as brand, v.model, v.license_plate, u.name as owner_name
       FROM batteries b
       LEFT JOIN vehicles v ON b.vehicle_id = v.vehicle_id
       LEFT JOIN users u ON v.owner_id = u.user_id
    `
    const params = []
    const conditions = []
    
    if (vehicle_id) {
      conditions.push('b.vehicle_id = ?')
      params.push(vehicle_id)
    }
    
    if (manufacturer) {
      conditions.push('b.manufacturer LIKE ?')
      params.push(`%${manufacturer}%`)
    }
    
    if (battery_type) {
      conditions.push('b.chemistry = ?')
      params.push(battery_type)
    }
    
    if (health_status) {
      conditions.push('b.status = ?')
      params.push(health_status)
    }
    
    if (min_capacity) {
      conditions.push('b.capacity_kwh >= ?')
      params.push(Number(min_capacity))
    }

    if (max_capacity) {
      conditions.push('b.capacity_kwh <= ?')
      params.push(Number(max_capacity))
    }
    
    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ')
    }
    
    // 获取总数
    const countQuery = query.replace('SELECT b.*, v.make as brand, v.model, v.license_plate, u.name as owner_name', 'SELECT COUNT(*) as total')
    const countResult = await db.query(countQuery, params)
    const total = countResult[0]?.total || 0

    // 添加排序和分页
    query += ' ORDER BY b.created_at DESC LIMIT ? OFFSET ?'
    params.push(Number(limit), Number(offset))
    
    const data = await db.query(query, params)
    
    const response: PaginatedResponse<Battery> = {
      success: true,
      data: data || [],
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: total,
        totalPages: Math.ceil(total / Number(limit))
      }
    }
    
    res.json(response)
  } catch (error) {
    console.error('获取电池列表异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取电池统计数据
router.get('/stats', async (req, res) => {
  try {
    // 获取电池总数和健康电池数
    const [totalResult, healthyResult] = await Promise.all([
      db.query('SELECT COUNT(*) as total FROM batteries'),
      db.query('SELECT COUNT(*) as healthy FROM batteries WHERE status = "healthy"')
    ])
    
    const total = totalResult[0]?.total || 0
    const healthy = healthyResult[0]?.healthy || 0
    const healthRate = total > 0 ? Number(((healthy / total) * 100).toFixed(1)) : 0

    res.json({
      success: true,
      data: {
        healthy,
        healthRate,
        total,
        unhealthy: total - healthy
      }
    } as ApiResponse)
  } catch (error) {
    console.error('获取电池统计异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取单个电池信息
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params
    
    const data = await db.query(`
      SELECT b.*, v.vehicle_id, v.make as brand, v.model, v.license_plate, v.vin,
             u.user_id, u.username as owner_name, u.email as owner_email
       FROM batteries b
       LEFT JOIN vehicles v ON b.vehicle_id = v.vehicle_id
       LEFT JOIN users u ON v.owner_id = u.user_id
      WHERE b.id = ?
    `, [id])
    
    if (data.length === 0) {
      return res.status(404).json({
        success: false,
        error: '电池不存在'
      } as ApiResponse)
    }
    
    res.json({
      success: true,
      data: data[0]
    } as ApiResponse<Battery>)
  } catch (error) {
    console.error('获取电池信息异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 创建电池记录
router.post('/', async (req, res) => {
  try {
    const {
      vehicle_id,
      serial_number,
      manufacturer,
      model,
      battery_type,
      capacity_kwh,
      voltage,
      manufacture_date,
      warranty_end_date
    } = req.body
    
    // 验证必填字段
    if (!vehicle_id || !serial_number || !manufacturer || !model || !battery_type || !capacity_kwh) {
      return res.status(400).json({
        success: false,
        error: '车辆ID、序列号、制造商、型号、电池类型和容量为必填字段'
      } as ApiResponse)
    }
    
    // 验证车辆是否存在
    const vehicleResult = await db.query('SELECT vehicle_id FROM vehicles WHERE vehicle_id = ?', [vehicle_id])
    
    if (vehicleResult.length === 0) {
      return res.status(400).json({
        success: false,
        error: '车辆不存在'
      } as ApiResponse)
    }
    
    // 检查序列号是否已存在
    const existingBattery = await db.query('SELECT id FROM batteries WHERE serial_number = ?', [serial_number])
    
    if (existingBattery.length > 0) {
      return res.status(400).json({
        success: false,
        error: '电池序列号已存在'
      } as ApiResponse)
    }
    
    const result = await db.query(`
      INSERT INTO batteries (
        vehicle_id, serial_number, manufacturer, model, chemistry, 
        capacity_kwh, voltage, manufacture_date, warranty_end_date,
        status, current_soc, cycle_count, temperature, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'excellent', 100, 0, 25, NOW(), NOW())
    `, [
      vehicle_id, serial_number, manufacturer, model, battery_type,
      Number(capacity_kwh), voltage ? Number(voltage) : null, 
      manufacture_date, warranty_end_date
    ])
    
    // 获取创建的电池记录
    const data = await db.query('SELECT * FROM batteries WHERE id = ?', [result.insertId])
    
    res.status(201).json({
      success: true,
      data: data[0],
      message: '电池记录创建成功'
    } as ApiResponse<Battery>)
  } catch (error) {
    console.error('创建电池记录异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 更新电池信息
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params
    const {
      current_soc,
      health_status,
      cycle_count,
      temperature,
      voltage,
      last_maintenance_date,
      next_maintenance_date
    } = req.body
    
    const updateData: any = {
      updated_at: new Date().toISOString()
    }
    
    if (current_soc !== undefined) updateData.current_soc = Number(current_soc)
    if (health_status) updateData.health_status = health_status
    if (cycle_count !== undefined) updateData.cycle_count = Number(cycle_count)
    if (temperature !== undefined) updateData.temperature = Number(temperature)
    if (voltage !== undefined) updateData.voltage = Number(voltage)
    if (last_maintenance_date) updateData.last_maintenance_date = last_maintenance_date
    if (next_maintenance_date) updateData.next_maintenance_date = next_maintenance_date
    
    const fields = Object.keys(updateData).map(key => `${key} = ?`).join(', ')
    const values = Object.values(updateData)
    values.push(id)
    
    await db.query(`UPDATE batteries SET ${fields} WHERE id = ?`, values)
    
    // 获取更新后的数据
    const data = await db.query('SELECT * FROM batteries WHERE id = ?', [id])
    
    if (data.length === 0) {
      return res.status(404).json({
        success: false,
        error: '电池不存在'
      } as ApiResponse)
    }
    
    res.json({
      success: true,
      data: data[0],
      message: '电池信息更新成功'
    } as ApiResponse<Battery>)
  } catch (error) {
    console.error('更新电池信息异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 删除电池记录
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params
    
    const result = await db.query('DELETE FROM batteries WHERE id = ?', [id])
    
    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        error: '电池不存在'
      } as ApiResponse)
    }
    
    res.json({
      success: true,
      message: '电池记录删除成功'
    } as ApiResponse)
  } catch (error) {
    console.error('删除电池记录异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取电池溯源记录
router.get('/:id/trace', async (req, res) => {
  try {
    const { id } = req.params
    const { page = 1, limit = 20 } = req.query
    const offset = (Number(page) - 1) * Number(limit)
    
    // 获取总数
    const countResult = await db.query('SELECT COUNT(*) as total FROM battery_trace WHERE battery_id = ?', [id])
    const total = countResult[0]?.total || 0
    
    // 获取分页数据
    const data = await db.query(`
      SELECT * FROM battery_trace 
      WHERE battery_id = ? 
      ORDER BY timestamp DESC 
      LIMIT ? OFFSET ?
    `, [id, Number(limit), offset])
    
    const response: PaginatedResponse<BatteryTrace> = {
      success: true,
      data: data || [],
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: total,
        totalPages: Math.ceil(total / Number(limit))
      }
    }
    
    res.json(response)
  } catch (error) {
    console.error('获取电池溯源记录异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 添加电池溯源记录
router.post('/:id/trace', async (req, res) => {
  try {
    const { id } = req.params
    const {
      event_type,
      location,
      operator,
      description,
      metadata
    } = req.body
    
    // 验证必填字段
    if (!event_type || !location) {
      return res.status(400).json({
        success: false,
        error: '事件类型和位置为必填字段'
      } as ApiResponse)
    }
    
    // 验证电池是否存在
    const batteryResult = await db.query('SELECT id FROM batteries WHERE id = ?', [id])
    
    if (batteryResult.length === 0) {
      return res.status(404).json({
        success: false,
        error: '电池不存在'
      } as ApiResponse)
    }
    
    const result = await db.query(`
      INSERT INTO battery_trace (
        battery_id, event_type, timestamp, location, operator, description, metadata
      ) VALUES (?, ?, NOW(), ?, ?, ?, ?)
    `, [id, event_type, location, operator, description, JSON.stringify(metadata)])
    
    // 获取创建的记录
    const data = await db.query('SELECT * FROM battery_trace WHERE id = ?', [result.insertId])
    
    res.status(201).json({
      success: true,
      data: data[0],
      message: '电池溯源记录添加成功'
    } as ApiResponse<BatteryTrace>)
  } catch (error) {
    console.error('添加电池溯源记录异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取电池健康统计
router.get('/:id/health-stats', async (req, res) => {
  try {
    const { id } = req.params
    const { days = 30 } = req.query
    
    // 获取电池基本信息
    const batteryResult = await db.query('SELECT * FROM batteries WHERE id = ?', [id])
    
    if (batteryResult.length === 0) {
      return res.status(404).json({
        success: false,
        error: '电池不存在'
      } as ApiResponse)
    }
    
    const battery = batteryResult[0]
    
    // 获取最近的溯源记录用于分析
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - Number(days))
    
    const traceRecords = await db.query(`
      SELECT * FROM battery_trace 
      WHERE battery_id = ? AND timestamp >= ? 
      ORDER BY timestamp ASC
    `, [id, startDate.toISOString()])
    
    // 计算健康统计
    const maintenanceEvents = traceRecords.filter(r => r.event_type === 'maintenance') || []
    const chargingEvents = traceRecords.filter(r => r.event_type === 'charging') || []
    
    const healthStats = {
      batteryInfo: {
        id: battery.id,
        serialNumber: battery.serial_number,
        manufacturer: battery.manufacturer,
        model: battery.model,
        capacity: battery.capacity_kwh,
        currentSoc: battery.current_soc,
        healthStatus: battery.health_status,
        cycleCount: battery.cycle_count,
        temperature: battery.temperature
      },
      recentActivity: {
        totalEvents: traceRecords.length || 0,
        maintenanceEvents: maintenanceEvents.length,
        chargingEvents: chargingEvents.length,
        lastMaintenanceDate: battery.last_maintenance_date,
        nextMaintenanceDate: battery.next_maintenance_date
      },
      healthMetrics: {
        capacityRetention: battery.capacity_kwh > 0 ? ((battery.current_soc / 100) * battery.capacity_kwh / battery.capacity_kwh * 100).toFixed(2) : '0',
        cycleLife: battery.cycle_count,
        temperatureRange: {
          current: battery.temperature,
          optimal: '20-25°C',
          status: battery.temperature >= 20 && battery.temperature <= 25 ? 'optimal' : 'warning'
        },
        warrantyStatus: {
          endDate: battery.warranty_end_date,
          isValid: battery.warranty_end_date ? new Date(battery.warranty_end_date) > new Date() : false
        }
      }
    }
    
    res.json({
      success: true,
      data: healthStats
    } as ApiResponse)
  } catch (error) {
    console.error('获取电池健康统计异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 批量更新电池状态
router.patch('/batch/status', async (req, res) => {
  try {
    const { battery_ids, health_status, current_soc, temperature } = req.body
    
    if (!battery_ids || !Array.isArray(battery_ids) || battery_ids.length === 0) {
      return res.status(400).json({
        success: false,
        error: '电池ID列表为必填字段'
      } as ApiResponse)
    }
    
    const updateData: any = {
      updated_at: new Date().toISOString()
    }
    
    if (health_status) updateData.health_status = health_status
    if (current_soc !== undefined) updateData.current_soc = Number(current_soc)
    if (temperature !== undefined) updateData.temperature = Number(temperature)
    
    const fields = Object.keys(updateData).map(key => `${key} = ?`).join(', ')
    const values = Object.values(updateData)
    const placeholders = battery_ids.map(() => '?').join(',')
    values.push(...battery_ids)
    
    await db.query(`UPDATE batteries SET ${fields} WHERE id IN (${placeholders})`, values)
    
    // 获取更新后的数据
    const data = await db.query(`SELECT * FROM batteries WHERE id IN (${placeholders})`, battery_ids)
    
    res.json({
      success: true,
      data,
      message: `成功更新 ${data.length || 0} 个电池状态`
    } as ApiResponse<Battery[]>)
  } catch (error) {
    console.error('批量更新电池状态异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取电池健康度分布数据
router.get('/health-distribution', async (req, res) => {
  try {
    const result = await db.query(`
      SELECT 
        CASE 
          WHEN current_soh >= 90 THEN '优秀'
          WHEN current_soh >= 80 THEN '良好'
          WHEN current_soh >= 70 THEN '一般'
          ELSE '需要关注'
        END as health_level,
        COUNT(*) as count
      FROM batteries 
      WHERE current_soh IS NOT NULL
      GROUP BY health_level
    `)
    
    const chartData = result.map((item: any) => ({
      name: item.health_level,
      value: item.count
    }))

    res.json({
      success: true,
      data: chartData
    } as ApiResponse)
  } catch (error) {
    console.error('获取电池健康度分布异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取电池概览统计
router.get('/overview', async (req, res) => {
  try {
    // 获取电池总数和各状态统计
    const [statsResult] = await db.execute(`
      SELECT
        COUNT(*) as total,
        SUM(CASE WHEN status = 'normal' THEN 1 ELSE 0 END) as healthy,
        SUM(CASE WHEN status = 'warning' THEN 1 ELSE 0 END) as warning,
        SUM(CASE WHEN status = 'critical' THEN 1 ELSE 0 END) as critical,
        SUM(CASE WHEN status = 'maintenance' THEN 1 ELSE 0 END) as maintenance,
        AVG(health_percentage) as avg_health,
        AVG(current_charge) as avg_charge,
        AVG(temperature) as avg_temperature
      FROM batteries
    `)

    const stats = (statsResult as any[])[0]

    const overview = {
      healthy: stats.healthy || 0,
      healthyRate: stats.total > 0 ? Math.round((stats.healthy / stats.total) * 100 * 100) / 100 : 0,
      warning: stats.warning || 0,
      warningRate: stats.total > 0 ? Math.round((stats.warning / stats.total) * 100 * 100) / 100 : 0,
      critical: stats.critical || 0,
      criticalRate: stats.total > 0 ? Math.round((stats.critical / stats.total) * 100 * 100) / 100 : 0,
      recycling: stats.maintenance || 0,
      recyclingRate: stats.total > 0 ? Math.round((stats.maintenance / stats.total) * 100 * 100) / 100 : 0,
      avgHealth: Math.round((stats.avg_health || 0) * 100) / 100,
      avgCharge: Math.round((stats.avg_charge || 0) * 100) / 100,
      avgTemperature: Math.round((stats.avg_temperature || 0) * 100) / 100
    }

    res.json({
      success: true,
      data: overview
    } as ApiResponse)
  } catch (error) {
    console.error('获取电池概览失败:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 保存预警设置
router.post('/alert-settings', async (req, res) => {
  try {
    const { temperatureThreshold, voltageThreshold, healthThreshold, cycleThreshold } = req.body

    // 这里可以将设置保存到数据库或配置文件
    // 目前返回成功响应
    res.json({
      success: true,
      message: '预警设置已保存',
      data: {
        temperatureThreshold,
        voltageThreshold,
        healthThreshold,
        cycleThreshold
      }
    } as ApiResponse)
  } catch (error) {
    console.error('保存预警设置失败:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 导出电池数据
router.post('/export', async (req, res) => {
  try {
    const { format = 'excel', filters = {} } = req.body

    // 获取电池数据
    const [batteriesResult] = await db.execute(`
      SELECT
        b.*,
        v.make as vehicle_brand,
        v.model as vehicle_model,
        v.license_plate
      FROM batteries b
      LEFT JOIN vehicles v ON b.vehicle_id = v.vehicle_id
      ORDER BY b.created_at DESC
    `)

    const batteries = batteriesResult as any[]

    // 模拟导出处理
    const exportData = {
      format,
      recordCount: batteries.length,
      exportTime: new Date().toISOString(),
      downloadUrl: `/downloads/batteries_export_${Date.now()}.${format}`
    }

    res.json({
      success: true,
      message: '数据导出成功',
      data: exportData
    } as ApiResponse)
  } catch (error) {
    console.error('导出电池数据失败:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

export default router