<template>
  <div class="map-component">
    <!-- 地图控制面板 -->
    <div class="map-controls">
      <div class="control-group">
        <div class="modern-button-group">
          <ModernButton 
            :variant="mapType === 'amap' ? 'primary' : 'ghost'"
            @click="switchMapType('amap')"
            size="small"
          >
            高德地图
          </ModernButton>
          <ModernButton 
            :variant="mapType === 'leaflet' ? 'primary' : 'ghost'"
            @click="switchMapType('leaflet')"
            size="small"
          >
            开源地图
          </ModernButton>
        </div>
      </div>
      
      <div class="control-group">
        <div style="position: relative;">
          <ModernLoader v-if="locating" />
          <ModernButton 
            size="small" 
            @click="getCurrentLocation"
            :disabled="locating"
            variant="primary"
          >
            <Position class="w-4 h-4" />
            定位
          </ModernButton>
        </div>
        
        <ModernButton 
          size="small" 
          @click="searchNearbyStations"
          variant="primary"
        >
          <Search class="w-4 h-4" />
          附近充电站
        </ModernButton>
      </div>
      
      <div class="control-group">
        <ModernInput
          v-model="searchKeyword"
          placeholder="搜索地点或充电站"
          size="small"
          style="width: 200px;"
          variant="glass"
          @keyup.enter="searchLocation"
        >
          <template #append>
            <ModernButton variant="ghost" size="small" @click="searchLocation">
              <Search class="w-4 h-4" />
            </ModernButton>
          </template>
        </ModernInput>
      </div>
    </div>

    <!-- 地图容器 -->
    <div class="map-container">
      <div 
        ref="mapContainer" 
        class="map-canvas"
        :class="{ 'map-loading': mapStatus === 'loading' }"
      >
        <!-- 地图加载状态 -->
        <div v-if="mapStatus === 'loading'" class="map-loading-overlay">
          <ModernLoader />
          <div class="loading-text">地图加载中...</div>
        </div>
        
        <!-- 地图错误状态 -->
        <div v-else-if="mapStatus === 'error'" class="map-error-overlay">
          <el-icon class="error-icon" :size="32">
            <Warning />
          </el-icon>
          <div class="error-text">地图加载失败</div>
          <ModernButton size="small" variant="primary" @click="retryMapInit">重试</ModernButton>
        </div>
      </div>
      
      <!-- 充电站列表侧边栏 -->
      <div class="stations-sidebar" v-if="showStationsList">
        <div class="sidebar-header">
          <h3>充电站列表</h3>
          <ModernButton 
            variant="ghost" 
            @click="showStationsList = false"
            size="small"
          >
            <Close class="w-4 h-4" />
          </ModernButton>
        </div>
        
        <div class="stations-list">
          <el-scrollbar height="400px">
            <div 
              v-for="station in nearbyStations" 
              :key="station.id"
              class="station-item"
              :class="{ active: selectedStation?.id === station.id }"
              @click="selectStation(station)"
            >
              <div class="station-info">
                <div class="station-name">{{ station.name }}</div>
                <div class="station-address">{{ station.address }}</div>
                <div class="station-meta">
                  <el-tag 
                    :type="getStationStatusType(station.status)" 
                    size="small"
                  >
                    {{ getStationStatusText(station.status) }}
                  </el-tag>
                  <span class="station-distance" v-if="station.distance">
                    {{ formatDistance(station.distance) }}
                  </span>
                </div>
                <div class="station-details">
                  <span>{{ station.availablePorts }}/{{ station.totalPorts }} 可用</span>
                  <span>¥{{ station.price }}/kWh</span>
                  <span>{{ station.rating }}⭐</span>
                </div>
              </div>
              
              <div class="station-actions">
                <ModernButton 
                  size="small" 
                  variant="primary" 
                  @click.stop="navigateToStation(station)"
                >
                  导航
                </ModernButton>
              </div>
            </div>
          </el-scrollbar>
        </div>
      </div>
    </div>

    <!-- 路线规划面板 -->
    <div class="route-panel" v-if="showRoutePanel">
      <div class="panel-header">
        <h3>路线规划</h3>
        <ModernButton 
          variant="ghost" 
          @click="closeRoutePanel"
          size="small"
        >
          <Close class="w-4 h-4" />
        </ModernButton>
      </div>
      
      <div class="route-info" v-if="currentRoute">
        <div class="route-summary">
          <div class="route-item">
            <el-icon><Position /></el-icon>
            <span>距离: {{ formatDistance(currentRoute.distance) }}</span>
          </div>
          <div class="route-item">
            <el-icon><Clock /></el-icon>
            <span>时间: {{ formatDuration(currentRoute.duration) }}</span>
          </div>
        </div>
        
        <div class="route-instructions">
          <h4>导航指引</h4>
          <el-scrollbar height="150px">
            <div class="instructions-list">
              <div 
                v-for="(instruction, index) in currentRoute.instructions" 
                :key="index"
                class="instruction-item"
              >
                <span class="step-number">{{ index + 1 }}</span>
                <span class="step-text">{{ instruction }}</span>
              </div>
            </div>
          </el-scrollbar>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick, watch } from 'vue';
import { 
  Position, 
  Search, 
  Loading, 
  Warning, 
  Close,
  Clock
} from '@element-plus/icons-vue';
import ModernInput from '@/components/ui/ModernInput.vue';
import { ElMessage } from 'element-plus';
import { ModernButton, ModernLoader } from '@/components/ui';
import { mapService, mapStatus, chargingStations, routePlanning } from '@/services/mapService';

// 组件属性
interface Props {
  height?: string;
  showControls?: boolean;
  showStationsList?: boolean;
  autoLocate?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  height: '500px',
  showControls: true,
  showStationsList: false,
  autoLocate: true
});

// 组件状态
const mapContainer = ref<HTMLElement>();
const mapType = ref<'amap' | 'leaflet'>('amap');
const locating = ref(false);
const searchKeyword = ref('');
const showStationsList = ref(props.showStationsList);
const showRoutePanel = ref(false);
const selectedStation = ref<any>(null);
const nearbyStations = ref<any[]>([]);
const currentRoute = ref<any>(null);

// 监听路线规划数据变化
watch(() => routePlanning.routes, (newRoutes) => {
  if (newRoutes.length > 0) {
    currentRoute.value = newRoutes[0];
    showRoutePanel.value = true;
  }
}, { deep: true });

// 切换地图类型
const switchMapType = async (type: 'amap' | 'leaflet') => {
  if (type === mapType.value) return;
  
  try {
    mapType.value = type;
    await initMap();
    ElMessage.success(`已切换到${type === 'amap' ? '高德地图' : '开源地图'}`);
  } catch (error) {
    ElMessage.error('地图切换失败');
  }
};

// 初始化地图
const initMap = async () => {
  if (!mapContainer.value) return;
  
  try {
    // 销毁现有地图
    mapService.destroy();
    
    // 初始化新地图
    if (mapType.value === 'amap') {
      await mapService.initAMap(mapContainer.value);
    } else {
      mapService.initLeafletMap(mapContainer.value);
    }
    
    // 如果需要自动定位
    if (props.autoLocate) {
      await getCurrentLocation();
    }
    
  } catch (error) {
    console.error('地图初始化失败:', error);
    ElMessage.error('地图初始化失败，请检查网络连接');
  }
};

// 获取当前位置
const getCurrentLocation = async () => {
  locating.value = true;
  
  try {
    const location = await mapService.getCurrentLocation();
    ElMessage.success('定位成功');
    
    // 搜索附近充电站
    searchNearbyStations();
    
  } catch (error) {
    ElMessage.warning('定位失败，使用默认位置');
  } finally {
    locating.value = false;
  }
};

// 搜索附近充电站
const searchNearbyStations = () => {
  if (!routePlanning.origin) {
    ElMessage.warning('请先获取当前位置');
    return;
  }
  
  const stations = mapService.searchNearbyStations(
    routePlanning.origin.lat,
    routePlanning.origin.lng,
    5000 // 5km范围
  );
  
  nearbyStations.value = stations;
  showStationsList.value = true;
  
  ElMessage.success(`找到 ${stations.length} 个附近充电站`);
};

// 搜索位置
const searchLocation = () => {
  if (!searchKeyword.value.trim()) {
    ElMessage.warning('请输入搜索关键词');
    return;
  }
  
  // 这里应该调用地图搜索API
  // 暂时显示提示信息
  ElMessage.info(`搜索功能开发中: ${searchKeyword.value}`);
};

// 选择充电站
const selectStation = (station: any) => {
  selectedStation.value = station;
  chargingStations.selected = station;
};

// 导航到充电站
const navigateToStation = (station: any) => {
  mapService.navigateToStation(station.id);
  ElMessage.success(`开始导航到 ${station.name}`);
};

// 关闭路线面板
const closeRoutePanel = () => {
  showRoutePanel.value = false;
  currentRoute.value = null;
};

// 重试地图初始化
const retryMapInit = () => {
  initMap();
};

// 获取充电站状态类型
const getStationStatusType = (status: string) => {
  const typeMap = {
    available: 'success',
    occupied: 'warning',
    offline: 'danger'
  };
  return typeMap[status as keyof typeof typeMap] || 'info';
};

// 获取充电站状态文本
const getStationStatusText = (status: string) => {
  const textMap = {
    available: '可用',
    occupied: '占用',
    offline: '离线'
  };
  return textMap[status as keyof typeof textMap] || '未知';
};

// 格式化距离
const formatDistance = (distance: number) => {
  if (distance < 1000) {
    return `${Math.round(distance)}m`;
  }
  return `${(distance / 1000).toFixed(1)}km`;
};

// 格式化时长
const formatDuration = (duration: number) => {
  const minutes = Math.round(duration / 60);
  if (minutes < 60) {
    return `${minutes}分钟`;
  }
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  return `${hours}小时${remainingMinutes}分钟`;
};

// 生命周期
onMounted(async () => {
  await nextTick();
  await initMap();
});

onUnmounted(() => {
  mapService.destroy();
});
</script>

<style scoped lang="scss">
.map-component {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #ffffff;
  border-radius: 8px;
  overflow: hidden;

.modern-button-group {
  display: flex;
  gap: 4px;
  padding: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  backdrop-filter: blur(10px);
}
  
  .map-controls {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 12px 16px;
    background: #f8fafc;
    border-bottom: 1px solid #e5e7eb;
    flex-wrap: wrap;
    
    .control-group {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
  
  .map-container {
    position: relative;
    flex: 1;
    display: flex;
    min-height: 400px;
    
    .map-canvas {
      flex: 1;
      position: relative;
      background: #f5f5f5;
      
      &.map-loading {
        pointer-events: none;
      }
      
      .map-loading-overlay,
      .map-error-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.9);
        z-index: 1000;
        
        .loading-icon,
        .error-icon {
          margin-bottom: 12px;
        }
        
        .loading-icon {
          color: #409EFF;
          animation: spin 1s linear infinite;
        }
        
        .error-icon {
          color: #F56C6C;
        }
        
        .loading-text,
        .error-text {
          font-size: 14px;
          color: #666;
          margin-bottom: 12px;
        }
      }
    }
    
    .stations-sidebar {
      width: 320px;
      background: #ffffff;
      border-left: 1px solid #e5e7eb;
      display: flex;
      flex-direction: column;
      
      .sidebar-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px;
        border-bottom: 1px solid #e5e7eb;
        
        h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #1f2937;
        }
      }
      
      .stations-list {
        flex: 1;
        
        .station-item {
          display: flex;
          padding: 16px;
          border-bottom: 1px solid #f3f4f6;
          cursor: pointer;
          transition: background-color 0.2s ease;
          
          &:hover {
            background-color: #f9fafb;
          }
          
          &.active {
            background-color: #eff6ff;
            border-left: 3px solid #409EFF;
          }
          
          .station-info {
            flex: 1;
            
            .station-name {
              font-size: 14px;
              font-weight: 500;
              color: #1f2937;
              margin-bottom: 4px;
            }
            
            .station-address {
              font-size: 12px;
              color: #6b7280;
              margin-bottom: 8px;
            }
            
            .station-meta {
              display: flex;
              align-items: center;
              gap: 8px;
              margin-bottom: 8px;
              
              .station-distance {
                font-size: 12px;
                color: #6b7280;
              }
            }
            
            .station-details {
              display: flex;
              gap: 12px;
              font-size: 12px;
              color: #6b7280;
            }
          }
          
          .station-actions {
            display: flex;
            align-items: center;
            margin-left: 12px;
          }
        }
      }
    }
  }
  
  .route-panel {
    position: absolute;
    top: 60px;
    right: 16px;
    width: 300px;
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    
    .panel-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px;
      border-bottom: 1px solid #e5e7eb;
      
      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #1f2937;
      }
    }
    
    .route-info {
      padding: 16px;
      
      .route-summary {
        display: flex;
        flex-direction: column;
        gap: 8px;
        margin-bottom: 16px;
        
        .route-item {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 14px;
          color: #374151;
        }
      }
      
      .route-instructions {
        h4 {
          margin: 0 0 8px 0;
          font-size: 14px;
          font-weight: 500;
          color: #1f2937;
        }
        
        .instructions-list {
          .instruction-item {
            display: flex;
            align-items: flex-start;
            gap: 8px;
            padding: 4px 0;
            font-size: 12px;
            
            .step-number {
              width: 20px;
              height: 20px;
              border-radius: 50%;
              background: #409EFF;
              color: white;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 10px;
              flex-shrink: 0;
            }
            
            .step-text {
              color: #374151;
              line-height: 1.4;
            }
          }
        }
      }
    }
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .map-component {
    .map-controls {
      flex-direction: column;
      align-items: stretch;
      gap: 8px;
      
      .control-group {
        justify-content: center;
      }
    }
    
    .map-container {
      flex-direction: column;
      
      .stations-sidebar {
        width: 100%;
        max-height: 300px;
        border-left: none;
        border-top: 1px solid #e5e7eb;
      }
    }
    
    .route-panel {
      position: relative;
      top: auto;
      right: auto;
      width: 100%;
      margin: 16px;
      width: calc(100% - 32px);
    }
  }
}
</style>