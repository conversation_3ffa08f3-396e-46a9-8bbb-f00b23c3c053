<template>
  <div class="charging-session-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <el-icon class="title-icon"><Timer /></el-icon>
            充电会话管理
          </h1>
          <p class="page-description">充电会话监控、历史记录、实时状态管理</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="startNewSession">
            <el-icon><Plus /></el-icon>
            新建会话
          </el-button>
          <el-button @click="exportSessions">
            <el-icon><Download /></el-icon>
            导出记录
          </el-button>
          <el-button @click="refreshData">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="stats-overview">
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon active">
            <el-icon><Lightning /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ activeSessions }}</div>
            <div class="stat-label">进行中会话</div>
            <div class="stat-change">{{ totalPower }} kW 总功率</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon completed">
            <el-icon><CircleCheck /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ completedToday }}</div>
            <div class="stat-label">今日完成</div>
            <div class="stat-change">{{ completionRate }}% 完成率</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon energy">
            <el-icon><Sunny /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ todayEnergy }}</div>
            <div class="stat-label">今日充电量(kWh)</div>
            <div class="stat-change">{{ avgEnergy }} kWh 平均</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon revenue">
            <el-icon><Money /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">¥{{ todayRevenue }}</div>
            <div class="stat-label">今日收入</div>
            <div class="stat-change positive">+{{ revenueGrowth }}% 增长</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <div class="filter-content">
        <div class="search-box">
          <el-input
            v-model="searchQuery"
            placeholder="搜索会话ID、充电站或用户"
            clearable
            style="width: 300px"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
        <div class="filter-controls">
          <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 120px">
            <el-option label="全部状态" value="" />
            <el-option label="进行中" value="active" />
            <el-option label="已完成" value="completed" />
            <el-option label="已取消" value="cancelled" />
            <el-option label="失败" value="failed" />
          </el-select>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px"
          />
          <el-button @click="resetFilters">重置</el-button>
        </div>
      </div>
    </div>

    <!-- 会话列表 -->
    <div class="sessions-table">
      <el-table
        :data="paginatedSessions"
        style="width: 100%"
        :row-class-name="getRowClassName"
        @row-click="selectSession"
      >
        <el-table-column prop="id" label="会话ID" width="100" />
        
        <el-table-column label="充电站" min-width="150">
          <template #default="{ row }">
            <div class="station-cell">
              <div class="station-name">{{ row.station_name }}</div>
              <div class="port-info">充电桩 {{ row.port_number || 'N/A' }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="用户" width="120">
          <template #default="{ row }">
            <div class="user-cell">
              <div class="user-name">{{ row.user_name || '用户' + row.user_id }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getSessionStatusType(row.status)" size="small">
              {{ getSessionStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="开始时间" width="140">
          <template #default="{ row }">
            {{ formatDateTime(row.start_time) }}
          </template>
        </el-table-column>
        
        <el-table-column label="持续时间" width="100">
          <template #default="{ row }">
            {{ getDuration(row) }}
          </template>
        </el-table-column>
        
        <el-table-column label="充电量" width="100">
          <template #default="{ row }">
            <span class="energy-value">{{ row.energy_delivered || 0 }} kWh</span>
          </template>
        </el-table-column>
        
        <el-table-column label="费用" width="100">
          <template #default="{ row }">
            <span class="cost-value">¥{{ row.total_cost || 0 }}</span>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button size="small" @click.stop="viewDetail(row)">
                详情
              </el-button>
              <el-button 
                v-if="row.status === 'active'" 
                size="small" 
                type="warning" 
                @click.stop="stopSession(row)"
              >
                停止
              </el-button>
              <el-button 
                v-if="row.status === 'completed'" 
                size="small" 
                type="success" 
                @click.stop="downloadReceipt(row)"
              >
                收据
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-section">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="totalSessions"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 会话详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="充电会话详情"
      width="800px"
    >
      <div v-if="selectedSession" class="session-detail">
        <div class="detail-grid">
          <div class="detail-item">
            <label>会话ID:</label>
            <span>{{ selectedSession.id }}</span>
          </div>
          <div class="detail-item">
            <label>充电站:</label>
            <span>{{ selectedSession.station_name }}</span>
          </div>
          <div class="detail-item">
            <label>用户:</label>
            <span>{{ selectedSession.user_name || '用户' + selectedSession.user_id }}</span>
          </div>
          <div class="detail-item">
            <label>状态:</label>
            <el-tag :type="getSessionStatusType(selectedSession.status)">
              {{ getSessionStatusText(selectedSession.status) }}
            </el-tag>
          </div>
          <div class="detail-item">
            <label>开始时间:</label>
            <span>{{ formatDateTime(selectedSession.start_time) }}</span>
          </div>
          <div class="detail-item">
            <label>结束时间:</label>
            <span>{{ selectedSession.end_time ? formatDateTime(selectedSession.end_time) : '进行中' }}</span>
          </div>
          <div class="detail-item">
            <label>充电量:</label>
            <span>{{ selectedSession.energy_delivered || 0 }} kWh</span>
          </div>
          <div class="detail-item">
            <label>总费用:</label>
            <span class="cost-highlight">¥{{ selectedSession.total_cost || 0 }}</span>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useChargingStore } from '@/stores/charging'
import {
  Timer,
  Plus,
  Download,
  Refresh,
  Search,
  Lightning,
  CircleCheck,
  Sunny,
  Money
} from '@element-plus/icons-vue'

const chargingStore = useChargingStore()

// 响应式数据
const searchQuery = ref('')
const statusFilter = ref('')
const dateRange = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const detailDialogVisible = ref(false)
const selectedSession = ref(null)

// 计算属性
const activeSessions = computed(() => 
  chargingStore.sessions.filter(s => s.status === 'active').length
)

const completedToday = computed(() => {
  const today = new Date().toDateString()
  return chargingStore.sessions.filter(s => 
    s.status === 'completed' && new Date(s.start_time).toDateString() === today
  ).length
})

const totalPower = computed(() => {
  return chargingStore.sessions
    .filter(s => s.status === 'active')
    .reduce((sum, s) => sum + (s.power || 60), 0)
})

const completionRate = computed(() => {
  const total = chargingStore.sessions.length
  const completed = chargingStore.sessions.filter(s => s.status === 'completed').length
  return total > 0 ? Math.round((completed / total) * 100) : 0
})

const todayEnergy = computed(() => {
  const today = new Date().toDateString()
  return chargingStore.sessions
    .filter(s => new Date(s.start_time).toDateString() === today)
    .reduce((sum, s) => sum + (s.energy_delivered || 0), 0)
})

const avgEnergy = computed(() => {
  const sessions = chargingStore.sessions.filter(s => s.energy_delivered > 0)
  return sessions.length > 0 ? 
    Math.round(sessions.reduce((sum, s) => sum + s.energy_delivered, 0) / sessions.length) : 0
})

const todayRevenue = computed(() => chargingStore.todayRevenue)
const revenueGrowth = computed(() => 12.5) // 示例增长率

const filteredSessions = computed(() => {
  let filtered = chargingStore.sessions
  
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(session => 
      session.id.toString().includes(query) ||
      session.station_name?.toLowerCase().includes(query) ||
      session.user_name?.toLowerCase().includes(query)
    )
  }
  
  if (statusFilter.value) {
    filtered = filtered.filter(session => session.status === statusFilter.value)
  }
  
  if (dateRange.value && dateRange.value.length === 2) {
    const [start, end] = dateRange.value
    filtered = filtered.filter(session => {
      const sessionDate = new Date(session.start_time)
      return sessionDate >= start && sessionDate <= end
    })
  }
  
  return filtered.sort((a, b) => new Date(b.start_time) - new Date(a.start_time))
})

const totalSessions = computed(() => filteredSessions.value.length)

const paginatedSessions = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredSessions.value.slice(start, end)
})

// 方法
const refreshData = async () => {
  try {
    await chargingStore.fetchSessions()
    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败')
  }
}

const resetFilters = () => {
  searchQuery.value = ''
  statusFilter.value = ''
  dateRange.value = []
}

const getRowClassName = ({ row }) => {
  if (row.status === 'failed') return 'failed-row'
  if (row.status === 'active') return 'active-row'
  return ''
}

const selectSession = (row: any) => {
  selectedSession.value = row
}

const getSessionStatusType = (status: string) => {
  const types = {
    active: 'warning',
    completed: 'success',
    cancelled: 'info',
    failed: 'danger'
  }
  return types[status] || 'info'
}

const getSessionStatusText = (status: string) => {
  const texts = {
    active: '进行中',
    completed: '已完成',
    cancelled: '已取消',
    failed: '失败'
  }
  return texts[status] || '未知'
}

const formatDateTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getDuration = (session: any) => {
  if (!session.start_time) return '0分钟'
  
  const start = new Date(session.start_time)
  const end = session.end_time ? new Date(session.end_time) : new Date()
  const duration = Math.floor((end - start) / (1000 * 60))
  
  if (duration < 60) {
    return `${duration}分钟`
  } else {
    const hours = Math.floor(duration / 60)
    const minutes = duration % 60
    return `${hours}小时${minutes}分钟`
  }
}

const startNewSession = () => {
  ElMessage.info('跳转到新建会话页面...')
  // 这里可以跳转到新建会话页面或打开对话框
}

const viewDetail = (row: any) => {
  selectedSession.value = row
  detailDialogVisible.value = true
}

const stopSession = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要停止会话 ${row.id} 吗？`,
      '确认停止',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await chargingStore.stopSession(row.id)
    ElMessage.success('会话已停止')
    await refreshData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('停止会话失败')
    }
  }
}

const downloadReceipt = (row: any) => {
  ElMessage.info('正在生成收据...')
  // 这里可以实现收据下载功能
}

const exportSessions = () => {
  ElMessage.info('导出功能开发中...')
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
}

// 生命周期
onMounted(async () => {
  await refreshData()
})
</script>

<style scoped>
.charging-session-management {
  padding: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.page-header {
  margin-bottom: 32px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 36px;
  color: #667eea;
}

.page-description {
  font-size: 16px;
  color: #718096;
  margin: 8px 0 0 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.stats-overview {
  margin-bottom: 32px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.active {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.stat-icon.completed {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.stat-icon.energy {
  background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.stat-icon.revenue {
  background: linear-gradient(135deg, #fa709a, #fee140);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #718096;
  margin-bottom: 4px;
}

.stat-change {
  font-size: 12px;
  color: #718096;
}

.stat-change.positive {
  color: #48bb78;
}

.filter-section {
  margin-bottom: 24px;
}

.filter-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.filter-controls {
  display: flex;
  gap: 16px;
  align-items: center;
}

.sessions-table {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.station-cell {
  display: flex;
  flex-direction: column;
}

.station-name {
  font-weight: 600;
  color: #1a202c;
}

.port-info {
  font-size: 12px;
  color: #718096;
}

.user-cell {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: 500;
  color: #1a202c;
}

.energy-value {
  color: #48bb78;
  font-weight: 600;
}

.cost-value {
  color: #667eea;
  font-weight: 600;
}

.cost-highlight {
  color: #667eea;
  font-weight: 600;
  font-size: 16px;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.pagination-section {
  display: flex;
  justify-content: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #e2e8f0;
}

.detail-item label {
  font-weight: 600;
  color: #4a5568;
}

.detail-item span {
  color: #1a202c;
}

:deep(.active-row) {
  background-color: #fff7ed;
}

:deep(.failed-row) {
  background-color: #fef2f2;
}

@media (max-width: 768px) {
  .charging-session-management {
    padding: 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .filter-content {
    flex-direction: column;
    gap: 16px;
  }
  
  .filter-controls {
    width: 100%;
    justify-content: center;
  }
  
  .detail-grid {
    grid-template-columns: 1fr;
  }
}
</style>
