-- 边缘设备管理表
-- 创建时间: 2024-12-28

-- 边缘设备表
CREATE TABLE IF NOT EXISTS edge_devices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    device_id VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(200) NOT NULL,
    type VARCHAR(30) NOT NULL CHECK (type IN ('sensor', 'controller', 'gateway', 'camera', 'actuator')),
    status VARCHAR(20) DEFAULT 'online' CHECK (status IN ('online', 'offline', 'error', 'maintenance')),
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    address TEXT,
    capabilities TEXT[], -- 设备能力列表
    firmware_version VARCHAR(50),
    hardware_version VARCHAR(50),
    manufacturer VARCHAR(100),
    model VARCHAR(100),
    installation_date DATE,
    last_heartbeat TIMESTAMP WITH TIME ZONE,
    config JSON<PERSON>, -- 设备配置
    metadata JSONB, -- 设备元数据
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 边缘设备数据表
CREATE TABLE IF NOT EXISTS edge_device_data (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    device_id VARCHAR(50) REFERENCES edge_devices(device_id) ON DELETE CASCADE,
    data_type VARCHAR(50) NOT NULL, -- 数据类型：temperature, humidity, power, status等
    value DECIMAL(15,6), -- 数值型数据
    text_value TEXT, -- 文本型数据
    json_value JSONB, -- JSON型数据
    unit VARCHAR(20), -- 数据单位
    quality DECIMAL(5,2) DEFAULT 100.00, -- 数据质量评分
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 边缘计算任务表
CREATE TABLE IF NOT EXISTS edge_tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_id VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(200) NOT NULL,
    type VARCHAR(30) NOT NULL CHECK (type IN ('data_processing', 'analytics', 'control', 'monitoring')),
    priority VARCHAR(20) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'critical')),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'completed', 'failed', 'cancelled')),
    device_id VARCHAR(50) REFERENCES edge_devices(device_id) ON DELETE CASCADE,
    config JSONB, -- 任务配置
    input_data JSONB, -- 输入数据
    output_data JSONB, -- 输出数据
    error_message TEXT, -- 错误信息
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_edge_devices_device_id ON edge_devices(device_id);
CREATE INDEX idx_edge_devices_type ON edge_devices(type);
CREATE INDEX idx_edge_devices_status ON edge_devices(status);
CREATE INDEX idx_edge_device_data_device_id ON edge_device_data(device_id);
CREATE INDEX idx_edge_device_data_timestamp ON edge_device_data(timestamp);
CREATE INDEX idx_edge_device_data_type ON edge_device_data(data_type);
CREATE INDEX idx_edge_tasks_device_id ON edge_tasks(device_id);
CREATE INDEX idx_edge_tasks_status ON edge_tasks(status);
CREATE INDEX idx_edge_tasks_created_at ON edge_tasks(created_at);

-- 启用行级安全策略
ALTER TABLE edge_devices ENABLE ROW LEVEL SECURITY;
ALTER TABLE edge_device_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE edge_tasks ENABLE ROW LEVEL SECURITY;

-- 创建RLS策略
-- 认证用户可以查看所有边缘设备
CREATE POLICY "Authenticated users can view edge devices" ON edge_devices FOR SELECT TO authenticated USING (true);
CREATE POLICY "Authenticated users can view edge device data" ON edge_device_data FOR SELECT TO authenticated USING (true);
CREATE POLICY "Authenticated users can view edge tasks" ON edge_tasks FOR SELECT TO authenticated USING (true);

-- 管理员可以管理边缘设备
CREATE POLICY "Admins can manage edge devices" ON edge_devices FOR ALL TO authenticated 
USING (EXISTS (SELECT 1 FROM users WHERE users.id = auth.uid() AND users.user_type = 'admin'));

CREATE POLICY "Admins can manage edge device data" ON edge_device_data FOR ALL TO authenticated 
USING (EXISTS (SELECT 1 FROM users WHERE users.id = auth.uid() AND users.user_type = 'admin'));

CREATE POLICY "Admins can manage edge tasks" ON edge_tasks FOR ALL TO authenticated 
USING (EXISTS (SELECT 1 FROM users WHERE users.id = auth.uid() AND users.user_type = 'admin'));

-- 插入初始边缘设备数据
INSERT INTO edge_devices (device_id, name, type, status, latitude, longitude, address, capabilities, firmware_version, hardware_version, manufacturer, model, installation_date, last_heartbeat) VALUES
('sensor_001', '温度传感器-001', 'sensor', 'online', 39.9042, 116.4074, '北京市朝阳区', ARRAY['temperature', 'humidity'], 'v1.2.3', 'v2.1', 'SensorTech', 'ST-T100', '2024-01-15', NOW()),
('controller_001', '充电桩控制器-001', 'controller', 'online', 31.2304, 121.4737, '上海市浦东新区', ARRAY['power_control', 'status_monitoring'], 'v2.0.1', 'v1.5', 'ChargeTech', 'CT-C200', '2024-02-20', NOW()),
('gateway_001', '边缘网关-001', 'gateway', 'online', 23.1291, 113.2644, '广州市天河区', ARRAY['network', 'aggregation'], 'v1.5.0', 'v3.0', 'GatewayPro', 'GP-G300', '2024-03-10', NOW()),
('camera_001', '智能摄像头-001', 'camera', 'online', 22.5431, 114.0579, '深圳市南山区', ARRAY['video', 'analytics'], 'v3.1.2', 'v2.8', 'VisionAI', 'VA-C400', '2024-04-05', NOW()),
('sensor_002', '湿度传感器-002', 'sensor', 'online', 30.2741, 120.1551, '杭州市西湖区', ARRAY['humidity', 'air_quality'], 'v1.1.8', 'v1.9', 'SensorTech', 'ST-H150', '2024-05-12', NOW());

-- 插入初始设备数据
INSERT INTO edge_device_data (device_id, data_type, value, unit, timestamp) VALUES
('sensor_001', 'temperature', 25.6, '°C', NOW() - INTERVAL '1 minute'),
('sensor_001', 'humidity', 65.2, '%', NOW() - INTERVAL '1 minute'),
('controller_001', 'power', 87.5, 'kW', NOW() - INTERVAL '2 minutes'),
('controller_001', 'voltage', 380.2, 'V', NOW() - INTERVAL '2 minutes'),
('gateway_001', 'cpu_usage', 45.8, '%', NOW() - INTERVAL '30 seconds'),
('gateway_001', 'memory_usage', 62.3, '%', NOW() - INTERVAL '30 seconds'),
('camera_001', 'motion_detected', 1, 'boolean', NOW() - INTERVAL '5 minutes'),
('sensor_002', 'humidity', 58.9, '%', NOW() - INTERVAL '3 minutes'),
('sensor_002', 'air_quality', 85.4, 'AQI', NOW() - INTERVAL '3 minutes');

-- 授予权限
GRANT ALL PRIVILEGES ON edge_devices TO authenticated;
GRANT ALL PRIVILEGES ON edge_device_data TO authenticated;
GRANT ALL PRIVILEGES ON edge_tasks TO authenticated;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO authenticated;