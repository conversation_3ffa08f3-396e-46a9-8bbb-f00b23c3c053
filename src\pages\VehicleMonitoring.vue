<template>
  <div class="vehicle-monitoring">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <el-icon class="title-icon"><Monitor /></el-icon>
            车辆实时监控
          </h1>
          <p class="page-description">实时监控车队状态，掌握每辆车的动态信息</p>
        </div>
        <div class="header-actions">
          <el-button @click="refreshAll">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
          <el-button type="primary" @click="showSettings">
            <el-icon><Setting /></el-icon>
            监控设置
          </el-button>
        </div>
      </div>
    </div>

    <!-- 实时统计 -->
    <div class="realtime-stats">
      <div class="stats-grid">
        <div class="stat-card online">
          <div class="stat-icon">
            <el-icon><SuccessFilled /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ realtimeStats.online }}</div>
            <div class="stat-label">在线车辆</div>
            <div class="stat-trend">
              <el-icon><TrendCharts /></el-icon>
              <span class="trend-text">+5.2%</span>
            </div>
          </div>
        </div>
        
        <div class="stat-card moving">
          <div class="stat-icon">
            <el-icon><Position /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ realtimeStats.moving }}</div>
            <div class="stat-label">行驶中</div>
            <div class="stat-trend">
              <el-icon><TrendCharts /></el-icon>
              <span class="trend-text">+12.8%</span>
            </div>
          </div>
        </div>
        
        <div class="stat-card charging">
          <div class="stat-icon">
            <el-icon><Lightning /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ realtimeStats.charging }}</div>
            <div class="stat-label">充电中</div>
            <div class="stat-trend">
              <el-icon><TrendCharts /></el-icon>
              <span class="trend-text">-2.1%</span>
            </div>
          </div>
        </div>
        
        <div class="stat-card alerts">
          <div class="stat-icon">
            <el-icon><Warning /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ realtimeStats.alerts }}</div>
            <div class="stat-label">告警数量</div>
            <div class="stat-trend">
              <el-icon><TrendCharts /></el-icon>
              <span class="trend-text">-15.3%</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <div class="content-grid">
        <!-- 左侧：地图和车辆分布 -->
        <div class="left-panel">
          <!-- 实时地图 -->
          <div class="map-section">
            <div class="section-header">
              <h3>车辆分布地图</h3>
              <div class="map-controls">
                <el-button-group>
                  <el-button :type="mapMode === 'all' ? 'primary' : ''" @click="setMapMode('all')">
                    全部
                  </el-button>
                  <el-button :type="mapMode === 'online' ? 'primary' : ''" @click="setMapMode('online')">
                    在线
                  </el-button>
                  <el-button :type="mapMode === 'charging' ? 'primary' : ''" @click="setMapMode('charging')">
                    充电中
                  </el-button>
                </el-button-group>
              </div>
            </div>
            <div class="map-container">
              <div id="monitoring-map" class="map-view"></div>
              <div class="map-legend">
                <div class="legend-item">
                  <div class="legend-dot online"></div>
                  <span>在线车辆</span>
                </div>
                <div class="legend-item">
                  <div class="legend-dot charging"></div>
                  <span>充电中</span>
                </div>
                <div class="legend-item">
                  <div class="legend-dot offline"></div>
                  <span>离线车辆</span>
                </div>
                <div class="legend-item">
                  <div class="legend-dot alert"></div>
                  <span>告警车辆</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 车辆状态分布 -->
          <div class="distribution-section">
            <div class="section-header">
              <h3>状态分布</h3>
            </div>
            <div class="distribution-chart">
              <div ref="distributionChartRef" class="chart-container"></div>
            </div>
          </div>
        </div>

        <!-- 右侧：实时数据和告警 -->
        <div class="right-panel">
          <!-- 实时告警 -->
          <div class="alerts-section">
            <div class="section-header">
              <h3>实时告警</h3>
              <el-badge :value="realtimeAlerts.length" class="alert-badge">
                <el-button size="small" @click="clearAllAlerts">清除全部</el-button>
              </el-badge>
            </div>
            <div class="alerts-list">
              <div 
                v-for="alert in realtimeAlerts" 
                :key="alert.id" 
                class="alert-item"
                :class="alert.level"
              >
                <div class="alert-icon">
                  <el-icon><component :is="getAlertIcon(alert.level)" /></el-icon>
                </div>
                <div class="alert-content">
                  <div class="alert-title">{{ alert.title }}</div>
                  <div class="alert-description">{{ alert.description }}</div>
                  <div class="alert-time">{{ formatTime(alert.time) }}</div>
                </div>
                <div class="alert-actions">
                  <el-button size="small" @click="handleAlert(alert)">处理</el-button>
                </div>
              </div>
            </div>
          </div>

          <!-- 实时数据流 -->
          <div class="data-stream-section">
            <div class="section-header">
              <h3>实时数据流</h3>
              <div class="stream-status">
                <el-tag :type="streamStatus ? 'success' : 'danger'" size="small">
                  {{ streamStatus ? '连接中' : '已断开' }}
                </el-tag>
              </div>
            </div>
            <div class="data-stream">
              <div 
                v-for="data in dataStream" 
                :key="data.id" 
                class="stream-item"
                :class="data.type"
              >
                <div class="stream-time">{{ formatTime(data.time) }}</div>
                <div class="stream-vehicle">{{ data.vehicleId }}</div>
                <div class="stream-data">{{ data.message }}</div>
              </div>
            </div>
          </div>

          <!-- 性能监控 -->
          <div class="performance-section">
            <div class="section-header">
              <h3>系统性能</h3>
            </div>
            <div class="performance-metrics">
              <div class="metric-item">
                <div class="metric-label">数据更新频率</div>
                <div class="metric-value">{{ performanceMetrics.updateRate }}/秒</div>
                <el-progress 
                  :percentage="(performanceMetrics.updateRate / 10) * 100" 
                  :show-text="false"
                  :stroke-width="6"
                />
              </div>
              <div class="metric-item">
                <div class="metric-label">网络延迟</div>
                <div class="metric-value">{{ performanceMetrics.latency }}ms</div>
                <el-progress 
                  :percentage="Math.max(0, 100 - performanceMetrics.latency)" 
                  :show-text="false"
                  :stroke-width="6"
                  :color="getLatencyColor(performanceMetrics.latency)"
                />
              </div>
              <div class="metric-item">
                <div class="metric-label">连接稳定性</div>
                <div class="metric-value">{{ performanceMetrics.stability }}%</div>
                <el-progress 
                  :percentage="performanceMetrics.stability" 
                  :show-text="false"
                  :stroke-width="6"
                  color="#10b981"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 车辆详情快速查看 -->
    <el-drawer
      v-model="showQuickView"
      title="车辆快速查看"
      direction="rtl"
      size="400px"
    >
      <div v-if="selectedVehicle" class="quick-view-content">
        <div class="vehicle-summary">
          <div class="vehicle-header">
            <h4>{{ selectedVehicle.brand }} {{ selectedVehicle.model }}</h4>
            <el-tag :type="getStatusType(selectedVehicle.status)">
              {{ getStatusText(selectedVehicle.status) }}
            </el-tag>
          </div>
          <p class="vehicle-plate">{{ selectedVehicle.license_plate }}</p>
        </div>
        
        <div class="realtime-data">
          <div class="data-item">
            <span class="data-label">电池电量</span>
            <div class="battery-display">
              <el-progress 
                :percentage="selectedVehicle.batteryLevel" 
                :show-text="false"
                :stroke-width="8"
                :color="getBatteryColor(selectedVehicle.batteryLevel)"
              />
              <span class="battery-text">{{ selectedVehicle.batteryLevel }}%</span>
            </div>
          </div>
          
          <div class="data-item">
            <span class="data-label">当前速度</span>
            <span class="data-value">{{ selectedVehicle.speed }} km/h</span>
          </div>
          
          <div class="data-item">
            <span class="data-label">位置</span>
            <span class="data-value">{{ selectedVehicle.location }}</span>
          </div>
          
          <div class="data-item">
            <span class="data-label">最后更新</span>
            <span class="data-value">{{ formatTime(selectedVehicle.lastUpdate) }}</span>
          </div>
        </div>
        
        <div class="quick-actions">
          <el-button type="primary" @click="openFullDetail">查看详情</el-button>
          <el-button @click="openRemoteControl">远程控制</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Monitor, Refresh, Setting, SuccessFilled, Position,
  Lightning, Warning, TrendCharts, CircleCloseFilled, InfoFilled
} from '@element-plus/icons-vue'

// 响应式数据
const mapMode = ref('all')
const streamStatus = ref(true)
const showQuickView = ref(false)
const selectedVehicle = ref<any>(null)
const distributionChartRef = ref()

// 实时统计数据
const realtimeStats = ref({
  online: 1156,
  moving: 234,
  charging: 89,
  alerts: 3
})

// 实时告警数据
const realtimeAlerts = ref([
  {
    id: 1,
    level: 'critical',
    title: '电池温度过高',
    description: '车辆 京A12345 电池温度达到45°C，请立即检查',
    time: new Date(Date.now() - 2 * 60 * 1000),
    vehicleId: '京A12345'
  },
  {
    id: 2,
    level: 'warning',
    title: '电量不足',
    description: '车辆 沪B67890 电量仅剩15%，建议尽快充电',
    time: new Date(Date.now() - 5 * 60 * 1000),
    vehicleId: '沪B67890'
  },
  {
    id: 3,
    level: 'info',
    title: '维护提醒',
    description: '车辆 粤C11111 即将到达保养里程',
    time: new Date(Date.now() - 10 * 60 * 1000),
    vehicleId: '粤C11111'
  }
])

// 实时数据流
const dataStream = ref([
  {
    id: 1,
    type: 'battery',
    vehicleId: '京A12345',
    message: '电量: 85% → 84%',
    time: new Date(Date.now() - 1000)
  },
  {
    id: 2,
    type: 'location',
    vehicleId: '沪B67890',
    message: '位置更新: 北京市朝阳区',
    time: new Date(Date.now() - 3000)
  },
  {
    id: 3,
    type: 'speed',
    vehicleId: '粤C11111',
    message: '速度: 45 km/h',
    time: new Date(Date.now() - 5000)
  }
])

// 性能指标
const performanceMetrics = ref({
  updateRate: 8.5,
  latency: 25,
  stability: 99.2
})

// 方法
const setMapMode = (mode: string) => {
  mapMode.value = mode
  // 更新地图显示
  updateMapDisplay()
}

const updateMapDisplay = () => {
  // 实际项目中这里会更新地图上的车辆显示
  ElMessage.success(`地图模式切换为: ${mapMode.value}`)
}

const refreshAll = async () => {
  // 刷新所有数据
  ElMessage.success('数据刷新成功')
}

const showSettings = () => {
  ElMessage.info('监控设置功能开发中...')
}

const getAlertIcon = (level: string) => {
  const icons: Record<string, string> = {
    'critical': 'CircleCloseFilled',
    'warning': 'Warning',
    'info': 'InfoFilled'
  }
  return icons[level] || 'InfoFilled'
}

const handleAlert = (alert: any) => {
  ElMessage.success(`处理告警: ${alert.title}`)
}

const clearAllAlerts = () => {
  realtimeAlerts.value = []
  ElMessage.success('所有告警已清除')
}

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    'active': 'success',
    'maintenance': 'warning',
    'retired': 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    'active': '在线',
    'maintenance': '维护中',
    'retired': '已退役'
  }
  return texts[status] || status
}

const getBatteryColor = (level: number) => {
  if (level > 80) return '#10b981'
  if (level > 50) return '#f59e0b'
  if (level > 20) return '#ef4444'
  return '#dc2626'
}

const getLatencyColor = (latency: number) => {
  if (latency < 30) return '#10b981'
  if (latency < 60) return '#f59e0b'
  return '#ef4444'
}

const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN')
}

const openFullDetail = () => {
  showQuickView.value = false
  // 跳转到车辆详情页面
}

const openRemoteControl = () => {
  showQuickView.value = false
  // 跳转到远程控制页面
}

// 模拟实时数据更新
let dataUpdateInterval: number

onMounted(() => {
  // 初始化地图
  initMap()
  
  // 启动实时数据更新
  dataUpdateInterval = setInterval(() => {
    updateRealtimeData()
  }, 2000)
})

onUnmounted(() => {
  if (dataUpdateInterval) {
    clearInterval(dataUpdateInterval)
  }
})

const initMap = () => {
  // 初始化地图（这里使用模拟数据）
  ElMessage.success('地图初始化完成')
}

const updateRealtimeData = () => {
  // 模拟实时数据更新
  const newData = {
    id: Date.now(),
    type: ['battery', 'location', 'speed'][Math.floor(Math.random() * 3)],
    vehicleId: ['京A12345', '沪B67890', '粤C11111'][Math.floor(Math.random() * 3)],
    message: '数据更新',
    time: new Date()
  }
  
  dataStream.value.unshift(newData)
  if (dataStream.value.length > 20) {
    dataStream.value = dataStream.value.slice(0, 20)
  }
  
  // 更新性能指标
  performanceMetrics.value.updateRate = Math.random() * 2 + 7
  performanceMetrics.value.latency = Math.floor(Math.random() * 20) + 20
}
</script>

<style scoped>
.vehicle-monitoring {
  padding: 0;
  background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 50%, #581c87 100%);
  min-height: 100vh;
  position: relative;
}

.vehicle-monitoring::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(16, 185, 129, 0.2) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

/* 页面头部 */
.page-header {
  padding: 40px 32px 20px;
  position: relative;
  z-index: 1;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  max-width: 1600px;
  margin: 0 auto;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  color: white;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 36px;
}

.page-description {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 实时统计 */
.realtime-stats {
  padding: 20px 32px;
  position: relative;
  z-index: 1;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  max-width: 1600px;
  margin: 0 auto;
}

.stat-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.stat-card.online .stat-icon {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.stat-card.moving .stat-icon {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.stat-card.charging .stat-icon {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.stat-card.alerts .stat-icon {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #111827;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
  margin: 4px 0;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #10b981;
}

/* 主要内容 */
.main-content {
  padding: 0 32px 40px;
  position: relative;
  z-index: 1;
}

.content-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
  max-width: 1600px;
  margin: 0 auto;
}

/* 左侧面板 */
.left-panel {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 地图区域 */
.map-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  overflow: hidden;
}

.section-header {
  padding: 20px 24px;
  border-bottom: 1px solid #f3f4f6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.map-container {
  position: relative;
  height: 400px;
}

.map-view {
  width: 100%;
  height: 100%;
  background: #f8fafc;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  font-size: 16px;
}

.map-legend {
  position: absolute;
  top: 16px;
  right: 16px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  padding: 12px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #374151;
}

.legend-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.legend-dot.online {
  background: #10b981;
}

.legend-dot.charging {
  background: #f59e0b;
}

.legend-dot.offline {
  background: #6b7280;
}

.legend-dot.alert {
  background: #ef4444;
}

/* 分布图表 */
.distribution-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  overflow: hidden;
}

.chart-container {
  height: 200px;
  padding: 20px;
}

/* 右侧面板 */
.right-panel {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 告警区域 */
.alerts-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  overflow: hidden;
}

.alerts-list {
  max-height: 300px;
  overflow-y: auto;
}

.alert-item {
  padding: 16px 20px;
  border-bottom: 1px solid #f3f4f6;
  display: flex;
  gap: 12px;
  transition: all 0.2s ease;
}

.alert-item:hover {
  background: #f9fafb;
}

.alert-item:last-child {
  border-bottom: none;
}

.alert-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
  flex-shrink: 0;
}

.alert-item.critical .alert-icon {
  background: #ef4444;
}

.alert-item.warning .alert-icon {
  background: #f59e0b;
}

.alert-item.info .alert-icon {
  background: #3b82f6;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-size: 14px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 4px;
}

.alert-description {
  font-size: 13px;
  color: #6b7280;
  margin-bottom: 4px;
  line-height: 1.4;
}

.alert-time {
  font-size: 12px;
  color: #9ca3af;
}

/* 数据流区域 */
.data-stream-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  overflow: hidden;
}

.data-stream {
  max-height: 250px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
}

.stream-item {
  padding: 8px 20px;
  border-bottom: 1px solid #f3f4f6;
  display: flex;
  gap: 12px;
  font-size: 12px;
  transition: all 0.2s ease;
}

.stream-item:hover {
  background: #f9fafb;
}

.stream-time {
  color: #9ca3af;
  min-width: 80px;
}

.stream-vehicle {
  color: #3b82f6;
  min-width: 80px;
  font-weight: 500;
}

.stream-data {
  color: #111827;
  flex: 1;
}

/* 性能监控 */
.performance-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  overflow: hidden;
}

.performance-metrics {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.metric-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.metric-label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.metric-value {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

/* 快速查看抽屉 */
.quick-view-content {
  padding: 20px 0;
}

.vehicle-summary {
  padding: 0 0 20px 0;
  border-bottom: 1px solid #f3f4f6;
  margin-bottom: 20px;
}

.vehicle-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.vehicle-header h4 {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.vehicle-plate {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
}

.realtime-data {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
}

.data-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.data-label {
  font-size: 14px;
  color: #6b7280;
}

.data-value {
  font-size: 14px;
  font-weight: 600;
  color: #111827;
}

.battery-display {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  max-width: 120px;
}

.battery-text {
  font-size: 12px;
  font-weight: 500;
  min-width: 35px;
}

.quick-actions {
  display: flex;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .content-grid {
    grid-template-columns: 1fr;
  }

  .right-panel {
    order: -1;
  }
}

@media (max-width: 768px) {
  .vehicle-monitoring {
    padding: 0;
  }

  .page-header {
    padding: 24px 16px 16px;
  }

  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .realtime-stats {
    padding: 16px;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .main-content {
    padding: 0 16px 24px;
  }

  .map-container {
    height: 300px;
  }

  .quick-actions {
    flex-direction: column;
  }
}
</style>
