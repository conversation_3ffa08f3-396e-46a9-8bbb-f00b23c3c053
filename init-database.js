/**
 * 数据库初始化脚本
 * 初始化MySQL数据库表结构和示例数据
 */

const mysql = require('mysql2/promise')
const fs = require('fs')
require('dotenv').config()

// MySQL 连接配置
const mysqlConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME || 'ev_management',
  charset: 'utf8mb4',
  multipleStatements: true
}

async function initDatabase() {
  let connection;
  try {
    console.log('🚀 开始初始化数据库...')

    // 先连接到MySQL创建数据库
    const baseConfig = { ...mysqlConfig }
    delete baseConfig.database
    let tempConnection = await mysql.createConnection(baseConfig)
    console.log('✅ MySQL 连接成功')

    // 创建数据库
    console.log('📋 创建数据库...')
    await tempConnection.execute(`CREATE DATABASE IF NOT EXISTS ${mysqlConfig.database} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`)
    await tempConnection.end()
    console.log('✅ 数据库创建完成')

    // 重新连接到指定数据库
    connection = await mysql.createConnection(mysqlConfig)
    console.log('✅ 连接到目标数据库')

    // 读取并执行初始化SQL脚本（跳过前几行）
    console.log('📋 执行表结构初始化...')
    const initSQL = fs.readFileSync('mysql_init.sql', 'utf8')
    const sqlStatements = initSQL.split(';').filter(stmt => {
      const trimmed = stmt.trim()
      return trimmed &&
             !trimmed.startsWith('--') &&
             !trimmed.startsWith('CREATE DATABASE') &&
             !trimmed.startsWith('USE ')
    })

    for (const statement of sqlStatements) {
      if (statement.trim()) {
        await connection.execute(statement.trim())
      }
    }
    console.log('✅ 表结构初始化完成')
    
    // 检查是否已有数据
    const [userRows] = await connection.execute('SELECT COUNT(*) as count FROM users')
    const userCount = userRows[0].count
    
    if (userCount === 0) {
      console.log('📊 导入示例数据...')
      const sampleSQL = fs.readFileSync('sample_data.sql', 'utf8')
      await connection.execute(sampleSQL)
      console.log('✅ 示例数据导入完成')
    } else {
      console.log(`ℹ️  数据库已有 ${userCount} 个用户，跳过示例数据导入`)
    }
    
    // 验证数据
    console.log('\n📊 数据库状态：')
    const tables = ['users', 'vehicles', 'batteries', 'charging_stations', 'charging_sessions']
    for (const table of tables) {
      const [rows] = await connection.execute(`SELECT COUNT(*) as count FROM ${table}`)
      console.log(`  ${table}: ${rows[0].count} 条记录`)
    }
    
    console.log('\n🎉 数据库初始化完成！')
    console.log('🌐 现在可以访问 http://localhost:5173 查看应用')
    
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error.message)
    console.log('\n请检查：')
    console.log('1. MySQL 服务是否启动')
    console.log('2. .env 文件中的数据库配置是否正确')
    console.log('3. 数据库权限是否正确')
  } finally {
    if (connection) {
      await connection.end()
    }
  }
}

// 运行初始化
initDatabase()
