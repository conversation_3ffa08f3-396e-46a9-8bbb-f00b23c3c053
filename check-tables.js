const mysql = require('mysql2/promise');

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'ev_management',
  charset: 'utf8mb4'
};

async function checkTables() {
  let connection;
  
  try {
    console.log('🔌 连接到MySQL数据库...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 检查用户表结构
    console.log('\n📋 检查用户表结构:');
    console.log('==================');
    const [userColumns] = await connection.execute('DESCRIBE users');
    console.table(userColumns);
    
    // 检查车辆表结构
    console.log('\n🚗 检查车辆表结构:');
    console.log('==================');
    const [vehicleColumns] = await connection.execute('DESCRIBE vehicles');
    console.table(vehicleColumns);
    
    // 检查电池表结构
    console.log('\n🔋 检查电池表结构:');
    console.log('==================');
    const [batteryColumns] = await connection.execute('DESCRIBE batteries');
    console.table(batteryColumns);
    
    // 检查充电站表结构
    console.log('\n⚡ 检查充电站表结构:');
    console.log('==================');
    const [stationColumns] = await connection.execute('DESCRIBE charging_stations');
    console.table(stationColumns);
    
    // 检查所有表
    console.log('\n📊 数据库中的所有表:');
    console.log('==================');
    const [tables] = await connection.execute('SHOW TABLES');
    console.table(tables);
    
  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 数据库连接已关闭');
    }
  }
}

// 运行检查脚本
checkTables();