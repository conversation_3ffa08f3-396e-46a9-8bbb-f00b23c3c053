/**
 * 系统功能测试脚本
 * 测试各个API端点和数据库连接
 */

const axios = require('axios')
const mysql = require('mysql2/promise')
require('dotenv').config()

const API_BASE = 'http://localhost:3001/api'
const FRONTEND_URL = 'http://localhost:5173'

// MySQL 连接配置
const mysqlConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME || 'ev_management',
  charset: 'utf8mb4'
}

async function testDatabaseConnection() {
  console.log('🔍 测试数据库连接...')
  try {
    const connection = await mysql.createConnection(mysqlConfig)
    const [rows] = await connection.execute('SELECT COUNT(*) as count FROM users')
    await connection.end()
    console.log(`✅ 数据库连接成功，用户数量: ${rows[0].count}`)
    return true
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message)
    return false
  }
}

async function testAPIEndpoints() {
  console.log('\n🔍 测试API端点...')
  
  const endpoints = [
    { name: '健康检查', url: '/health' },
    { name: '用户列表', url: '/users' },
    { name: '车辆列表', url: '/vehicles' },
    { name: '电池列表', url: '/batteries' },
    { name: '充电站列表', url: '/charging-stations' },
    { name: '边缘设备', url: '/edge-devices' },
    { name: '系统统计', url: '/system/stats' }
  ]
  
  let successCount = 0
  
  for (const endpoint of endpoints) {
    try {
      const response = await axios.get(`${API_BASE}${endpoint.url}`, {
        timeout: 5000
      })
      
      if (response.status === 200) {
        console.log(`✅ ${endpoint.name}: ${response.status}`)
        successCount++
      } else {
        console.log(`⚠️  ${endpoint.name}: ${response.status}`)
      }
    } catch (error) {
      console.log(`❌ ${endpoint.name}: ${error.message}`)
    }
  }
  
  console.log(`\n📊 API测试结果: ${successCount}/${endpoints.length} 个端点正常`)
  return successCount === endpoints.length
}

async function testFrontendAccess() {
  console.log('\n🔍 测试前端访问...')
  try {
    const response = await axios.get(FRONTEND_URL, {
      timeout: 5000
    })
    
    if (response.status === 200) {
      console.log('✅ 前端服务器正常访问')
      return true
    } else {
      console.log(`⚠️  前端服务器状态码: ${response.status}`)
      return false
    }
  } catch (error) {
    console.error('❌ 前端服务器访问失败:', error.message)
    return false
  }
}

async function checkDataIntegrity() {
  console.log('\n🔍 检查数据完整性...')
  try {
    const connection = await mysql.createConnection(mysqlConfig)
    
    const tables = [
      'users', 'vehicles', 'batteries', 'charging_stations', 
      'charging_sessions', 'carbon_credit', 'driving_behavior',
      'fleet_management', 'edge_devices'
    ]
    
    console.log('📊 数据表统计:')
    let totalRecords = 0
    
    for (const table of tables) {
      try {
        const [rows] = await connection.execute(`SELECT COUNT(*) as count FROM ${table}`)
        const count = rows[0].count
        totalRecords += count
        console.log(`  ${table}: ${count} 条记录`)
      } catch (error) {
        console.log(`  ${table}: 表不存在或查询失败`)
      }
    }
    
    await connection.end()
    console.log(`\n📈 总记录数: ${totalRecords}`)
    return totalRecords > 0
  } catch (error) {
    console.error('❌ 数据完整性检查失败:', error.message)
    return false
  }
}

async function runSystemTest() {
  console.log('🚀 开始系统功能测试...\n')
  
  const results = {
    database: await testDatabaseConnection(),
    api: await testAPIEndpoints(),
    frontend: await testFrontendAccess(),
    data: await checkDataIntegrity()
  }
  
  console.log('\n📋 测试结果总结:')
  console.log(`数据库连接: ${results.database ? '✅ 正常' : '❌ 异常'}`)
  console.log(`API服务: ${results.api ? '✅ 正常' : '❌ 异常'}`)
  console.log(`前端服务: ${results.frontend ? '✅ 正常' : '❌ 异常'}`)
  console.log(`数据完整性: ${results.data ? '✅ 正常' : '❌ 异常'}`)
  
  const allPassed = Object.values(results).every(result => result)
  
  if (allPassed) {
    console.log('\n🎉 所有测试通过！系统运行正常')
    console.log('🌐 访问地址: http://localhost:5173')
  } else {
    console.log('\n⚠️  部分测试失败，请检查相关服务')
  }
  
  return allPassed
}

// 运行测试
runSystemTest().catch(console.error)
