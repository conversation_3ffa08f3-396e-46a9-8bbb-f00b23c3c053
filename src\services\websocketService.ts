/**
 * WebSocket 服务 - 实时数据监控增强
 */

export interface WebSocketMessage {
  type: string
  data: any
  timestamp: number
  id?: string
}

export interface WebSocketConfig {
  url: string
  reconnectInterval: number
  maxReconnectAttempts: number
  heartbeatInterval: number
  timeout: number
}

export interface RealtimeData {
  batteryStatus: {
    level: number
    temperature: number
    voltage: number
    current: number
    health: string
    chargingStatus: string
  }
  chargingStations: {
    id: string
    status: 'available' | 'occupied' | 'maintenance' | 'offline'
    power: number
    queue: number
    location: { lat: number; lng: number }
  }[]
  vehicleStatus: {
    speed: number
    location: { lat: number; lng: number }
    direction: number
    engineStatus: string
    fuelLevel: number
    mileage: number
  }
  alerts: {
    id: string
    type: 'warning' | 'error' | 'info'
    message: string
    timestamp: number
    priority: 'low' | 'medium' | 'high' | 'critical'
  }[]
}

export class WebSocketService {
  private static instance: WebSocketService
  private ws: WebSocket | null = null
  private config: WebSocketConfig
  private reconnectAttempts = 0
  private heartbeatTimer: number | null = null
  private reconnectTimer: number | null = null
  private listeners: Map<string, Function[]> = new Map()
  private isConnecting = false
  private connectionPromise: Promise<void> | null = null
  private mockMode = false // 使用真实数据
  private mockDataTimer: number | null = null

  constructor(config: Partial<WebSocketConfig> = {}) {
    this.config = {
      url: 'ws://localhost:8080/ws',
      reconnectInterval: 3000,
      maxReconnectAttempts: 10,
      heartbeatInterval: 30000,
      timeout: 10000,
      ...config
    }
    
    // 检测是否为开发环境
    this.mockMode = import.meta.env.DEV || !navigator.onLine
  }

  static getInstance(config?: Partial<WebSocketConfig>): WebSocketService {
    if (!WebSocketService.instance) {
      WebSocketService.instance = new WebSocketService(config)
    }
    return WebSocketService.instance
  }

  /**
   * 连接WebSocket
   */
  async connect(): Promise<void> {
    if (this.ws?.readyState === WebSocket.OPEN) {
      return Promise.resolve()
    }

    if (this.isConnecting && this.connectionPromise) {
      return this.connectionPromise
    }

    // 启动真实数据获取
    this.startMockData()
    return Promise.resolve()

    this.isConnecting = true
    this.connectionPromise = new Promise((resolve, reject) => {
      try {
        this.ws = new WebSocket(this.config.url)
        
        const timeout = setTimeout(() => {
          this.ws?.close()
          reject(new Error('WebSocket connection timeout'))
        }, this.config.timeout)

        this.ws.onopen = () => {
          clearTimeout(timeout)
          this.isConnecting = false
          this.reconnectAttempts = 0
          this.startHeartbeat()
          this.emit('connected')
          console.log('WebSocket connected')
          resolve()
        }

        this.ws.onmessage = (event) => {
          try {
            const message: WebSocketMessage = JSON.parse(event.data)
            this.handleMessage(message)
          } catch (error) {
            console.error('Failed to parse WebSocket message:', error)
          }
        }

        this.ws.onclose = (event) => {
          clearTimeout(timeout)
          this.isConnecting = false
          this.stopHeartbeat()
          this.emit('disconnected', event)
          console.log('WebSocket disconnected:', event.code, event.reason)
          
          if (!event.wasClean && this.reconnectAttempts < this.config.maxReconnectAttempts) {
            this.scheduleReconnect()
          }
        }

        this.ws.onerror = (error) => {
          clearTimeout(timeout)
          this.isConnecting = false
          this.emit('error', error)
          console.error('WebSocket error:', error)
          reject(error)
        }
      } catch (error) {
        this.isConnecting = false
        reject(error)
      }
    })

    return this.connectionPromise
  }

  /**
   * 断开连接
   */
  disconnect(): void {
    this.stopHeartbeat()
    this.clearReconnectTimer()
    this.stopMockData()
    
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect')
      this.ws = null
    }
    
    this.isConnecting = false
    this.connectionPromise = null
    this.reconnectAttempts = 0
  }

  /**
   * 发送消息
   */
  send(message: Omit<WebSocketMessage, 'timestamp'>): boolean {
    if (this.ws?.readyState === WebSocket.OPEN) {
      const fullMessage: WebSocketMessage = {
        ...message,
        timestamp: Date.now()
      }
      this.ws.send(JSON.stringify(fullMessage))
      return true
    }
    return false
  }

  /**
   * 订阅消息
   */
  on(event: string, callback: Function): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event)!.push(callback)
  }

  /**
   * 取消订阅
   */
  off(event: string, callback?: Function): void {
    if (!this.listeners.has(event)) return
    
    if (callback) {
      const callbacks = this.listeners.get(event)!
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    } else {
      this.listeners.delete(event)
    }
  }

  /**
   * 触发事件
   */
  private emit(event: string, data?: any): void {
    const callbacks = this.listeners.get(event)
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error('Error in WebSocket event callback:', error)
        }
      })
    }
  }

  /**
   * 处理接收到的消息
   */
  private handleMessage(message: WebSocketMessage): void {
    this.emit('message', message)
    this.emit(message.type, message.data)
  }

  /**
   * 开始心跳
   */
  private startHeartbeat(): void {
    this.stopHeartbeat()
    this.heartbeatTimer = window.setInterval(() => {
      this.send({ type: 'ping', data: {} })
    }, this.config.heartbeatInterval)
  }

  /**
   * 停止心跳
   */
  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }

  /**
   * 安排重连
   */
  private scheduleReconnect(): void {
    this.clearReconnectTimer()
    this.reconnectAttempts++
    
    const delay = Math.min(
      this.config.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1),
      30000
    )
    
    console.log(`Scheduling reconnect attempt ${this.reconnectAttempts} in ${delay}ms`)
    
    this.reconnectTimer = window.setTimeout(() => {
      this.connect().catch(error => {
        console.error('Reconnect failed:', error)
      })
    }, delay)
  }

  /**
   * 清除重连定时器
   */
  private clearReconnectTimer(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
  }

  /**
   * 获取连接状态
   */
  getConnectionState(): string {
    // 模拟模式下返回OPEN状态
    if (this.mockMode && this.mockDataTimer) {
      return 'OPEN'
    }
    
    if (!this.ws) return 'CLOSED'
    
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING: return 'CONNECTING'
      case WebSocket.OPEN: return 'OPEN'
      case WebSocket.CLOSING: return 'CLOSING'
      case WebSocket.CLOSED: return 'CLOSED'
      default: return 'UNKNOWN'
    }
  }

  /**
   * 获取连接统计
   */
  getStats() {
    return {
      state: this.getConnectionState(),
      reconnectAttempts: this.reconnectAttempts,
      maxReconnectAttempts: this.config.maxReconnectAttempts,
      listeners: Object.fromEntries(
        Array.from(this.listeners.entries()).map(([key, value]) => [key, value.length])
      )
    }
  }

  /**
   * 开始模拟数据
   */
  private startMockData(): void {
    this.stopMockData()
    
    // 从真实API获取数据
    const fetchRealData = async () => {
      try {
        // 使用现有的边缘设备数据API
        const response = await fetch('/api/edge-devices/data?deviceIds=EV001,EV002,EV003,CS001,CS002&limit=50')
        if (response.ok) {
          const result = await response.json()
          if (result.success && result.data && result.data.length > 0) {
            const data = result.data
            
            // 处理电池数据
            const batteryData = data.filter((item: any) => item.data_type === 'battery_level')
            if (batteryData.length > 0) {
              const latestBattery = batteryData[0]
              const value = JSON.parse(latestBattery.value || '{}')
              this.emit('batteryData', {
                level: value.level || parseInt(latestBattery.text_value) || 85,
                temperature: value.temperature || 25.8,
                voltage: value.voltage || 12.6,
                current: value.current || 2.3,
                health: value.health || 'Good',
                chargingStatus: value.status || 'Normal'
              })
            }
            
            // 处理充电站数据
            const stationData = data.filter((item: any) => item.device_id.startsWith('CS'))
            const stations = []
            const stationIds = [...new Set(stationData.map((item: any) => item.device_id))]
            
            stationIds.forEach((stationId: string) => {
              const stationItems = stationData.filter((item: any) => item.device_id === stationId)
              const utilizationItem = stationItems.find((item: any) => item.data_type === 'utilization')
              const powerItem = stationItems.find((item: any) => item.data_type === 'power_output')
              
              if (utilizationItem || powerItem) {
                const utilizationValue = utilizationItem ? JSON.parse(utilizationItem.value || '{}') : {}
                const powerValue = powerItem ? JSON.parse(powerItem.value || '{}') : {}
                
                stations.push({
                  id: stationId,
                  status: utilizationValue.rate > 80 ? 'occupied' : 'available',
                  power: powerValue.power || parseFloat(powerItem?.text_value) || 50,
                  queue: utilizationValue.queue || 0,
                  location: { 
                    lat: stationId === 'CS001' ? 22.5431 : 30.2741, 
                    lng: stationId === 'CS001' ? 114.0579 : 120.1551 
                  }
                })
              }
            })
            
            if (stations.length > 0) {
              this.emit('chargingStations', stations)
            }
            
            // 处理车辆状态
            const vehicleData = data.filter((item: any) => item.device_id.startsWith('EV'))
            if (vehicleData.length > 0) {
              const speedItem = vehicleData.find((item: any) => item.data_type === 'speed')
              const locationItem = vehicleData.find((item: any) => item.data_type === 'location')
              const batteryItem = vehicleData.find((item: any) => item.data_type === 'battery_level')
              
              const speedValue = speedItem ? JSON.parse(speedItem.value || '{}') : {}
              const locationValue = locationItem ? JSON.parse(locationItem.value || '{}') : {}
              const batteryValue = batteryItem ? JSON.parse(batteryItem.value || '{}') : {}
              
              this.emit('vehicleStatus', {
                speed: speedValue.speed || parseFloat(speedItem?.text_value) || 0,
                location: { 
                  lat: locationValue.lat || 39.9042, 
                  lng: locationValue.lng || 116.4074 
                },
                direction: speedValue.direction || 0,
                engineStatus: batteryValue.level > 20 ? 'running' : 'idle',
                fuelLevel: batteryValue.level || parseInt(batteryItem?.text_value) || 85,
                mileage: speedValue.mileage || 12580
              })
            }
            
            // 生成基于真实数据的告警
            const alerts = []
            
            // 检查电池电量低告警
            const lowBatteryItems = data.filter((item: any) => {
              if (item.data_type === 'battery_level') {
                const value = JSON.parse(item.value || '{}')
                const level = value.level || parseInt(item.text_value) || 100
                return level < 30
              }
              return false
            })
            
            lowBatteryItems.forEach((item: any) => {
              const value = JSON.parse(item.value || '{}')
              const level = value.level || parseInt(item.text_value) || 0
              alerts.push({
                id: `battery-low-${item.device_id}`,
                type: level < 15 ? 'error' : 'warning',
                message: `车辆 ${item.device_id} 电池电量低 (${level}%)`,
                timestamp: new Date(item.timestamp).getTime(),
                priority: level < 15 ? 'high' : 'medium'
              })
            })
            
            // 检查充电站高利用率告警
            const highUtilizationItems = data.filter((item: any) => {
              if (item.data_type === 'utilization') {
                const value = JSON.parse(item.value || '{}')
                return value.rate > 90
              }
              return false
            })
            
            highUtilizationItems.forEach((item: any) => {
              const value = JSON.parse(item.value || '{}')
              alerts.push({
                id: `utilization-high-${item.device_id}`,
                type: 'warning',
                message: `充电站 ${item.device_id} 利用率过高 (${value.rate}%)`,
                timestamp: new Date(item.timestamp).getTime(),
                priority: 'medium'
              })
            })
            
            // 如果没有告警，添加一个正常状态信息
            if (alerts.length === 0) {
              alerts.push({
                id: `status-${Date.now()}`,
                type: 'info',
                message: '系统运行正常，所有设备状态良好',
                timestamp: Date.now(),
                priority: 'low'
              })
            }
            
            // 发送告警
            alerts.forEach(alert => this.emit('alert', alert))
            
          } else {
            // 如果没有数据，发送模拟数据
            this.sendMockData()
          }
        } else {
          console.warn('边缘设备API响应失败，使用模拟数据')
          this.sendMockData()
        }
      } catch (error) {
        console.error('获取实时数据失败:', error)
        this.sendMockData()
      }
    }
    
    // 立即获取一次数据
    fetchRealData()
    
    // 定期获取真实数据
    this.mockDataTimer = window.setInterval(fetchRealData, 8000) // 每8秒更新一次
  }
  
  /**
   * 发送模拟数据
   */
  private sendMockData(): void {
    // 发送模拟电池数据
    this.emit('batteryData', {
      level: 75 + Math.random() * 20,
      temperature: 20 + Math.random() * 15,
      voltage: 12 + Math.random() * 2,
      current: Math.random() * 5,
      health: 'Good',
      chargingStatus: Math.random() > 0.5 ? 'Charging' : 'Normal'
    })
    
    // 发送模拟充电站数据
    this.emit('chargingStations', [
      {
        id: 'CS001',
        status: Math.random() > 0.3 ? 'available' : 'occupied',
        power: 50 + Math.random() * 100,
        queue: Math.floor(Math.random() * 3),
        location: { lat: 22.5431, lng: 114.0579 }
      },
      {
        id: 'CS002',
        status: Math.random() > 0.4 ? 'available' : 'occupied',
        power: 30 + Math.random() * 80,
        queue: Math.floor(Math.random() * 2),
        location: { lat: 30.2741, lng: 120.1551 }
      }
    ])
    
    // 发送模拟车辆数据
    this.emit('vehicleStatus', {
      speed: Math.random() * 80,
      location: { lat: 39.9042 + (Math.random() - 0.5) * 0.1, lng: 116.4074 + (Math.random() - 0.5) * 0.1 },
      direction: Math.random() * 360,
      engineStatus: 'running',
      fuelLevel: 60 + Math.random() * 30,
      mileage: 12000 + Math.random() * 5000
    })
    
    // 发送模拟告警
    if (Math.random() < 0.1) {
      this.emit('alert', {
        id: `mock-alert-${Date.now()}`,
        type: 'info',
        message: '系统状态正常',
        timestamp: Date.now(),
        priority: 'low'
      })
    }
  }
  
  /**
   * 停止模拟数据
   */
  private stopMockData(): void {
    if (this.mockDataTimer) {
      clearInterval(this.mockDataTimer)
      this.mockDataTimer = null
    }
  }
}

/**
 * 实时数据管理器
 */
export class RealtimeDataManager {
  private static instance: RealtimeDataManager
  public wsService: WebSocketService
  private dataCache: Map<string, any> = new Map()
  private subscribers: Map<string, Function[]> = new Map()
  private alertQueue: RealtimeData['alerts'] = []
  private maxAlerts = 100

  constructor() {
    this.wsService = WebSocketService.getInstance()
    this.setupEventListeners()
  }

  static getInstance(): RealtimeDataManager {
    if (!RealtimeDataManager.instance) {
      RealtimeDataManager.instance = new RealtimeDataManager()
    }
    return RealtimeDataManager.instance
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    this.wsService.on('batteryData', (data: RealtimeData['batteryStatus']) => {
      this.updateData('batteryStatus', data)
    })

    this.wsService.on('chargingStations', (data: RealtimeData['chargingStations']) => {
      this.updateData('chargingStations', data)
    })

    this.wsService.on('vehicleStatus', (data: RealtimeData['vehicleStatus']) => {
      this.updateData('vehicleStatus', data)
    })

    this.wsService.on('alert', (alert: RealtimeData['alerts'][0]) => {
      this.addAlert(alert)
    })
  }

  /**
   * 更新数据
   */
  private updateData(key: string, data: any): void {
    this.dataCache.set(key, data)
    this.notifySubscribers(key, data)
  }

  /**
   * 添加告警
   */
  private addAlert(alert: RealtimeData['alerts'][0]): void {
    this.alertQueue.unshift(alert)
    
    // 限制告警队列长度
    if (this.alertQueue.length > this.maxAlerts) {
      this.alertQueue = this.alertQueue.slice(0, this.maxAlerts)
    }
    
    this.notifySubscribers('alerts', this.alertQueue)
    
    // 触发告警通知
    if (alert.priority === 'critical' || alert.priority === 'high') {
      this.triggerNotification(alert)
    }
  }

  /**
   * 触发系统通知
   */
  private triggerNotification(alert: RealtimeData['alerts'][0]): void {
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(`${alert.type.toUpperCase()}: ${alert.message}`, {
        icon: '/favicon.ico',
        badge: '/favicon.ico',
        tag: alert.id
      })
    }
  }

  /**
   * 订阅数据更新
   */
  subscribe(key: string, callback: Function): void {
    if (!this.subscribers.has(key)) {
      this.subscribers.set(key, [])
    }
    this.subscribers.get(key)!.push(callback)
    
    // 立即发送当前数据
    const currentData = this.dataCache.get(key)
    if (currentData !== undefined) {
      callback(currentData)
    }
  }

  /**
   * 取消订阅
   */
  unsubscribe(key: string, callback?: Function): void {
    if (!this.subscribers.has(key)) return
    
    if (callback) {
      const callbacks = this.subscribers.get(key)!
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    } else {
      this.subscribers.delete(key)
    }
  }

  /**
   * 通知订阅者
   */
  private notifySubscribers(key: string, data: any): void {
    const callbacks = this.subscribers.get(key)
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error('Error in data subscriber callback:', error)
        }
      })
    }
  }

  /**
   * 获取数据
   */
  getData(key: string): any {
    return this.dataCache.get(key)
  }

  /**
   * 获取所有告警
   */
  getAlerts(): RealtimeData['alerts'] {
    return [...this.alertQueue]
  }

  /**
   * 清除告警
   */
  clearAlerts(): void {
    this.alertQueue = []
    this.notifySubscribers('alerts', this.alertQueue)
  }

  /**
   * 请求通知权限
   */
  async requestNotificationPermission(): Promise<boolean> {
    if (!('Notification' in window)) {
      return false
    }
    
    if (Notification.permission === 'granted') {
      return true
    }
    
    if (Notification.permission === 'denied') {
      return false
    }
    
    const permission = await Notification.requestPermission()
    return permission === 'granted'
  }

  /**
   * 连接WebSocket
   */
  async connect(): Promise<void> {
    return this.wsService.connect()
  }

  /**
   * 断开连接
   */
  disconnect(): void {
    this.wsService.disconnect()
  }

  /**
   * 获取连接状态
   */
  getConnectionState(): string {
    return this.wsService.getConnectionState()
  }
}

// 导出默认实例
export const realtimeDataManager = RealtimeDataManager.getInstance()
export const websocketService = WebSocketService.getInstance()