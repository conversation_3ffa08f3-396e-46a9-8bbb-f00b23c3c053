import { Router } from 'express'
import type { ApiResponse } from '../../shared/types'

const router = Router()

// 获取最近的告警数据
router.get('/recent', async (req, res) => {
  try {
    const { limit = 10 } = req.query
    
    // 模拟告警数据
    const alertTypes = ['warning', 'error', 'info']
    const alertMessages = [
      '车辆电池温度过高',
      '充电站离线',
      '系统负载过高',
      '网络连接异常',
      '车辆定位信号丢失',
      '充电功率异常',
      '电池健康度下降',
      '充电桩故障',
      '车辆超速预警',
      '系统内存不足'
    ]
    const priorities = ['low', 'medium', 'high', 'critical']
    
    const alerts = Array.from({ length: Number(limit) }, (_, index) => {
      const type = alertTypes[Math.floor(Math.random() * alertTypes.length)]
      const message = alertMessages[Math.floor(Math.random() * alertMessages.length)]
      const priority = priorities[Math.floor(Math.random() * priorities.length)]
      
      return {
        id: `alert-${Date.now()}-${index}`,
        type,
        message,
        timestamp: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toISOString(),
        priority,
        level: type === 'error' ? 'error' : type === 'warning' ? 'warning' : 'info'
      }
    })
    
    // 按时间倒序排列
    alerts.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())

    res.json({
      success: true,
      data: alerts
    } as ApiResponse)
  } catch (error) {
    console.error('获取告警数据异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取告警统计数据
router.get('/stats', async (req, res) => {
  try {
    // 模拟告警统计数据
    const stats = {
      total: Math.floor(Math.random() * 100) + 50,
      critical: Math.floor(Math.random() * 5) + 1,
      high: Math.floor(Math.random() * 10) + 5,
      medium: Math.floor(Math.random() * 20) + 10,
      low: Math.floor(Math.random() * 30) + 15,
      resolved: Math.floor(Math.random() * 80) + 40,
      pending: Math.floor(Math.random() * 20) + 10
    }

    res.json({
      success: true,
      data: stats
    } as ApiResponse)
  } catch (error) {
    console.error('获取告警统计异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

export default router