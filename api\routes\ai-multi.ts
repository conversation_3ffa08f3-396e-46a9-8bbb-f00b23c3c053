import express from 'express';
import type { Request, Response } from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { db } from '../config/mysql';
import { openaiService } from '../services/openai';
import { geminiService } from '../services/gemini';
import { simpleAIService } from '../services/ai-simple';

const router = express.Router();

// 配置文件上传
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(process.cwd(), 'api/uploads');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ 
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = [
      'text/plain',
      'text/csv',
      'application/json',
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp'
    ];
    
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('不支持的文件类型'));
    }
  }
});

// 选择可用的AI服务
function selectAIService() {
  // 优先级：OpenAI > Gemini > 本地服务
  if (openaiService.isAvailable()) {
    return { service: openaiService, name: 'OpenAI GPT-4o' };
  } else if (geminiService.isAvailable()) {
    return { service: geminiService, name: 'Google Gemini Pro' };
  } else {
    return { service: simpleAIService, name: '本地知识库' };
  }
}

// AI聊天接口 - 多服务支持
router.post('/chat', async (req: Request, res: Response) => {
  try {
    const { message, context = [] } = req.body;
    
    if (!message || typeof message !== 'string') {
      return res.status(400).json({
        success: false,
        message: '消息内容不能为空'
      });
    }

    console.log('收到聊天请求:', message);

    // 记录用户问题
    try {
      await db.query(
        'INSERT INTO ai_chat_logs (user_id, question, timestamp) VALUES (?, ?, NOW())',
        [(req as any).user?.id || 'anonymous', message]
      );
    } catch (error) {
      console.log('记录聊天日志失败:', error);
    }

    // 选择AI服务
    const { service, name: serviceName } = selectAIService();
    console.log(`使用AI服务: ${serviceName}`);

    let response: string;
    
    try {
      // 尝试使用选定的AI服务
      response = await service.chat(message, context);
    } catch (error) {
      console.error(`${serviceName}服务失败:`, error);
      
      // 如果不是本地服务，则降级到本地服务
      if (service !== simpleAIService) {
        console.log('降级到本地知识库服务');
        response = await simpleAIService.chat(message, context);
        const fallbackServiceName = '本地知识库（降级）';

        res.json({
          success: true,
          response: response,
          timestamp: new Date().toISOString(),
          model: fallbackServiceName,
          service: fallbackServiceName
        });
        return;
      } else {
        throw error;
      }
    }
    
    console.log('AI回复生成成功');
    
    res.json({
      success: true,
      response: response,
      timestamp: new Date().toISOString(),
      model: serviceName,
      service: serviceName
    });
    
  } catch (error) {
    console.error('AI聊天错误:', error);
    res.status(500).json({
      success: false,
      message: 'AI服务暂时不可用，请稍后再试'
    });
  }
});

// 文件上传和分析接口 - 多服务支持
router.post('/analyze-file', upload.single('file'), async (req: Request, res: Response) => {
  try {
    const { question = '请分析这个文件的内容' } = req.body;
    const file = req.file;

    console.log('收到文件分析请求:', file?.originalname, file?.mimetype);

    if (!file) {
      return res.status(400).json({
        success: false,
        message: '请上传文件'
      });
    }

    // 选择AI服务
    const { service, name: serviceName } = selectAIService();
    console.log(`使用AI服务进行文件分析: ${serviceName}`);

    let analysisResult: string;

    try {
      // 尝试使用选定的AI服务
      analysisResult = await service.analyzeFile(file.path, file.originalname, file.mimetype, question);
    } catch (error) {
      console.error(`${serviceName}文件分析失败:`, error);
      
      // 如果不是本地服务，则降级到本地服务
      if (service !== simpleAIService) {
        console.log('降级到本地文件分析服务');
        analysisResult = await simpleAIService.analyzeFile(file.path, file.originalname, file.mimetype, question);
      } else {
        throw error;
      }
    }

    console.log('文件分析完成');

    // 记录文件分析日志
    try {
      await db.query(
        'INSERT INTO ai_chat_logs (user_id, question, file_name, file_type, timestamp) VALUES (?, ?, ?, ?, NOW())',
        [(req as any).user?.id || 'anonymous', question, file.originalname, file.mimetype]
      );
    } catch (error) {
      console.log('记录文件分析日志失败:', error);
    }

    // 清理上传的文件
    fs.unlinkSync(file.path);

    res.json({
      success: true,
      response: analysisResult,
      fileName: file.originalname,
      fileType: file.mimetype,
      timestamp: new Date().toISOString(),
      service: serviceName
    });

  } catch (error) {
    console.error('文件分析错误:', error);
    
    // 清理上传的文件
    if (req.file) {
      try {
        fs.unlinkSync(req.file.path);
      } catch (cleanupError) {
        console.error('清理文件失败:', cleanupError);
      }
    }

    res.status(500).json({
      success: false,
      message: '文件分析失败，请稍后再试'
    });
  }
});

// AI服务状态检查
router.get('/status', async (req: Request, res: Response) => {
  try {
    // 检查各个AI服务状态
    const openaiStatus = openaiService.isAvailable() ? await openaiService.checkStatus() : false;
    const geminiStatus = geminiService.isAvailable() ? await geminiService.checkStatus() : false;
    const localStatus = await simpleAIService.checkStatus();
    
    const { service, name: currentService } = selectAIService();
    
    res.json({
      success: true,
      status: 'online',
      message: `AI助手服务正常运行（${currentService}）`,
      timestamp: new Date().toISOString(),
      services: {
        openai: {
          available: openaiStatus,
          name: 'OpenAI GPT-4o'
        },
        gemini: {
          available: geminiStatus,
          name: 'Google Gemini Pro'
        },
        local: {
          available: localStatus,
          name: '本地知识库'
        }
      },
      currentService: currentService,
      capabilities: [
        '智能对话',
        '文件分析',
        '图片识别',
        '系统咨询',
        '操作指导',
        '问题解答'
      ]
    });
    
  } catch (error) {
    console.error('AI状态检查错误:', error);
    res.status(500).json({
      success: false,
      status: 'offline',
      message: 'AI助手服务暂时不可用'
    });
  }
});

// 获取AI助手信息
router.get('/info', async (req: Request, res: Response) => {
  try {
    const { service } = selectAIService();
    const aiConfig = await service.getAIConfig();
    
    res.json({
      success: true,
      data: {
        ...aiConfig,
        version: '3.0.0-multi-ai'
      }
    });
  } catch (error) {
    console.error('获取AI信息错误:', error);
    res.status(500).json({
      success: false,
      message: '获取AI信息失败'
    });
  }
});

export default router;
