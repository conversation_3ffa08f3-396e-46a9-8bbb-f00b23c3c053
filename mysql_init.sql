-- 新能源汽车智能综合管理系统 MySQL数据库初始化脚本
-- 创建数据库
CREATE DATABASE IF NOT EXISTS ev_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE ev_management;

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20),
    name VARCHAR(100) NOT NULL,
    avatar_url TEXT,
    user_type ENUM('individual', 'enterprise', 'fleet') NOT NULL DEFAULT 'individual',
    status ENUM('active', 'inactive', 'suspended') NOT NULL DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_user_type (user_type),
    INDEX idx_status (status)
);

-- 车辆表
CREATE TABLE IF NOT EXISTS vehicles (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    vin VARCHAR(17) UNIQUE NOT NULL,
    license_plate VARCHAR(20),
    brand VARCHAR(50) NOT NULL,
    model VARCHAR(100) NOT NULL,
    year INT,
    color VARCHAR(30),
    vehicle_type ENUM('bev', 'phev', 'fcev') NOT NULL DEFAULT 'bev',
    battery_capacity DECIMAL(8,2),
    max_range INT,
    status ENUM('active', 'maintenance', 'retired') NOT NULL DEFAULT 'active',
    location JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_vin (vin),
    INDEX idx_vehicle_type (vehicle_type),
    INDEX idx_status (status)
);

-- 电池表
CREATE TABLE IF NOT EXISTS batteries (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    vehicle_id VARCHAR(36) NOT NULL,
    battery_id VARCHAR(50) NOT NULL,
    manufacturer VARCHAR(100),
    model VARCHAR(100),
    capacity DECIMAL(8,2) NOT NULL,
    voltage DECIMAL(8,2),
    chemistry VARCHAR(50),
    manufacture_date DATE,
    warranty_end_date DATE,
    current_soh DECIMAL(5,2) NOT NULL DEFAULT 100.00,
    current_soc DECIMAL(5,2),
    temperature DECIMAL(5,2),
    cycle_count INT DEFAULT 0,
    status ENUM('normal', 'warning', 'critical', 'maintenance') NOT NULL DEFAULT 'normal',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (vehicle_id) REFERENCES vehicles(id) ON DELETE CASCADE,
    INDEX idx_vehicle_id (vehicle_id),
    INDEX idx_battery_id (battery_id),
    INDEX idx_status (status)
);

-- 电池追踪表
CREATE TABLE IF NOT EXISTS battery_trace (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    battery_id VARCHAR(36) NOT NULL,
    soc DECIMAL(5,2),
    soh DECIMAL(5,2),
    voltage DECIMAL(8,2),
    current DECIMAL(8,2),
    temperature DECIMAL(5,2),
    power DECIMAL(8,2),
    energy DECIMAL(8,2),
    cycle_count INT,
    location JSON,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (battery_id) REFERENCES batteries(id) ON DELETE CASCADE,
    INDEX idx_battery_id (battery_id),
    INDEX idx_timestamp (timestamp)
);

-- 充电站表
CREATE TABLE IF NOT EXISTS charging_stations (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    station_code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(200) NOT NULL,
    operator VARCHAR(100),
    address TEXT NOT NULL,
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    total_ports INT NOT NULL DEFAULT 1,
    available_ports INT NOT NULL DEFAULT 1,
    power_type ENUM('ac', 'dc', 'mixed') DEFAULT 'ac',
    max_power DECIMAL(8,2),
    pricing JSON,
    amenities JSON,
    operating_hours JSON,
    status ENUM('active', 'maintenance', 'offline') NOT NULL DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_station_code (station_code),
    INDEX idx_location (latitude, longitude),
    INDEX idx_status (status)
);

-- 充电会话表
CREATE TABLE IF NOT EXISTS charging_sessions (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    vehicle_id VARCHAR(36) NOT NULL,
    station_id VARCHAR(36) NOT NULL,
    session_code VARCHAR(50) UNIQUE NOT NULL,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    start_soc DECIMAL(5,2),
    end_soc DECIMAL(5,2),
    energy_delivered DECIMAL(8,2),
    peak_power DECIMAL(8,2),
    total_cost DECIMAL(10,2),
    payment_method VARCHAR(50),
    payment_status ENUM('pending', 'completed', 'failed', 'refunded') NOT NULL DEFAULT 'pending',
    session_status ENUM('active', 'completed', 'interrupted', 'failed') NOT NULL DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (vehicle_id) REFERENCES vehicles(id) ON DELETE CASCADE,
    FOREIGN KEY (station_id) REFERENCES charging_stations(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_vehicle_id (vehicle_id),
    INDEX idx_station_id (station_id),
    INDEX idx_session_code (session_code),
    INDEX idx_start_time (start_time)
);

-- 碳积分表
CREATE TABLE IF NOT EXISTS carbon_credit (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    vehicle_id VARCHAR(36),
    credit_type ENUM('driving', 'charging', 'sharing', 'maintenance') NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    description TEXT,
    reference_id VARCHAR(36),
    status ENUM('pending', 'approved', 'rejected') NOT NULL DEFAULT 'pending',
    earned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (vehicle_id) REFERENCES vehicles(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_vehicle_id (vehicle_id),
    INDEX idx_credit_type (credit_type),
    INDEX idx_status (status)
);

-- 驾驶行为表
CREATE TABLE IF NOT EXISTS driving_behavior (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    vehicle_id VARCHAR(36) NOT NULL,
    trip_id VARCHAR(36),
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    distance DECIMAL(8,2),
    duration INT,
    avg_speed DECIMAL(5,2),
    max_speed DECIMAL(5,2),
    acceleration_score DECIMAL(3,1),
    braking_score DECIMAL(3,1),
    cornering_score DECIMAL(3,1),
    eco_score DECIMAL(3,1),
    safety_score DECIMAL(3,1),
    energy_efficiency DECIMAL(5,2),
    route_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (vehicle_id) REFERENCES vehicles(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_vehicle_id (vehicle_id),
    INDEX idx_trip_id (trip_id),
    INDEX idx_start_time (start_time)
);

-- 车队管理表
CREATE TABLE IF NOT EXISTS fleet_management (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    fleet_name VARCHAR(200) NOT NULL,
    fleet_code VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    manager_name VARCHAR(100),
    manager_phone VARCHAR(20),
    manager_email VARCHAR(255),
    total_vehicles INT DEFAULT 0,
    active_vehicles INT DEFAULT 0,
    status ENUM('active', 'inactive', 'suspended') NOT NULL DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_fleet_code (fleet_code),
    INDEX idx_status (status)
);

-- 车队车辆关联表
CREATE TABLE IF NOT EXISTS fleet_vehicles (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    fleet_id VARCHAR(36) NOT NULL,
    vehicle_id VARCHAR(36) NOT NULL,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('active', 'inactive') NOT NULL DEFAULT 'active',
    FOREIGN KEY (fleet_id) REFERENCES fleet_management(id) ON DELETE CASCADE,
    FOREIGN KEY (vehicle_id) REFERENCES vehicles(id) ON DELETE CASCADE,
    UNIQUE KEY unique_fleet_vehicle (fleet_id, vehicle_id),
    INDEX idx_fleet_id (fleet_id),
    INDEX idx_vehicle_id (vehicle_id)
);

-- 边缘设备表
CREATE TABLE IF NOT EXISTS edge_devices (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    name VARCHAR(200) NOT NULL,
    type ENUM('sensor', 'controller', 'gateway', 'camera') NOT NULL,
    status ENUM('online', 'offline', 'error') NOT NULL DEFAULT 'offline',
    location JSON,
    capabilities JSON,
    last_heartbeat TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_last_heartbeat (last_heartbeat)
);

-- 边缘设备数据表
CREATE TABLE IF NOT EXISTS edge_device_data (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    device_id VARCHAR(36) NOT NULL,
    data_type VARCHAR(100) NOT NULL,
    value DECIMAL(15,6),
    text_value TEXT,
    json_value JSON,
    unit VARCHAR(20),
    quality DECIMAL(5,2) DEFAULT 100.00,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (device_id) REFERENCES edge_devices(id) ON DELETE CASCADE,
    INDEX idx_device_id (device_id),
    INDEX idx_data_type (data_type),
    INDEX idx_timestamp (timestamp)
);

-- 边缘任务表
CREATE TABLE IF NOT EXISTS edge_tasks (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    name VARCHAR(200) NOT NULL,
    type ENUM('data_processing', 'ai_inference', 'real_time_analysis', 'alert_detection') NOT NULL,
    priority ENUM('low', 'medium', 'high', 'critical') NOT NULL DEFAULT 'medium',
    status ENUM('pending', 'running', 'completed', 'failed') NOT NULL DEFAULT 'pending',
    device_id VARCHAR(36) NOT NULL,
    config JSON,
    result JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    FOREIGN KEY (device_id) REFERENCES edge_devices(id) ON DELETE CASCADE,
    INDEX idx_device_id (device_id),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_priority (priority)
);

-- 插入示例数据

-- 插入示例用户
INSERT INTO users (id, email, name, user_type) VALUES 
('user-001', '<EMAIL>', '系统管理员', 'enterprise'),
('user-002', '<EMAIL>', '车队管理员', 'fleet'),
('user-003', '<EMAIL>', '个人用户', 'individual');

-- 插入示例车辆
INSERT INTO vehicles (id, user_id, vin, license_plate, brand, model, year, vehicle_type, battery_capacity, max_range) VALUES 
('vehicle-001', 'user-001', 'WVWZZZ1JZXW123456', '京A12345', '特斯拉', 'Model 3', 2023, 'bev', 75.0, 500),
('vehicle-002', 'user-002', 'WVWZZZ1JZXW123457', '京B67890', '比亚迪', '汉EV', 2023, 'bev', 85.4, 605),
('vehicle-003', 'user-003', 'WVWZZZ1JZXW123458', '京C11111', '蔚来', 'ES6', 2023, 'bev', 100.0, 610);

-- 插入示例电池
INSERT INTO batteries (id, vehicle_id, battery_id, manufacturer, capacity, current_soh, current_soc) VALUES 
('battery-001', 'vehicle-001', 'BAT001', '宁德时代', 75.0, 98.5, 85.2),
('battery-002', 'vehicle-002', 'BAT002', '比亚迪', 85.4, 99.1, 72.8),
('battery-003', 'vehicle-003', 'BAT003', '宁德时代', 100.0, 97.8, 90.5);

-- 插入示例充电站
INSERT INTO charging_stations (id, station_code, name, address, latitude, longitude, total_ports, available_ports, power_type, max_power) VALUES 
('station-001', 'CS001', '北京国贸充电站', '北京市朝阳区国贸中心', 39.9075, 116.4574, 8, 6, 'dc', 120.0),
('station-002', 'CS002', '上海陆家嘴充电站', '上海市浦东新区陆家嘴金融区', 31.2397, 121.4999, 12, 10, 'mixed', 180.0),
('station-003', 'CS003', '深圳科技园充电站', '深圳市南山区科技园', 22.5431, 113.9344, 6, 4, 'dc', 150.0);

-- 插入示例边缘设备
INSERT INTO edge_devices (id, name, type, status, location, capabilities, last_heartbeat) VALUES 
('edge-001', '温度传感器-01', 'sensor', 'online', JSON_OBJECT('lat', 39.9075, 'lng', 116.4574, 'address', '北京国贸'), JSON_ARRAY('temperature', 'humidity'), NOW()),
('edge-002', '智能控制器-01', 'controller', 'online', JSON_OBJECT('lat', 31.2397, 'lng', 121.4999, 'address', '上海陆家嘴'), JSON_ARRAY('power_control', 'load_balancing'), NOW()),
('edge-003', '网关设备-01', 'gateway', 'online', JSON_OBJECT('lat', 22.5431, 'lng', 113.9344, 'address', '深圳科技园'), JSON_ARRAY('data_aggregation', 'protocol_conversion'), NOW());

-- 插入示例边缘设备数据
INSERT INTO edge_device_data (device_id, data_type, value, unit) VALUES 
('edge-001', 'temperature', 25.6, '°C'),
('edge-001', 'humidity', 65.2, '%'),
('edge-002', 'power_consumption', 1250.5, 'kW'),
('edge-002', 'load_factor', 0.85, ''),
('edge-003', 'data_throughput', 1024.0, 'MB/s'),
('edge-003', 'connection_count', 156, '');

-- 创建视图

-- 车辆详细信息视图
CREATE VIEW vehicle_details AS
SELECT 
    v.*,
    u.name as owner_name,
    u.email as owner_email,
    b.current_soh,
    b.current_soc,
    b.status as battery_status
FROM vehicles v
LEFT JOIN users u ON v.user_id = u.id
LEFT JOIN batteries b ON v.id = b.vehicle_id;

-- 充电会话统计视图
CREATE VIEW charging_session_stats AS
SELECT 
    DATE(start_time) as date,
    COUNT(*) as total_sessions,
    SUM(energy_delivered) as total_energy,
    AVG(energy_delivered) as avg_energy,
    SUM(total_cost) as total_revenue
FROM charging_sessions
WHERE session_status = 'completed'
GROUP BY DATE(start_time);

-- 边缘设备状态视图
CREATE VIEW edge_device_status AS
SELECT 
    ed.*,
    COUNT(edd.id) as data_points_today,
    MAX(edd.timestamp) as last_data_time
FROM edge_devices ed
LEFT JOIN edge_device_data edd ON ed.id = edd.device_id 
    AND DATE(edd.timestamp) = CURDATE()
GROUP BY ed.id;

COMMIT;

SELECT 'MySQL数据库初始化完成！' as message;