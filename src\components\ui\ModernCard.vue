<template>
  <div 
    ref="cardRef"
    :class="[
      'modern-card',
      `modern-card--${variant}`,
      {
        'modern-card--hoverable': hoverable,
        'modern-card--clickable': clickable,
        'modern-card--loading': loading
      },
      cardClasses
    ]"
    :style="cardStyles"
    @click="handleClick"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <!-- 背景装饰 -->
    <div class="modern-card__bg-decoration"></div>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="modern-card__loading">
      <ModernLoader type="quantum" />
    </div>
    
    <!-- 卡片内容 -->
    <div v-else class="modern-card__content">
      <!-- 头部 -->
      <div v-if="$slots.header" class="modern-card__header">
        <slot name="header"></slot>
      </div>
      
      <!-- 主体内容 -->
      <div class="modern-card__body">
        <slot></slot>
      </div>
      
      <!-- 底部 -->
      <div v-if="$slots.footer" class="modern-card__footer">
        <slot name="footer"></slot>
      </div>
    </div>
    
    <!-- 悬浮效果 -->
    <div class="modern-card__glow"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import ModernLoader from './ModernLoader.vue'
import { useBreakpoint, useResponsiveValue, useDeviceFeatures } from '@/composables/useResponsive'
import type { ResponsiveValue } from '@/utils/responsive'

interface Props {
  variant?: 'glass' | 'gradient' | 'neon' | 'minimal' | 'holographic'
  hoverable?: boolean
  clickable?: boolean
  loading?: boolean
  padding?: ResponsiveValue<string>
  borderRadius?: ResponsiveValue<string>
  elevation?: ResponsiveValue<number>
  fullWidth?: boolean
  customStyles?: Record<string, any>
}

interface Emits {
  click: [event: MouseEvent]
  mouseenter: [event: MouseEvent]
  mouseleave: [event: MouseEvent]
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'glass',
  hoverable: true,
  clickable: false,
  loading: false,
  padding: '24px',
  borderRadius: '20px',
  elevation: 1,
  fullWidth: false
})

const emit = defineEmits<Emits>()

const cardRef = ref<HTMLElement>()
const isHovered = ref(false)

// 响应式功能
const { currentBreakpoint, isMobileDevice, isTabletDevice } = useBreakpoint()
const { reducedMotion, isTouch } = useDeviceFeatures()

// 响应式值计算
const responsivePadding = useResponsiveValue(props.padding)
const responsiveBorderRadius = useResponsiveValue(props.borderRadius)
const responsiveElevation = useResponsiveValue(props.elevation)

// 计算样式
const cardStyles = computed(() => ({
  padding: responsivePadding,
  borderRadius: responsiveBorderRadius,
  '--elevation': responsiveElevation,
  width: props.fullWidth ? '100%' : 'auto',
  ...props.customStyles
}))

// 计算类名
const cardClasses = computed(() => ({
  'modern-card--mobile': isMobileDevice,
  'modern-card--tablet': isTabletDevice,
  'modern-card--touch': isTouch,
  'modern-card--reduced-motion': reducedMotion,
  'modern-card--full-width': props.fullWidth
}))

const handleClick = (event: MouseEvent) => {
  if (props.clickable && !props.loading) {
    emit('click', event)
  }
}

const handleMouseEnter = (event: MouseEvent) => {
  isHovered.value = true
  emit('mouseenter', event)
}

const handleMouseLeave = (event: MouseEvent) => {
  isHovered.value = false
  emit('mouseleave', event)
}
</script>

<style scoped>
.modern-card {
  position: relative;
  border-radius: var(--card-border-radius, 20px);
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transform-style: preserve-3d;
  will-change: transform;
  
  /* CSS变量 */
  --card-padding: 24px;
  --card-border-radius: 20px;
  --card-elevation: 1;
}

.modern-card--hoverable:hover {
  transform: translateY(-8px) scale(1.02);
}

.modern-card--clickable {
  cursor: pointer;
}

.modern-card--clickable:active {
  transform: translateY(-4px) scale(0.98);
}

.modern-card--loading {
  pointer-events: none;
}

/* 背景装饰 */
.modern-card__bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.modern-card:hover .modern-card__bg-decoration {
  opacity: 1;
}

/* 玻璃态变体 */
.modern-card--glass {
  background: rgba(255, 255, 255, 0.1);
  box-shadow: 
    0 8px 32px 0 rgba(31, 38, 135, 0.37),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.2);
}

.modern-card--glass:hover {
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 
    0 16px 64px 0 rgba(31, 38, 135, 0.5),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.3);
}

.modern-card--glass .modern-card__bg-decoration {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.1) 0%, 
    rgba(255, 255, 255, 0.05) 100%);
}

/* 渐变变体 */
.modern-card--gradient {
  background: linear-gradient(135deg, 
    rgba(102, 126, 234, 0.1) 0%, 
    rgba(118, 75, 162, 0.1) 100%);
  box-shadow: 0 8px 32px 0 rgba(102, 126, 234, 0.3);
}

.modern-card--gradient:hover {
  background: linear-gradient(135deg, 
    rgba(102, 126, 234, 0.2) 0%, 
    rgba(118, 75, 162, 0.2) 100%);
  box-shadow: 0 16px 64px 0 rgba(102, 126, 234, 0.4);
}

.modern-card--gradient .modern-card__bg-decoration {
  background: linear-gradient(135deg, 
    rgba(102, 126, 234, 0.1) 0%, 
    rgba(118, 75, 162, 0.1) 100%);
}

/* 霓虹变体 */
.modern-card--neon {
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid #00f5ff;
  box-shadow: 
    0 0 20px rgba(0, 245, 255, 0.3),
    inset 0 0 20px rgba(0, 245, 255, 0.1);
}

.modern-card--neon:hover {
  box-shadow: 
    0 0 40px rgba(0, 245, 255, 0.5),
    0 0 80px rgba(0, 245, 255, 0.3),
    inset 0 0 40px rgba(0, 245, 255, 0.2);
}

.modern-card--neon .modern-card__bg-decoration {
  background: radial-gradient(circle at center, 
    rgba(0, 245, 255, 0.1) 0%, 
    transparent 70%);
}

/* 极简变体 */
.modern-card--minimal {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
}

.modern-card--minimal:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.15);
}

/* 全息变体 */
.modern-card--holographic {
  background: linear-gradient(45deg, 
    rgba(255, 0, 150, 0.1) 0%,
    rgba(0, 204, 255, 0.1) 25%,
    rgba(255, 255, 0, 0.1) 50%,
    rgba(255, 0, 150, 0.1) 75%,
    rgba(0, 204, 255, 0.1) 100%);
  background-size: 400% 400%;
  animation: holographicShift 4s ease-in-out infinite;
  box-shadow: 0 8px 32px 0 rgba(255, 0, 150, 0.2);
}

.modern-card--holographic:hover {
  animation-duration: 2s;
  box-shadow: 0 16px 64px 0 rgba(255, 0, 150, 0.3);
}

@keyframes holographicShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* 内容区域 */
.modern-card__content {
  position: relative;
  z-index: 2;
  padding: var(--card-padding);
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 响应式修饰符 */
.modern-card--full-width {
  width: 100%;
}

.modern-card--mobile {
  --card-padding: 16px;
  --card-border-radius: 16px;
}

.modern-card--tablet {
  --card-padding: 20px;
  --card-border-radius: 18px;
}

.modern-card--touch {
  min-height: 44px;
}

.modern-card--reduced-motion {
  transition: none;
  animation: none;
}

.modern-card--reduced-motion .modern-card__bg-decoration,
.modern-card--reduced-motion .modern-card__glow {
  transition: none;
  animation: none;
}

.modern-card--reduced-motion.modern-card--hoverable:hover {
  transform: none;
}

.modern-card__header {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modern-card__body {
  flex: 1;
}

.modern-card__footer {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* 加载状态 */
.modern-card__loading {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  padding: var(--card-padding);
}

/* 发光效果 */
.modern-card__glow {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, 
    transparent 30%, 
    rgba(102, 126, 234, 0.1) 50%, 
    transparent 70%);
  border-radius: 22px;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: -1;
}

.modern-card:hover .modern-card__glow {
  opacity: 1;
  animation: glowRotate 3s linear infinite;
}

@keyframes glowRotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modern-card:not(.modern-card--mobile) {
    --card-padding: 16px;
    --card-border-radius: 16px;
  }
  
  .modern-card--hoverable:hover {
    transform: translateY(-4px) scale(1.01);
  }
  
  .modern-card__loading {
    min-height: 150px;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .modern-card:not(.modern-card--tablet) {
    --card-padding: 20px;
    --card-border-radius: 18px;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .modern-card {
    min-height: 44px;
  }
  
  .modern-card--hoverable:hover {
    transform: translateY(-2px) scale(1.005);
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .modern-card--glass {
    background: rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.05);
  }
  
  .modern-card--minimal {
    background: rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.03);
  }
  
  .modern-card__header,
  .modern-card__footer {
    border-color: rgba(255, 255, 255, 0.05);
  }
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
  .modern-card:not(.modern-card--reduced-motion),
  .modern-card:not(.modern-card--reduced-motion) .modern-card__bg-decoration,
  .modern-card:not(.modern-card--reduced-motion) .modern-card__glow {
    transition: none;
    animation: none;
  }
  
  .modern-card:not(.modern-card--reduced-motion).modern-card--hoverable:hover {
    transform: none;
  }
}
</style>