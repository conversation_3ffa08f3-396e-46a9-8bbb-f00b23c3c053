<template>
  <el-dialog
    v-model="dialogVisible"
    title="电池详情"
    width="800px"
    @close="$emit('close')"
    class="battery-detail-dialog"
  >
    <div v-if="battery" class="battery-detail" v-loading="loading" element-loading-text="加载中...">
      <!-- 基本信息 -->
      <div class="detail-section">
        <h4 class="section-title">基本信息</h4>
        <div class="info-grid">
          <div class="info-item">
            <label>电池ID:</label>
            <span>{{ battery.id }}</span>
          </div>
          <div class="info-item">
            <label>序列号:</label>
            <span>{{ battery.serial_number }}</span>
          </div>
          <div class="info-item">
            <label>制造商:</label>
            <span>{{ battery.manufacturer }}</span>
          </div>
          <div class="info-item">
            <label>型号:</label>
            <span>{{ battery.model }}</span>
          </div>
          <div class="info-item">
            <label>电池类型:</label>
            <span>{{ battery.battery_type }}</span>
          </div>
          <div class="info-item">
            <label>容量:</label>
            <span>{{ battery.capacity }} kWh</span>
          </div>
          <div class="info-item">
            <label>安装日期:</label>
            <span>{{ formatDate(battery.installation_date) }}</span>
          </div>
          <div class="info-item">
            <label>保修到期:</label>
            <span>{{ formatDate(battery.warranty_expiry) }}</span>
          </div>
        </div>
      </div>

      <!-- 状态信息 -->
      <div class="detail-section">
        <h4 class="section-title">状态信息</h4>
        <div class="status-grid">
          <div class="status-card">
            <div class="status-icon health">
              <el-icon><Lightning /></el-icon>
            </div>
            <div class="status-content">
              <div class="status-label">健康度 (SOH)</div>
              <div class="status-value">{{ battery.soh }}%</div>
              <el-progress
                :percentage="battery.soh"
                :color="getHealthColor(battery.soh)"
                :stroke-width="8"
              />
            </div>
          </div>
          
          <div class="status-card">
            <div class="status-icon charge">
              <el-icon><BatteryIcon /></el-icon>
            </div>
            <div class="status-content">
              <div class="status-label">电量 (SOC)</div>
              <div class="status-value">{{ battery.soc }}%</div>
              <el-progress
                :percentage="battery.soc"
                color="#3b82f6"
                :stroke-width="8"
              />
            </div>
          </div>
          
          <div class="status-card">
            <div class="status-icon temperature">
              <el-icon><Thermometer /></el-icon>
            </div>
            <div class="status-content">
              <div class="status-label">温度</div>
              <div class="status-value" :class="getTemperatureClass(battery.temperature)">
                {{ battery.temperature }}°C
              </div>
            </div>
          </div>
          
          <div class="status-card">
            <div class="status-icon voltage">
              <el-icon><Flash /></el-icon>
            </div>
            <div class="status-content">
              <div class="status-label">电压</div>
              <div class="status-value">{{ battery.voltage }}V</div>
            </div>
          </div>
          
          <div class="status-card">
            <div class="status-icon cycles">
              <el-icon><Refresh /></el-icon>
            </div>
            <div class="status-content">
              <div class="status-label">循环次数</div>
              <div class="status-value">{{ battery.cycle_count }}</div>
            </div>
          </div>
          
          <div class="status-card">
            <div class="status-icon status">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="status-content">
              <div class="status-label">健康状态</div>
              <el-tag :type="getStatusType(battery.health_status)" size="small">
                {{ getStatusText(battery.health_status) }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 关联车辆信息 -->
      <div v-if="battery.vehicle" class="detail-section">
        <h4 class="section-title">关联车辆</h4>
        <div class="vehicle-info-card">
          <div class="vehicle-header">
            <div class="vehicle-brand">
              <span class="brand-name">{{ battery.vehicle.brand }}</span>
              <span class="vehicle-model">{{ battery.vehicle.model }}</span>
            </div>
            <el-button size="small" @click="viewVehicle">查看车辆</el-button>
          </div>
          <div class="vehicle-details">
            <div class="vehicle-item">
              <label>VIN码:</label>
              <span>{{ battery.vehicle.vin }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 维护信息 -->
      <div class="detail-section">
        <h4 class="section-title">维护信息</h4>
        <div class="maintenance-info">
          <div class="maintenance-item">
            <label>上次维护:</label>
            <span>{{ formatDate(battery.last_maintenance) || '暂无记录' }}</span>
          </div>
          <div class="maintenance-actions">
            <el-button size="small" @click="viewMaintenanceHistory">
              <el-icon><Tools /></el-icon>
              维护历史
            </el-button>
            <el-button size="small" type="primary" @click="addMaintenanceRecord">
              <el-icon><Plus /></el-icon>
              添加记录
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="$emit('close')">关闭</el-button>
        <el-button type="primary" @click="editBattery">编辑电池</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Lightning, Battery as BatteryIcon, Thermometer, Flash, Refresh,
  CircleCheck, Tools, Plus
} from '@element-plus/icons-vue'
import { useBatteryStore } from '@/stores/battery'
import type { Battery } from '@/stores/battery'

// Props
const props = defineProps<{
  modelValue: boolean
  batteryId: string
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'close': []
}>()

// Store
const batteryStore = useBatteryStore()

// 响应式数据
const loading = ref(false)
const battery = ref<Battery | null>(null)

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 监听器
watch(() => props.batteryId, async (newId) => {
  if (newId && props.modelValue) {
    await loadBatteryDetail(newId)
  }
}, { immediate: true })

watch(() => props.modelValue, async (visible) => {
  if (visible && props.batteryId) {
    await loadBatteryDetail(props.batteryId)
  }
})

// 方法
const loadBatteryDetail = async (batteryId: string) => {
  loading.value = true
  try {
    battery.value = await batteryStore.fetchBatteryById(batteryId)
  } catch (error) {
    ElMessage.error('加载电池详情失败')
    battery.value = null
  } finally {
    loading.value = false
  }
}

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    'good': 'success',
    'warning': 'warning',
    'critical': 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    'good': '健康',
    'warning': '预警',
    'critical': '异常'
  }
  return texts[status] || '未知'
}

const getHealthColor = (health: number) => {
  if (health >= 80) return '#67C23A'
  if (health >= 60) return '#E6A23C'
  return '#F56C6C'
}

const getTemperatureClass = (temperature: number) => {
  if (temperature > 35) return 'temperature-high'
  if (temperature > 30) return 'temperature-medium'
  return 'temperature-normal'
}

const formatDate = (dateString: string) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const viewVehicle = () => {
  ElMessage.info('跳转到车辆详情页面')
}

const viewMaintenanceHistory = () => {
  ElMessage.info('查看维护历史')
}

const addMaintenanceRecord = () => {
  ElMessage.info('添加维护记录')
}

const editBattery = () => {
  ElMessage.info('编辑电池信息')
}
</script>

<style scoped>
.battery-detail-dialog :deep(.el-dialog__body) {
  padding: 0;
}

.battery-detail {
  padding: 24px;
}

.detail-section {
  margin-bottom: 32px;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

/* 基本信息网格 */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-item label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.info-item span {
  font-size: 14px;
  color: #111827;
  font-weight: 500;
}

/* 状态信息网格 */
.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.status-card {
  background: #f9fafb;
  border-radius: 12px;
  padding: 16px;
  border: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
}

.status-icon.health {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.status-icon.charge {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

.status-icon.temperature {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.status-icon.voltage {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.status-icon.cycles {
  background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
}

.status-icon.status {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.status-content {
  flex: 1;
}

.status-label {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
}

.status-value {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 8px;
}

.temperature-normal {
  color: #10b981;
}

.temperature-medium {
  color: #f59e0b;
}

.temperature-high {
  color: #ef4444;
}

/* 车辆信息卡片 */
.vehicle-info-card {
  background: #f0f9ff;
  border-radius: 12px;
  padding: 16px;
  border: 1px solid #bae6fd;
}

.vehicle-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.vehicle-brand {
  display: flex;
  flex-direction: column;
}

.brand-name {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

.vehicle-model {
  font-size: 14px;
  color: #6b7280;
}

.vehicle-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
}

.vehicle-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.vehicle-item label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.vehicle-item span {
  font-size: 14px;
  color: #111827;
  font-weight: 500;
}

/* 维护信息 */
.maintenance-info {
  background: #fef3c7;
  border-radius: 12px;
  padding: 16px;
  border: 1px solid #fde68a;
}

.maintenance-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.maintenance-item label {
  font-size: 14px;
  color: #92400e;
  font-weight: 500;
}

.maintenance-item span {
  font-size: 14px;
  color: #111827;
  font-weight: 500;
}

.maintenance-actions {
  display: flex;
  gap: 8px;
}

/* 对话框底部 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr;
  }

  .status-grid {
    grid-template-columns: 1fr;
  }

  .vehicle-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .vehicle-details {
    grid-template-columns: 1fr;
  }

  .maintenance-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>
