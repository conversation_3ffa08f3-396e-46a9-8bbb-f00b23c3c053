const mysql = require('mysql2/promise');
const bcrypt = require('bcrypt');

async function checkAdminPassword() {
  let connection;
  
  try {
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '123456',
      database: 'ev_management'
    });
    
    console.log('🔌 连接到MySQL数据库...');
    
    // 查询*****************用户
    const [rows] = await connection.execute(
      'SELECT email, password_hash FROM users WHERE email = ?',
      ['<EMAIL>']
    );
    
    if (rows.length === 0) {
      console.log('❌ 未找到*****************用户');
      return;
    }
    
    const user = rows[0];
    console.log('📧 用户邮箱:', user.email);
    console.log('🔐 密码哈希:', user.password_hash);
    
    // 测试密码验证
    const testPassword = '123456';
    const isValid = await bcrypt.compare(testPassword, user.password_hash);
    
    console.log('\n🧪 密码验证测试:');
    console.log('测试密码:', testPassword);
    console.log('验证结果:', isValid ? '✅ 密码正确' : '❌ 密码错误');
    
    // 如果密码错误，生成新的密码哈希
    if (!isValid) {
      console.log('\n🔧 生成新的密码哈希...');
      const newHash = await bcrypt.hash(testPassword, 10);
      console.log('新密码哈希:', newHash);
      
      // 更新密码
      await connection.execute(
        'UPDATE users SET password_hash = ? WHERE email = ?',
        [newHash, '<EMAIL>']
      );
      
      console.log('✅ 密码已更新');
      
      // 再次验证
      const recheck = await bcrypt.compare(testPassword, newHash);
      console.log('🔍 重新验证:', recheck ? '✅ 密码正确' : '❌ 密码仍然错误');
    }
    
  } catch (error) {
    console.error('❌ 错误:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

checkAdminPassword();