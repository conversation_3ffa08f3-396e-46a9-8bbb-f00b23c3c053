<template>
  <div class="charging-station-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <el-icon class="title-icon"><OfficeBuilding /></el-icon>
            充电站管理
          </h1>
          <p class="page-description">充电站信息管理、状态监控、运营分析</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="showAddDialog">
            <el-icon><Plus /></el-icon>
            新增充电站
          </el-button>
          <el-button @click="exportData">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
          <el-button @click="refreshData">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <div class="filter-content">
        <div class="search-box">
          <el-input
            v-model="searchQuery"
            placeholder="搜索充电站名称、地址或运营商"
            clearable
            style="width: 300px"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
        <div class="filter-controls">
          <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 120px">
            <el-option label="全部状态" value="" />
            <el-option label="正常" value="active" />
            <el-option label="维护中" value="maintenance" />
            <el-option label="离线" value="offline" />
          </el-select>
          <el-select v-model="operatorFilter" placeholder="运营商" style="width: 150px">
            <el-option label="全部运营商" value="" />
            <el-option label="国家电网" value="国家电网" />
            <el-option label="特来电" value="特来电" />
            <el-option label="星星充电" value="星星充电" />
          </el-select>
          <el-button @click="resetFilters">重置</el-button>
        </div>
      </div>
    </div>

    <!-- 充电站列表 -->
    <div class="stations-table">
      <el-table
        :data="filteredStations"
        style="width: 100%"
        :row-class-name="getRowClassName"
        @row-click="selectStation"
      >
        <el-table-column prop="name" label="充电站名称" min-width="180">
          <template #default="{ row }">
            <div class="station-name-cell">
              <div class="station-name">{{ row.name }}</div>
              <div class="station-id">ID: {{ row.id }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="address" label="地址" min-width="200" show-overflow-tooltip />
        
        <el-table-column prop="operator" label="运营商" width="120" />
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="充电桩" width="120">
          <template #default="{ row }">
            <div class="ports-info">
              <span class="available">{{ row.available_ports }}</span>
              <span class="separator">/</span>
              <span class="total">{{ row.total_ports }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="power" label="功率(kW)" width="100" />
        
        <el-table-column prop="price" label="价格(元/kWh)" width="120">
          <template #default="{ row }">
            <span class="price">¥{{ row.price }}</span>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button size="small" @click.stop="viewDetail(row)">
                详情
              </el-button>
              <el-button size="small" type="primary" @click.stop="editStation(row)">
                编辑
              </el-button>
              <el-button size="small" type="danger" @click.stop="deleteStation(row)">
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-section">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="totalStations"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑充电站' : '新增充电站'"
      width="600px"
    >
      <el-form
        ref="formRef"
        :model="stationForm"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="充电站名称" prop="name">
          <el-input v-model="stationForm.name" placeholder="请输入充电站名称" />
        </el-form-item>
        
        <el-form-item label="地址" prop="address">
          <el-input v-model="stationForm.address" placeholder="请输入详细地址" />
        </el-form-item>
        
        <el-form-item label="运营商" prop="operator">
          <el-select v-model="stationForm.operator" placeholder="选择运营商" style="width: 100%">
            <el-option label="国家电网" value="国家电网" />
            <el-option label="特来电" value="特来电" />
            <el-option label="星星充电" value="星星充电" />
            <el-option label="小桔充电" value="小桔充电" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="充电桩数量" prop="total_ports">
          <el-input-number v-model="stationForm.total_ports" :min="1" :max="50" />
        </el-form-item>
        
        <el-form-item label="充电功率" prop="power">
          <el-input-number v-model="stationForm.power" :min="7" :max="350" />
          <span style="margin-left: 8px">kW</span>
        </el-form-item>
        
        <el-form-item label="充电价格" prop="price">
          <el-input-number v-model="stationForm.price" :min="0.5" :max="3" :precision="2" />
          <span style="margin-left: 8px">元/kWh</span>
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="stationForm.status">
            <el-radio label="active">正常</el-radio>
            <el-radio label="maintenance">维护中</el-radio>
            <el-radio label="offline">离线</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="充电站详情"
      width="800px"
    >
      <div v-if="selectedStation" class="station-detail">
        <div class="detail-grid">
          <div class="detail-item">
            <label>充电站名称:</label>
            <span>{{ selectedStation.name }}</span>
          </div>
          <div class="detail-item">
            <label>地址:</label>
            <span>{{ selectedStation.address }}</span>
          </div>
          <div class="detail-item">
            <label>运营商:</label>
            <span>{{ selectedStation.operator }}</span>
          </div>
          <div class="detail-item">
            <label>状态:</label>
            <el-tag :type="getStatusType(selectedStation.status)">
              {{ getStatusText(selectedStation.status) }}
            </el-tag>
          </div>
          <div class="detail-item">
            <label>充电桩总数:</label>
            <span>{{ selectedStation.total_ports }}</span>
          </div>
          <div class="detail-item">
            <label>可用充电桩:</label>
            <span>{{ selectedStation.available_ports }}</span>
          </div>
          <div class="detail-item">
            <label>充电功率:</label>
            <span>{{ selectedStation.power }} kW</span>
          </div>
          <div class="detail-item">
            <label>充电价格:</label>
            <span>¥{{ selectedStation.price }}/kWh</span>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useChargingStore } from '@/stores/charging'
import {
  OfficeBuilding,
  Plus,
  Download,
  Refresh,
  Search
} from '@element-plus/icons-vue'

const chargingStore = useChargingStore()

// 响应式数据
const searchQuery = ref('')
const statusFilter = ref('')
const operatorFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const dialogVisible = ref(false)
const detailDialogVisible = ref(false)
const isEdit = ref(false)
const selectedStation = ref(null)
const formRef = ref()

// 表单数据
const stationForm = ref({
  id: '',
  name: '',
  address: '',
  operator: '',
  total_ports: 4,
  power: 60,
  price: 1.2,
  status: 'active'
})

// 表单验证规则
const formRules = {
  name: [{ required: true, message: '请输入充电站名称', trigger: 'blur' }],
  address: [{ required: true, message: '请输入地址', trigger: 'blur' }],
  operator: [{ required: true, message: '请选择运营商', trigger: 'change' }],
  total_ports: [{ required: true, message: '请输入充电桩数量', trigger: 'blur' }],
  power: [{ required: true, message: '请输入充电功率', trigger: 'blur' }],
  price: [{ required: true, message: '请输入充电价格', trigger: 'blur' }]
}

// 计算属性
const filteredStations = computed(() => {
  let filtered = chargingStore.stations
  
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(station => 
      station.name.toLowerCase().includes(query) ||
      station.address.toLowerCase().includes(query) ||
      station.operator.toLowerCase().includes(query)
    )
  }
  
  if (statusFilter.value) {
    filtered = filtered.filter(station => station.status === statusFilter.value)
  }
  
  if (operatorFilter.value) {
    filtered = filtered.filter(station => station.operator === operatorFilter.value)
  }
  
  return filtered
})

const totalStations = computed(() => filteredStations.value.length)

// 方法
const refreshData = async () => {
  try {
    await chargingStore.fetchStations()
    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败')
  }
}

const resetFilters = () => {
  searchQuery.value = ''
  statusFilter.value = ''
  operatorFilter.value = ''
}

const getRowClassName = ({ row }) => {
  if (row.status === 'offline') return 'offline-row'
  if (row.status === 'maintenance') return 'maintenance-row'
  return ''
}

const selectStation = (row: any) => {
  selectedStation.value = row
}

const getStatusType = (status: string) => {
  const types = {
    active: 'success',
    maintenance: 'warning',
    offline: 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts = {
    active: '正常',
    maintenance: '维护中',
    offline: '离线'
  }
  return texts[status] || '未知'
}

const showAddDialog = () => {
  isEdit.value = false
  stationForm.value = {
    id: '',
    name: '',
    address: '',
    operator: '',
    total_ports: 4,
    power: 60,
    price: 1.2,
    status: 'active'
  }
  dialogVisible.value = true
}

const editStation = (row: any) => {
  isEdit.value = true
  stationForm.value = { ...row }
  dialogVisible.value = true
}

const viewDetail = (row: any) => {
  selectedStation.value = row
  detailDialogVisible.value = true
}

const deleteStation = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除充电站 "${row.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await chargingStore.deleteStation(row.id)
    ElMessage.success('删除成功')
    await refreshData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const submitForm = async () => {
  try {
    await formRef.value.validate()
    
    if (isEdit.value) {
      await chargingStore.updateStation(stationForm.value.id, stationForm.value)
      ElMessage.success('更新成功')
    } else {
      await chargingStore.createStation(stationForm.value)
      ElMessage.success('创建成功')
    }
    
    dialogVisible.value = false
    await refreshData()
  } catch (error) {
    ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
  }
}

const exportData = () => {
  ElMessage.info('导出功能开发中...')
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
}

// 生命周期
onMounted(async () => {
  await refreshData()
})
</script>

<style scoped>
.charging-station-management {
  padding: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.page-header {
  margin-bottom: 32px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 36px;
  color: #667eea;
}

.page-description {
  font-size: 16px;
  color: #718096;
  margin: 8px 0 0 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.filter-section {
  margin-bottom: 24px;
}

.filter-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.filter-controls {
  display: flex;
  gap: 16px;
  align-items: center;
}

.stations-table {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.station-name-cell {
  display: flex;
  flex-direction: column;
}

.station-name {
  font-weight: 600;
  color: #1a202c;
}

.station-id {
  font-size: 12px;
  color: #718096;
}

.ports-info {
  display: flex;
  align-items: center;
  gap: 4px;
}

.available {
  color: #48bb78;
  font-weight: 600;
}

.separator {
  color: #cbd5e1;
}

.total {
  color: #718096;
}

.price {
  color: #667eea;
  font-weight: 600;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.pagination-section {
  display: flex;
  justify-content: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #e2e8f0;
}

.detail-item label {
  font-weight: 600;
  color: #4a5568;
}

.detail-item span {
  color: #1a202c;
}

:deep(.offline-row) {
  background-color: #fef2f2;
}

:deep(.maintenance-row) {
  background-color: #fffbeb;
}

@media (max-width: 768px) {
  .charging-station-management {
    padding: 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }
  
  .filter-content {
    flex-direction: column;
    gap: 16px;
  }
  
  .filter-controls {
    width: 100%;
    justify-content: center;
  }
  
  .detail-grid {
    grid-template-columns: 1fr;
  }
}
</style>
