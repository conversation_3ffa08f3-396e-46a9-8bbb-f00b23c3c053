/* 移动端适配样式 */

/* 基础响应式断点 */
@media (max-width: 768px) {
  /* 全局容器适配 */
  .container {
    padding: 10px !important;
    margin: 0 !important;
  }

  /* 页面标题适配 */
  .page-title {
    font-size: 20px !important;
    margin-bottom: 15px !important;
  }

  .page-subtitle {
    font-size: 14px !important;
  }

  /* 卡片布局适配 */
  .el-card {
    margin-bottom: 15px !important;
    border-radius: 8px !important;
  }

  .el-card__body {
    padding: 15px !important;
  }

  /* 网格布局适配 */
  .service-overview {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 10px !important;
  }

  .main-content {
    grid-template-columns: 1fr !important;
    gap: 15px !important;
  }

  /* 按钮适配 */
  .modern-button {
    min-height: 44px !important;
    padding: 12px 16px !important;
    font-size: 14px !important;
    border-radius: 6px !important;
  }

  .modern-button--small {
    min-height: 36px !important;
    padding: 8px 12px !important;
    font-size: 12px !important;
  }

  .modern-button--large {
    min-height: 48px !important;
    padding: 14px 20px !important;
    font-size: 16px !important;
  }

  /* 表单适配 */
  .el-input__inner {
    height: 44px !important;
    font-size: 16px !important;
  }

  .el-select {
    width: 100% !important;
  }

  .el-select__input {
    height: 44px !important;
    font-size: 16px !important;
  }

  /* 表格适配 */
  .el-table {
    font-size: 12px !important;
  }

  .el-table th,
  .el-table td {
    padding: 8px 4px !important;
  }

  /* 对话框适配 */
  .el-dialog {
    width: 95% !important;
    margin: 5vh auto !important;
    max-height: 90vh !important;
    overflow-y: auto !important;
  }

  .el-dialog__body {
    padding: 15px !important;
    max-height: 70vh !important;
    overflow-y: auto !important;
  }

  /* 浮动操作按钮适配 */
  .floating-actions {
    position: fixed !important;
    bottom: 20px !important;
    right: 20px !important;
    z-index: 1000 !important;
    display: flex !important;
    flex-direction: column !important;
    gap: 10px !important;
  }

  .floating-actions .modern-button {
    width: 56px !important;
    height: 56px !important;
    border-radius: 50% !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  }

  /* 图表容器适配 */
  .chart-container {
    height: 250px !important;
    width: 100% !important;
  }

  /* 地图容器适配 */
  .map-container {
    height: 300px !important;
    border-radius: 8px !important;
  }

  /* 列表项适配 */
  .station-item {
    padding: 12px !important;
    flex-direction: column !important;
    align-items: flex-start !important;
    gap: 10px !important;
  }

  .station-actions {
    width: 100% !important;
    flex-direction: row !important;
    justify-content: space-between !important;
  }

  /* 统计卡片适配 */
  .overview-content {
    flex-direction: column !important;
    text-align: center !important;
    padding: 15px !important;
  }

  .overview-icon {
    margin: 0 0 10px 0 !important;
    width: 40px !important;
    height: 40px !important;
  }

  .overview-info h3 {
    font-size: 18px !important;
  }

  .overview-info p {
    font-size: 12px !important;
  }
}

/* 小屏幕设备适配 */
@media (max-width: 480px) {
  .service-overview {
    grid-template-columns: 1fr !important;
  }

  .page-title {
    font-size: 18px !important;
  }

  .modern-button {
    width: 100% !important;
    margin-bottom: 8px !important;
  }

  .station-actions .modern-button {
    width: auto !important;
    flex: 1 !important;
    margin: 0 2px !important;
  }

  .overview-info h3 {
    font-size: 16px !important;
  }

  .chart-container {
    height: 200px !important;
  }

  .map-container {
    height: 250px !important;
  }
}

/* 触摸交互优化 */
@media (hover: none) and (pointer: coarse) {
  /* 增加可点击区域 */
  .modern-button,
  .station-item,
  .record-item {
    min-height: 44px !important;
  }

  /* 移除悬停效果 */
  .modern-button:hover,
  .station-item:hover,
  .el-card:hover {
    transform: none !important;
  }

  /* 添加触摸反馈 */
  .modern-button:active,
  .station-item:active {
    transform: scale(0.98) !important;
    transition: transform 0.1s ease !important;
  }

  /* 优化滚动体验 */
  .stations-list,
  .records-list {
    -webkit-overflow-scrolling: touch !important;
    scroll-behavior: smooth !important;
  }
}

/* 横屏适配 */
@media (max-width: 768px) and (orientation: landscape) {
  .main-content {
    grid-template-columns: 1fr 1fr !important;
  }

  .service-overview {
    grid-template-columns: repeat(4, 1fr) !important;
  }

  .chart-container {
    height: 200px !important;
  }

  .map-container {
    height: 200px !important;
  }
}

/* 高DPI屏幕适配 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .overview-icon {
    font-size: 18px !important;
  }

  .el-icon {
    font-size: 14px !important;
  }
}

/* 暗色模式适配 */
@media (prefers-color-scheme: dark) {
  .charging-service-container {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d3748 100%) !important;
  }

  .el-card {
    background-color: #2d3748 !important;
    border-color: #4a5568 !important;
  }

  .page-title {
    color: #f7fafc !important;
  }

  .page-subtitle {
    color: #a0aec0 !important;
  }

  .station-item {
    border-bottom-color: #4a5568 !important;
  }

  .station-item:hover,
  .station-item.selected {
    background-color: #4a5568 !important;
  }
}

/* 无障碍访问优化 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 打印样式 */
@media print {
  .floating-actions,
  .el-button {
    display: none !important;
  }

  .el-card {
    box-shadow: none !important;
    border: 1px solid #ddd !important;
  }

  .chart-container {
    height: 300px !important;
  }
}