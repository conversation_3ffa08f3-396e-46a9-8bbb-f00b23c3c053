-- 检查当前权限设置
-- 为新能源汽车管理系统的表授予权限

-- 为 anon 角色授予基本读取权限
GRANT SELECT ON users TO anon;
GRANT SELECT ON vehicles TO anon;
GRANT SELECT ON batteries TO anon;
GRANT SELECT ON battery_trace TO anon;
GRANT SELECT ON charging_stations TO anon;
GRANT SELECT ON charging_sessions TO anon;
GRANT SELECT ON carbon_credit TO anon;
GRANT SELECT ON driving_behavior TO anon;
GRANT SELECT ON fleet_management TO anon;
GRANT SELECT ON fleet_vehicles TO anon;

-- 为 authenticated 角色授予完整权限
GRANT ALL PRIVILEGES ON users TO authenticated;
GRANT ALL PRIVILEGES ON vehicles TO authenticated;
GRANT ALL PRIVILEGES ON batteries TO authenticated;
GRANT ALL PRIVILEGES ON battery_trace TO authenticated;
GRANT ALL PRIVILEGES ON charging_stations TO authenticated;
GRANT ALL PRIVILEGES ON charging_sessions TO authenticated;
GRANT ALL PRIVILEGES ON carbon_credit TO authenticated;
GRANT ALL PRIVILEGES ON driving_behavior TO authenticated;
GRANT ALL PRIVILEGES ON fleet_management TO authenticated;
GRANT ALL PRIVILEGES ON fleet_vehicles TO authenticated;

-- 为序列授予权限
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO anon;

-- 检查权限是否正确设置
SELECT 
    grantee, 
    table_name, 
    privilege_type 
FROM information_schema.role_table_grants 
WHERE table_schema = 'public' 
    AND grantee IN ('anon', 'authenticated')
    AND table_name IN (
        'users', 'vehicles', 'batteries', 'battery_trace', 
        'charging_stations', 'charging_sessions', 'carbon_credit', 
        'driving_behavior', 'fleet_management', 'fleet_vehicles'
    )
ORDER BY table_name, grantee;