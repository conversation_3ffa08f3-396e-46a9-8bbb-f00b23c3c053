const mysql = require('mysql2/promise');

async function checkChargingStationsTable() {
  let connection;
  
  try {
    // 连接数据库
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '123456',
      database: 'ev_management'
    });
    
    console.log('✅ 成功连接到MySQL数据库');
    
    // 检查充电站表结构
    const [rows] = await connection.execute('DESCRIBE charging_stations');
    console.log('\n📋 充电站表结构:');
    rows.forEach(row => {
      console.log(`${row.Field} - ${row.Type} - ${row.Null} - ${row.Key} - ${row.Default}`);
    });
    
  } catch (error) {
    console.error('❌ 错误:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 数据库连接已关闭');
    }
  }
}

checkChargingStationsTable();