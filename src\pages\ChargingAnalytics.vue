<template>
  <div class="charging-analytics">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <el-icon class="title-icon"><TrendCharts /></el-icon>
            充电数据分析
          </h1>
          <p class="page-description">充电统计、收入分析、趋势预测、运营报告</p>
        </div>
        <div class="header-actions">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px"
          />
          <el-button type="primary" @click="generateReport">
            <el-icon><Document /></el-icon>
            生成报告
          </el-button>
          <el-button @click="refreshData">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
    </div>

    <!-- 关键指标 -->
    <div class="kpi-section">
      <div class="kpi-grid">
        <div class="kpi-card">
          <div class="kpi-icon revenue">
            <el-icon><Money /></el-icon>
          </div>
          <div class="kpi-content">
            <div class="kpi-value">¥{{ totalRevenue }}</div>
            <div class="kpi-label">总收入</div>
            <div class="kpi-trend positive">+{{ revenueGrowth }}%</div>
          </div>
        </div>

        <div class="kpi-card">
          <div class="kpi-icon energy">
            <el-icon><Lightning /></el-icon>
          </div>
          <div class="kpi-content">
            <div class="kpi-value">{{ totalEnergy }}</div>
            <div class="kpi-label">总充电量(kWh)</div>
            <div class="kpi-trend">{{ avgEnergyPerSession }} kWh/次</div>
          </div>
        </div>

        <div class="kpi-card">
          <div class="kpi-icon sessions">
            <el-icon><Timer /></el-icon>
          </div>
          <div class="kpi-content">
            <div class="kpi-value">{{ totalSessions }}</div>
            <div class="kpi-label">总会话数</div>
            <div class="kpi-trend">{{ avgDuration }} 分钟/次</div>
          </div>
        </div>

        <div class="kpi-card">
          <div class="kpi-icon utilization">
            <el-icon><PieChart /></el-icon>
          </div>
          <div class="kpi-content">
            <div class="kpi-value">{{ utilizationRate }}%</div>
            <div class="kpi-label">设备利用率</div>
            <div class="kpi-trend">{{ peakHours }} 高峰时段</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <div class="charts-grid">
        <!-- 收入趋势图 -->
        <div class="chart-card">
          <div class="chart-header">
            <h3>收入趋势</h3>
            <div class="chart-controls">
              <el-radio-group v-model="revenueTimeRange" size="small">
                <el-radio-button label="7d">7天</el-radio-button>
                <el-radio-button label="30d">30天</el-radio-button>
                <el-radio-button label="90d">90天</el-radio-button>
              </el-radio-group>
            </div>
          </div>
          <div class="chart-container">
            <div class="chart-placeholder">
              <el-icon class="chart-icon"><TrendCharts /></el-icon>
              <p>收入趋势图表</p>
              <div class="chart-data">
                <span>最高: ¥{{ maxDailyRevenue }}</span>
                <span>平均: ¥{{ avgDailyRevenue }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 充电量分布 -->
        <div class="chart-card">
          <div class="chart-header">
            <h3>充电量分布</h3>
            <div class="chart-controls">
              <el-select v-model="energyGroupBy" size="small" style="width: 100px">
                <el-option label="按小时" value="hour" />
                <el-option label="按天" value="day" />
                <el-option label="按周" value="week" />
              </el-select>
            </div>
          </div>
          <div class="chart-container">
            <div class="chart-placeholder">
              <el-icon class="chart-icon"><Histogram /></el-icon>
              <p>充电量分布图表</p>
              <div class="chart-data">
                <span>峰值: {{ peakEnergyHour }}</span>
                <span>低谷: {{ lowEnergyHour }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 充电站使用率 -->
        <div class="chart-card">
          <div class="chart-header">
            <h3>充电站使用率</h3>
            <div class="chart-controls">
              <el-button size="small" @click="viewStationDetails">
                详细数据
              </el-button>
            </div>
          </div>
          <div class="chart-container">
            <div class="chart-placeholder">
              <el-icon class="chart-icon"><PieChart /></el-icon>
              <p>充电站使用率图表</p>
              <div class="chart-data">
                <span>最高: {{ maxStationUtilization }}%</span>
                <span>最低: {{ minStationUtilization }}%</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 用户行为分析 -->
        <div class="chart-card">
          <div class="chart-header">
            <h3>用户行为分析</h3>
            <div class="chart-controls">
              <el-select v-model="userAnalysisType" size="small" style="width: 120px">
                <el-option label="充电时长" value="duration" />
                <el-option label="充电频次" value="frequency" />
                <el-option label="消费金额" value="spending" />
              </el-select>
            </div>
          </div>
          <div class="chart-container">
            <div class="chart-placeholder">
              <el-icon class="chart-icon"><User /></el-icon>
              <p>用户行为分析图表</p>
              <div class="chart-data">
                <span>活跃用户: {{ activeUsers }}</span>
                <span>新用户: {{ newUsers }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="data-table-section">
      <div class="table-header">
        <h3>详细数据</h3>
        <div class="table-controls">
          <el-select v-model="tableView" style="width: 150px">
            <el-option label="按充电站" value="station" />
            <el-option label="按时间" value="time" />
            <el-option label="按用户" value="user" />
          </el-select>
          <el-button @click="exportTableData">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
        </div>
      </div>
      
      <div class="table-content">
        <el-table :data="tableData" style="width: 100%">
          <el-table-column 
            v-for="column in tableColumns" 
            :key="column.prop"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            :min-width="column.minWidth"
          />
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useChargingStore } from '@/stores/charging'
import {
  TrendCharts,
  Document,
  Refresh,
  Money,
  Lightning,
  Timer,
  PieChart,
  Histogram,
  User,
  Download
} from '@element-plus/icons-vue'

const chargingStore = useChargingStore()

// 响应式数据
const dateRange = ref([])
const revenueTimeRange = ref('30d')
const energyGroupBy = ref('day')
const userAnalysisType = ref('duration')
const tableView = ref('station')

// 计算属性
const totalRevenue = computed(() => chargingStore.totalRevenue)
const revenueGrowth = computed(() => 15.8) // 示例增长率

const totalEnergy = computed(() => 
  chargingStore.sessions.reduce((sum, s) => sum + (s.energy_delivered || 0), 0)
)

const avgEnergyPerSession = computed(() => {
  const sessions = chargingStore.sessions.filter(s => s.energy_delivered > 0)
  return sessions.length > 0 ? 
    Math.round(totalEnergy.value / sessions.length) : 0
})

const totalSessions = computed(() => chargingStore.sessions.length)

const avgDuration = computed(() => {
  const completedSessions = chargingStore.sessions.filter(s => s.end_time)
  if (completedSessions.length === 0) return 0
  
  const totalMinutes = completedSessions.reduce((sum, s) => {
    const start = new Date(s.start_time)
    const end = new Date(s.end_time)
    return sum + Math.floor((end - start) / (1000 * 60))
  }, 0)
  
  return Math.round(totalMinutes / completedSessions.length)
})

const utilizationRate = computed(() => {
  const totalPorts = chargingStore.stations.reduce((sum, s) => sum + s.total_ports, 0)
  const busyPorts = chargingStore.stations.reduce((sum, s) => sum + (s.total_ports - s.available_ports), 0)
  return totalPorts > 0 ? Math.round((busyPorts / totalPorts) * 100) : 0
})

const peakHours = computed(() => '14:00-18:00') // 示例高峰时段

// 示例数据
const maxDailyRevenue = computed(() => 8500)
const avgDailyRevenue = computed(() => 4200)
const peakEnergyHour = computed(() => '16:00-17:00')
const lowEnergyHour = computed(() => '02:00-03:00')
const maxStationUtilization = computed(() => 95)
const minStationUtilization = computed(() => 25)
const activeUsers = computed(() => 156)
const newUsers = computed(() => 23)

// 表格数据
const tableColumns = computed(() => {
  const columns = {
    station: [
      { prop: 'name', label: '充电站名称', minWidth: 150 },
      { prop: 'sessions', label: '会话数', width: 100 },
      { prop: 'revenue', label: '收入(元)', width: 120 },
      { prop: 'energy', label: '充电量(kWh)', width: 130 },
      { prop: 'utilization', label: '利用率(%)', width: 120 }
    ],
    time: [
      { prop: 'date', label: '日期', width: 120 },
      { prop: 'sessions', label: '会话数', width: 100 },
      { prop: 'revenue', label: '收入(元)', width: 120 },
      { prop: 'energy', label: '充电量(kWh)', width: 130 },
      { prop: 'avgDuration', label: '平均时长(分钟)', width: 140 }
    ],
    user: [
      { prop: 'userName', label: '用户', width: 120 },
      { prop: 'sessions', label: '会话数', width: 100 },
      { prop: 'totalCost', label: '总消费(元)', width: 120 },
      { prop: 'totalEnergy', label: '总充电量(kWh)', width: 140 },
      { prop: 'avgSession', label: '平均每次(kWh)', width: 140 }
    ]
  }
  return columns[tableView.value] || columns.station
})

const tableData = computed(() => {
  // 根据tableView返回不同的数据
  if (tableView.value === 'station') {
    return chargingStore.stations.map(station => ({
      name: station.name,
      sessions: chargingStore.sessions.filter(s => s.station_id === station.id).length,
      revenue: Math.round(Math.random() * 10000),
      energy: Math.round(Math.random() * 5000),
      utilization: Math.round(Math.random() * 100)
    }))
  }
  
  // 示例数据
  return [
    { date: '2024-01-01', sessions: 45, revenue: 5400, energy: 2100, avgDuration: 65 },
    { date: '2024-01-02', sessions: 52, revenue: 6200, energy: 2400, avgDuration: 58 },
    { date: '2024-01-03', sessions: 38, revenue: 4500, energy: 1800, avgDuration: 72 }
  ]
})

// 方法
const refreshData = async () => {
  try {
    await chargingStore.fetchSessions()
    await chargingStore.fetchStations()
    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败')
  }
}

const generateReport = () => {
  ElMessage.info('正在生成报告...')
  // 这里可以实现报告生成功能
}

const viewStationDetails = () => {
  ElMessage.info('查看充电站详细数据...')
  // 这里可以跳转到充电站详情页面
}

const exportTableData = () => {
  ElMessage.info('导出表格数据...')
  // 这里可以实现数据导出功能
}

// 生命周期
onMounted(async () => {
  await refreshData()
})
</script>

<style scoped>
.charging-analytics {
  padding: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.page-header {
  margin-bottom: 32px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 36px;
  color: #667eea;
}

.page-description {
  font-size: 16px;
  color: #718096;
  margin: 8px 0 0 0;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.kpi-section {
  margin-bottom: 32px;
}

.kpi-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
}

.kpi-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.kpi-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.kpi-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.kpi-icon.revenue {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.kpi-icon.energy {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.kpi-icon.sessions {
  background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.kpi-icon.utilization {
  background: linear-gradient(135deg, #fa709a, #fee140);
}

.kpi-content {
  flex: 1;
}

.kpi-value {
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 4px;
}

.kpi-label {
  font-size: 14px;
  color: #718096;
  margin-bottom: 4px;
}

.kpi-trend {
  font-size: 12px;
  color: #718096;
}

.kpi-trend.positive {
  color: #48bb78;
}

.charts-section {
  margin-bottom: 32px;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

.chart-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
}

.chart-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
  margin: 0;
}

.chart-container {
  height: 300px;
  padding: 24px;
}

.chart-placeholder {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #4a5568;
  text-align: center;
}

.chart-icon {
  font-size: 48px;
  color: #667eea;
  margin-bottom: 16px;
}

.chart-placeholder p {
  font-size: 16px;
  color: #718096;
  margin: 0 0 16px 0;
}

.chart-data {
  display: flex;
  gap: 24px;
  font-size: 14px;
  color: #4a5568;
}

.data-table-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
}

.table-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: #1a202c;
  margin: 0;
}

.table-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.table-content {
  padding: 24px;
}

@media (max-width: 1200px) {
  .charts-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .charging-analytics {
    padding: 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }
  
  .kpi-grid {
    grid-template-columns: 1fr;
  }
  
  .table-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .chart-data {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
