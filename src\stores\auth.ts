import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

// 用户信息接口
export interface User {
  id: string
  email: string
  name: string
  phone?: string
  avatar_url?: string
  user_type: 'individual' | 'enterprise'
  created_at: string
  updated_at: string
}

// 登录请求接口
export interface LoginRequest {
  email: string
  password: string
  remember?: boolean
}

// 注册请求接口
export interface RegisterRequest {
  email: string
  name: string
  phone: string
  password: string
}

// API响应接口
export interface AuthResponse {
  success: boolean
  message?: string
  data?: {
    user: User
    token: string
    refreshToken?: string
  }
}

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref<User | null>(null)
  const token = ref<string | null>(null)
  const refreshToken = ref<string | null>(null)
  const isLoading = ref(false)
  const isInitialized = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!user.value && !!token.value)
  const userRole = computed(() => user.value?.user_type || 'individual')
  const userName = computed(() => user.value?.name || '')
  const userEmail = computed(() => user.value?.email || '')

  // 初始化认证状态 - 从本地存储恢复认证信息
  const initAuth = async () => {
    if (isInitialized.value) return
    
    try {
      isLoading.value = true
      
      // 尝试从本地存储恢复认证信息
      const storedToken = localStorage.getItem('auth_token')
      const storedUser = localStorage.getItem('auth_user')
      const storedRefreshToken = localStorage.getItem('auth_refresh_token')
      
      if (storedToken && storedUser) {
        try {
          const userData = JSON.parse(storedUser)
          
          // 验证token是否仍然有效
          const isValid = await validateTokenWithServer(storedToken)
          
          if (isValid) {
            user.value = userData
            token.value = storedToken
            refreshToken.value = storedRefreshToken
          } else {
            // Token无效，清除存储
            localStorage.removeItem('auth_token')
            localStorage.removeItem('auth_refresh_token')
            localStorage.removeItem('auth_user')
            localStorage.removeItem('auth_expire')
          }
        } catch (error) {
          console.error('解析用户数据失败:', error)
          // 清除无效数据
          localStorage.removeItem('auth_token')
          localStorage.removeItem('auth_refresh_token')
          localStorage.removeItem('auth_user')
          localStorage.removeItem('auth_expire')
        }
      }
      
    } catch (error) {
      console.error('初始化认证状态失败:', error)
    } finally {
      isLoading.value = false
      isInitialized.value = true
    }
  }

  // 验证token有效性（内部方法）
  const validateTokenWithServer = async (tokenToValidate: string): Promise<boolean> => {
    try {
      const response = await fetch('http://localhost:3001/api/auth/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${tokenToValidate}`
        }
      })
      
      if (response.ok) {
        const data = await response.json()
        return data.success
      }
      
      return false
    } catch (error) {
      console.error('验证token失败:', error)
      return false
    }
  }

  // 验证token有效性
  const validateToken = async (): Promise<boolean> => {
    if (!token.value) return false
    
    try {
      const response = await fetch('http://localhost:3001/api/auth/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token.value}`
        }
      })
      
      if (response.ok) {
        const data = await response.json()
        return data.success
      }
      
      return false
    } catch (error) {
      console.error('验证token失败:', error)
      return false
    }
  }

  // 登录
  const login = async (credentials: LoginRequest): Promise<AuthResponse> => {
    try {
      isLoading.value = true
      
      const response = await fetch('http://localhost:3001/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(credentials)
      })
      
      const data: AuthResponse = await response.json()
      
      if (data.success && data.data) {
        // 保存用户信息和token
        user.value = data.data.user
        token.value = data.data.token
        refreshToken.value = data.data.refreshToken || null
        
        // 保存到本地存储
        localStorage.setItem('auth_token', data.data.token)
        if (data.data.refreshToken) {
          localStorage.setItem('auth_refresh_token', data.data.refreshToken)
        }
        localStorage.setItem('auth_user', JSON.stringify(data.data.user))
        
        // 如果选择记住我，设置更长的过期时间
        if (credentials.remember) {
          const expireDate = new Date()
          expireDate.setDate(expireDate.getDate() + 30) // 30天
          localStorage.setItem('auth_expire', expireDate.toISOString())
        }
        
        return {
          success: true,
          message: '登录成功',
          data: data.data
        }
      } else {
        return {
          success: false,
          message: data.message || '登录失败'
        }
      }
    } catch (error) {
      console.error('登录请求失败:', error)
      return {
        success: false,
        message: '网络错误，请重试'
      }
    } finally {
      isLoading.value = false
    }
  }

  // 注册
  const register = async (userData: RegisterRequest): Promise<AuthResponse> => {
    try {
      isLoading.value = true
      
      const response = await fetch('http://localhost:3001/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(userData)
      })
      
      const data: AuthResponse = await response.json()
      
      if (data.success) {
        return {
          success: true,
          message: '注册成功，请登录'
        }
      } else {
        return {
          success: false,
          message: data.message || '注册失败'
        }
      }
    } catch (error) {
      console.error('注册请求失败:', error)
      return {
        success: false,
        message: '网络错误，请重试'
      }
    } finally {
      isLoading.value = false
    }
  }

  // 登出
  const logout = async () => {
    try {
      // 调用后端登出接口
      if (token.value) {
        await fetch('http://localhost:3001/api/auth/logout', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token.value}`
          }
        })
      }
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      // 清除本地状态
      user.value = null
      token.value = null
      refreshToken.value = null
      
      // 清除本地存储
      localStorage.removeItem('auth_token')
      localStorage.removeItem('auth_refresh_token')
      localStorage.removeItem('auth_user')
      localStorage.removeItem('auth_expire')
    }
  }

  // 刷新token
  const refreshAuthToken = async (): Promise<boolean> => {
    if (!refreshToken.value) return false
    
    try {
      const response = await fetch('http://localhost:3001/api/auth/refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          refreshToken: refreshToken.value
        })
      })
      
      const data = await response.json()
      
      if (data.success && data.data) {
        token.value = data.data.token
        localStorage.setItem('auth_token', data.data.token)
        
        if (data.data.refreshToken) {
          refreshToken.value = data.data.refreshToken
          localStorage.setItem('auth_refresh_token', data.data.refreshToken)
        }
        
        return true
      }
      
      return false
    } catch (error) {
      console.error('刷新token失败:', error)
      return false
    }
  }

  // 更新用户信息
  const updateUser = async (userData: Partial<User>): Promise<boolean> => {
    if (!user.value || !token.value) return false
    
    try {
      const response = await fetch(`http://localhost:3001/api/users/${user.value.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token.value}`
        },
        body: JSON.stringify(userData)
      })
      
      const data = await response.json()
      
      if (data.success && data.data) {
        user.value = { ...user.value, ...data.data }
        localStorage.setItem('auth_user', JSON.stringify(user.value))
        return true
      }
      
      return false
    } catch (error) {
      console.error('更新用户信息失败:', error)
      return false
    }
  }

  // 检查权限
  const hasPermission = (permission: string): boolean => {
    if (!user.value) return false
    
    // 管理员拥有所有权限
    if (user.value.user_type === 'enterprise') {
      return true
    }
    
    // 这里可以根据实际需求扩展权限检查逻辑
    return false
  }

  // 获取认证头
  const getAuthHeaders = () => {
    if (!token.value) return {}
    
    return {
      'Authorization': `Bearer ${token.value}`
    }
  }

  return {
    // 状态
    user,
    token,
    isLoading,
    isInitialized,
    
    // 计算属性
    isAuthenticated,
    userRole,
    userName,
    userEmail,
    
    // 方法
    initAuth,
    validateToken,
    login,
    register,
    logout,
    refreshAuthToken,
    updateUser,
    hasPermission,
    getAuthHeaders
  }
})