<template>
  <el-dialog
    v-model="dialogVisible"
    :title="mode === 'add' ? '添加电池' : '编辑电池'"
    width="600px"
    @close="$emit('close')"
    class="battery-form-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="loading"
      element-loading-text="处理中..."
    >
      <!-- 基本信息 -->
      <div class="form-section">
        <h4 class="section-title">基本信息</h4>
        
        <el-form-item label="序列号" prop="serial_number">
          <el-input v-model="formData.serial_number" placeholder="请输入电池序列号" />
        </el-form-item>
        
        <el-form-item label="制造商" prop="manufacturer">
          <el-select v-model="formData.manufacturer" placeholder="请选择制造商" style="width: 100%">
            <el-option label="宁德时代" value="宁德时代" />
            <el-option label="比亚迪" value="比亚迪" />
            <el-option label="国轩高科" value="国轩高科" />
            <el-option label="中航锂电" value="中航锂电" />
            <el-option label="蜂巢能源" value="蜂巢能源" />
            <el-option label="其他" value="其他" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="型号" prop="model">
          <el-input v-model="formData.model" placeholder="请输入电池型号" />
        </el-form-item>
        
        <el-form-item label="电池类型" prop="battery_type">
          <el-select v-model="formData.battery_type" placeholder="请选择电池类型" style="width: 100%">
            <el-option label="三元锂电池 (NCM)" value="NCM" />
            <el-option label="磷酸铁锂电池 (LFP)" value="LFP" />
            <el-option label="钴酸锂电池 (LCO)" value="LCO" />
            <el-option label="锰酸锂电池 (LMO)" value="LMO" />
            <el-option label="其他" value="其他" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="容量 (kWh)" prop="capacity">
          <el-input-number
            v-model="formData.capacity"
            :min="0"
            :max="200"
            :precision="1"
            :step="0.1"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="额定电压 (V)" prop="voltage">
          <el-input-number
            v-model="formData.voltage"
            :min="0"
            :max="1000"
            :precision="1"
            :step="0.1"
            style="width: 100%"
          />
        </el-form-item>
      </div>

      <!-- 关联信息 -->
      <div class="form-section">
        <h4 class="section-title">关联信息</h4>
        
        <el-form-item label="关联车辆" prop="vehicle_id">
          <el-select
            v-model="formData.vehicle_id"
            placeholder="请选择关联车辆（可选）"
            style="width: 100%"
            clearable
            filterable
          >
            <el-option
              v-for="vehicle in availableVehicles"
              :key="vehicle.id"
              :label="`${vehicle.brand} ${vehicle.model} (${vehicle.vin})`"
              :value="vehicle.id"
            />
          </el-select>
        </el-form-item>
      </div>

      <!-- 时间信息 -->
      <div class="form-section">
        <h4 class="section-title">时间信息</h4>
        
        <el-form-item label="安装日期" prop="installation_date">
          <el-date-picker
            v-model="formData.installation_date"
            type="date"
            placeholder="请选择安装日期"
            style="width: 100%"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        
        <el-form-item label="保修到期" prop="warranty_expiry">
          <el-date-picker
            v-model="formData.warranty_expiry"
            type="date"
            placeholder="请选择保修到期日期"
            style="width: 100%"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="$emit('close')">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          {{ mode === 'add' ? '添加' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { useBatteryStore } from '@/stores/battery'
import { useVehicleStore } from '@/stores/vehicle'

// Props
const props = defineProps<{
  modelValue: boolean
  batteryId?: string
  mode: 'add' | 'edit'
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'close': []
  'success': []
}>()

// Stores
const batteryStore = useBatteryStore()
const vehicleStore = useVehicleStore()

// 响应式数据
const formRef = ref<FormInstance>()
const loading = ref(false)
const availableVehicles = ref<any[]>([])

const formData = ref({
  serial_number: '',
  manufacturer: '',
  model: '',
  battery_type: '',
  capacity: 0,
  voltage: 0,
  vehicle_id: '',
  installation_date: '',
  warranty_expiry: ''
})

// 表单验证规则
const formRules: FormRules = {
  serial_number: [
    { required: true, message: '请输入电池序列号', trigger: 'blur' }
  ],
  manufacturer: [
    { required: true, message: '请选择制造商', trigger: 'change' }
  ],
  model: [
    { required: true, message: '请输入电池型号', trigger: 'blur' }
  ],
  battery_type: [
    { required: true, message: '请选择电池类型', trigger: 'change' }
  ],
  capacity: [
    { required: true, message: '请输入电池容量', trigger: 'blur' },
    { type: 'number', min: 0.1, message: '容量必须大于0', trigger: 'blur' }
  ],
  voltage: [
    { required: true, message: '请输入额定电压', trigger: 'blur' },
    { type: 'number', min: 1, message: '电压必须大于0', trigger: 'blur' }
  ],
  installation_date: [
    { required: true, message: '请选择安装日期', trigger: 'change' }
  ],
  warranty_expiry: [
    { required: true, message: '请选择保修到期日期', trigger: 'change' }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 监听器
watch(() => props.modelValue, async (visible) => {
  if (visible) {
    await loadAvailableVehicles()
    if (props.mode === 'edit' && props.batteryId) {
      await loadBatteryData(props.batteryId)
    } else {
      resetForm()
    }
  }
})

// 方法
const loadAvailableVehicles = async () => {
  try {
    await vehicleStore.fetchVehicles()
    availableVehicles.value = vehicleStore.vehicles
  } catch (error) {
    console.error('加载车辆列表失败:', error)
  }
}

const loadBatteryData = async (batteryId: string) => {
  loading.value = true
  try {
    const battery = await batteryStore.fetchBatteryById(batteryId)
    if (battery) {
      formData.value = {
        serial_number: battery.serial_number,
        manufacturer: battery.manufacturer,
        model: battery.model,
        battery_type: battery.battery_type,
        capacity: battery.capacity,
        voltage: battery.voltage,
        vehicle_id: battery.vehicle_id,
        installation_date: battery.installation_date,
        warranty_expiry: battery.warranty_expiry
      }
    }
  } catch (error) {
    ElMessage.error('加载电池数据失败')
  } finally {
    loading.value = false
  }
}

const resetForm = () => {
  formData.value = {
    serial_number: '',
    manufacturer: '',
    model: '',
    battery_type: '',
    capacity: 0,
    voltage: 0,
    vehicle_id: '',
    installation_date: '',
    warranty_expiry: ''
  }
  formRef.value?.clearValidate()
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    loading.value = true
    
    if (props.mode === 'add') {
      await batteryStore.createBattery(formData.value)
      ElMessage.success('电池添加成功')
    } else if (props.batteryId) {
      await batteryStore.updateBattery(props.batteryId, formData.value)
      ElMessage.success('电池更新成功')
    }
    
    emit('success')
  } catch (error) {
    if (error !== false) { // 不是表单验证错误
      ElMessage.error(props.mode === 'add' ? '添加电池失败' : '更新电池失败')
    }
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  loadAvailableVehicles()
})
</script>

<style scoped>
.battery-form-dialog :deep(.el-dialog__body) {
  padding: 24px;
  max-height: 70vh;
  overflow-y: auto;
}

.form-section {
  margin-bottom: 32px;
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 表单样式优化 */
.battery-form-dialog :deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
}

.battery-form-dialog :deep(.el-input__wrapper) {
  border-radius: 8px;
}

.battery-form-dialog :deep(.el-select .el-input__wrapper) {
  border-radius: 8px;
}

.battery-form-dialog :deep(.el-date-editor.el-input) {
  border-radius: 8px;
}

.battery-form-dialog :deep(.el-input-number .el-input__wrapper) {
  border-radius: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .battery-form-dialog :deep(.el-dialog) {
    width: 95% !important;
    margin: 5vh auto;
  }

  .battery-form-dialog :deep(.el-form-item__label) {
    width: 80px !important;
  }
}
</style>
