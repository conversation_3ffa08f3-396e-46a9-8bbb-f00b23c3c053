const mysql = require('mysql2/promise');
const { config } = require('dotenv');

// 加载环境变量
config();

async function testDatabaseConnection() {
  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'ev_management',
    charset: 'utf8mb4',
    timezone: '+08:00'
  };

  console.log('数据库配置:', {
    ...dbConfig,
    password: '***'
  });

  try {
    // 测试连接到MySQL服务器（不指定数据库）
    console.log('\n1. 测试MySQL服务器连接...');
    const serverConnection = await mysql.createConnection({
      host: dbConfig.host,
      port: dbConfig.port,
      user: dbConfig.user,
      password: dbConfig.password
    });
    console.log('✅ MySQL服务器连接成功');

    // 检查数据库是否存在
    console.log('\n2. 检查数据库是否存在...');
    const [databases] = await serverConnection.execute('SHOW DATABASES');
    const dbExists = databases.some(db => db.Database === dbConfig.database);
    console.log('数据库列表:', databases.map(db => db.Database));
    
    if (!dbExists) {
      console.log(`❌ 数据库 '${dbConfig.database}' 不存在`);
      console.log('创建数据库...');
      await serverConnection.execute(`CREATE DATABASE IF NOT EXISTS ${dbConfig.database} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
      console.log('✅ 数据库创建成功');
    } else {
      console.log(`✅ 数据库 '${dbConfig.database}' 存在`);
    }

    await serverConnection.end();

    // 测试连接到指定数据库
    console.log('\n3. 测试数据库连接...');
    const dbConnection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 检查表是否存在
    console.log('\n4. 检查表结构...');
    const [tables] = await dbConnection.execute('SHOW TABLES');
    console.log('现有表:', tables.map(table => Object.values(table)[0]));

    // 检查关键表
    const requiredTables = ['users', 'vehicles', 'batteries', 'charging_stations', 'charging_sessions', 'carbon_credit'];
    const existingTables = tables.map(table => Object.values(table)[0]);
    
    for (const table of requiredTables) {
      if (existingTables.includes(table)) {
        console.log(`✅ 表 '${table}' 存在`);
        // 检查表结构
        const [columns] = await dbConnection.execute(`DESCRIBE ${table}`);
        console.log(`   字段数量: ${columns.length}`);
      } else {
        console.log(`❌ 表 '${table}' 不存在`);
      }
    }

    // 测试简单查询
    console.log('\n5. 测试查询...');
    const [result] = await dbConnection.execute('SELECT 1 as test');
    console.log('✅ 查询测试成功:', result);

    await dbConnection.end();
    console.log('\n🎉 数据库连接测试完成');

  } catch (error) {
    console.error('❌ 数据库连接测试失败:', error.message);
    console.error('错误详情:', error);
  }
}

testDatabaseConnection();