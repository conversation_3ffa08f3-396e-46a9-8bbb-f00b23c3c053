const mysql = require('mysql2/promise');

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'ev_management',
  charset: 'utf8mb4'
};

async function importData() {
  let connection;
  
  try {
    console.log('🔌 连接到MySQL数据库...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 1. 插入用户数据
    console.log('👥 插入用户数据...');
    const users = [
      ['user-001', '张伟', '<EMAIL>', 'hashed_password_1', '13800138001', 'admin', 'active', null],
      ['user-002', '李明', '<EMAIL>', 'hashed_password_2', '13800138002', 'user', 'active', null],
      ['user-003', '王芳', '<EMAIL>', 'hashed_password_3', '13800138003', 'user', 'active', null],
      ['user-004', '刘强', '<EMAIL>', 'hashed_password_4', '13800138004', 'user', 'active', null],
      ['user-005', '陈静', '<EMAIL>', 'hashed_password_5', '13800138005', 'user', 'active', null],
      ['user-006', '赵军', '<EMAIL>', 'hashed_password_6', '13800138006', 'user', 'active', null],
      ['user-007', '孙丽', '<EMAIL>', 'hashed_password_7', '13800138007', 'user', 'active', null],
      ['user-008', '周涛', '<EMAIL>', 'hashed_password_8', '13800138008', 'operator', 'active', null]
    ];
    
    for (const user of users) {
      await connection.execute(
        'INSERT IGNORE INTO users (user_id, username, email, password_hash, phone, role, status, avatar_url) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
        user
      );
    }
    console.log(`✅ 成功插入 ${users.length} 条用户数据`);
    
    // 2. 插入车辆数据
    console.log('🚗 插入车辆数据...');
    const vehicles = [
      ['vehicle-001', 'user-001', '特斯拉', 'Model 3', 2023, 'WVWZZZ1JZXW123456', '京A12345', '珍珠白', 75.0, 500, 'active', JSON.stringify({lat: 39.9075, lng: 116.4574}), '2023-12-01', '2024-06-01'],
      ['vehicle-002', 'user-002', '比亚迪', '汉EV', 2023, 'WVWZZZ1JZXW123457', '京B67890', '汉宫红', 85.4, 605, 'active', JSON.stringify({lat: 31.2397, lng: 121.4999}), '2023-11-15', '2024-05-15'],
      ['vehicle-003', 'user-003', '蔚来', 'ES6', 2023, 'WVWZZZ1JZXW123458', '京C11111', '星空蓝', 100.0, 610, 'active', JSON.stringify({lat: 22.5431, lng: 113.9344}), '2023-10-20', '2024-04-20'],
      ['vehicle-004', 'user-004', '小鹏', 'P7', 2023, 'WVWZZZ1JZXW123459', '沪A88888', '机甲灰', 80.9, 586, 'active', JSON.stringify({lat: 39.9388, lng: 116.3974}), '2023-09-10', '2024-03-10'],
      ['vehicle-005', 'user-005', '理想', 'ONE', 2023, 'WVWZZZ1JZXW123460', '粤B99999', '珠光白', 40.5, 800, 'active', JSON.stringify({lat: 23.1291, lng: 113.2644}), '2023-08-05', '2024-02-05'],
      ['vehicle-006', 'user-006', '广汽埃安', 'AION S', 2023, 'WVWZZZ1JZXW123461', '京D55555', '极地白', 58.8, 510, 'active', JSON.stringify({lat: 39.9075, lng: 116.4574}), '2023-07-15', '2024-01-15'],
      ['vehicle-007', 'user-007', '威马', 'EX5', 2022, 'WVWZZZ1JZXW123462', '沪B77777', '雪山白', 69.0, 520, 'maintenance', JSON.stringify({lat: 31.2397, lng: 121.4999}), '2022-12-01', '2023-12-01'],
      ['vehicle-008', 'user-008', '哪吒', 'U Pro', 2023, 'WVWZZZ1JZXW123463', '粤A66666', '科技银', 68.0, 500, 'active', JSON.stringify({lat: 22.5431, lng: 113.9344}), '2023-06-20', '2023-12-20']
    ];
    
    for (const vehicle of vehicles) {
      await connection.execute(
        'INSERT IGNORE INTO vehicles (vehicle_id, owner_id, make, model, year, vin, license_plate, color, battery_capacity, range_km, status, current_location, last_service_date, next_service_date) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
        vehicle
      );
    }
    console.log(`✅ 成功插入 ${vehicles.length} 条车辆数据`);
    
    // 3. 插入电池数据
    console.log('🔋 插入电池数据...');
    const batteries = [
      ['battery-001', 'vehicle-001', '宁德时代', 'NCM811-75kWh', 75.0, 85.2, 98.5, 245, 28.5, 400.0, 'normal', '2024-01-15 09:45:00', '2026-01-15'],
      ['battery-002', 'vehicle-002', '比亚迪', 'LFP-85kWh', 85.4, 72.8, 99.1, 189, 26.8, 403.2, 'normal', '2024-01-15 15:50:00', '2026-01-15'],
      ['battery-003', 'vehicle-003', '宁德时代', 'NCM523-100kWh', 100.0, 90.5, 97.8, 312, 29.2, 408.0, 'normal', '2024-01-15 21:05:00', '2026-01-15'],
      ['battery-004', 'vehicle-004', '宁德时代', 'NCM811-81kWh', 80.9, 68.3, 96.7, 278, 31.1, 355.2, 'warning', '2024-01-16 09:20:00', '2026-01-16'],
      ['battery-005', 'vehicle-005', '宁德时代', 'NCM622-40kWh', 40.5, 45.7, 98.9, 156, 27.3, 288.0, 'normal', '2024-01-16 17:15:00', '2026-01-16'],
      ['battery-006', 'vehicle-006', '中航锂电', 'LFP-59kWh', 58.8, 82.1, 97.2, 203, 30.5, 345.6, 'normal', '2024-01-17 08:30:00', '2026-01-17'],
      ['battery-007', 'vehicle-007', '谷神电池', 'NCM523-69kWh', 69.0, 15.6, 94.5, 456, 35.2, 350.4, 'critical', '2023-12-01 10:00:00', '2025-12-01'],
      ['battery-008', 'vehicle-008', '宁德时代', 'NCM811-68kWh', 68.0, 77.9, 98.3, 167, 28.9, 345.6, 'normal', '2024-01-17 14:20:00', '2026-01-17']
    ];
    
    for (const battery of batteries) {
      await connection.execute(
        'INSERT IGNORE INTO batteries (battery_id, vehicle_id, manufacturer, model, capacity_kwh, current_charge, health_percentage, cycle_count, temperature, voltage, status, last_charged, warranty_expiry) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
        battery
      );
    }
    console.log(`✅ 成功插入 ${batteries.length} 条电池数据`);
    
    // 插入充电站数据
    console.log('⚡ 插入充电站数据...');
    const stations = [
      ['station-001', '北京国贸充电站', '北京市朝阳区国贸中心地下停车场B2层', 39.9075, 116.4574, 8, 6, 120.0, 0.8, '国家电网', 'active', JSON.stringify({"wifi": true, "parking": true}), JSON.stringify({"weekdays": "24小时", "weekend": "24小时"})],
      ['station-002', '上海陆家嘴充电站', '上海市浦东新区陆家嘴金融区世纪大道88号', 31.2397, 121.4999, 12, 10, 180.0, 0.9, '特来电', 'active', JSON.stringify({"wifi": true, "restaurant": true}), JSON.stringify({"weekdays": "6:00-22:00", "weekend": "6:00-22:00"})],
      ['station-003', '深圳科技园充电站', '深圳市南山区科技园南区深南大道9988号', 22.5431, 113.9344, 6, 4, 150.0, 0.85, '星星充电', 'active', JSON.stringify({"wifi": true}), JSON.stringify({"weekdays": "24小时", "weekend": "24小时"})],
      ['station-004', '北京中关村充电站', '北京市海淀区中关村大街1号', 39.9388, 116.3974, 10, 8, 200.0, 0.75, '小桔充电', 'active', JSON.stringify({"wifi": true, "shopping": true}), JSON.stringify({"weekdays": "7:00-23:00", "weekend": "7:00-23:00"})],
      ['station-005', '广州珠江新城充电站', '广州市天河区珠江新城花城大道85号', 23.1291, 113.2644, 15, 12, 160.0, 0.88, '南方电网', 'active', JSON.stringify({"wifi": true, "parking": true, "restaurant": true}), JSON.stringify({"weekdays": "24小时", "weekend": "24小时"})]
    ];
    
    for (const station of stations) {
      await connection.execute(
        'INSERT IGNORE INTO charging_stations (station_id, name, address, latitude, longitude, total_ports, available_ports, power_output_kw, pricing_per_kwh, operator, status, amenities, operating_hours) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
        station
      );
    }
    console.log(`✅ 成功插入 ${stations.length} 条充电站数据`);
    
    // 5. 插入充电会话数据
    console.log('🔌 插入充电会话数据...');
    const sessions = [
      ['session-001', 'user-001', 'vehicle-001', 'station-001', '2024-01-15 08:30:00', '2024-01-15 09:45:00', 25.5, 85.2, 44.8, 53.76, '微信支付', 'completed'],
      ['session-002', 'user-002', 'vehicle-002', 'station-002', '2024-01-15 14:20:00', '2024-01-15 15:50:00', 15.3, 72.8, 49.1, 63.83, '支付宝', 'completed'],
      ['session-003', 'user-003', 'vehicle-003', 'station-003', '2024-01-15 19:10:00', '2024-01-15 21:05:00', 12.8, 90.5, 77.7, 97.13, '银行卡', 'completed'],
      ['session-004', 'user-004', 'vehicle-004', 'station-004', '2024-01-16 07:45:00', '2024-01-16 09:20:00', 8.2, 68.3, 48.7, 57.24, '微信支付', 'completed'],
      ['session-005', 'user-005', 'vehicle-005', 'station-005', '2024-01-16 16:30:00', '2024-01-16 17:15:00', 22.1, 45.7, 9.6, 16.8, '支付宝', 'completed']
    ];
    
    for (const session of sessions) {
      await connection.execute(
        'INSERT IGNORE INTO charging_sessions (session_id, user_id, vehicle_id, station_id, start_time, end_time, start_battery_level, end_battery_level, energy_delivered_kwh, cost, payment_method, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
        session
      );
    }
    console.log(`✅ 成功插入 ${sessions.length} 条充电会话数据`);
    
    // 6. 插入碳积分数据
    console.log('🌱 插入碳积分数据...');
    const credits = [
      ['user-001', 'vehicle-001', 15.6, 'driving', '2024-01-15 09:45:00', 2.3, 25.6, '绿色驾驶行为奖励', 'approved'],
      ['user-002', 'vehicle-002', 24.5, 'charging', '2024-01-15 15:50:00', 3.2, 0, '使用清洁能源充电奖励', 'approved'],
      ['user-003', 'vehicle-003', 38.9, 'driving', '2024-01-15 21:05:00', 5.1, 35.8, '长距离绿色出行奖励', 'approved'],
      ['user-004', 'vehicle-004', 12.3, 'referral', '2024-01-16 09:20:00', 1.8, 18.9, '推荐好友奖励', 'approved'],
      ['user-005', 'vehicle-005', 8.7, 'bonus', '2024-01-16 17:15:00', 1.2, 0, '特殊奖励', 'approved']
    ];
    
    for (const credit of credits) {
      await connection.execute(
        'INSERT IGNORE INTO carbon_credit (user_id, vehicle_id, credits_earned, activity_type, activity_date, co2_saved_kg, distance_km, description, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)',
        credit
      );
    }
    console.log(`✅ 成功插入 ${credits.length} 条碳积分数据`);
    
    // 7. 插入驾驶行为数据
    console.log('🚙 插入驾驶行为数据...');
    const trips = [
      ['vehicle-001', 'user-001', 'TRIP20240115001', '2024-01-15 08:00:00', '2024-01-15 08:30:00', 25.6, 51.2, 78.5, 15.2, 8.5, 2, 1, 5, 8.9, JSON.stringify([{lat: 39.9075, lng: 116.4574, time: '08:00'}, {lat: 39.9088, lng: 116.4588, time: '08:30'}])],
      ['vehicle-002', 'user-002', 'TRIP20240115002', '2024-01-15 14:00:00', '2024-01-15 14:20:00', 12.3, 36.9, 65.2, 18.7, 7.8, 1, 0, 2, 8.5, JSON.stringify([{lat: 31.2397, lng: 121.4999, time: '14:00'}, {lat: 31.2410, lng: 121.5012, time: '14:20'}])],
      ['vehicle-003', 'user-003', 'TRIP20240115003', '2024-01-15 18:30:00', '2024-01-15 19:10:00', 35.8, 53.7, 95.3, 14.8, 9.3, 0, 2, 8, 9.2, JSON.stringify([{lat: 22.5431, lng: 113.9344, time: '18:30'}, {lat: 22.5444, lng: 113.9358, time: '19:10'}])],
      ['vehicle-004', 'user-004', 'TRIP20240116001', '2024-01-16 07:15:00', '2024-01-16 07:45:00', 18.9, 37.8, 72.1, 16.9, 8.1, 3, 1, 12, 8.3, JSON.stringify([{lat: 39.9388, lng: 116.3974, time: '07:15'}, {lat: 39.9401, lng: 116.3988, time: '07:45'}])],
      ['vehicle-005', 'user-005', 'TRIP20240116002', '2024-01-16 16:00:00', '2024-01-16 16:30:00', 22.1, 44.2, 88.7, 12.3, 7.5, 1, 0, 3, 7.9, JSON.stringify([{lat: 23.1291, lng: 113.2644, time: '16:00'}, {lat: 23.1304, lng: 113.2658, time: '16:30'}])]
    ];
    
    for (const trip of trips) {
      await connection.execute(
        'INSERT IGNORE INTO driving_behavior (vehicle_id, user_id, trip_id, start_time, end_time, distance_km, avg_speed_kmh, max_speed_kmh, energy_consumption_kwh, efficiency_km_per_kwh, harsh_braking_count, rapid_acceleration_count, speeding_duration_minutes, eco_score, route_data) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
        trip
      );
    }
    console.log(`✅ 成功插入 ${trips.length} 条驾驶行为数据`);
    
    // 8. 插入车队管理数据
    console.log('🚛 插入车队管理数据...');
    const fleets = [
      ['fleet-001', '京东物流车队', '京东物流有限公司', 'user-002', 2, 2, 'logistics', 'active', '<EMAIL>', '13800138002', '北京市朝阳区京东总部'],
      ['fleet-002', '顺丰速运车队', '顺丰速运有限公司', 'user-006', 1, 1, 'express', 'active', '<EMAIL>', '13800138006', '广州市天河区顺丰总部']
    ];
    
    for (const fleet of fleets) {
      await connection.execute(
        'INSERT IGNORE INTO fleet_management (fleet_id, fleet_name, company_name, manager_id, total_vehicles, active_vehicles, fleet_type, status, contact_email, contact_phone, address) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
        fleet
      );
    }
    console.log(`✅ 成功插入 ${fleets.length} 条车队管理数据`);
    
    // 9. 插入边缘设备数据
    console.log('📡 插入边缘设备数据...');
    const devices = [
      ['edge-001', '温度传感器-北京国贸-01', 'sensor', 'online', JSON.stringify({address: '北京国贸充电站', zone: 'A区'}), '*************', 'v2.1.3', JSON.stringify({interval: 30, threshold: 35}), JSON.stringify({install_date: '2023-01-15', maintenance: 'monthly'})],
      ['edge-002', '智能控制器-上海陆家嘴-01', 'controller', 'online', JSON.stringify({address: '上海陆家嘴充电站', zone: 'B区'}), '*************', 'v3.0.1', JSON.stringify({mode: 'auto', power_limit: 200}), JSON.stringify({install_date: '2023-02-20', maintenance: 'quarterly'})],
      ['edge-003', '网关设备-深圳科技园-01', 'gateway', 'online', JSON.stringify({address: '深圳科技园充电站', zone: 'C区'}), '*************', 'v1.8.5', JSON.stringify({protocol: 'MQTT', port: 1883}), JSON.stringify({install_date: '2023-03-10', maintenance: 'monthly'})],
      ['edge-004', '监控摄像头-北京中关村-01', 'camera', 'online', JSON.stringify({address: '北京中关村充电站', zone: 'D区'}), '*************', 'v4.2.0', JSON.stringify({resolution: '1080p', fps: 30}), JSON.stringify({install_date: '2023-04-05', maintenance: 'semi-annual'})],
      ['edge-005', '环境传感器-广州珠江新城-01', 'sensor', 'online', JSON.stringify({address: '广州珠江新城充电站', zone: 'E区'}), '*************', 'v2.3.1', JSON.stringify({sensors: ['temperature', 'humidity', 'air_quality']}), JSON.stringify({install_date: '2023-05-12', maintenance: 'monthly'})]
    ];
    
    for (const device of devices) {
      await connection.execute(
        'INSERT IGNORE INTO edge_devices (device_id, name, type, status, location, ip_address, firmware_version, configuration, metadata, last_heartbeat) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW() - INTERVAL 30 SECOND)',
        device
      );
    }
    console.log(`✅ 成功插入 ${devices.length} 条边缘设备数据`);
    
    // 验证数据导入结果
    console.log('\n📊 数据导入统计:');
    console.log('==================');
    
    const tables = [
      'users', 'vehicles', 'batteries', 'charging_stations', 
      'charging_sessions', 'carbon_credit', 'driving_behavior', 
      'fleet_management', 'edge_devices'
    ];
    
    for (const table of tables) {
      try {
        const [rows] = await connection.execute(`SELECT COUNT(*) as count FROM ${table}`);
        const count = rows[0].count;
        console.log(`${table}: ${count} 条记录`);
      } catch (error) {
        console.log(`${table}: 查询失败 - ${error.message}`);
      }
    }
    
    console.log('\n🎉 批量数据导入完成！');
    console.log('💡 现在可以访问各个模块查看真实数据了');
    
  } catch (error) {
    console.error('❌ 数据导入失败:', error.message);
    console.error('错误详情:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 运行导入脚本
importData();