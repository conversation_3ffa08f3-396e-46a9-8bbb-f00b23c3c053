const mysql = require('mysql2/promise')

async function checkAndFixTables() {
  let connection
  
  try {
    console.log('✅ 连接数据库...')
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '123456',
      database: 'ev_management'
    })
    
    // 1. 检查现有表结构
    console.log('🔍 检查现有表结构...')
    
    const [edgeDevicesColumns] = await connection.execute(`
      DESCRIBE edge_devices
    `)
    
    console.log('edge_devices 表结构:')
    edgeDevicesColumns.forEach(col => {
      console.log(`  - ${col.Field}: ${col.Type} ${col.Null === 'YES' ? '(可空)' : '(非空)'} ${col.Default ? '默认: ' + col.Default : ''}`)
    })
    
    // 2. 修复 edge_devices 表结构
    console.log('🔧 修复 edge_devices 表结构...')
    
    // 检查是否存在 device_name 字段
    const hasDeviceName = edgeDevicesColumns.some(col => col.Field === 'device_name')
    if (!hasDeviceName) {
      await connection.execute(`
        ALTER TABLE edge_devices 
        ADD COLUMN device_name VARCHAR(100) NOT NULL DEFAULT 'Unknown Device' AFTER device_id
      `)
      console.log('✅ 添加 device_name 字段成功')
    }
    
    // 检查是否存在 device_type 字段
    const hasDeviceType = edgeDevicesColumns.some(col => col.Field === 'device_type')
    if (!hasDeviceType) {
      await connection.execute(`
        ALTER TABLE edge_devices 
        ADD COLUMN device_type VARCHAR(50) NOT NULL DEFAULT 'unknown' AFTER device_name
      `)
      console.log('✅ 添加 device_type 字段成功')
    }
    
    // 检查是否存在 status 字段
    const hasStatus = edgeDevicesColumns.some(col => col.Field === 'status')
    if (!hasStatus) {
      await connection.execute(`
        ALTER TABLE edge_devices 
        ADD COLUMN status ENUM('online', 'offline', 'maintenance') DEFAULT 'offline' AFTER device_type
      `)
      console.log('✅ 添加 status 字段成功')
    }
    
    // 检查是否存在 location 字段
    const hasLocation = edgeDevicesColumns.some(col => col.Field === 'location')
    if (!hasLocation) {
      await connection.execute(`
        ALTER TABLE edge_devices 
        ADD COLUMN location VARCHAR(200) AFTER status
      `)
      console.log('✅ 添加 location 字段成功')
    }
    
    // 检查是否存在 ip_address 字段
    const hasIpAddress = edgeDevicesColumns.some(col => col.Field === 'ip_address')
    if (!hasIpAddress) {
      await connection.execute(`
        ALTER TABLE edge_devices 
        ADD COLUMN ip_address VARCHAR(45) AFTER location
      `)
      console.log('✅ 添加 ip_address 字段成功')
    }
    
    // 检查是否存在 last_heartbeat 字段
    const hasLastHeartbeat = edgeDevicesColumns.some(col => col.Field === 'last_heartbeat')
    if (!hasLastHeartbeat) {
      await connection.execute(`
        ALTER TABLE edge_devices 
        ADD COLUMN last_heartbeat TIMESTAMP NULL AFTER ip_address
      `)
      console.log('✅ 添加 last_heartbeat 字段成功')
    }
    
    // 3. 插入测试设备（使用简单的字段）
    console.log('🔧 插入测试设备...')
    const testDevices = [
      {
        device_id: 'EV001',
        device_name: '电动车001',
        device_type: 'electric_vehicle',
        status: 'online',
        location: '北京市朝阳区',
        ip_address: '*************'
      },
      {
        device_id: 'EV002', 
        device_name: '电动车002',
        device_type: 'electric_vehicle',
        status: 'online',
        location: '上海市浦东新区',
        ip_address: '*************'
      },
      {
        device_id: 'EV003',
        device_name: '电动车003',
        device_type: 'electric_vehicle', 
        status: 'offline',
        location: '广州市天河区',
        ip_address: '*************'
      },
      {
        device_id: 'CS001',
        device_name: '充电站001',
        device_type: 'charging_station',
        status: 'online',
        location: '深圳市南山区',
        ip_address: '*************'
      },
      {
        device_id: 'CS002',
        device_name: '充电站002', 
        device_type: 'charging_station',
        status: 'online',
        location: '杭州市西湖区',
        ip_address: '*************'
      }
    ]
    
    for (const device of testDevices) {
      try {
        await connection.execute(`
          INSERT INTO edge_devices 
          (device_id, device_name, device_type, status, location, ip_address, last_heartbeat) 
          VALUES (?, ?, ?, ?, ?, ?, NOW())
          ON DUPLICATE KEY UPDATE
          device_name = VALUES(device_name),
          device_type = VALUES(device_type),
          status = VALUES(status),
          location = VALUES(location),
          ip_address = VALUES(ip_address),
          last_heartbeat = NOW()
        `, [
          device.device_id,
          device.device_name,
          device.device_type,
          device.status,
          device.location,
          device.ip_address
        ])
        console.log(`✅ 设备 ${device.device_id} 创建/更新成功`)
      } catch (error) {
        console.log(`⚠️  设备 ${device.device_id} 创建失败:`, error.message)
      }
    }
    
    // 4. 插入测试数据
    console.log('🔧 插入测试数据...')
    const testData = [
      {
        device_id: 'EV001',
        data_type: 'battery_level',
        value: JSON.stringify({ level: 85, voltage: 400.5, temperature: 25.8 }),
        text_value: '85%',
        unit: '%',
        quality: 95.5
      },
      {
        device_id: 'EV001',
        data_type: 'speed',
        value: JSON.stringify({ speed: 60, max_speed: 120 }),
        text_value: '60km/h',
        unit: 'km/h',
        quality: 98.0
      },
      {
        device_id: 'EV002', 
        data_type: 'charging_power',
        value: JSON.stringify({ power: 50.2, current: 125.5, voltage: 400 }),
        text_value: '50.2kW',
        unit: 'kW',
        quality: 98.0
      },
      {
        device_id: 'EV002',
        data_type: 'battery_level',
        value: JSON.stringify({ level: 92, voltage: 405.2, temperature: 23.5 }),
        text_value: '92%',
        unit: '%',
        quality: 97.8
      },
      {
        device_id: 'CS001',
        data_type: 'station_status',
        value: JSON.stringify({ status: 'available', slots: 8, occupied: 3 }),
        text_value: '可用',
        unit: 'slots',
        quality: 100.0
      },
      {
        device_id: 'CS002',
        data_type: 'utilization',
        value: JSON.stringify({ rate: 78.5, active_sessions: 6, total_slots: 10 }),
        text_value: '78.5%',
        unit: '%',
        quality: 96.8
      }
    ]
    
    for (const data of testData) {
      try {
        await connection.execute(`
          INSERT INTO edge_device_data 
          (device_id, data_type, value, text_value, unit, quality, timestamp) 
          VALUES (?, ?, ?, ?, ?, ?, NOW())
        `, [
          data.device_id,
          data.data_type, 
          data.value,
          data.text_value,
          data.unit,
          data.quality
        ])
        console.log(`✅ 数据插入成功: ${data.device_id} - ${data.data_type}`)
      } catch (error) {
        console.log(`⚠️  数据插入失败: ${data.device_id} - ${data.data_type}:`, error.message)
      }
    }
    
    // 5. 验证最终结果
    console.log('🔍 验证最终结果...')
    
    const [deviceRows] = await connection.execute(`
      SELECT device_id, device_name, device_type, status 
      FROM edge_devices 
      ORDER BY device_id
    `)
    
    console.log('设备列表:')
    deviceRows.forEach(row => {
      console.log(`  - ${row.device_id}: ${row.device_name} (${row.device_type}) - ${row.status}`)
    })
    
    const [dataRows] = await connection.execute(`
      SELECT device_id, data_type, text_value, unit, quality 
      FROM edge_device_data 
      ORDER BY created_at DESC 
      LIMIT 10
    `)
    
    console.log('最新数据:')
    dataRows.forEach(row => {
      console.log(`  - ${row.device_id}: ${row.data_type} = ${row.text_value} ${row.unit || ''} (质量: ${row.quality})`)
    })
    
    console.log('🎉 数据库修复完成！')
    
  } catch (error) {
    console.error('❌ 修复失败:', error.message)
  } finally {
    if (connection) {
      await connection.end()
      console.log('🔌 数据库连接已关闭')
    }
  }
}

checkAndFixTables()