const mysql = require('mysql2/promise');

async function insertCompleteTestData() {
  let connection;
  
  try {
    console.log('🔌 连接到MySQL数据库...');
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '123456',
      database: 'ev_management'
    });
    console.log('✅ 数据库连接成功');
    
    // 插入电池数据
    console.log('🔋 插入电池数据...');
    const batteries = [
      ['battery_001', 'vehicle_001', '松下', 'NCR18650B', 75.0, 400.0, 'NCM', '2023-01-15', 8, 98.5, 85.2, 25.5, 150, 'normal'],
      ['battery_002', 'vehicle_002', '比亚迪', 'Blade Battery', 85.4, 400.0, 'LFP', '2023-02-20', 8, 99.2, 92.1, 23.8, 120, 'normal'],
      ['battery_003', 'vehicle_003', '宁德时代', 'NCM811', 100.0, 400.0, 'NCM', '2023-03-10', 8, 97.8, 78.5, 26.2, 180, 'normal']
    ];
    
    for (const battery of batteries) {
      try {
        await connection.execute(
          'INSERT IGNORE INTO batteries (battery_id, vehicle_id, manufacturer, model, capacity_kwh, voltage, chemistry, manufacture_date, warranty_years, current_soh, current_soc, temperature, cycle_count, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
          battery
        );
        console.log(`✅ 电池 ${battery[0]} 插入成功`);
      } catch (err) {
        console.log(`⚠️ 电池插入失败: ${err.message}`);
      }
    }
    
    // 插入充电会话数据
    console.log('⚡ 插入充电会话数据...');
    const sessions = [
      ['session_001', 'vehicle_001', 'station_001', 'user_001', '2024-01-15 09:30:00', '2024-01-15 10:45:00', 45.5, 54.60, '微信支付', 'completed', 20.5, 85.2],
      ['session_002', 'vehicle_002', 'station_002', 'user_002', '2024-01-16 14:20:00', '2024-01-16 15:50:00', 52.3, 57.53, '支付宝', 'completed', 35.8, 92.1],
      ['session_003', 'vehicle_003', 'station_003', 'user_003', '2024-01-17 11:15:00', '2024-01-17 12:30:00', 38.2, 38.20, '银行卡', 'completed', 40.3, 78.5]
    ];
    
    for (const session of sessions) {
      try {
        await connection.execute(
          'INSERT IGNORE INTO charging_sessions (session_id, vehicle_id, station_id, user_id, start_time, end_time, energy_delivered_kwh, cost, payment_method, status, start_battery_level, end_battery_level) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
          session
        );
        console.log(`✅ 充电会话 ${session[0]} 插入成功`);
      } catch (err) {
        console.log(`⚠️ 充电会话插入失败: ${err.message}`);
      }
    }
    
    // 插入碳积分数据
    console.log('🌱 插入碳积分数据...');
    const carbonCredits = [
      ['user_001', 'vehicle_001', 'driving', 125.50, '绿色出行奖励', '2024-01-15', '2025-01-15', 'active'],
      ['user_002', 'vehicle_002', 'charging', 89.30, '清洁能源充电奖励', '2024-01-16', '2025-01-16', 'active'],
      ['user_003', 'vehicle_003', 'sharing', 45.80, '车辆共享奖励', '2024-01-17', '2025-01-17', 'active']
    ];
    
    for (const credit of carbonCredits) {
      try {
        await connection.execute(
          'INSERT IGNORE INTO carbon_credit (user_id, vehicle_id, credit_type, amount, description, earned_date, expiry_date, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
          credit
        );
        console.log(`✅ 碳积分记录插入成功`);
      } catch (err) {
        console.log(`⚠️ 碳积分插入失败: ${err.message}`);
      }
    }
    
    // 插入驾驶行为数据
    console.log('🚗 插入驾驶行为数据...');
    const drivingBehaviors = [
      ['vehicle_001', 'user_001', 'trip_001', '2024-01-15 08:00:00', '2024-01-15 09:15:00', 45.2, 65.5, 95.0, 8.5, 5.32, 2, 1, 5, 85.5, JSON.stringify({"start_location": "北京市朝阳区", "end_location": "北京市海淀区"})],
      ['vehicle_002', 'user_002', 'trip_002', '2024-01-16 13:30:00', '2024-01-16 14:45:00', 38.7, 58.2, 88.0, 7.2, 5.38, 1, 0, 2, 92.3, JSON.stringify({"start_location": "上海市浦东新区", "end_location": "上海市徐汇区"})],
      ['vehicle_003', 'user_003', 'trip_003', '2024-01-17 10:00:00', '2024-01-17 11:30:00', 52.1, 72.8, 110.0, 9.8, 5.32, 3, 2, 8, 78.9, JSON.stringify({"start_location": "深圳市南山区", "end_location": "深圳市福田区"})]
    ];
    
    for (const behavior of drivingBehaviors) {
      try {
        await connection.execute(
          'INSERT IGNORE INTO driving_behavior (vehicle_id, user_id, trip_id, start_time, end_time, distance_km, avg_speed_kmh, max_speed_kmh, energy_consumption_kwh, efficiency_km_per_kwh, harsh_braking_count, rapid_acceleration_count, speeding_duration_minutes, eco_score, route_data) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
          behavior
        );
        console.log(`✅ 驾驶行为记录 ${behavior[2]} 插入成功`);
      } catch (err) {
        console.log(`⚠️ 驾驶行为插入失败: ${err.message}`);
      }
    }
    
    // 插入车队管理数据
    console.log('🚛 插入车队管理数据...');
    const fleets = [
      ['fleet_001', '绿色出行车队', '北京绿色出行科技有限公司', 'user_004', 25, 23, 'rental', 'active', '<EMAIL>', '************', '北京市朝阳区望京SOHO'],
      ['fleet_002', '智慧物流车队', '上海智慧物流有限公司', 'user_005', 50, 48, 'delivery', 'active', '<EMAIL>', '************', '上海市浦东新区张江高科技园区']
    ];
    
    for (const fleet of fleets) {
      try {
        await connection.execute(
          'INSERT IGNORE INTO fleet_management (fleet_id, fleet_name, company_name, manager_id, total_vehicles, active_vehicles, fleet_type, status, contact_email, contact_phone, address) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
          fleet
        );
        console.log(`✅ 车队 ${fleet[1]} 插入成功`);
      } catch (err) {
        console.log(`⚠️ 车队插入失败: ${err.message}`);
      }
    }
    
    // 插入边缘设备数据
    console.log('📡 插入边缘设备数据...');
    const edgeDevices = [
      ['edge_001', '北京国贸边缘计算节点', 'monitoring', 'online', '北京市朝阳区建国门外大街1号', '*************', 'v2.1.5', new Date(), JSON.stringify({"cpu_cores": 8, "memory_gb": 16, "storage_gb": 512}), JSON.stringify({"deployment_date": "2024-01-01", "maintenance_contact": "<EMAIL>"})],
      ['edge_002', '上海陆家嘴数据处理器', 'data_collection', 'online', '上海市浦东新区陆家嘴环路1000号', '*************', 'v2.1.3', new Date(), JSON.stringify({"cpu_cores": 16, "memory_gb": 32, "storage_gb": 1024}), JSON.stringify({"deployment_date": "2024-01-05", "maintenance_contact": "<EMAIL>"})],
      ['edge_003', '深圳科技园传感器集群', 'control', 'online', '深圳市南山区科技园南区', '*************', 'v1.8.2', new Date(), JSON.stringify({"sensor_count": 50, "data_rate": "1MB/s"}), JSON.stringify({"deployment_date": "2024-01-10", "sensor_types": ["temperature", "humidity", "air_quality"]})]
    ];
    
    for (const device of edgeDevices) {
      try {
        await connection.execute(
          'INSERT IGNORE INTO edge_devices (device_id, name, type, status, location, ip_address, firmware_version, last_heartbeat, configuration, metadata) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
          device
        );
        console.log(`✅ 边缘设备 ${device[1]} 插入成功`);
      } catch (err) {
        console.log(`⚠️ 边缘设备插入失败: ${err.message}`);
      }
    }
    
    // 最终统计
    console.log('\n📊 最终数据统计:');
    console.log('='.repeat(40));
    const tables = [
      'users', 'vehicles', 'batteries', 'charging_stations', 
      'charging_sessions', 'carbon_credit', 'driving_behavior', 
      'fleet_management', 'edge_devices'
    ];
    
    for (const table of tables) {
      try {
        const [rows] = await connection.execute(`SELECT COUNT(*) as count FROM ${table}`);
        console.log(`📋 ${table.padEnd(20)}: ${rows[0].count.toString().padStart(3)} 条记录`);
      } catch (err) {
        console.log(`❌ ${table.padEnd(20)}: 查询失败`);
      }
    }
    
    console.log('\n🎉 所有测试数据插入完成！');
    console.log('现在您的系统已经有了完整的测试数据，可以正常运行和展示各种功能。');
    
  } catch (error) {
    console.error('❌ 错误:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 数据库连接已关闭');
    }
  }
}

// 运行完整数据插入脚本
insertCompleteTestData();