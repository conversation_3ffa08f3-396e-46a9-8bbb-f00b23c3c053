<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .debug-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>🔧 登录调试工具</h1>
    
    <div class="debug-container">
        <h2>1. 测试后端API连接</h2>
        <button onclick="testBackendConnection()">测试后端连接</button>
        <div id="connectionResult" class="result" style="display: none;"></div>
    </div>

    <div class="debug-container">
        <h2>2. 测试登录API</h2>
        <div class="form-group">
            <label for="email">邮箱:</label>
            <input type="email" id="email" value="<EMAIL>">
        </div>
        <div class="form-group">
            <label for="password">密码:</label>
            <input type="password" id="password" value="123456">
        </div>
        <button onclick="testLogin()">测试登录</button>
        <div id="loginResult" class="result" style="display: none;"></div>
    </div>

    <div class="debug-container">
        <h2>3. 检查本地存储</h2>
        <button onclick="checkLocalStorage()">检查本地存储</button>
        <button onclick="clearLocalStorage()">清除本地存储</button>
        <div id="storageResult" class="result" style="display: none;"></div>
    </div>

    <div class="debug-container">
        <h2>4. 测试Token验证</h2>
        <button onclick="testTokenValidation()">验证Token</button>
        <div id="tokenResult" class="result" style="display: none;"></div>
    </div>

    <script>
        // 测试后端连接
        async function testBackendConnection() {
            const resultDiv = document.getElementById('connectionResult');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '正在测试连接...';
            resultDiv.className = 'result';

            try {
                const response = await fetch('http://localhost:3001/api/auth/validate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = {
                    status: response.status,
                    statusText: response.statusText,
                    headers: Object.fromEntries(response.headers.entries()),
                    body: await response.text()
                };

                resultDiv.textContent = `✅ 后端连接正常\n\n响应详情:\n${JSON.stringify(result, null, 2)}`;
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.textContent = `❌ 后端连接失败\n\n错误详情:\n${error.message}\n${error.stack}`;
                resultDiv.className = 'result error';
            }
        }

        // 测试登录
        async function testLogin() {
            const resultDiv = document.getElementById('loginResult');
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;

            resultDiv.style.display = 'block';
            resultDiv.textContent = '正在测试登录...';
            resultDiv.className = 'result';

            try {
                const response = await fetch('http://localhost:3001/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });

                const data = await response.json();
                
                const result = {
                    status: response.status,
                    statusText: response.statusText,
                    headers: Object.fromEntries(response.headers.entries()),
                    body: data
                };

                if (data.success) {
                    // 保存到本地存储
                    if (data.data && data.data.token) {
                        localStorage.setItem('auth_token', data.data.token);
                        localStorage.setItem('auth_user', JSON.stringify(data.data.user));
                    }

                    resultDiv.textContent = `✅ 登录成功!\n\n响应详情:\n${JSON.stringify(result, null, 2)}`;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.textContent = `❌ 登录失败\n\n响应详情:\n${JSON.stringify(result, null, 2)}`;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.textContent = `❌ 登录请求失败\n\n错误详情:\n${error.message}\n${error.stack}`;
                resultDiv.className = 'result error';
            }
        }

        // 检查本地存储
        function checkLocalStorage() {
            const resultDiv = document.getElementById('storageResult');
            resultDiv.style.display = 'block';

            const authToken = localStorage.getItem('auth_token');
            const authUser = localStorage.getItem('auth_user');
            const authExpire = localStorage.getItem('auth_expire');

            const storageInfo = {
                auth_token: authToken ? `${authToken.substring(0, 50)}...` : null,
                auth_user: authUser ? JSON.parse(authUser) : null,
                auth_expire: authExpire,
                localStorage_keys: Object.keys(localStorage),
                localStorage_length: localStorage.length
            };

            resultDiv.textContent = `本地存储状态:\n${JSON.stringify(storageInfo, null, 2)}`;
            resultDiv.className = 'result';
        }

        // 清除本地存储
        function clearLocalStorage() {
            localStorage.removeItem('auth_token');
            localStorage.removeItem('auth_user');
            localStorage.removeItem('auth_expire');
            localStorage.removeItem('auth_refresh_token');
            
            const resultDiv = document.getElementById('storageResult');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '✅ 已清除所有认证相关的本地存储';
            resultDiv.className = 'result success';
        }

        // 测试Token验证
        async function testTokenValidation() {
            const resultDiv = document.getElementById('tokenResult');
            const token = localStorage.getItem('auth_token');

            resultDiv.style.display = 'block';
            resultDiv.className = 'result';

            if (!token) {
                resultDiv.textContent = '❌ 没有找到Token，请先登录';
                resultDiv.className = 'result error';
                return;
            }

            resultDiv.textContent = '正在验证Token...';

            try {
                const response = await fetch('http://localhost:3001/api/auth/validate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    }
                });

                const data = await response.json();
                
                const result = {
                    status: response.status,
                    statusText: response.statusText,
                    body: data
                };

                if (data.success) {
                    resultDiv.textContent = `✅ Token验证成功!\n\n响应详情:\n${JSON.stringify(result, null, 2)}`;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.textContent = `❌ Token验证失败\n\n响应详情:\n${JSON.stringify(result, null, 2)}`;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.textContent = `❌ Token验证请求失败\n\n错误详情:\n${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // 页面加载时自动检查
        window.onload = function() {
            console.log('🔧 登录调试工具已加载');
            console.log('请按顺序测试各个功能');
        };
    </script>
</body>
</html>