<template>
  <div
    :class="[
      'modern-card',
      variant,
      {
        'hoverable': hoverable,
        'loading': loading,
        'clickable': clickable
      }
    ]"
    @click="handleClick"
  >
    <div v-if="loading" class="card-loader">
      <div class="loader-spinner"></div>
    </div>
    
    <div class="card-header" v-if="$slots.header || title">
      <slot name="header">
        <h3 v-if="title" class="card-title">{{ title }}</h3>
      </slot>
    </div>
    
    <div class="card-content">
      <slot></slot>
    </div>
    
    <div class="card-footer" v-if="$slots.footer">
      <slot name="footer"></slot>
    </div>
    
    <div class="card-glow"></div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  variant?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'
  hoverable?: boolean
  loading?: boolean
  clickable?: boolean
  title?: string
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  hoverable: true,
  loading: false,
  clickable: false
})

const emit = defineEmits<{
  click: [event: MouseEvent]
}>()

const handleClick = (event: MouseEvent) => {
  if (props.clickable && !props.loading) {
    emit('click', event)
  }
}
</script>

<style scoped>
.modern-card {
  position: relative;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(var(--blur-md));
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  transition: all var(--transition-duration) var(--transition-timing);
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

.modern-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.1) 0%, 
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.02) 100%);
  border-radius: inherit;
  pointer-events: none;
  z-index: 1;
}

.modern-card.hoverable:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: rgba(255, 255, 255, 0.2);
}

.modern-card.clickable {
  cursor: pointer;
}

.modern-card.clickable:active {
  transform: translateY(-2px);
}

/* Variants */
.modern-card.primary {
  border-color: rgba(var(--primary-rgb), 0.3);
  background: rgba(var(--primary-rgb), 0.05);
}

.modern-card.primary:hover {
  border-color: rgba(var(--primary-rgb), 0.5);
  box-shadow: 
    var(--shadow-lg),
    0 0 30px rgba(var(--primary-rgb), 0.2);
}

.modern-card.secondary {
  border-color: rgba(var(--secondary-rgb), 0.3);
  background: rgba(var(--secondary-rgb), 0.05);
}

.modern-card.secondary:hover {
  border-color: rgba(var(--secondary-rgb), 0.5);
  box-shadow: 
    var(--shadow-lg),
    0 0 30px rgba(var(--secondary-rgb), 0.2);
}

.modern-card.success {
  border-color: rgba(var(--success-rgb), 0.3);
  background: rgba(var(--success-rgb), 0.05);
}

.modern-card.success:hover {
  border-color: rgba(var(--success-rgb), 0.5);
  box-shadow: 
    var(--shadow-lg),
    0 0 30px rgba(var(--success-rgb), 0.2);
}

.modern-card.warning {
  border-color: rgba(var(--warning-rgb), 0.3);
  background: rgba(var(--warning-rgb), 0.05);
}

.modern-card.warning:hover {
  border-color: rgba(var(--warning-rgb), 0.5);
  box-shadow: 
    var(--shadow-lg),
    0 0 30px rgba(var(--warning-rgb), 0.2);
}

.modern-card.danger {
  border-color: rgba(var(--danger-rgb), 0.3);
  background: rgba(var(--danger-rgb), 0.05);
}

.modern-card.danger:hover {
  border-color: rgba(var(--danger-rgb), 0.5);
  box-shadow: 
    var(--shadow-lg),
    0 0 30px rgba(var(--danger-rgb), 0.2);
}

/* Loading state */
.modern-card.loading {
  pointer-events: none;
}

.card-loader {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(2px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: inherit;
}

.loader-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Card sections */
.card-header {
  position: relative;
  z-index: 2;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.card-title {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  text-shadow: var(--text-shadow);
}

.card-content {
  position: relative;
  z-index: 2;
  color: var(--text-secondary);
  line-height: 1.6;
}

.card-footer {
  position: relative;
  z-index: 2;
  margin-top: var(--spacing-md);
  padding-top: var(--spacing-sm);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Glow effect */
.card-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: inherit;
  border-radius: inherit;
  opacity: 0;
  filter: blur(20px);
  transition: opacity var(--transition-duration);
  z-index: -1;
}

.modern-card.hoverable:hover .card-glow {
  opacity: 0.3;
}

/* Shimmer effect */
.modern-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.1), 
    transparent);
  transition: left 0.6s;
  z-index: 1;
}

.modern-card.hoverable:hover::after {
  left: 100%;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
  .modern-card {
    padding: var(--spacing-md);
  }
  
  .card-title {
    font-size: var(--font-size-base);
  }
}
</style>