<template>
  <div class="battery-list-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <el-icon class="title-icon"><List /></el-icon>
            电池列表管理
          </h1>
          <p class="page-description">查看和管理所有电池信息</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="addBattery">
            <el-icon><Plus /></el-icon>
            添加电池
          </el-button>
          <el-button @click="batchImport">
            <el-icon><Upload /></el-icon>
            批量导入
          </el-button>
          <el-button @click="exportData">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
          <el-button @click="refreshData">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <div class="filter-row">
        <div class="filter-left">
          <el-input
            v-model="searchQuery"
            placeholder="搜索电池（ID、制造商、型号、序列号）"
            @input="handleSearch"
            clearable
            style="width: 400px"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
        <div class="filter-right">
          <el-select v-model="statusFilter" placeholder="健康状态" @change="handleFilter" style="width: 120px">
            <el-option label="全部状态" value="" />
            <el-option label="健康" value="good" />
            <el-option label="预警" value="warning" />
            <el-option label="异常" value="critical" />
          </el-select>
          <el-select v-model="manufacturerFilter" placeholder="制造商" @change="handleFilter" style="width: 120px">
            <el-option label="全部制造商" value="" />
            <el-option label="宁德时代" value="宁德时代" />
            <el-option label="比亚迪" value="比亚迪" />
            <el-option label="国轩高科" value="国轩高科" />
            <el-option label="中航锂电" value="中航锂电" />
          </el-select>
          <el-select v-model="typeFilter" placeholder="电池类型" @change="handleFilter" style="width: 120px">
            <el-option label="全部类型" value="" />
            <el-option label="NCM" value="NCM" />
            <el-option label="LFP" value="LFP" />
            <el-option label="LCO" value="LCO" />
            <el-option label="LMO" value="LMO" />
          </el-select>
          <el-button-group>
            <el-button 
              :type="viewMode === 'grid' ? 'primary' : ''"
              @click="viewMode = 'grid'"
              size="small"
            >
              <el-icon><Grid /></el-icon>
            </el-button>
            <el-button 
              :type="viewMode === 'table' ? 'primary' : ''"
              @click="viewMode = 'table'"
              size="small"
            >
              <el-icon><List /></el-icon>
            </el-button>
          </el-button-group>
        </div>
      </div>
    </div>

    <!-- 电池列表 -->
    <div class="list-content">
      <BatteryList 
        :batteries="filteredBatteries"
        :loading="loading"
        :view-mode="viewMode"
        @view-detail="viewBatteryDetail"
        @edit-battery="editBattery"
        @delete-battery="deleteBattery"
        @view-maintenance="viewMaintenance"
        @view-analysis="viewAnalysis"
      />
    </div>

    <!-- 分页 -->
    <div class="pagination-section">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="totalBatteries"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 电池详情对话框 -->
    <BatteryDetail
      v-model="showDetailDialog"
      :battery-id="selectedBatteryId"
      @close="showDetailDialog = false"
    />

    <!-- 电池表单对话框 -->
    <BatteryForm
      v-model="showFormDialog"
      :battery-id="editingBatteryId"
      :mode="formMode"
      @success="handleFormSuccess"
      @close="showFormDialog = false"
    />

    <!-- 批量导入对话框 -->
    <el-dialog
      v-model="showImportDialog"
      title="批量导入电池"
      width="500px"
    >
      <div class="import-content">
        <el-upload
          class="upload-demo"
          drag
          action="#"
          :auto-upload="false"
          :on-change="handleFileChange"
          accept=".xlsx,.xls,.csv"
        >
          <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
          <div class="el-upload__text">
            将文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              支持 xlsx/xls/csv 格式文件，文件大小不超过 10MB
            </div>
          </template>
        </el-upload>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showImportDialog = false">取消</el-button>
          <el-button type="primary" @click="handleImport">
            开始导入
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  List, Plus, Upload, Download, Refresh, Search,
  Grid, UploadFilled
} from '@element-plus/icons-vue'
import { useBatteryStore } from '@/stores/battery'
import BatteryList from '@/components/BatteryList.vue'
import BatteryDetail from '@/components/BatteryDetail.vue'
import BatteryForm from '@/components/BatteryForm.vue'

// Router
const router = useRouter()

// Store
const batteryStore = useBatteryStore()

// 响应式数据
const loading = ref(false)
const searchQuery = ref('')
const statusFilter = ref('')
const manufacturerFilter = ref('')
const typeFilter = ref('')
const viewMode = ref<'grid' | 'table'>('table')
const currentPage = ref(1)
const pageSize = ref(20)
const showDetailDialog = ref(false)
const showFormDialog = ref(false)
const showImportDialog = ref(false)
const selectedBatteryId = ref('')
const editingBatteryId = ref('')
const formMode = ref<'add' | 'edit'>('add')

// 计算属性
const filteredBatteries = computed(() => {
  let result = batteryStore.batteries
  
  // 搜索筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(battery => 
      battery.id.toLowerCase().includes(query) ||
      battery.manufacturer.toLowerCase().includes(query) ||
      battery.model.toLowerCase().includes(query) ||
      battery.serial_number.toLowerCase().includes(query)
    )
  }
  
  // 状态筛选
  if (statusFilter.value) {
    result = result.filter(battery => battery.health_status === statusFilter.value)
  }
  
  // 制造商筛选
  if (manufacturerFilter.value) {
    result = result.filter(battery => battery.manufacturer === manufacturerFilter.value)
  }
  
  // 类型筛选
  if (typeFilter.value) {
    result = result.filter(battery => battery.battery_type === typeFilter.value)
  }
  
  return result
})

const totalBatteries = computed(() => filteredBatteries.value.length)

const paginatedBatteries = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredBatteries.value.slice(start, end)
})

// 方法
const loadBatteries = async () => {
  loading.value = true
  try {
    await batteryStore.fetchBatteries()
  } catch (error) {
    ElMessage.error('加载电池数据失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  currentPage.value = 1
}

const handleFilter = () => {
  currentPage.value = 1
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
}

const addBattery = () => {
  formMode.value = 'add'
  editingBatteryId.value = ''
  showFormDialog.value = true
}

const editBattery = (batteryId: string) => {
  formMode.value = 'edit'
  editingBatteryId.value = batteryId
  showFormDialog.value = true
}

const viewBatteryDetail = (batteryId: string) => {
  selectedBatteryId.value = batteryId
  showDetailDialog.value = true
}

const deleteBattery = async (batteryId: string) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个电池记录吗？此操作不可恢复。',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // await batteryStore.deleteBattery(batteryId)
    ElMessage.success('电池删除成功')
    await loadBatteries()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除电池失败')
    }
  }
}

const viewMaintenance = (batteryId: string) => {
  router.push(`/battery-management/maintenance?batteryId=${batteryId}`)
}

const viewAnalysis = (batteryId: string) => {
  router.push(`/battery-management/analysis?batteryId=${batteryId}`)
}

const batchImport = () => {
  showImportDialog.value = true
}

const exportData = () => {
  ElMessage.info('数据导出功能开发中')
}

const refreshData = async () => {
  await loadBatteries()
  ElMessage.success('数据刷新成功')
}

const handleFormSuccess = async () => {
  showFormDialog.value = false
  await loadBatteries()
  ElMessage.success(formMode.value === 'add' ? '电池添加成功' : '电池更新成功')
}

const handleFileChange = (file: any) => {
  ElMessage.info(`选择文件: ${file.name}`)
}

const handleImport = () => {
  ElMessage.info('批量导入功能开发中')
  showImportDialog.value = false
}

// 生命周期
onMounted(() => {
  batteryStore.initializeMockData()
  loadBatteries()
})
</script>

<style scoped>
.battery-list-page {
  padding: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  position: relative;
}

.battery-list-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 50%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
  pointer-events: none;
}

/* 页面头部 */
.page-header {
  position: relative;
  z-index: 1;
  padding: 32px 24px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  color: white;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 36px;
  color: #fbbf24;
}

.page-description {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 筛选区域 */
.filter-section {
  position: relative;
  z-index: 1;
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.filter-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.filter-left {
  flex: 1;
}

.filter-right {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 列表内容 */
.list-content {
  position: relative;
  z-index: 1;
  padding: 0 24px 32px;
  max-width: 1200px;
  margin: 0 auto;
}

/* 分页 */
.pagination-section {
  position: relative;
  z-index: 1;
  padding: 0 24px 32px;
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: center;
}

.pagination-section :deep(.el-pagination) {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 12px;
  padding: 16px 24px;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* 导入对话框 */
.import-content {
  padding: 20px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .header-actions {
    flex-direction: column;
    gap: 8px;
    width: 100%;
  }

  .filter-row {
    flex-direction: column;
    gap: 16px;
  }

  .filter-left {
    width: 100%;
  }

  .filter-left .el-input {
    width: 100% !important;
  }

  .filter-right {
    width: 100%;
    flex-wrap: wrap;
    justify-content: space-between;
  }

  .filter-right .el-select {
    flex: 1;
    min-width: 100px;
  }
}
</style>
