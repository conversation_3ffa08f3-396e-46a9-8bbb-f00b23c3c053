/**
 * 移动端性能优化工具
 * 包括图片懒加载、虚拟滚动、内存管理等功能
 */

/**
 * 图片懒加载管理器
 */
export class LazyImageLoader {
  private observer: IntersectionObserver | null = null
  private images: Set<HTMLImageElement> = new Set()
  private options: IntersectionObserverInit
  
  constructor(options: IntersectionObserverInit = {}) {
    this.options = {
      root: null,
      rootMargin: '50px',
      threshold: 0.1,
      ...options
    }
    
    this.init()
  }
  
  private init() {
    if ('IntersectionObserver' in window) {
      this.observer = new IntersectionObserver(this.handleIntersection.bind(this), this.options)
    }
  }
  
  private handleIntersection(entries: IntersectionObserverEntry[]) {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const img = entry.target as HTMLImageElement
        this.loadImage(img)
        this.unobserve(img)
      }
    })
  }
  
  private loadImage(img: HTMLImageElement) {
    const src = img.dataset.src
    if (src) {
      // 创建新的图片对象进行预加载
      const newImg = new Image()
      
      newImg.onload = () => {
        img.src = src
        img.classList.remove('lazy-loading')
        img.classList.add('lazy-loaded')
        
        // 触发自定义事件
        img.dispatchEvent(new CustomEvent('lazyloaded', {
          detail: { src }
        }))
      }
      
      newImg.onerror = () => {
        img.classList.remove('lazy-loading')
        img.classList.add('lazy-error')
        
        // 设置错误占位图
        const errorSrc = img.dataset.errorSrc || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkVycm9yPC90ZXh0Pjwvc3ZnPg=='
        img.src = errorSrc
        
        img.dispatchEvent(new CustomEvent('lazyerror', {
          detail: { src, error: 'Failed to load image' }
        }))
      }
      
      newImg.src = src
    }
  }
  
  observe(img: HTMLImageElement) {
    if (this.observer && !this.images.has(img)) {
      this.images.add(img)
      img.classList.add('lazy-loading')
      this.observer.observe(img)
    } else if (!this.observer) {
      // 降级处理：直接加载图片
      this.loadImage(img)
    }
  }
  
  unobserve(img: HTMLImageElement) {
    if (this.observer && this.images.has(img)) {
      this.images.delete(img)
      this.observer.unobserve(img)
    }
  }
  
  disconnect() {
    if (this.observer) {
      this.observer.disconnect()
      this.images.clear()
    }
  }
}

/**
 * 虚拟滚动管理器
 */
export class VirtualScroller {
  private container: HTMLElement
  private itemHeight: number
  private bufferSize: number
  private items: any[] = []
  private visibleItems: any[] = []
  private startIndex = 0
  private endIndex = 0
  private scrollTop = 0
  private containerHeight = 0
  private totalHeight = 0
  private renderCallback: (items: any[], startIndex: number) => void
  
  constructor(
    container: HTMLElement,
    itemHeight: number,
    renderCallback: (items: any[], startIndex: number) => void,
    bufferSize = 5
  ) {
    this.container = container
    this.itemHeight = itemHeight
    this.bufferSize = bufferSize
    this.renderCallback = renderCallback
    
    this.init()
  }
  
  private init() {
    this.containerHeight = this.container.clientHeight
    this.container.addEventListener('scroll', this.handleScroll.bind(this), { passive: true })
    window.addEventListener('resize', this.handleResize.bind(this))
  }
  
  private handleScroll() {
    this.scrollTop = this.container.scrollTop
    this.updateVisibleItems()
  }
  
  private handleResize() {
    this.containerHeight = this.container.clientHeight
    this.updateVisibleItems()
  }
  
  private updateVisibleItems() {
    const visibleCount = Math.ceil(this.containerHeight / this.itemHeight)
    const startIndex = Math.floor(this.scrollTop / this.itemHeight)
    
    this.startIndex = Math.max(0, startIndex - this.bufferSize)
    this.endIndex = Math.min(this.items.length - 1, startIndex + visibleCount + this.bufferSize)
    
    this.visibleItems = this.items.slice(this.startIndex, this.endIndex + 1)
    
    // 调用渲染回调
    this.renderCallback(this.visibleItems, this.startIndex)
    
    // 更新容器高度
    this.updateContainerHeight()
  }
  
  private updateContainerHeight() {
    this.totalHeight = this.items.length * this.itemHeight
    
    // 创建或更新占位元素
    let spacer = this.container.querySelector('.virtual-spacer') as HTMLElement
    if (!spacer) {
      spacer = document.createElement('div')
      spacer.className = 'virtual-spacer'
      if (this.container) {
        this.container.appendChild(spacer)
      }
    }
    
    spacer.style.height = `${this.totalHeight}px`
    spacer.style.paddingTop = `${this.startIndex * this.itemHeight}px`
  }
  
  setItems(items: any[]) {
    this.items = items
    this.updateVisibleItems()
  }
  
  scrollToIndex(index: number) {
    const scrollTop = index * this.itemHeight
    this.container.scrollTop = scrollTop
  }
  
  destroy() {
    this.container.removeEventListener('scroll', this.handleScroll.bind(this))
    window.removeEventListener('resize', this.handleResize.bind(this))
  }
}

/**
 * 内存管理器
 */
export class MemoryManager {
  private static instance: MemoryManager
  private cache: Map<string, any> = new Map()
  private maxCacheSize: number
  private cleanupInterval: number
  private lastCleanup = Date.now()
  
  constructor(maxCacheSize = 100, cleanupInterval = 5 * 60 * 1000) {
    this.maxCacheSize = maxCacheSize
    this.cleanupInterval = cleanupInterval
    
    // 定期清理缓存
    setInterval(() => {
      this.cleanup()
    }, cleanupInterval)
  }
  
  static getInstance(): MemoryManager {
    if (!MemoryManager.instance) {
      MemoryManager.instance = new MemoryManager()
    }
    return MemoryManager.instance
  }
  
  set(key: string, value: any, ttl?: number) {
    const item = {
      value,
      timestamp: Date.now(),
      ttl: ttl ? Date.now() + ttl : null
    }
    
    this.cache.set(key, item)
    
    // 检查缓存大小
    if (this.cache.size > this.maxCacheSize) {
      this.evictOldest()
    }
  }
  
  get(key: string): any {
    const item = this.cache.get(key)
    
    if (!item) {
      return null
    }
    
    // 检查是否过期
    if (item.ttl && Date.now() > item.ttl) {
      this.cache.delete(key)
      return null
    }
    
    return item.value
  }
  
  delete(key: string) {
    this.cache.delete(key)
  }
  
  clear() {
    this.cache.clear()
  }
  
  private evictOldest() {
    let oldestKey = ''
    let oldestTime = Date.now()
    
    for (const [key, item] of this.cache) {
      if (item.timestamp < oldestTime) {
        oldestTime = item.timestamp
        oldestKey = key
      }
    }
    
    if (oldestKey) {
      this.cache.delete(oldestKey)
    }
  }
  
  private cleanup() {
    const now = Date.now()
    const keysToDelete: string[] = []
    
    for (const [key, item] of this.cache) {
      if (item.ttl && now > item.ttl) {
        keysToDelete.push(key)
      }
    }
    
    keysToDelete.forEach(key => this.cache.delete(key))
    this.lastCleanup = now
  }
  
  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxCacheSize,
      lastCleanup: this.lastCleanup
    }
  }
}

/**
 * 性能监控器
 */
export class PerformanceMonitor {
  private metrics: Map<string, number[]> = new Map()
  private observers: PerformanceObserver[] = []
  
  constructor() {
    this.init()
  }
  
  private init() {
    // 监控长任务
    if ('PerformanceObserver' in window) {
      try {
        const longTaskObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.recordMetric('longTask', entry.duration)
          }
        })
        longTaskObserver.observe({ entryTypes: ['longtask'] })
        this.observers.push(longTaskObserver)
      } catch (e) {
        console.warn('Long task observer not supported')
      }
      
      // 监控导航性能
      try {
        const navigationObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            const navEntry = entry as PerformanceNavigationTiming
            this.recordMetric('domContentLoaded', navEntry.domContentLoadedEventEnd - navEntry.domContentLoadedEventStart)
            this.recordMetric('loadComplete', navEntry.loadEventEnd - navEntry.loadEventStart)
          }
        })
        navigationObserver.observe({ entryTypes: ['navigation'] })
        this.observers.push(navigationObserver)
      } catch (e) {
        console.warn('Navigation observer not supported')
      }
    }
  }
  
  recordMetric(name: string, value: number) {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, [])
    }
    
    const values = this.metrics.get(name)!
    values.push(value)
    
    // 保持最近100个值
    if (values.length > 100) {
      values.shift()
    }
  }
  
  getMetric(name: string) {
    const values = this.metrics.get(name) || []
    
    if (values.length === 0) {
      return null
    }
    
    const sum = values.reduce((a, b) => a + b, 0)
    const avg = sum / values.length
    const min = Math.min(...values)
    const max = Math.max(...values)
    
    return { avg, min, max, count: values.length }
  }
  
  getAllMetrics() {
    const result: Record<string, any> = {}
    
    for (const [name] of this.metrics) {
      result[name] = this.getMetric(name)
    }
    
    return result
  }
  
  measureFunction<T extends (...args: any[]) => any>(name: string, fn: T): T {
    return ((...args: any[]) => {
      const start = performance.now()
      const result = fn(...args)
      const end = performance.now()
      
      this.recordMetric(name, end - start)
      
      return result
    }) as T
  }
  
  async measureAsync<T>(name: string, fn: () => Promise<T>): Promise<T> {
    const start = performance.now()
    const result = await fn()
    const end = performance.now()
    
    this.recordMetric(name, end - start)
    
    return result
  }
  
  destroy() {
    this.observers.forEach(observer => observer.disconnect())
    this.observers = []
    this.metrics.clear()
  }
}

/**
 * 资源预加载器
 */
export class ResourcePreloader {
  private loadedResources: Set<string> = new Set()
  private loadingPromises: Map<string, Promise<any>> = new Map()
  
  async preloadImage(src: string): Promise<HTMLImageElement> {
    if (this.loadedResources.has(src)) {
      const img = new Image()
      img.src = src
      return img
    }
    
    if (this.loadingPromises.has(src)) {
      return this.loadingPromises.get(src)!
    }
    
    const promise = new Promise<HTMLImageElement>((resolve, reject) => {
      const img = new Image()
      
      img.onload = () => {
        this.loadedResources.add(src)
        this.loadingPromises.delete(src)
        resolve(img)
      }
      
      img.onerror = () => {
        this.loadingPromises.delete(src)
        reject(new Error(`Failed to load image: ${src}`))
      }
      
      img.src = src
    })
    
    this.loadingPromises.set(src, promise)
    return promise
  }
  
  async preloadScript(src: string): Promise<void> {
    if (this.loadedResources.has(src)) {
      return
    }
    
    if (this.loadingPromises.has(src)) {
      return this.loadingPromises.get(src)!
    }
    
    const promise = new Promise<void>((resolve, reject) => {
      const script = document.createElement('script')
      
      script.onload = () => {
        this.loadedResources.add(src)
        this.loadingPromises.delete(src)
        resolve()
      }
      
      script.onerror = () => {
        this.loadingPromises.delete(src)
        reject(new Error(`Failed to load script: ${src}`))
      }
      
      script.src = src
      if (document.head) {
        document.head.appendChild(script)
      }
    })
    
    this.loadingPromises.set(src, promise)
    return promise
  }
  
  async preloadCSS(href: string): Promise<void> {
    if (this.loadedResources.has(href)) {
      return
    }
    
    if (this.loadingPromises.has(href)) {
      return this.loadingPromises.get(href)!
    }
    
    const promise = new Promise<void>((resolve, reject) => {
      const link = document.createElement('link')
      
      link.onload = () => {
        this.loadedResources.add(href)
        this.loadingPromises.delete(href)
        resolve()
      }
      
      link.onerror = () => {
        this.loadingPromises.delete(href)
        reject(new Error(`Failed to load CSS: ${href}`))
      }
      
      link.rel = 'stylesheet'
      link.href = href
      if (document.head) {
        document.head.appendChild(link)
      }
    })
    
    this.loadingPromises.set(href, promise)
    return promise
  }
  
  isLoaded(src: string): boolean {
    return this.loadedResources.has(src)
  }
  
  isLoading(src: string): boolean {
    return this.loadingPromises.has(src)
  }
}

/**
 * 防抖和节流工具
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: number | null = null
  
  return (...args: Parameters<T>) => {
    if (timeoutId !== null) {
      clearTimeout(timeoutId)
    }
    
    timeoutId = window.setTimeout(() => {
      func(...args)
      timeoutId = null
    }, delay)
  }
}

export function throttle<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let lastCall = 0
  
  return (...args: Parameters<T>) => {
    const now = Date.now()
    
    if (now - lastCall >= delay) {
      lastCall = now
      func(...args)
    }
  }
}

/**
 * 获取设备性能等级
 */
export function getDevicePerformanceLevel(): 'low' | 'medium' | 'high' {
  // 基于硬件并发数判断
  const cores = navigator.hardwareConcurrency || 2
  
  // 基于内存判断（如果可用）
  const memory = (navigator as any).deviceMemory || 4
  
  // 基于连接速度判断（如果可用）
  const connection = (navigator as any).connection
  const effectiveType = connection?.effectiveType || '4g'
  
  let score = 0
  
  // CPU 评分
  if (cores >= 8) score += 3
  else if (cores >= 4) score += 2
  else score += 1
  
  // 内存评分
  if (memory >= 8) score += 3
  else if (memory >= 4) score += 2
  else score += 1
  
  // 网络评分
  if (effectiveType === '4g') score += 2
  else if (effectiveType === '3g') score += 1
  
  if (score >= 7) return 'high'
  if (score >= 4) return 'medium'
  return 'low'
}

/**
 * 性能优化配置
 */
export function getOptimizedConfig() {
  const performanceLevel = getDevicePerformanceLevel()
  
  const configs = {
    low: {
      imageQuality: 0.7,
      maxConcurrentRequests: 2,
      enableAnimations: false,
      virtualScrollThreshold: 20,
      cacheSize: 50
    },
    medium: {
      imageQuality: 0.8,
      maxConcurrentRequests: 4,
      enableAnimations: true,
      virtualScrollThreshold: 50,
      cacheSize: 100
    },
    high: {
      imageQuality: 0.9,
      maxConcurrentRequests: 6,
      enableAnimations: true,
      virtualScrollThreshold: 100,
      cacheSize: 200
    }
  }
  
  return configs[performanceLevel]
}

// 导出单例实例
export const memoryManager = MemoryManager.getInstance()
export const performanceMonitor = new PerformanceMonitor()
export const resourcePreloader = new ResourcePreloader()