/**
 * Vercel deploy entry handler, for serverless deployment, please don't modify this file
 */
import type { VercelRequest, VercelResponse } from '@vercel/node';
import express from 'express'
import cors from 'cors'
import authRoutes from './routes/auth'
import userRoutes from './routes/users'
import vehicleRoutes from './routes/vehicles'
import chargingStationRoutes from './routes/charging-stations'
import chargingSessionRoutes from './routes/charging-sessions'
import batteryRoutes from './routes/batteries'
import carbonCreditRoutes from './routes/carbon-credits'
import drivingBehaviorRoutes from './routes/driving-behavior'
import fleetManagementRoutes from './routes/fleet-management'

const app = express()

// 中间件
app.use(cors())
app.use(express.json())
app.use(express.urlencoded({ extended: true }))

// 路由
app.use('/api/auth', authRoutes)
app.use('/api/users', userRoutes)
app.use('/api/vehicles', vehicleRoutes)
app.use('/api/charging-stations', chargingStationRoutes)
app.use('/api/charging-sessions', chargingSessionRoutes)
app.use('/api/batteries', batteryRoutes)
app.use('/api/carbon-credits', carbonCreditRoutes)
app.use('/api/driving-behavior', drivingBehaviorRoutes)
app.use('/api/fleet-management', fleetManagementRoutes)

// 健康检查
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    services: {
      database: 'connected',
      auth: 'active',
      api: 'running'
    }
  })
})

export default function handler(req: VercelRequest, res: VercelResponse) {
  return app(req, res);
}