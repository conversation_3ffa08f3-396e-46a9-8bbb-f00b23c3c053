<template>
  <div class="mobile-layout" :class="layoutClasses">
    <!-- 移动端顶部导航栏 -->
    <MobileNavbar
      v-if="showNavbar"
      :title="title"
      :show-back="showBack"
      :show-menu="showMenu"
      :actions="navActions"
      :auto-hide="autoHideNavbar"
      @back="handleBack"
      @menu-toggle="handleMenuToggle"
    />
    
    <!-- 侧边菜单 -->
    <div 
      v-if="showSidebar"
      class="sidebar-overlay"
      :class="{ 'overlay-visible': sidebarVisible }"
      @click="closeSidebar"
    >
      <div 
        class="sidebar"
        :class="{ 'sidebar-visible': sidebarVisible }"
        @click.stop
      >
        <div class="sidebar-header">
          <div class="user-info">
            <div class="user-avatar">
              <User class="avatar-icon" />
            </div>
            <div class="user-details">
              <h3 class="user-name">{{ userInfo.name || '用户' }}</h3>
              <p class="user-role">{{ userInfo.role || '车主' }}</p>
            </div>
          </div>
          <button class="close-button" @click="closeSidebar">
            <X class="close-icon" />
          </button>
        </div>
        
        <div class="sidebar-content">
          <nav class="sidebar-nav">
            <div 
              v-for="item in menuItems"
              :key="item.path"
              class="nav-item"
              :class="{ 'nav-active': isCurrentRoute(item.path) }"
              @click="navigateToPage(item.path)"
            >
              <component :is="item.icon" class="nav-icon" />
              <span class="nav-label">{{ item.label }}</span>
              <ChevronRight v-if="item.children" class="nav-arrow" />
            </div>
          </nav>
          
          <div class="sidebar-footer">
            <div class="app-info">
              <p class="app-version">版本 {{ appVersion }}</p>
              <p class="copyright">© 2025 新能源汽车管理系统</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 主内容区域 -->
    <main class="main-content" :style="mainContentStyle">
      <div class="content-wrapper">
        <slot></slot>
      </div>
      
      <!-- 回到顶部按钮 -->
      <Transition name="fade">
        <button 
          v-if="showBackToTop"
          class="back-to-top"
          @click="scrollToTop"
        >
          <ArrowUp class="back-to-top-icon" />
        </button>
      </Transition>
    </main>
    
    <!-- 移动端底部导航栏 -->
    <MobileTabbar
      v-if="showTabbar"
      :auto-hide="autoHideTabbar"
      :show-badges="showTabbarBadges"
    />
    
    <!-- 全局加载指示器 -->
    <Transition name="fade">
      <div v-if="loading" class="global-loading">
        <div class="loading-spinner">
          <div class="spinner"></div>
          <p class="loading-text">{{ loadingText }}</p>
        </div>
      </div>
    </Transition>
    
    <!-- 网络状态提示 -->
    <Transition name="slide-down">
      <div v-if="!isOnline" class="network-status">
        <WifiOff class="network-icon" />
        <span>网络连接已断开</span>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  User,
  ArrowUp,
  House,
  Connection
} from '@element-plus/icons-vue'
import { X, ChevronRight, WifiOff } from 'lucide-vue-next'
import { Battery, Zap, Car, Shield, Settings } from 'lucide-vue-next'
import MobileNavbar from './MobileNavbar.vue'
import MobileTabbar from './MobileTabbar.vue'
import { getDeviceType, DeviceType, throttle } from '../../utils/mobile'

interface NavAction {
  key: string
  icon: any
  handler: () => void
}

interface MenuItem {
  path: string
  label: string
  icon: any
  children?: MenuItem[]
}

interface UserInfo {
  name?: string
  role?: string
  avatar?: string
}

interface Props {
  title?: string
  showNavbar?: boolean
  showTabbar?: boolean
  showSidebar?: boolean
  showBack?: boolean
  showMenu?: boolean
  autoHideNavbar?: boolean
  autoHideTabbar?: boolean
  showTabbarBadges?: boolean
  navActions?: NavAction[]
  loading?: boolean
  loadingText?: string
}

const props = withDefaults(defineProps<Props>(), {
  title: '新能源汽车管理系统',
  showNavbar: true,
  showTabbar: true,
  showSidebar: true,
  showBack: false,
  showMenu: true,
  autoHideNavbar: false,
  autoHideTabbar: false,
  showTabbarBadges: true,
  navActions: () => [],
  loading: false,
  loadingText: '加载中...'
})

const emit = defineEmits<{
  back: []
  menuToggle: []
}>()

const router = useRouter()
const route = useRoute()

// 响应式数据
const sidebarVisible = ref(false)
const showBackToTop = ref(false)
const isOnline = ref(navigator.onLine)
const scrollY = ref(0)
const deviceType = ref(getDeviceType())
const appVersion = ref('1.0.0')

// 用户信息
const userInfo = ref<UserInfo>({
  name: '张三',
  role: '车主',
  avatar: ''
})

// 菜单项配置
const menuItems = ref<MenuItem[]>([
  {
    path: '/dashboard',
    label: '仪表板',
    icon: House
  },
  {
    path: '/battery',
    label: '电池管理',
    icon: Battery
  },
  {
    path: '/charging',
    label: '充电服务',
    icon: Zap
  },
  {
    path: '/adas',
    label: '驾驶辅助',
    icon: Car
  },
  {
    path: '/network',
    label: '车联网',
    icon: Connection
  },
  {
    path: '/security',
    label: '系统安全',
    icon: Shield
  },
  {
    path: '/settings',
    label: '设置',
    icon: Settings
  }
])

// 计算属性
const layoutClasses = computed(() => ({
  'mobile-device': deviceType.value === DeviceType.MOBILE,
  'tablet-device': deviceType.value === DeviceType.TABLET,
  'desktop-device': deviceType.value === DeviceType.DESKTOP,
  'sidebar-open': sidebarVisible.value,
  'offline': !isOnline.value
}))

const mainContentStyle = computed(() => {
  const navbarHeight = props.showNavbar ? '76px' : '0px' // 状态栏 + 导航栏
  const tabbarHeight = props.showTabbar ? '80px' : '0px' // 底部导航栏 + 安全区域
  
  return {
    paddingTop: navbarHeight,
    paddingBottom: tabbarHeight,
    minHeight: `calc(100vh - ${navbarHeight} - ${tabbarHeight})`
  }
})

// 方法
const handleBack = () => {
  emit('back')
}

const handleMenuToggle = () => {
  sidebarVisible.value = !sidebarVisible.value
  emit('menuToggle')
}

const closeSidebar = () => {
  sidebarVisible.value = false
}

const isCurrentRoute = (path: string): boolean => {
  return route.path === path || route.path.startsWith(path + '/')
}

const navigateToPage = (path: string) => {
  if (route.path !== path) {
    router.push(path)
  }
  closeSidebar()
}

const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}

// 滚动处理
const handleScroll = throttle(() => {
  scrollY.value = window.scrollY
  showBackToTop.value = scrollY.value > 300
}, 100)

// 网络状态处理
const handleOnline = () => {
  isOnline.value = true
}

const handleOffline = () => {
  isOnline.value = false
}

// 窗口大小变化处理
const handleResize = throttle(() => {
  deviceType.value = getDeviceType()
  
  // 在桌面端自动关闭侧边栏
  if (deviceType.value === DeviceType.DESKTOP) {
    sidebarVisible.value = false
  }
}, 250)

// 键盘事件处理
const handleKeydown = (e: KeyboardEvent) => {
  // ESC 键关闭侧边栏
  if (e.key === 'Escape' && sidebarVisible.value) {
    closeSidebar()
  }
}

// 生命周期
onMounted(() => {
  window.addEventListener('scroll', handleScroll, { passive: true })
  window.addEventListener('online', handleOnline)
  window.addEventListener('offline', handleOffline)
  window.addEventListener('resize', handleResize)
  window.addEventListener('keydown', handleKeydown)
  
  // 初始化设备类型
  deviceType.value = getDeviceType()
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
  window.removeEventListener('online', handleOnline)
  window.removeEventListener('offline', handleOffline)
  window.removeEventListener('resize', handleResize)
  window.removeEventListener('keydown', handleKeydown)
})

// 暴露方法给父组件
defineExpose({
  openSidebar: () => { sidebarVisible.value = true },
  closeSidebar,
  toggleSidebar: () => { sidebarVisible.value = !sidebarVisible.value },
  scrollToTop
})
</script>

<style scoped>
.mobile-layout {
  position: relative;
  min-height: 100vh;
  background: #f8fafc;
  overflow-x: hidden;
}

/* 侧边栏样式 */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1500;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.overlay-visible {
  opacity: 1;
  visibility: visible;
}

.sidebar {
  position: absolute;
  top: 0;
  left: 0;
  width: 280px;
  height: 100%;
  background: white;
  transform: translateX(-100%);
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  display: flex;
  flex-direction: column;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar-visible {
  transform: translateX(0);
}

.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-icon {
  width: 24px;
  height: 24px;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 4px 0;
}

.user-role {
  font-size: 12px;
  opacity: 0.8;
  margin: 0;
}

.close-button {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background 0.2s ease;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

.close-icon {
  width: 16px;
  height: 16px;
}

.sidebar-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.sidebar-nav {
  flex: 1;
  padding: 16px 0;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
}

.nav-item:hover {
  background: #f8fafc;
}

.nav-active {
  background: #f0f4ff;
  border-left-color: #667eea;
  color: #667eea;
}

.nav-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.nav-label {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
}

.nav-arrow {
  width: 16px;
  height: 16px;
  opacity: 0.5;
}

.sidebar-footer {
  padding: 20px;
  border-top: 1px solid #e5e7eb;
}

.app-info {
  text-align: center;
}

.app-version,
.copyright {
  font-size: 12px;
  color: #6b7280;
  margin: 4px 0;
}

/* 主内容区域 */
.main-content {
  position: relative;
  width: 100%;
  background: #f8fafc;
}

.content-wrapper {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  padding: 0;
}

/* 回到顶部按钮 */
.back-to-top {
  position: fixed;
  bottom: 100px;
  right: 20px;
  width: 48px;
  height: 48px;
  border: none;
  border-radius: 50%;
  background: #667eea;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
  z-index: 100;
}

.back-to-top:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}

.back-to-top-icon {
  width: 20px;
  height: 20px;
}

/* 全局加载指示器 */
.global-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  backdrop-filter: blur(4px);
}

.loading-spinner {
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

/* 网络状态提示 */
.network-status {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: #ff4757;
  color: white;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 14px;
  z-index: 1600;
}

.network-icon {
  width: 16px;
  height: 16px;
}

/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-down-enter-active,
.slide-down-leave-active {
  transition: transform 0.3s ease;
}

.slide-down-enter-from,
.slide-down-leave-to {
  transform: translateY(-100%);
}

/* 暗色模式适配 */
@media (prefers-color-scheme: dark) {
  .mobile-layout {
    background: #1a1a1a;
  }
  
  .sidebar {
    background: #2d2d2d;
    color: #e5e7eb;
  }
  
  .nav-item:hover {
    background: #374151;
  }
  
  .nav-active {
    background: #1e3a8a;
  }
  
  .sidebar-footer {
    border-top-color: #374151;
  }
  
  .main-content {
    background: #1a1a1a;
  }
  
  .global-loading {
    background: rgba(26, 26, 26, 0.9);
  }
}

/* 平板适配 */
@media screen and (min-width: 768px) and (max-width: 1024px) {
  .sidebar {
    width: 320px;
  }
  
  .content-wrapper {
    padding: 0 20px;
  }
}

/* 桌面端适配 */
@media screen and (min-width: 1024px) {
  .mobile-layout.desktop-device .sidebar {
    position: relative;
    transform: none;
    width: 260px;
    height: auto;
    box-shadow: none;
    border-right: 1px solid #e5e7eb;
  }
  
  .mobile-layout.desktop-device {
    display: flex;
  }
  
  .mobile-layout.desktop-device .main-content {
    flex: 1;
  }
  
  .mobile-layout.desktop-device .sidebar-overlay {
    position: relative;
    background: none;
    opacity: 1;
    visibility: visible;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .sidebar,
  .sidebar-overlay,
  .back-to-top,
  .nav-item,
  .close-button {
    transition: none;
  }
  
  .spinner {
    animation: none;
  }
}
</style>