import OpenAI from 'openai'
import fs from 'fs'
import path from 'path'
// import pdfParse from 'pdf-parse'
import { db } from '../config/mysql'

// 初始化OpenAI客户端
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY || '',
})

// 系统知识库 - 关于新能源汽车管理系统的详细信息
const SYSTEM_KNOWLEDGE = `
你是EVAdmin Pro新能源汽车智能综合管理系统的专属AI助手，名字叫"小E"。

## 系统概述
EVAdmin Pro是一个专业的新能源汽车智能综合管理系统，包含以下核心模块：

### 1. 智能电池管理
- 实时监控电池状态、健康度、温度、电压等关键参数
- 电池健康度分析和预测
- 充电优化建议和策略
- 电池维护记录管理
- 电池性能趋势分析

### 2. 智能充电服务
- 充电站地图和实时状态查看
- 充电站预约和管理
- 充电会话监控和记录
- 充电数据统计分析
- 充电价格管理和优惠策略

### 3. 车辆管理
- 车辆信息管理和档案
- 车辆实时状态监控
- 车辆维护记录和提醒
- 车辆性能分析
- 远程车辆控制

### 4. 高级驾驶辅助系统(ADAS)
- 碰撞预警系统
- 车道保持辅助
- 自适应巡航控制
- 盲点监测
- 自动紧急制动

### 5. 车联网交互中心
- 远程车辆控制
- 实时位置追踪
- 故障诊断和预警
- OTA升级管理
- 车辆数据同步

### 6. 用户生态服务
- 个人档案管理
- 驾驶行为分析
- 碳积分系统
- 社区互动功能
- 个性化推荐

### 7. 系统安全运维
- 数据安全保护
- 系统监控和告警
- 用户权限管理
- 安全审计日志

## 技术架构
- 前端：Vue 3 + TypeScript + Element Plus
- 后端：Node.js + Express + TypeScript
- 数据库：MySQL 8.0
- 实时通信：WebSocket
- 地图服务：高德地图API
- 部署：Docker容器化

## 使用指南
用户可以通过以下方式使用系统：
1. 登录系统后进入仪表板查看概览信息
2. 通过左侧导航菜单访问各个功能模块
3. 使用搜索功能快速查找信息
4. 通过AI助手获取帮助和建议

请始终以友好、专业的态度回答用户问题，并提供准确的系统操作指导。
`

// OpenAI服务类
export class OpenAIService {
  private systemPrompt: string

  constructor() {
    this.systemPrompt = SYSTEM_KNOWLEDGE
  }

  // 从数据库获取AI助手配置
  async getAIConfig() {
    try {
      const [rows] = await db.query(
        'SELECT * FROM ai_assistant_config WHERE is_active = TRUE ORDER BY id DESC LIMIT 1'
      ) as any[]

      if (rows.length > 0) {
        const config = rows[0]
        return {
          name: config.name,
          fullName: config.full_name,
          description: config.description,
          avatar: config.avatar_url,
          model: config.model,
          temperature: config.temperature,
          maxTokens: config.max_tokens,
          capabilities: config.capabilities ? JSON.parse(config.capabilities) : [],
          systemPrompt: config.system_prompt || this.systemPrompt
        }
      }

      // 返回默认配置
      return {
        name: '小E',
        fullName: 'EVAdmin Pro AI Assistant',
        description: '我是EVAdmin Pro的专属AI助手，专门为新能源汽车管理系统提供智能服务',
        avatar: '/images/ai-avatar.svg',
        model: 'gpt-4o',
        temperature: 0.7,
        maxTokens: 2000,
        capabilities: [
          '文本对话和问答',
          '文件内容分析',
          '图片和视频识别',
          '系统功能指导',
          '数据分析建议',
          '故障诊断帮助'
        ],
        systemPrompt: this.systemPrompt
      }
    } catch (error) {
      console.error('获取AI配置失败:', error)
      // 返回默认配置
      return {
        name: '小E',
        fullName: 'EVAdmin Pro AI Assistant',
        description: '我是EVAdmin Pro的专属AI助手',
        avatar: '/images/ai-avatar.svg',
        model: 'gpt-4o',
        temperature: 0.7,
        maxTokens: 2000,
        capabilities: [],
        systemPrompt: this.systemPrompt
      }
    }
  }

  // 从知识库搜索相关信息
  async searchKnowledge(query: string): Promise<string> {
    try {
      // 使用简单的LIKE搜索，避免全文索引问题
      const [rows] = await db.query(`
        SELECT question, answer, category
        FROM ai_knowledge_base
        WHERE is_active = TRUE
        AND (
          question LIKE CONCAT('%', ?, '%')
          OR answer LIKE CONCAT('%', ?, '%')
          OR keywords LIKE CONCAT('%', ?, '%')
        )
        ORDER BY priority DESC, id DESC
        LIMIT 3
      `, [query, query, query])

      if (Array.isArray(rows) && rows.length > 0) {
        return rows.map((row: any) =>
          `【${row.category}】${row.question}: ${row.answer}`
        ).join('\n\n')
      }

      return ''
    } catch (error) {
      console.error('搜索知识库失败:', error)
      return ''
    }
  }

  // 文本对话
  async chat(message: string, context: any[] = []): Promise<string> {
    try {
      // 获取AI配置
      const aiConfig = await this.getAIConfig()

      // 搜索相关知识
      const knowledgeContext = await this.searchKnowledge(message)

      // 构建系统提示词
      let systemPrompt = aiConfig.systemPrompt || this.systemPrompt
      if (knowledgeContext) {
        systemPrompt += `\n\n相关知识库信息：\n${knowledgeContext}`
      }

      // 构建对话历史
      const messages: any[] = [
        {
          role: 'system',
          content: systemPrompt
        }
      ]

      // 添加上下文对话历史
      context.forEach(msg => {
        if (msg.type === 'user') {
          messages.push({
            role: 'user',
            content: msg.content
          })
        } else if (msg.type === 'ai') {
          messages.push({
            role: 'assistant',
            content: msg.content
          })
        }
      })

      // 添加当前用户消息
      messages.push({
        role: 'user',
        content: message
      })

      const completion = await openai.chat.completions.create({
        model: aiConfig.model || 'gpt-4o',
        messages: messages,
        max_tokens: aiConfig.maxTokens || 2000,
        temperature: aiConfig.temperature || 0.7,
      })

      return completion.choices[0]?.message?.content || '抱歉，我无法生成回复。'
    } catch (error) {
      console.error('OpenAI聊天错误:', error)

      // 降级处理：使用本地知识库
      return await this.fallbackToLocalKnowledge(message)
    }
  }

  // 降级处理：使用本地知识库回答
  async fallbackToLocalKnowledge(query: string): Promise<string> {
    try {
      const knowledgeResult = await this.searchKnowledge(query)

      if (knowledgeResult) {
        return `基于系统知识库的回答：\n\n${knowledgeResult}\n\n💡 提示：当前使用本地知识库，如需更智能的回答，请检查网络连接或联系管理员。`
      }

      // 如果没有找到相关知识，返回通用回答
      return this.getGenericResponse(query)
    } catch (error) {
      console.error('本地知识库查询失败:', error)
      return '抱歉，我暂时无法回答您的问题。请稍后再试或联系技术支持。'
    }
  }

  // 通用回答生成
  private getGenericResponse(query: string): string {
    const lowerQuery = query.toLowerCase()

    if (lowerQuery.includes('电池') || lowerQuery.includes('battery')) {
      return '关于电池管理，您可以在"智能电池管理"页面查看电池状态、健康度、温度等信息。如需详细帮助，请查看系统帮助文档或联系技术支持。'
    }

    if (lowerQuery.includes('充电') || lowerQuery.includes('charge')) {
      return '关于充电服务，您可以在"智能充电服务"页面查找充电站、预约充电、查看充电记录等。如需详细帮助，请查看系统帮助文档。'
    }

    if (lowerQuery.includes('车辆') || lowerQuery.includes('vehicle')) {
      return '关于车辆管理，您可以在"车辆管理"页面查看车辆信息、状态监控、维护记录等。如需详细帮助，请查看系统帮助文档。'
    }

    if (lowerQuery.includes('系统') || lowerQuery.includes('功能')) {
      return 'EVAdmin Pro是新能源汽车智能综合管理系统，包含智能电池管理、智能充电服务、车辆管理、ADAS系统、车联网交互、用户生态服务等核心功能。您可以通过左侧导航菜单访问各个功能模块。'
    }

    return '您好！我是EVAdmin Pro的AI助手小E。我可以帮您解答关于新能源汽车管理系统的问题。请尝试询问关于电池管理、充电服务、车辆管理等方面的问题，或者查看系统帮助文档获取更多信息。'
  }

  // 分析文件（支持多种格式）
  async analyzeFile(filePath: string, fileName: string, mimeType: string, question: string): Promise<string> {
    try {
      let fileContent = ''

      // 根据文件类型读取内容
      if (mimeType === 'application/pdf') {
        // PDF文件处理 - 暂时不支持，返回提示
        fileContent = '抱歉，PDF文件分析功能正在开发中，请上传文本文件或图片文件。'
      } else if (mimeType.startsWith('text/') || mimeType === 'application/json') {
        // 文本文件处理
        fileContent = fs.readFileSync(filePath, 'utf-8')
      } else {
        throw new Error(`不支持的文件类型: ${mimeType}`)
      }

      // 获取AI配置
      const aiConfig = await this.getAIConfig()

      // 搜索相关知识
      const knowledgeContext = await this.searchKnowledge(question)

      // 构建系统提示词
      let systemPrompt = aiConfig.systemPrompt || this.systemPrompt
      if (knowledgeContext) {
        systemPrompt += `\n\n相关知识库信息：\n${knowledgeContext}`
      }

      const prompt = `
请分析以下文件内容，并回答用户的问题。

文件名：${fileName}
文件类型：${mimeType}

文件内容：
${fileContent.substring(0, 8000)} ${fileContent.length > 8000 ? '...(内容已截断)' : ''}

用户问题：${question}

请提供详细的分析和回答。如果文件内容与新能源汽车管理系统相关，请结合系统知识进行专业解答。
`

      const completion = await openai.chat.completions.create({
        model: aiConfig.model || 'gpt-4o',
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: aiConfig.maxTokens || 2000,
        temperature: aiConfig.temperature || 0.7,
      })

      return completion.choices[0]?.message?.content || '抱歉，我无法分析这个文件。'
    } catch (error) {
      console.error('OpenAI文件分析错误:', error)
      throw new Error('文件分析服务暂时不可用')
    }
  }

  // 分析图片
  async analyzeImage(imageBase64: string, question: string): Promise<string> {
    try {
      const completion = await openai.chat.completions.create({
        model: 'gpt-4o',
        messages: [
          {
            role: 'system',
            content: this.systemPrompt
          },
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: `请分析这张图片并回答问题：${question}`
              },
              {
                type: 'image_url',
                image_url: {
                  url: `data:image/jpeg;base64,${imageBase64}`
                }
              }
            ]
          }
        ],
        max_tokens: parseInt(process.env.OPENAI_MAX_TOKENS || '2000'),
      })

      return completion.choices[0]?.message?.content || '抱歉，我无法分析这张图片。'
    } catch (error) {
      console.error('OpenAI图片分析错误:', error)
      throw new Error('图片分析服务暂时不可用')
    }
  }

  // 检查API状态
  async checkStatus(): Promise<boolean> {
    try {
      const completion = await openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [{ role: 'user', content: 'Hello' }],
        max_tokens: 10,
      })
      return !!completion.choices[0]?.message?.content
    } catch (error) {
      console.error('OpenAI状态检查错误:', error)
      return false
    }
  }
}

export const openaiService = new OpenAIService()
