<template>
  <div :class="['modern-loader', variant, size, { 'overlay': overlay }]">
    <div v-if="overlay" class="loader-backdrop" @click="handleBackdropClick"></div>
    
    <div class="loader-container">
      <!-- Spinner loader -->
      <div v-if="type === 'spinner'" class="spinner-loader">
        <div class="spinner"></div>
      </div>
      
      <!-- Dots loader -->
      <div v-else-if="type === 'dots'" class="dots-loader">
        <div class="dot" v-for="i in 3" :key="i"></div>
      </div>
      
      <!-- Pulse loader -->
      <div v-else-if="type === 'pulse'" class="pulse-loader">
        <div class="pulse-ring" v-for="i in 3" :key="i"></div>
      </div>
      
      <!-- Wave loader -->
      <div v-else-if="type === 'wave'" class="wave-loader">
        <div class="wave-bar" v-for="i in 5" :key="i"></div>
      </div>
      
      <!-- Orbit loader -->
      <div v-else-if="type === 'orbit'" class="orbit-loader">
        <div class="orbit-center"></div>
        <div class="orbit-ring">
          <div class="orbit-dot"></div>
        </div>
      </div>
      
      <!-- Progress loader -->
      <div v-else-if="type === 'progress'" class="progress-loader">
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: `${progress}%` }"></div>
        </div>
        <div v-if="showProgress" class="progress-text">{{ progress }}%</div>
      </div>
      
      <!-- Text -->
      <div v-if="text" class="loader-text">
        {{ text }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  type?: 'spinner' | 'dots' | 'pulse' | 'wave' | 'orbit' | 'progress'
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger'
  size?: 'small' | 'medium' | 'large'
  overlay?: boolean
  text?: string
  progress?: number
  showProgress?: boolean
  dismissible?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'spinner',
  variant: 'primary',
  size: 'medium',
  overlay: false,
  progress: 0,
  showProgress: false,
  dismissible: false
})

const emit = defineEmits<{
  dismiss: []
}>()

const handleBackdropClick = () => {
  if (props.dismissible) {
    emit('dismiss')
  }
}
</script>

<style scoped>
.modern-loader {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.modern-loader.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
}

.loader-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.loader-container {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
}

/* Spinner Loader */
.spinner-loader {
  display: flex;
  align-items: center;
  justify-content: center;
}

.spinner {
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.modern-loader.primary .spinner {
  border-top-color: var(--primary-color);
  box-shadow: 0 0 20px rgba(var(--primary-rgb), 0.3);
}

.modern-loader.secondary .spinner {
  border-top-color: var(--secondary-color);
  box-shadow: 0 0 20px rgba(var(--secondary-rgb), 0.3);
}

.modern-loader.success .spinner {
  border-top-color: var(--success-color);
  box-shadow: 0 0 20px rgba(var(--success-rgb), 0.3);
}

.modern-loader.warning .spinner {
  border-top-color: var(--warning-color);
  box-shadow: 0 0 20px rgba(var(--warning-rgb), 0.3);
}

.modern-loader.danger .spinner {
  border-top-color: var(--danger-color);
  box-shadow: 0 0 20px rgba(var(--danger-rgb), 0.3);
}

/* Dots Loader */
.dots-loader {
  display: flex;
  gap: var(--spacing-xs);
}

.dot {
  width: 8px;
  height: 8px;
  background: var(--primary-color);
  border-radius: 50%;
  animation: dotPulse 1.4s ease-in-out infinite both;
  box-shadow: 0 0 10px currentColor;
}

.dot:nth-child(1) { animation-delay: -0.32s; }
.dot:nth-child(2) { animation-delay: -0.16s; }
.dot:nth-child(3) { animation-delay: 0s; }

/* Pulse Loader */
.pulse-loader {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pulse-ring {
  position: absolute;
  border: 2px solid var(--primary-color);
  border-radius: 50%;
  animation: pulseRing 2s ease-out infinite;
}

.pulse-ring:nth-child(1) { animation-delay: 0s; }
.pulse-ring:nth-child(2) { animation-delay: 0.7s; }
.pulse-ring:nth-child(3) { animation-delay: 1.4s; }

/* Wave Loader */
.wave-loader {
  display: flex;
  align-items: end;
  gap: 2px;
}

.wave-bar {
  width: 4px;
  background: var(--primary-color);
  border-radius: 2px;
  animation: waveBar 1.2s ease-in-out infinite;
  box-shadow: 0 0 10px currentColor;
}

.wave-bar:nth-child(1) { animation-delay: 0s; }
.wave-bar:nth-child(2) { animation-delay: 0.1s; }
.wave-bar:nth-child(3) { animation-delay: 0.2s; }
.wave-bar:nth-child(4) { animation-delay: 0.3s; }
.wave-bar:nth-child(5) { animation-delay: 0.4s; }

/* Orbit Loader */
.orbit-loader {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.orbit-center {
  width: 8px;
  height: 8px;
  background: var(--primary-color);
  border-radius: 50%;
  box-shadow: 0 0 15px currentColor;
}

.orbit-ring {
  position: absolute;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: orbitRotate 2s linear infinite;
}

.orbit-dot {
  position: absolute;
  top: -3px;
  left: 50%;
  transform: translateX(-50%);
  width: 6px;
  height: 6px;
  background: var(--primary-color);
  border-radius: 50%;
  box-shadow: 0 0 10px currentColor;
}

/* Progress Loader */
.progress-loader {
  width: 100%;
  max-width: 200px;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
  border-radius: 3px;
  transition: width 0.3s ease;
  box-shadow: 0 0 10px rgba(var(--primary-rgb), 0.5);
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: progressShimmer 2s infinite;
}

.progress-text {
  text-align: center;
  margin-top: var(--spacing-xs);
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  text-shadow: var(--text-shadow);
}

/* Loader Text */
.loader-text {
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  text-shadow: var(--text-shadow);
  text-align: center;
}

/* Sizes */
.modern-loader.small .spinner { width: 20px; height: 20px; }
.modern-loader.medium .spinner { width: 32px; height: 32px; }
.modern-loader.large .spinner { width: 48px; height: 48px; }

.modern-loader.small .pulse-ring { width: 20px; height: 20px; }
.modern-loader.medium .pulse-ring { width: 32px; height: 32px; }
.modern-loader.large .pulse-ring { width: 48px; height: 48px; }

.modern-loader.small .orbit-ring { width: 24px; height: 24px; }
.modern-loader.medium .orbit-ring { width: 40px; height: 40px; }
.modern-loader.large .orbit-ring { width: 56px; height: 56px; }

.modern-loader.small .wave-bar { height: 16px; }
.modern-loader.medium .wave-bar { height: 24px; }
.modern-loader.large .wave-bar { height: 32px; }

/* Animations */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes dotPulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes pulseRing {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

@keyframes waveBar {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}

@keyframes orbitRotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes progressShimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}
</style>