const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

// 系统知识库数据
const knowledgeData = [
  // 系统架构相关
  {
    category: '系统架构',
    question: '系统使用了什么技术栈',
    answer: '系统采用现代化技术栈：前端使用Vue 3 + TypeScript + Element Plus，后端使用Node.js + Express + TypeScript，数据库使用MySQL 8.0，支持实时通信WebSocket，集成高德地图API，采用Docker容器化部署。',
    keywords: '技术栈,Vue,Node.js,MySQL,架构',
    priority: 5
  },
  {
    category: '系统架构',
    question: '系统有哪些核心模块',
    answer: '系统包含六大核心模块：1. 智能电池管理 - 电池监控和优化 2. 智能充电服务 - 充电站管理和预约 3. 车辆管理 - 车辆信息和状态监控 4. ADAS系统 - 高级驾驶辅助 5. 车联网交互 - 远程控制和诊断 6. 用户生态 - 个性化服务和社区',
    keywords: '模块,功能,电池,充电,车辆,ADAS',
    priority: 5
  },
  
  // 电池管理相关
  {
    category: '电池管理',
    question: '电池健康度如何计算',
    answer: '电池健康度通过多个参数综合计算：1. 电池容量衰减率 2. 充放电循环次数 3. 电池内阻变化 4. 温度影响因素 5. 充电效率等。系统实时监控这些参数并给出0-100的健康度评分。',
    keywords: '电池,健康度,计算,容量,循环',
    priority: 5
  },
  {
    category: '电池管理',
    question: '电池温度异常怎么处理',
    answer: '电池温度异常处理：1. 立即停止快速充电 2. 将车辆停放在阴凉处 3. 开启电池冷却系统 4. 避免高负荷驾驶 5. 温度持续异常请联系服务中心。系统会自动发送温度告警。',
    keywords: '电池,温度,异常,冷却,告警',
    priority: 5
  },
  
  // 充电服务相关
  {
    category: '充电服务',
    question: '如何查找充电站',
    answer: '查找充电站方法：1. 进入"智能充电服务"页面 2. 点击"充电站地图"查看附近充电站 3. 可按距离、价格、充电速度筛选 4. 实时查看充电桩状态和可用性 5. 支持预约功能',
    keywords: '充电站,地图,查找,预约,状态',
    priority: 5
  },
  {
    category: '充电服务',
    question: '充电费用如何计算',
    answer: '充电费用包括：1. 电费：按实际用电量×电价计算 2. 服务费：充电站运营费用 3. 停车费：部分充电站收取 4. 会员优惠：VIP用户享受折扣。充电前会显示预估费用。',
    keywords: '充电,费用,计算,电费,服务费',
    priority: 4
  },
  
  // 车辆管理相关
  {
    category: '车辆管理',
    question: '如何添加新车辆',
    answer: '添加车辆步骤：1. 进入"车辆管理"页面 2. 点击"添加车辆"按钮 3. 填写车辆基本信息（品牌、型号、VIN码等）4. 上传车辆照片和证件 5. 绑定电池信息 6. 保存并激活',
    keywords: '车辆,添加,管理,VIN,绑定',
    priority: 4
  },
  {
    category: '车辆管理',
    question: '车辆保养提醒如何设置',
    answer: '保养提醒设置：1. 在车辆详情页面点击"保养设置" 2. 设置保养周期（按里程或时间）3. 选择提醒方式（系统通知、短信、邮件）4. 设置提前提醒天数 5. 可预约服务网点',
    keywords: '保养,提醒,设置,周期,预约',
    priority: 4
  },
  
  // ADAS系统相关
  {
    category: 'ADAS系统',
    question: 'ADAS功能如何开启',
    answer: 'ADAS功能开启：1. 进入"ADAS系统"页面 2. 选择要开启的功能（碰撞预警、车道保持等）3. 调整灵敏度参数 4. 进行功能测试 5. 保存配置。建议在安全环境下测试功能。',
    keywords: 'ADAS,开启,配置,测试,安全',
    priority: 4
  },
  
  // 用户生态相关
  {
    category: '用户生态',
    question: '碳积分如何获得和使用',
    answer: '碳积分获得方式：1. 绿色出行使用电动车 2. 节能驾驶保持良好习惯 3. 低碳时段充电 4. 参与环保活动。使用方式：1. 兑换充电优惠券 2. 购买商城商品 3. 参与抽奖活动',
    keywords: '碳积分,获得,使用,环保,优惠',
    priority: 4
  },
  
  // 系统使用相关
  {
    category: '系统使用',
    question: '如何切换主题和个性化设置',
    answer: '个性化设置：1. 点击右上角用户头像进入个人中心 2. 在"偏好设置"中选择主题（明亮/暗黑）3. 设置语言和时区 4. 配置通知偏好 5. 自定义仪表板布局',
    keywords: '主题,个性化,设置,偏好,仪表板',
    priority: 3
  },
  
  // 故障处理相关
  {
    category: '故障处理',
    question: '系统出现错误如何解决',
    answer: '故障处理步骤：1. 查看错误提示信息 2. 检查网络连接状态 3. 尝试刷新页面或重新登录 4. 查看系统状态页面 5. 联系技术支持（提供错误截图和操作步骤）',
    keywords: '故障,错误,解决,支持,网络',
    priority: 4
  }
];

async function buildKnowledgeBase() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: '123456',
    database: 'ev_management'
  });
  
  try {
    console.log('🔄 开始构建AI知识库...');
    
    // 清空现有知识库（可选）
    await connection.execute('DELETE FROM ai_knowledge_base WHERE priority < 10');
    console.log('清理旧数据完成');
    
    // 插入新的知识库数据
    for (const item of knowledgeData) {
      await connection.execute(
        'INSERT INTO ai_knowledge_base (category, question, answer, keywords, priority) VALUES (?, ?, ?, ?, ?)',
        [item.category, item.question, item.answer, item.keywords, item.priority]
      );
      console.log(`✅ 添加知识: ${item.question}`);
    }
    
    // 统计知识库数据
    const [countResult] = await connection.execute(
      'SELECT category, COUNT(*) as count FROM ai_knowledge_base GROUP BY category'
    );

    console.log('\n📊 知识库统计:');
    console.table(countResult);

    const [totalResult] = await connection.execute(
      'SELECT COUNT(*) as total FROM ai_knowledge_base'
    );
    
    console.log(`\n🎉 知识库构建完成！总计 ${totalResult[0].total} 条知识条目`);
    
  } catch (error) {
    console.error('❌ 构建知识库失败:', error.message);
  } finally {
    await connection.end();
  }
}

buildKnowledgeBase();
