const mysql = require('mysql2/promise');
const bcrypt = require('bcrypt');

async function migrateDatabase() {
  let connection;
  
  try {
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '123456',
      database: 'ev_management'
    });
    
    console.log('连接到数据库成功');
    
    // 检查password_hash字段是否已存在
    const [columns] = await connection.execute(
      "SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = 'ev_management' AND TABLE_NAME = 'users' AND COLUMN_NAME = 'password_hash'"
    );
    
    if (columns.length > 0) {
      console.log('password_hash字段已存在，跳过迁移');
      return;
    }
    
    // 添加password_hash字段
    await connection.execute('ALTER TABLE users ADD COLUMN password_hash VARCHAR(255)');
    console.log('添加password_hash字段成功');
    
    // 为现有用户生成默认密码hash (密码: 123456)
    const defaultPassword = '123456';
    const saltRounds = 10;
    const passwordHash = await bcrypt.hash(defaultPassword, saltRounds);
    
    await connection.execute(
      'UPDATE users SET password_hash = ? WHERE password_hash IS NULL',
      [passwordHash]
    );
    console.log('为现有用户设置默认密码成功');
    
    // 设置password_hash为NOT NULL
    await connection.execute('ALTER TABLE users MODIFY COLUMN password_hash VARCHAR(255) NOT NULL');
    console.log('设置password_hash为NOT NULL成功');
    
    // 创建邮箱索引（如果不存在）
    try {
      await connection.execute('CREATE INDEX idx_users_email ON users(email)');
      console.log('创建邮箱索引成功');
    } catch (error) {
      if (error.code === 'ER_DUP_KEYNAME') {
        console.log('邮箱索引已存在，跳过创建');
      } else {
        throw error;
      }
    }
    
    // 显示表结构
    const [tableInfo] = await connection.execute('DESCRIBE users');
    console.log('\n用户表结构:');
    console.table(tableInfo);
    
    console.log('\n数据库迁移完成！');
    console.log('默认密码: 123456');
    console.log('建议用户首次登录后修改密码');
    
  } catch (error) {
    console.error('数据库迁移失败:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('数据库连接已关闭');
    }
  }
}

// 执行迁移
migrateDatabase();