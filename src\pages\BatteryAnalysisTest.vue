<template>
  <div class="test-page">
    <h1>电池健康度分析 - 测试页面</h1>
    <p>这是一个简单的测试页面，用于验证路由和页面渲染是否正常工作。</p>
    
    <div class="test-content">
      <h2>页面信息</h2>
      <ul>
        <li>当前路由: {{ $route.path }}</li>
        <li>页面加载时间: {{ loadTime }}</li>
        <li>Vue版本: {{ vueVersion }}</li>
      </ul>
      
      <h2>导航测试</h2>
      <div class="nav-buttons">
        <button @click="goToMonitoring" class="btn">前往监控页面</button>
        <button @click="goToMaintenance" class="btn">前往维护页面</button>
        <button @click="refreshPage" class="btn">刷新页面</button>
      </div>
      
      <h2>功能测试</h2>
      <div class="test-section">
        <p>计数器: {{ counter }}</p>
        <button @click="increment" class="btn">增加</button>
        <button @click="decrement" class="btn">减少</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const loadTime = ref('')
const vueVersion = ref('3.x')
const counter = ref(0)

onMounted(() => {
  loadTime.value = new Date().toLocaleString()
  console.log('测试页面已加载')
})

const goToMonitoring = () => {
  router.push('/battery-management/monitoring')
}

const goToMaintenance = () => {
  router.push('/battery-management/maintenance')
}

const refreshPage = () => {
  window.location.reload()
}

const increment = () => {
  counter.value++
}

const decrement = () => {
  counter.value--
}
</script>

<style scoped>
.test-page {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
  background: white;
  min-height: 100vh;
  font-family: Arial, sans-serif;
}

h1 {
  color: #409eff;
  margin-bottom: 20px;
  font-size: 28px;
}

h2 {
  color: #333;
  margin: 20px 0 10px 0;
  font-size: 20px;
}

p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 15px;
}

ul {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 5px;
  margin-bottom: 20px;
}

li {
  margin-bottom: 5px;
  color: #333;
}

.test-content {
  margin-top: 30px;
}

.nav-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  margin-bottom: 20px;
}

.test-section {
  background: #f9f9f9;
  padding: 15px;
  border-radius: 5px;
  margin-bottom: 20px;
}

.btn {
  background: #409eff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.btn:hover {
  background: #66b1ff;
}

.btn:active {
  background: #3a8ee6;
}
</style>
