import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'

const isDark = ref(false)

// 主题CSS变量配置
const lightTheme = {
  // 背景色
  '--bg-primary': 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%)',
  '--bg-secondary': 'rgba(248, 250, 252, 0.8)',
  '--bg-tertiary': '#f1f5f9',
  '--bg-glass': 'rgba(255, 255, 255, 0.1)',
  '--bg-card': 'rgba(255, 255, 255, 0.9)',
  '--bg-card-hover': 'rgba(255, 255, 255, 0.95)',
  '--bg-header': 'rgba(248, 250, 252, 0.95)',
  '--bg-footer': 'linear-gradient(135deg, #f8fafc, #e2e8f0)',
  '--bg-tag': 'rgba(59, 130, 246, 0.1)',
  
  // 文字颜色
  '--text-primary': '#1f2937',
  '--text-secondary': '#6b7280',
  '--text-tertiary': '#9ca3af',
  '--white': '#1f2937',
  
  // 边框颜色
  '--border-primary': '#e5e7eb',
  '--border-secondary': '#d1d5db',
  '--border-glass': 'rgba(255, 255, 255, 0.2)',
  
  // 阴影颜色
  '--shadow-primary': 'rgba(59, 130, 246, 0.1)',
  '--shadow-secondary': 'rgba(16, 185, 129, 0.1)',
  
  // 灰色系
  '--gray-50': '#f9fafb',
  '--gray-100': '#f3f4f6',
  '--gray-200': '#e5e7eb',
  '--gray-300': '#d1d5db',
  '--gray-400': '#9ca3af',
  '--gray-500': '#6b7280',
  '--gray-600': '#4b5563',
  '--gray-700': '#374151',
  '--gray-800': '#1f2937',
  '--gray-900': '#111827',
  
  // 阴影
  '--shadow-sm': '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  '--shadow-md': '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
  '--shadow-lg': '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
  '--shadow-xl': '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
}

const darkTheme = {
  // 背景色
  '--bg-primary': 'linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%)',
  '--bg-secondary': 'rgba(30, 41, 59, 0.8)',
  '--bg-tertiary': '#16213e',
  '--bg-glass': 'rgba(255, 255, 255, 0.1)',
  '--bg-card': 'rgba(255, 255, 255, 0.05)',
  '--bg-card-hover': 'rgba(255, 255, 255, 0.1)',
  '--bg-header': 'rgba(15, 23, 42, 0.95)',
  '--bg-footer': 'linear-gradient(135deg, #0f172a, #1e293b)',
  '--bg-tag': 'rgba(255, 255, 255, 0.1)',
  
  // 文字颜色
  '--text-primary': '#ffffff',
  '--text-secondary': '#e2e8f0',
  '--text-tertiary': '#94a3b8',
  '--white': '#ffffff',
  
  // 阴影颜色
  '--shadow-primary': 'rgba(59, 130, 246, 0.2)',
  '--shadow-secondary': 'rgba(16, 185, 129, 0.2)',
  
  // 边框颜色
  '--border-primary': 'rgba(255, 255, 255, 0.1)',
  '--border-secondary': 'rgba(255, 255, 255, 0.05)',
  '--border-glass': 'rgba(255, 255, 255, 0.2)',
  
  // 灰色系
  '--gray-50': '#1e293b',
  '--gray-100': '#334155',
  '--gray-200': '#475569',
  '--gray-300': '#64748b',
  '--gray-400': '#94a3b8',
  '--gray-500': '#cbd5e1',
  '--gray-600': '#e2e8f0',
  '--gray-700': '#f1f5f9',
  '--gray-800': '#f8fafc',
  '--gray-900': '#ffffff',
  
  // 阴影
  '--shadow-sm': '0 1px 2px 0 rgba(0, 0, 0, 0.3)',
  '--shadow-md': '0 4px 6px -1px rgba(0, 0, 0, 0.4)',
  '--shadow-lg': '0 10px 15px -3px rgba(0, 0, 0, 0.5)',
  '--shadow-xl': '0 20px 25px -5px rgba(0, 0, 0, 0.6)',
}

export function useTheme() {
  const toggleTheme = () => {
    isDark.value = !isDark.value
    updateTheme()
    
    // 显示切换提示
    ElMessage.success(isDark.value ? '已切换到深色模式' : '已切换到亮色模式')
  }

  const updateTheme = () => {
    const root = document.documentElement
    const themeVars = isDark.value ? darkTheme : lightTheme
    
    // 应用CSS变量
    Object.entries(themeVars).forEach(([key, value]) => {
      root.style.setProperty(key, value)
    })
    
    // 更新类名
    if (isDark.value) {
      root.classList.add('dark')
      root.classList.remove('light')
      document.body.style.backgroundColor = '#0f0f23'
    } else {
      root.classList.add('light')
      root.classList.remove('dark')
      document.body.style.backgroundColor = '#ffffff'
    }
    
    // 保存到本地存储
    localStorage.setItem('theme', isDark.value ? 'dark' : 'light')
  }

  const initTheme = () => {
    // 从本地存储读取主题设置
    const savedTheme = localStorage.getItem('theme')
    if (savedTheme) {
      isDark.value = savedTheme === 'dark'
    } else {
      // 如果没有保存的设置，使用系统偏好
      isDark.value = window.matchMedia('(prefers-color-scheme: dark)').matches
    }
    updateTheme()
  }

  // 监听系统主题变化
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
  mediaQuery.addEventListener('change', (e) => {
    if (!localStorage.getItem('theme')) {
      isDark.value = e.matches
      updateTheme()
    }
  })

  onMounted(() => {
    initTheme()
  })

  return {
    isDark: computed(() => isDark.value),
    toggleTheme,
    updateTheme,
    initTheme
  }
}