/**
 * 移动端手势处理工具类
 * 提供滑动、缩放、长按等手势识别功能
 */

export interface TouchPoint {
  x: number
  y: number
  timestamp: number
}

export interface SwipeEvent {
  direction: 'left' | 'right' | 'up' | 'down'
  distance: number
  duration: number
  velocity: number
  startPoint: TouchPoint
  endPoint: TouchPoint
}

export interface PinchEvent {
  scale: number
  center: TouchPoint
  distance: number
}

export interface LongPressEvent {
  point: TouchPoint
  duration: number
}

export interface TapEvent {
  point: TouchPoint
  tapCount: number
}

export interface GestureOptions {
  // 滑动配置
  swipeThreshold?: number // 最小滑动距离
  swipeVelocityThreshold?: number // 最小滑动速度
  
  // 长按配置
  longPressDelay?: number // 长按延迟时间
  longPressMoveThreshold?: number // 长按移动阈值
  
  // 双击配置
  doubleTapDelay?: number // 双击间隔时间
  doubleTapDistance?: number // 双击距离阈值
  
  // 缩放配置
  pinchThreshold?: number // 缩放阈值
  
  // 防抖配置
  debounceDelay?: number
}

export class GestureDetector {
  private element: HTMLElement
  private options: Required<GestureOptions>
  private touches: TouchPoint[] = []
  private startTouches: TouchPoint[] = []
  private longPressTimer: number | null = null
  private lastTap: TouchPoint | null = null
  private lastTapTime = 0
  private isLongPressing = false
  private isPinching = false
  private initialPinchDistance = 0
  private lastPinchScale = 1
  
  // 事件回调
  private callbacks: {
    onSwipe?: (event: SwipeEvent) => void
    onPinch?: (event: PinchEvent) => void
    onLongPress?: (event: LongPressEvent) => void
    onTap?: (event: TapEvent) => void
    onDoubleTap?: (event: TapEvent) => void
    onTouchStart?: (touches: TouchPoint[]) => void
    onTouchMove?: (touches: TouchPoint[]) => void
    onTouchEnd?: (touches: TouchPoint[]) => void
  } = {}
  
  constructor(element: HTMLElement, options: GestureOptions = {}) {
    this.element = element
    this.options = {
      swipeThreshold: 30,
      swipeVelocityThreshold: 0.3,
      longPressDelay: 500,
      longPressMoveThreshold: 10,
      doubleTapDelay: 300,
      doubleTapDistance: 30,
      pinchThreshold: 10,
      debounceDelay: 16,
      ...options
    }
    
    this.bindEvents()
  }
  
  private bindEvents() {
    this.element.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: false })
    this.element.addEventListener('touchmove', this.handleTouchMove.bind(this), { passive: false })
    this.element.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: false })
    this.element.addEventListener('touchcancel', this.handleTouchCancel.bind(this), { passive: false })
  }
  
  private handleTouchStart(event: TouchEvent) {
    event.preventDefault()
    
    this.touches = this.getTouchPoints(event.touches)
    this.startTouches = [...this.touches]
    this.isLongPressing = false
    this.isPinching = false
    
    // 清除之前的长按定时器
    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer)
      this.longPressTimer = null
    }
    
    // 单指触摸 - 可能是点击或长按
    if (this.touches.length === 1) {
      this.startLongPressDetection()
    }
    
    // 双指触摸 - 可能是缩放
    if (this.touches.length === 2) {
      this.startPinchDetection()
    }
    
    this.callbacks.onTouchStart?.(this.touches)
  }
  
  private handleTouchMove(event: TouchEvent) {
    event.preventDefault()
    
    this.touches = this.getTouchPoints(event.touches)
    
    // 检查是否超出长按移动阈值
    if (this.longPressTimer && this.touches.length === 1) {
      const distance = this.getDistance(this.startTouches[0], this.touches[0])
      if (distance > this.options.longPressMoveThreshold) {
        this.cancelLongPress()
      }
    }
    
    // 处理缩放手势
    if (this.isPinching && this.touches.length === 2) {
      this.handlePinchMove()
    }
    
    this.callbacks.onTouchMove?.(this.touches)
  }
  
  private handleTouchEnd(event: TouchEvent) {
    event.preventDefault()
    
    const endTouches = this.getTouchPoints(event.changedTouches)
    
    // 处理滑动手势
    if (!this.isLongPressing && !this.isPinching && this.startTouches.length === 1 && endTouches.length === 1) {
      this.handleSwipeGesture(this.startTouches[0], endTouches[0])
      this.handleTapGesture(endTouches[0])
    }
    
    // 清理状态
    this.cancelLongPress()
    this.isPinching = false
    this.touches = []
    
    this.callbacks.onTouchEnd?.(endTouches)
  }
  
  private handleTouchCancel(event: TouchEvent) {
    this.cancelLongPress()
    this.isPinching = false
    this.touches = []
  }
  
  private getTouchPoints(touches: TouchList): TouchPoint[] {
    const points: TouchPoint[] = []
    for (let i = 0; i < touches.length; i++) {
      const touch = touches[i]
      points.push({
        x: touch.clientX,
        y: touch.clientY,
        timestamp: Date.now()
      })
    }
    return points
  }
  
  private startLongPressDetection() {
    this.longPressTimer = window.setTimeout(() => {
      if (this.touches.length === 1) {
        this.isLongPressing = true
        const duration = Date.now() - this.startTouches[0].timestamp
        this.callbacks.onLongPress?.({
          point: this.touches[0],
          duration
        })
      }
    }, this.options.longPressDelay)
  }
  
  private cancelLongPress() {
    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer)
      this.longPressTimer = null
    }
  }
  
  private startPinchDetection() {
    if (this.touches.length === 2) {
      this.isPinching = true
      this.initialPinchDistance = this.getDistance(this.touches[0], this.touches[1])
      this.lastPinchScale = 1
    }
  }
  
  private handlePinchMove() {
    if (this.touches.length === 2) {
      const currentDistance = this.getDistance(this.touches[0], this.touches[1])
      const scale = currentDistance / this.initialPinchDistance
      
      if (Math.abs(scale - this.lastPinchScale) > 0.01) {
        const center = this.getMidpoint(this.touches[0], this.touches[1])
        
        this.callbacks.onPinch?.({
          scale,
          center,
          distance: currentDistance
        })
        
        this.lastPinchScale = scale
      }
    }
  }
  
  private handleSwipeGesture(start: TouchPoint, end: TouchPoint) {
    const deltaX = end.x - start.x
    const deltaY = end.y - start.y
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)
    const duration = end.timestamp - start.timestamp
    const velocity = distance / duration
    
    if (distance >= this.options.swipeThreshold && velocity >= this.options.swipeVelocityThreshold) {
      let direction: SwipeEvent['direction']
      
      if (Math.abs(deltaX) > Math.abs(deltaY)) {
        direction = deltaX > 0 ? 'right' : 'left'
      } else {
        direction = deltaY > 0 ? 'down' : 'up'
      }
      
      this.callbacks.onSwipe?.({
        direction,
        distance,
        duration,
        velocity,
        startPoint: start,
        endPoint: end
      })
    }
  }
  
  private handleTapGesture(point: TouchPoint) {
    const now = Date.now()
    
    // 检查双击
    if (this.lastTap && 
        (now - this.lastTapTime) <= this.options.doubleTapDelay &&
        this.getDistance(this.lastTap, point) <= this.options.doubleTapDistance) {
      
      this.callbacks.onDoubleTap?.({
        point,
        tapCount: 2
      })
      
      this.lastTap = null
      this.lastTapTime = 0
    } else {
      // 单击
      this.callbacks.onTap?.({
        point,
        tapCount: 1
      })
      
      this.lastTap = point
      this.lastTapTime = now
    }
  }
  
  private getDistance(point1: TouchPoint, point2: TouchPoint): number {
    const deltaX = point2.x - point1.x
    const deltaY = point2.y - point1.y
    return Math.sqrt(deltaX * deltaX + deltaY * deltaY)
  }
  
  private getMidpoint(point1: TouchPoint, point2: TouchPoint): TouchPoint {
    return {
      x: (point1.x + point2.x) / 2,
      y: (point1.y + point2.y) / 2,
      timestamp: Date.now()
    }
  }
  
  // 公共方法 - 设置事件回调
  onSwipe(callback: (event: SwipeEvent) => void) {
    this.callbacks.onSwipe = callback
    return this
  }
  
  onPinch(callback: (event: PinchEvent) => void) {
    this.callbacks.onPinch = callback
    return this
  }
  
  onLongPress(callback: (event: LongPressEvent) => void) {
    this.callbacks.onLongPress = callback
    return this
  }
  
  onTap(callback: (event: TapEvent) => void) {
    this.callbacks.onTap = callback
    return this
  }
  
  onDoubleTap(callback: (event: TapEvent) => void) {
    this.callbacks.onDoubleTap = callback
    return this
  }
  
  onTouchStart(callback: (touches: TouchPoint[]) => void) {
    this.callbacks.onTouchStart = callback
    return this
  }
  
  onTouchMove(callback: (touches: TouchPoint[]) => void) {
    this.callbacks.onTouchMove = callback
    return this
  }
  
  onTouchEnd(callback: (touches: TouchPoint[]) => void) {
    this.callbacks.onTouchEnd = callback
    return this
  }
  
  // 销毁手势检测器
  destroy() {
    this.element.removeEventListener('touchstart', this.handleTouchStart.bind(this))
    this.element.removeEventListener('touchmove', this.handleTouchMove.bind(this))
    this.element.removeEventListener('touchend', this.handleTouchEnd.bind(this))
    this.element.removeEventListener('touchcancel', this.handleTouchCancel.bind(this))
    
    this.cancelLongPress()
    this.callbacks = {}
  }
}

/**
 * 创建手势检测器的便捷函数
 */
export function createGestureDetector(element: HTMLElement, options?: GestureOptions): GestureDetector {
  return new GestureDetector(element, options)
}

/**
 * 防抖函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: number | null = null
  
  return (...args: Parameters<T>) => {
    if (timeoutId !== null) {
      clearTimeout(timeoutId)
    }
    
    timeoutId = window.setTimeout(() => {
      func(...args)
      timeoutId = null
    }, delay)
  }
}

/**
 * 节流函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let lastCall = 0
  
  return (...args: Parameters<T>) => {
    const now = Date.now()
    
    if (now - lastCall >= delay) {
      lastCall = now
      func(...args)
    }
  }
}

/**
 * 触觉反馈
 */
export function hapticFeedback(type: 'light' | 'medium' | 'heavy' = 'light') {
  if ('vibrate' in navigator) {
    const patterns = {
      light: [10],
      medium: [20],
      heavy: [30]
    }
    
    navigator.vibrate(patterns[type])
  }
}

/**
 * 阻止默认的触摸行为（如滚动、缩放）
 */
export function preventDefaultTouch(element: HTMLElement) {
  const preventDefault = (e: Event) => {
    e.preventDefault()
  }
  
  element.addEventListener('touchstart', preventDefault, { passive: false })
  element.addEventListener('touchmove', preventDefault, { passive: false })
  element.addEventListener('touchend', preventDefault, { passive: false })
  
  return () => {
    element.removeEventListener('touchstart', preventDefault)
    element.removeEventListener('touchmove', preventDefault)
    element.removeEventListener('touchend', preventDefault)
  }
}

/**
 * 获取触摸点相对于元素的位置
 */
export function getTouchPosition(touch: Touch, element: HTMLElement): TouchPoint {
  const rect = element.getBoundingClientRect()
  
  return {
    x: touch.clientX - rect.left,
    y: touch.clientY - rect.top,
    timestamp: Date.now()
  }
}

/**
 * 检测是否支持触摸
 */
export function isTouchSupported(): boolean {
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0
}

/**
 * 检测是否支持触觉反馈
 */
export function isHapticSupported(): boolean {
  return 'vibrate' in navigator
}

/**
 * 移动端手势组合器
 * 用于组合多个手势检测器
 */
export class GestureComposer {
  private detectors: GestureDetector[] = []
  
  add(detector: GestureDetector): this {
    this.detectors.push(detector)
    return this
  }
  
  remove(detector: GestureDetector): this {
    const index = this.detectors.indexOf(detector)
    if (index > -1) {
      this.detectors.splice(index, 1)
    }
    return this
  }
  
  destroy(): void {
    this.detectors.forEach(detector => detector.destroy())
    this.detectors = []
  }
}

/**
 * 创建手势组合器
 */
export function createGestureComposer(): GestureComposer {
  return new GestureComposer()
}