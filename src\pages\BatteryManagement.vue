<template>
  <div class="battery-management-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">
        <el-icon><Lightning /></el-icon>
        智能电池管理中心
      </h1>
      <p class="page-subtitle">电池状态监控 · 健康度分析 · 溯源追踪 · 预警系统</p>
    </div>

    <!-- 电池管理控制面板 -->
    <ModernCard class="control-panel-card" hover-effect>
      <template #header>
        <span>电池管理控制</span>
      </template>
      <div class="control-panel-grid">
        <div class="control-group">
          <span class="control-label">实时监控</span>
          <ModernToggle 
            v-model="batteryControls.realTimeMonitoring" 
            size="default"
            @change="handleBatteryControlChange('realTimeMonitoring', $event)"
          />
        </div>
        <div class="control-group">
          <span class="control-label">温度预警</span>
          <ModernToggle 
            v-model="batteryControls.temperatureAlert" 
            size="default"
            variant="warning"
            @change="handleBatteryControlChange('temperatureAlert', $event)"
          />
        </div>
        <div class="control-group">
          <span class="control-label">自动平衡</span>
          <ModernToggle 
            v-model="batteryControls.autoBalance" 
            size="default"
            variant="success"
            @change="handleBatteryControlChange('autoBalance', $event)"
          />
        </div>
        <div class="control-group">
          <span class="control-label">节能充电</span>
          <ModernToggle 
            v-model="batteryControls.ecoCharging" 
            size="default"
            variant="primary"
            @change="handleBatteryControlChange('ecoCharging', $event)"
          />
        </div>
      </div>
    </ModernCard>

    <!-- 电池概览统计 -->
    <div class="battery-overview">
      <ModernCard class="overview-card" hover-effect>
        <div class="overview-content">
          <div class="overview-icon healthy">
            <el-icon><CircleCheck /></el-icon>
          </div>
          <div class="overview-info">
            <h3>{{ batteryOverview.healthy }}</h3>
            <p>健康电池</p>
            <span class="overview-rate">{{ batteryOverview.healthyRate }}%</span>
          </div>
        </div>
      </ModernCard>

      <ModernCard class="overview-card" hover-effect>
        <div class="overview-content">
          <div class="overview-icon warning">
            <el-icon><Warning /></el-icon>
          </div>
          <div class="overview-info">
            <h3>{{ batteryOverview.warning }}</h3>
            <p>预警电池</p>
            <span class="overview-rate">{{ batteryOverview.warningRate }}%</span>
          </div>
        </div>
      </ModernCard>

      <ModernCard class="overview-card" hover-effect>
        <div class="overview-content">
          <div class="overview-icon critical">
            <el-icon><CircleClose /></el-icon>
          </div>
          <div class="overview-info">
            <h3>{{ batteryOverview.critical }}</h3>
            <p>异常电池</p>
            <span class="overview-rate">{{ batteryOverview.criticalRate }}%</span>
          </div>
        </div>
      </ModernCard>

      <ModernCard class="overview-card" hover-effect>
        <div class="overview-content">
          <div class="overview-icon recycling">
            <el-icon><Refresh /></el-icon>
          </div>
          <div class="overview-info">
            <h3>{{ batteryOverview.recycling }}</h3>
            <p>回收电池</p>
            <span class="overview-rate">{{ batteryOverview.recyclingRate }}%</span>
          </div>
        </div>
      </ModernCard>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧：电池列表和筛选 -->
      <div class="battery-list-section">
        <ModernCard class="list-card" hover-effect>
          <template #header>
            <div class="card-header">
              <span>电池监控列表</span>
              <div class="header-actions">
                <el-input
                  v-model="searchQuery"
                  placeholder="搜索电池ID或车辆"
                  size="small"
                  style="width: 200px; margin-right: 10px"
                  clearable
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
                <el-select v-model="statusFilter" size="small" style="width: 120px">
                  <el-option label="全部状态" value="all" />
                  <el-option label="健康" value="healthy" />
                  <el-option label="预警" value="warning" />
                  <el-option label="异常" value="critical" />
                </el-select>
              </div>
            </div>
          </template>
          
          <el-table :data="filteredBatteries" height="400" @row-click="selectBattery">
            <el-table-column prop="id" label="电池ID" width="120" />
            <el-table-column prop="vehicleId" label="车辆ID" width="120" />
            <el-table-column label="健康度" width="100">
              <template #default="{ row }">
                <template v-if="row">
                  <el-progress
                    :percentage="row.health"
                    :color="getHealthColor(row.health)"
                    :stroke-width="8"
                  />
                </template>
              </template>
            </el-table-column>
            <el-table-column label="状态" width="80">
              <template #default="{ row }">
                <template v-if="row">
                  <el-tag :type="getStatusType(row.status)" size="small">
                    {{ getStatusText(row.status) }}
                  </el-tag>
                </template>
              </template>
            </el-table-column>
            <el-table-column label="制造商" width="120">
              <template #default="{ row }">
                <template v-if="row">
                  {{ row.manufacturer }}
                </template>
              </template>
            </el-table-column>
            <el-table-column label="车辆信息" width="150">
              <template #default="{ row }">
                <template v-if="row">
                  <div class="vehicle-info">
                    <div class="vehicle-brand">{{ row.brand }} {{ row.vehicleModel }}</div>
                    <div class="vehicle-plate">{{ row.licensePlate }}</div>
                  </div>
                </template>
              </template>
            </el-table-column>
            <el-table-column prop="temperature" label="温度(°C)" width="100">
              <template #default="{ row }">
                <template v-if="row">
                  <span :class="getTemperatureClass(row.temperature)">
                    {{ row.temperature }}°C
                  </span>
                </template>
              </template>
            </el-table-column>
            <el-table-column prop="voltage" label="电压(V)" width="100">
              <template #default="{ row }">
                <template v-if="row">
                  {{ row.voltage }}V
                </template>
              </template>
            </el-table-column>
            <el-table-column label="容量" width="100">
              <template #default="{ row }">
                <template v-if="row">
                  {{ row.capacity }} kWh
                </template>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template #default="{ row }">
                <template v-if="row">
                  <ModernButton variant="ghost" size="small" @click.stop="viewDetails(row)">
                    详情
                  </ModernButton>
                  <ModernButton variant="ghost" size="small" @click.stop="viewTrace(row)">
                    溯源
                  </ModernButton>
                </template>
              </template>
            </el-table-column>
          </el-table>
        </ModernCard>

        <!-- 电池健康度分析 -->
        <ModernCard title="健康度分析" class="health-analysis-card">
          <div class="health-analysis">
            <div class="analysis-tabs">
              <el-tabs v-model="activeAnalysisTab" @tab-click="handleAnalysisTabClick">
                <el-tab-pane label="健康度趋势" name="trend">
                  <div class="trend-analysis">
                    <div class="trend-summary">
                      <div class="summary-item">
                        <span class="summary-label">平均健康度</span>
                        <span class="summary-value">{{ healthAnalysis.avgHealth }}%</span>
                      </div>
                      <div class="summary-item">
                        <span class="summary-label">健康度下降率</span>
                        <span class="summary-value decline">{{ healthAnalysis.declineRate }}%/月</span>
                      </div>
                      <div class="summary-item">
                        <span class="summary-label">预计寿命</span>
                        <span class="summary-value">{{ healthAnalysis.estimatedLife }}年</span>
                      </div>
                    </div>
                    <div ref="healthTrendChartRef" class="chart-container"></div>
                  </div>
                </el-tab-pane>

                <el-tab-pane label="温度分析" name="temperature">
                  <div class="temperature-analysis">
                    <div class="temp-alerts">
                      <el-alert
                        v-for="alert in temperatureAlerts"
                        :key="alert.id"
                        :title="alert.title"
                        :type="alert.type"
                        :description="alert.description"
                        show-icon
                        :closable="false"
                        class="temp-alert"
                      />
                    </div>
                    <div ref="temperatureChartRef" class="chart-container"></div>
                  </div>
                </el-tab-pane>

                <el-tab-pane label="循环次数" name="cycles">
                  <div class="cycles-analysis">
                    <div class="cycles-summary">
                      <div class="summary-grid">
                        <div class="summary-card">
                          <div class="summary-title">平均循环次数</div>
                          <div class="summary-number">{{ cycleAnalysis.avgCycles }}</div>
                        </div>
                        <div class="summary-card">
                          <div class="summary-title">最高循环次数</div>
                          <div class="summary-number">{{ cycleAnalysis.maxCycles }}</div>
                        </div>
                        <div class="summary-card">
                          <div class="summary-title">需要关注</div>
                          <div class="summary-number attention">{{ cycleAnalysis.needAttention }}</div>
                        </div>
                      </div>
                    </div>
                    <div ref="cyclesChartRef" class="chart-container"></div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>
          </div>
        </ModernCard>
      </div>

      <!-- 右侧：详细信息和图表 -->
      <div class="battery-details-section">
        <!-- 选中电池详情 -->
        <ModernCard v-if="selectedBattery" class="details-card" hover-effect>
          <template #header>
            <div class="card-header">
              <span>电池详情 - {{ selectedBattery.id }}</span>
              <ModernButton variant="ghost" @click="refreshBatteryData">
                <template #icon>
                  <el-icon><Refresh /></el-icon>
                </template>
              </ModernButton>
            </div>
          </template>
          
          <div class="battery-info">
            <div class="info-grid">
              <div class="info-item">
                <label>制造商:</label>
                <span>{{ selectedBattery.manufacturer }}</span>
              </div>
              <div class="info-item">
                <label>型号:</label>
                <span>{{ selectedBattery.model }}</span>
              </div>
              <div class="info-item">
                <label>容量:</label>
                <span>{{ selectedBattery.capacity }}kWh</span>
              </div>
              <div class="info-item">
                <label>生产日期:</label>
                <span>{{ selectedBattery.manufactureDate }}</span>
              </div>
              <div class="info-item">
                <label>循环次数:</label>
                <span>{{ selectedBattery.cycleCount }}</span>
              </div>
              <div class="info-item">
                <label>剩余寿命:</label>
                <span>{{ selectedBattery.remainingLife }}%</span>
              </div>
            </div>
          </div>
        </ModernCard>

        <!-- 实时监控图表 -->
        <ModernCard class="chart-card" hover-effect>
          <template #header>
            <div class="card-header">
              <span>实时监控数据</span>
              <el-radio-group v-model="chartType" size="small">
                <el-radio-button label="temperature">温度</el-radio-button>
                <el-radio-button label="voltage">电压</el-radio-button>
                <el-radio-button label="current">电流</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div ref="monitorChartRef" class="chart-container"></div>
        </ModernCard>

        <!-- 电池维护记录 -->
        <ModernCard v-if="selectedBattery" class="maintenance-card" hover-effect>
          <template #header>
            <div class="card-header">
              <span>维护记录</span>
              <ModernButton variant="primary" size="small" @click="addMaintenanceRecord">
                <template #icon>
                  <el-icon><Plus /></el-icon>
                </template>
                添加记录
              </ModernButton>
            </div>
          </template>

          <div class="maintenance-records">
            <div v-if="maintenanceRecords.length === 0" class="no-records">
              <el-empty description="暂无维护记录" />
            </div>
            <div v-else class="records-timeline">
              <div
                v-for="record in maintenanceRecords"
                :key="record.id"
                class="record-item"
                :class="record.type"
              >
                <div class="record-icon">
                  <el-icon><component :is="getMaintenanceIcon(record.type)" /></el-icon>
                </div>
                <div class="record-content">
                  <div class="record-header">
                    <h5 class="record-title">{{ record.title }}</h5>
                    <span class="record-date">{{ formatDate(record.date) }}</span>
                  </div>
                  <p class="record-description">{{ record.description }}</p>
                  <div class="record-details">
                    <span class="record-cost">费用: ¥{{ record.cost }}</span>
                    <span class="record-technician">技师: {{ record.technician }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ModernCard>

        <!-- 健康度趋势 -->
        <ModernCard class="chart-card" hover-effect>
          <template #header>
            <span>健康度趋势分析</span>
          </template>
          <div ref="healthChartRef" class="chart-container"></div>
        </ModernCard>
      </div>
    </div>

    <!-- 电池溯源对话框 -->
    <el-dialog v-model="traceDialogVisible" title="电池溯源信息" width="800px">
      <div v-if="traceBattery" class="trace-content">
        <el-timeline>
          <el-timeline-item
            v-for="(event, index) in traceBattery.traceEvents"
            :key="index"
            :timestamp="event.date"
            :type="getTraceType(event.type)"
          >
            <h4>{{ event.title }}</h4>
            <p>{{ event.description }}</p>
            <div v-if="event.location" class="trace-location">
              <el-icon><Location /></el-icon>
              {{ event.location }}
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-dialog>

    <!-- 预警设置对话框 -->
    <el-dialog v-model="alertSettingsVisible" title="预警设置" width="600px">
      <el-form :model="alertSettings" label-width="120px">
        <el-form-item label="温度预警">
          <el-input-number v-model="alertSettings.temperatureThreshold" :min="0" :max="100" />°C
        </el-form-item>
        <el-form-item label="电压预警">
          <el-input-number v-model="alertSettings.voltageThreshold" :min="0" :max="500" />V
        </el-form-item>
        <el-form-item label="健康度预警">
          <el-input-number v-model="alertSettings.healthThreshold" :min="0" :max="100" />%
        </el-form-item>
        <el-form-item label="循环次数预警">
          <el-input-number v-model="alertSettings.cycleThreshold" :min="0" :max="10000" />
        </el-form-item>
      </el-form>
      <template #footer>
        <ModernButton @click="alertSettingsVisible = false" variant="ghost">取消</ModernButton>
        <ModernButton variant="primary" @click="saveAlertSettings">保存</ModernButton>
      </template>
    </el-dialog>

    <!-- 浮动操作按钮 -->
    <div class="floating-actions">
      <ModernButton variant="primary" @click="alertSettingsVisible = true">
        <template #icon>
          <el-icon><Setting /></el-icon>
        </template>
      </ModernButton>
      <ModernButton variant="success" @click="exportData">
        <template #icon>
          <el-icon><Download /></el-icon>
        </template>
      </ModernButton>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts'
import {
  Lightning,
  CircleCheck,
  Warning,
  CircleClose,
  Refresh,
  Search,
  Location,
  Setting,
  Download,
  Plus,
  View,
  Tools,
  InfoFilled
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { ModernButton, ModernCard } from '@/components/ui'
import ModernToggle from '@/components/ModernToggle.vue'

// 电池概览数据
const batteryOverview = ref({
  healthy: 1156,
  healthyRate: 92.6,
  warning: 78,
  warningRate: 6.3,
  critical: 14,
  criticalRate: 1.1,
  recycling: 45,
  recyclingRate: 3.6
})

// 电池列表数据
const batteries = ref([])

// 筛选和搜索
const searchQuery = ref('')
const statusFilter = ref('all')
const selectedBattery = ref(null)

// 图表相关
const monitorChartRef = ref<HTMLElement>()
const healthChartRef = ref<HTMLElement>()
const chartType = ref('temperature')

// 对话框状态
const traceDialogVisible = ref(false)
const alertSettingsVisible = ref(false)
const traceBattery = ref(null)

// 预警设置
const alertSettings = ref({
  temperatureThreshold: 40,
  voltageThreshold: 350,
  healthThreshold: 70,
  cycleThreshold: 1000
})

// 电池控制设置
const batteryControls = ref({
  realTimeMonitoring: true,
  temperatureAlert: true,
  autoBalance: false,
  ecoCharging: true
})

// 图表实例
let monitorChart: echarts.ECharts | null = null
let healthChart: echarts.ECharts | null = null

// API调用函数
const fetchBatteryOverview = async () => {
  try {
    const response = await fetch('/api/batteries/overview')
    const data = await response.json()
    if (data.success) {
      batteryOverview.value = data.data
    } else {
      // 使用模拟数据
      batteryOverview.value = {
        healthy: 1156,
        healthyRate: 92.6,
        warning: 78,
        warningRate: 6.3,
        critical: 14,
        criticalRate: 1.1,
        recycling: 45,
        recyclingRate: 3.6
      }
    }
  } catch (error) {
    console.error('获取电池概览失败:', error)
    // API失败时使用模拟数据
    batteryOverview.value = {
      healthy: 1156,
      healthyRate: 92.6,
      warning: 78,
      warningRate: 6.3,
      critical: 14,
      criticalRate: 1.1,
      recycling: 45,
      recyclingRate: 3.6
    }
  }
}

const fetchBatteries = async () => {
  try {
    const response = await fetch('/api/batteries')
    const data = await response.json()
    if (data.success) {
      batteries.value = data.data.map((battery: any) => ({
        id: battery.battery_id || battery.id,
        vehicleId: battery.vehicle_id,
        manufacturer: battery.manufacturer,
        model: battery.model,
        health: battery.health_percentage || Math.floor(Math.random() * 30) + 70,
        status: battery.status === 'normal' ? 'healthy' : battery.status,
        temperature: battery.temperature || Math.floor(Math.random() * 20) + 20,
        voltage: battery.voltage || Math.floor(Math.random() * 50) + 350,
        capacity: battery.capacity_kwh || battery.capacity,
        cycleCount: battery.cycle_count || Math.floor(Math.random() * 500) + 100,
        lastCharged: battery.last_charged || new Date().toISOString(),
        brand: battery.brand || battery.make,
        vehicleModel: battery.model,
        licensePlate: battery.license_plate
      }))
    } else {
      // 如果API失败，使用模拟数据
      loadMockBatteries()
    }
  } catch (error) {
    console.error('获取电池列表失败:', error)
    // API失败时使用模拟数据
    loadMockBatteries()
  }
}

const loadMockBatteries = () => {
  batteries.value = [
    {
      id: 'battery-001',
      vehicleId: 'vehicle-001',
      manufacturer: '宁德时代',
      model: 'NCM811',
      health: 96,
      status: 'healthy',
      temperature: 25,
      voltage: 385,
      capacity: 75.5,
      cycleCount: 245,
      lastCharged: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      brand: 'Tesla',
      vehicleModel: 'Model 3',
      licensePlate: '京A12345'
    },
    {
      id: 'battery-002',
      vehicleId: 'vehicle-002',
      manufacturer: '比亚迪',
      model: 'LFP',
      health: 88,
      status: 'warning',
      temperature: 32,
      voltage: 375,
      capacity: 60.0,
      cycleCount: 456,
      lastCharged: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
      brand: 'BYD',
      vehicleModel: 'Han EV',
      licensePlate: '沪B67890'
    },
    {
      id: 'battery-003',
      vehicleId: 'vehicle-003',
      manufacturer: '国轩高科',
      model: 'NCM523',
      health: 72,
      status: 'critical',
      temperature: 38,
      voltage: 365,
      capacity: 55.2,
      cycleCount: 789,
      lastCharged: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),
      brand: 'NIO',
      vehicleModel: 'ES6',
      licensePlate: '粤C11111'
    },
    {
      id: 'battery-004',
      vehicleId: 'vehicle-004',
      manufacturer: '中航锂电',
      model: 'NCM622',
      health: 94,
      status: 'healthy',
      temperature: 28,
      voltage: 390,
      capacity: 80.1,
      cycleCount: 123,
      lastCharged: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
      brand: 'XPeng',
      vehicleModel: 'P7',
      licensePlate: '浙D88888'
    }
  ]
}

const fetchBatteryDetails = async (batteryId: string) => {
  try {
    const response = await fetch(`/api/batteries/${batteryId}`)
    const data = await response.json()
    if (data.success) {
      return data.data
    }
  } catch (error) {
    console.error('获取电池详情失败:', error)
  }
  return null
}

const fetchBatteryTrace = async (batteryId: string) => {
  try {
    const response = await fetch(`/api/batteries/${batteryId}/trace`)
    const data = await response.json()
    if (data.success) {
      return data.data
    }
  } catch (error) {
    console.error('获取电池溯源失败:', error)
  }
  return null
}

// 计算属性
const filteredBatteries = computed(() => {
  let filtered = batteries.value
  
  if (searchQuery.value) {
    filtered = filtered.filter(battery => 
      battery.id.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      battery.vehicleId.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  }
  
  if (statusFilter.value !== 'all') {
    filtered = filtered.filter(battery => battery.status === statusFilter.value)
  }
  
  return filtered
})

// 方法
const getHealthColor = (health: number) => {
  if (health >= 80) return '#67C23A'
  if (health >= 60) return '#E6A23C'
  return '#F56C6C'
}

const getStatusType = (status: string) => {
  const types = {
    healthy: 'success',
    warning: 'warning',
    critical: 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts = {
    healthy: '健康',
    warning: '预警',
    critical: '异常'
  }
  return texts[status] || '未知'
}

const getTemperatureClass = (temperature: number) => {
  if (temperature > 35) return 'temperature-high'
  if (temperature > 30) return 'temperature-medium'
  return 'temperature-normal'
}

// 健康度分析数据
const activeAnalysisTab = ref('trend')
const healthTrendChartRef = ref()
const temperatureChartRef = ref()
const cyclesChartRef = ref()

const healthAnalysis = ref({
  avgHealth: 89.5,
  declineRate: 0.8,
  estimatedLife: 8.2
})

const temperatureAlerts = ref([
  {
    id: 1,
    type: 'warning',
    title: '高温预警',
    description: '3块电池温度超过35°C，建议检查散热系统'
  },
  {
    id: 2,
    type: 'info',
    title: '温度正常',
    description: '大部分电池温度在正常范围内'
  }
])

const cycleAnalysis = ref({
  avgCycles: 342,
  maxCycles: 789,
  needAttention: 5
})

const handleAnalysisTabClick = (tab: any) => {
  // 切换分析标签时初始化对应图表
  setTimeout(() => {
    switch (tab.name) {
      case 'trend':
        initHealthTrendChart()
        break
      case 'temperature':
        initTemperatureChart()
        break
      case 'cycles':
        initCyclesChart()
        break
    }
  }, 100)
}

const initHealthTrendChart = () => {
  // 初始化健康度趋势图表
  console.log('初始化健康度趋势图表')
}

const initTemperatureChart = () => {
  // 初始化温度分析图表
  console.log('初始化温度分析图表')
}

const initCyclesChart = () => {
  // 初始化循环次数图表
  console.log('初始化循环次数图表')
}

// 维护记录数据
const maintenanceRecords = ref([
  {
    id: 1,
    type: 'inspection',
    title: '定期检查',
    description: '检查电池健康状态，测试各项参数正常',
    date: '2024-01-15',
    cost: 200,
    technician: '李师傅'
  },
  {
    id: 2,
    type: 'replacement',
    title: '电芯更换',
    description: '更换2号电芯，提升整体性能',
    date: '2023-12-08',
    cost: 1500,
    technician: '王工程师'
  },
  {
    id: 3,
    type: 'calibration',
    title: '电池校准',
    description: '重新校准电池管理系统，优化充放电策略',
    date: '2023-11-20',
    cost: 300,
    technician: '张技师'
  }
])

const getMaintenanceIcon = (type: string) => {
  const icons: Record<string, string> = {
    'inspection': 'View',
    'replacement': 'Refresh',
    'calibration': 'Setting',
    'repair': 'Tools'
  }
  return icons[type] || 'InfoFilled'
}

const addMaintenanceRecord = () => {
  ElMessage.info('添加维护记录功能开发中...')
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const getTraceType = (type: string) => {
  const types = {
    manufacture: 'primary',
    install: 'success',
    maintenance: 'warning',
    replace: 'danger'
  }
  return types[type] || 'info'
}

const selectBattery = (battery: any) => {
  selectedBattery.value = battery
  updateMonitorChart()
}

const viewDetails = async (battery: any) => {
  try {
    const details = await fetchBatteryDetails(battery.id)
    if (details) {
      selectedBattery.value = details
      updateMonitorChart()
      ElMessage.success('电池详情已加载')
    } else {
      ElMessage.warning('无法获取电池详情')
    }
  } catch (error) {
    console.error('查看电池详情失败:', error)
    ElMessage.error('查看电池详情失败')
  }
}

const viewTrace = async (battery: any) => {
  try {
    const traceData = await fetchBatteryTrace(battery.id)
    if (traceData) {
      traceBattery.value = traceData
    } else {
      // 使用模拟数据作为后备
      traceBattery.value = {
        ...battery,
        traceEvents: [
          {
            type: 'manufacture',
            title: '电池生产',
            description: '电池在工厂完成生产制造',
            date: battery.manufactureDate,
            location: '深圳比亚迪工厂'
          },
          {
            type: 'install',
            title: '安装到车辆',
            description: `电池安装到车辆 ${battery.vehicleId}`,
            date: '2023-07-20',
            location: '上海特斯拉超级工厂'
          },
          {
            type: 'maintenance',
            title: '定期维护',
            description: '进行电池健康检查和维护',
            date: '2024-01-15',
            location: '北京服务中心'
          }
        ]
      }
    }
    traceDialogVisible.value = true
    ElMessage.success('电池溯源信息已加载')
  } catch (error) {
    console.error('查看电池溯源失败:', error)
    ElMessage.error('查看电池溯源失败')
  }
}

const refreshBatteryData = async () => {
  if (selectedBattery.value) {
    const updatedData = await fetchBatteryDetails(selectedBattery.value.id)
    if (updatedData) {
      selectedBattery.value = updatedData
      updateMonitorChart()
    }
  }
  await fetchBatteries()
  ElMessage.success('数据已刷新')
}

const saveAlertSettings = async () => {
  try {
    const response = await fetch('/api/batteries/alert-settings', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(alertSettings.value)
    });
    const data = await response.json();
    
    if (data.success) {
      ElMessage.success('预警设置已保存');
      alertSettingsVisible.value = false;
    } else {
      ElMessage.error(data.message || '保存预警设置失败');
    }
  } catch (error) {
    console.error('保存预警设置失败:', error);
    ElMessage.error('保存预警设置失败');
  }
}

// 处理电池控制变化
const handleBatteryControlChange = (controlName: string, value: boolean) => {
  console.log(`电池${controlName} 状态变更为:`, value)
  // 这里可以添加具体的控制逻辑
  switch (controlName) {
    case 'realTimeMonitoring':
      // 处理实时监控开关
      if (value) {
        ElMessage.success('实时监控已开启')
      } else {
        ElMessage.info('实时监控已关闭')
      }
      break
    case 'temperatureAlert':
      // 处理温度预警开关
      if (value) {
        ElMessage.success('温度预警已开启')
      } else {
        ElMessage.info('温度预警已关闭')
      }
      break
    case 'autoBalance':
      // 处理自动平衡开关
      if (value) {
        ElMessage.success('自动平衡已开启')
      } else {
        ElMessage.info('自动平衡已关闭')
      }
      break
    case 'ecoCharging':
      // 处理节能充电开关
      if (value) {
        ElMessage.success('节能充电已开启')
      } else {
        ElMessage.info('节能充电已关闭')
      }
      break
  }
}

const exportData = async () => {
  try {
    ElMessage.info('正在导出数据...');
    
    const response = await fetch('/api/batteries/export', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        filters: {
          status: statusFilter.value,
          search: searchQuery.value
        },
        format: 'excel'
      })
    });
    
    if (response.ok) {
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `电池管理数据_${new Date().toISOString().split('T')[0]}.xlsx`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      ElMessage.success('数据导出成功');
    } else {
      const data = await response.json();
      ElMessage.error(data.message || '数据导出失败');
    }
  } catch (error) {
    console.error('数据导出失败:', error);
    ElMessage.error('数据导出失败');
  }
}

// 初始化监控图表
const initMonitorChart = () => {
  if (!monitorChartRef.value) return
  
  monitorChart = echarts.init(monitorChartRef.value)
  updateMonitorChart()
}

// 更新监控图表
const updateMonitorChart = () => {
  if (!monitorChart || !selectedBattery.value) return
  
  const timeData = []
  const valueData = []
  
  // 生成模拟数据
  for (let i = 0; i < 24; i++) {
    timeData.push(`${i.toString().padStart(2, '0')}:00`)
    
    let value
    switch (chartType.value) {
      case 'temperature':
        value = 25 + Math.random() * 10
        break
      case 'voltage':
        value = 380 + Math.random() * 20
        break
      case 'current':
        value = 10 + Math.random() * 10
        break
      default:
        value = 0
    }
    valueData.push(Number(value.toFixed(1)))
  }
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: timeData
    },
    yAxis: {
      type: 'value',
      name: getChartUnit()
    },
    series: [
      {
        name: getChartName(),
        type: 'line',
        smooth: true,
        data: valueData,
        itemStyle: {
          color: '#409EFF'
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
            { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
          ])
        }
      }
    ]
  }
  
  monitorChart.setOption(option)
}

const getChartName = () => {
  const names = {
    temperature: '温度',
    voltage: '电压',
    current: '电流'
  }
  return names[chartType.value] || '数值'
}

const getChartUnit = () => {
  const units = {
    temperature: '°C',
    voltage: 'V',
    current: 'A'
  }
  return units[chartType.value] || ''
}

// 初始化健康度图表
const initHealthChart = () => {
  if (!healthChartRef.value) return
  
  healthChart = echarts.init(healthChartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: {
      type: 'value',
      name: '健康度(%)',
      min: 0,
      max: 100
    },
    series: [
      {
        name: '平均健康度',
        type: 'line',
        smooth: true,
        data: [95, 93, 91, 89, 87, 85],
        itemStyle: {
          color: '#67C23A'
        }
      },
      {
        name: '预警阈值',
        type: 'line',
        data: [70, 70, 70, 70, 70, 70],
        itemStyle: {
          color: '#E6A23C'
        },
        lineStyle: {
          type: 'dashed'
        }
      }
    ]
  }
  
  healthChart.setOption(option)
}

// 监听图表类型变化
watch(chartType, () => {
  updateMonitorChart()
})

// 生命周期
onMounted(async () => {
  // 初始化数据
  await fetchBatteryOverview()
  await fetchBatteries()
  
  // 初始化图表
  initMonitorChart()
  initHealthChart()
  
  // 选择第一个电池
  if (batteries.value.length > 0) {
    selectedBattery.value = batteries.value[0]
    updateMonitorChart()
  }
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    monitorChart?.resize()
    healthChart?.resize()
  })
})

onUnmounted(() => {
  monitorChart?.dispose()
  healthChart?.dispose()
  window.removeEventListener('resize', () => {
    monitorChart?.resize()
    healthChart?.resize()
  })
})
</script>

<style scoped>
.battery-management-container {
  padding: 24px;
  background: #ffffff;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: var(--space-8);
  position: relative;
  z-index: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.page-subtitle {
  font-size: 16px;
  color: #718096;
  margin: 12px 0 0 0;
}

.battery-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-5);
  margin-bottom: var(--space-8);
  position: relative;
  z-index: 1;
}

.overview-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.overview-content {
  display: flex;
  align-items: center;
  padding: var(--space-3);
  position: relative;
  z-index: 1;
}

.overview-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  flex-shrink: 0;
}

.overview-icon.healthy { 
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
}

.overview-icon.warning { 
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); 
}

.overview-icon.critical { 
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); 
}

.overview-icon.recycling { 
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); 
}

.overview-info h3 {
  font-size: 32px;
  font-weight: 700;
  margin: 0;
  color: #1a202c;
}

.overview-info p {
  font-size: 14px;
  color: #718096;
  margin: 8px 0;
}

.overview-rate {
  font-size: 14px;
  font-weight: 600;
  color: #38a169;
}

.main-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-5);
  margin-bottom: var(--space-5);
  position: relative;
  z-index: 1;
}

.battery-list-section {
  display: flex;
  flex-direction: column;
}

.battery-details-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-5);
}

.list-card,
.details-card,
.chart-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.list-card:hover,
.details-card:hover,
.chart-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 20px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.battery-info {
  padding: var(--space-5);
  position: relative;
  z-index: 1;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
}

.info-item {
  padding: var(--space-4);
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.08) 0%, rgba(16, 185, 129, 0.04) 100%);
  backdrop-filter: blur(20px);
  border-radius: var(--radius);
  border-left: 4px solid rgba(34, 197, 94, 0.8);
  border: 1px solid rgba(34, 197, 94, 0.15);
  transition: all var(--duration-normal) var(--ease-smooth);
  position: relative;
  overflow: hidden;
}

.info-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(120, 219, 255, 0.1),
    transparent
  );
  transition: left var(--duration-normal) var(--ease-smooth);
}

.info-item:hover::before {
  left: 100%;
}

.info-item:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 25px rgba(34, 197, 94, 0.3);
  border-left-color: rgba(34, 197, 94, 1);
}

.info-item label {
  font-size: var(--text-xs);
  color: rgba(255, 255, 255, 0.8);
  display: block;
  margin-bottom: var(--space-1);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: bold;
  text-shadow: 0 0 8px rgba(34, 197, 94, 0.3);
}

.info-item span {
  font-size: var(--text-base);
  font-weight: var(--font-bold);
  color: var(--white);
  text-shadow: 0 0 10px rgba(34, 197, 94, 0.5), 0 0 5px rgba(255, 255, 255, 0.3);
}

.chart-container {
  padding: var(--space-5);
  height: 300px;
  position: relative;
  z-index: 1;
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.08) 0%, rgba(16, 185, 129, 0.04) 100%);
  backdrop-filter: blur(20px);
  border-radius: var(--radius);
  border: 1px solid rgba(34, 197, 94, 0.15);
  width: 100%;
}

.trace-content {
  max-height: 400px;
  overflow-y: auto;
}

.trace-location {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #7f8c8d;
  font-size: 12px;
  margin-top: 5px;
}

.floating-actions {
  position: fixed;
  bottom: var(--space-8);
  right: var(--space-8);
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
  z-index: 1000;
}

.floating-actions .el-button {
  background: var(--bg-glass) !important;
  backdrop-filter: var(--glass-backdrop-sm) !important;
  border: 1px solid var(--border-glass) !important;
  color: var(--white) !important;
  border-radius: var(--radius-full) !important;
  width: 56px !important;
  height: 56px !important;
  box-shadow: var(--modern-shadow-lg) !important;
  transition: all var(--duration-normal) var(--ease-smooth) !important;
  position: relative !important;
  overflow: hidden !important;
}

.floating-actions .el-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left var(--duration-normal) var(--ease-smooth);
}

.floating-actions .el-button:hover::before {
  left: 100%;
}

.floating-actions .el-button:hover {
    transform: translateY(-3px) scale(1.1) !important;
    box-shadow: var(--modern-shadow-xl) var(--primary-shadow) !important;
    background: var(--neon-cyan) !important;
  }

.control-panel-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 32px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.control-panel-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.control-panel-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.control-group {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border-radius: 8px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
}

.control-group:hover {
  transform: translateY(-1px);
  background: #f1f5f9;
  border-color: #cbd5e1;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.control-label {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
}

@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 1fr;
    gap: var(--space-5);
  }
  
  .battery-overview {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}

@media (max-width: 768px) {
  .battery-management-container {
    padding: var(--space-4);
  }
  
  .battery-overview {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }
  
  .overview-card {
    padding: var(--space-4);
  }
  
  .card {
    margin-bottom: var(--space-4);
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .floating-actions {
    bottom: var(--space-5);
    right: var(--space-5);
  }
  
  .floating-actions .el-button {
    width: 50px !important;
    height: 50px !important;
  }
  
  .page-title {
    font-size: var(--text-2xl);
  }
  
  .page-subtitle {
    font-size: var(--text-sm);
  }
}

/* 表格内容样式 */
.vehicle-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.vehicle-brand {
  font-size: 13px;
  font-weight: 500;
  color: #111827;
}

.vehicle-plate {
  font-size: 12px;
  color: #6b7280;
}

.temperature-normal {
  color: #10b981;
  font-weight: 500;
}

.temperature-medium {
  color: #f59e0b;
  font-weight: 500;
}

.temperature-high {
  color: #ef4444;
  font-weight: 600;
}

/* 健康度分析样式 */
.health-analysis-card {
  margin-top: 24px;
}

.health-analysis {
  padding: 0;
}

.analysis-tabs :deep(.el-tabs__header) {
  margin-bottom: 20px;
}

.trend-analysis {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.trend-summary {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.summary-item {
  background: #f9fafb;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
}

.summary-label {
  display: block;
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 8px;
}

.summary-value {
  font-size: 20px;
  font-weight: 700;
  color: #111827;
}

.summary-value.decline {
  color: #ef4444;
}

.temperature-analysis {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.temp-alerts {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.temp-alert {
  margin: 0;
}

.cycles-analysis {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.cycles-summary {
  margin-bottom: 20px;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.summary-card {
  background: #f9fafb;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
}

.summary-title {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 8px;
}

.summary-number {
  font-size: 24px;
  font-weight: 700;
  color: #111827;
}

.summary-number.attention {
  color: #ef4444;
}

.chart-container {
  height: 200px;
  background: #f8fafc;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  font-size: 14px;
}

/* 维护记录样式 */
.maintenance-card {
  margin-top: 24px;
}

.maintenance-records {
  max-height: 400px;
  overflow-y: auto;
}

.no-records {
  padding: 40px 0;
  text-align: center;
}

.records-timeline {
  position: relative;
  padding-left: 24px;
}

.records-timeline::before {
  content: '';
  position: absolute;
  left: 12px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #e5e7eb;
}

.record-item {
  position: relative;
  margin-bottom: 24px;
  display: flex;
  gap: 16px;
}

.record-item:last-child {
  margin-bottom: 0;
}

.record-icon {
  position: absolute;
  left: -28px;
  top: 4px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  border: 2px solid white;
  z-index: 1;
}

.record-item.inspection .record-icon {
  background: #10b981;
}

.record-item.replacement .record-icon {
  background: #ef4444;
}

.record-item.calibration .record-icon {
  background: #3b82f6;
}

.record-item.repair .record-icon {
  background: #f59e0b;
}

.record-content {
  flex: 1;
  background: #f9fafb;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e5e7eb;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.record-title {
  font-size: 14px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.record-date {
  font-size: 12px;
  color: #9ca3af;
}

.record-description {
  font-size: 13px;
  color: #6b7280;
  margin: 0 0 12px 0;
  line-height: 1.4;
}

.record-details {
  display: flex;
  gap: 16px;
  font-size: 12px;
}

.record-cost {
  color: #059669;
  font-weight: 500;
}

.record-technician {
  color: #6b7280;
}
  .header-actions {
    flex-direction: column;
    gap: 10px;
  }
}
</style>