<template>
  <div class="battery-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <el-icon class="title-icon"><Lightning /></el-icon>
            智能电池管理
          </h1>
          <p class="page-description">全面管理电池资产，实时监控健康状态</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="addBattery">
            <el-icon><Plus /></el-icon>
            添加电池
          </el-button>
          <el-button @click="refreshData">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon healthy">
            <el-icon><CircleCheck /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ batteryStats.healthy_batteries }}</div>
            <div class="stat-label">健康电池</div>
            <div class="stat-change positive">{{ healthyRate }}% 健康率</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon warning">
            <el-icon><Warning /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ batteryStats.warning_batteries }}</div>
            <div class="stat-label">预警电池</div>
            <div class="stat-change warning">{{ warningRate }}% 预警率</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon critical">
            <el-icon><CircleClose /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ batteryStats.critical_batteries }}</div>
            <div class="stat-label">异常电池</div>
            <div class="stat-change critical">{{ criticalRate }}% 异常率</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon total">
            <el-icon><Lightning /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ batteryStats.total_batteries }}</div>
            <div class="stat-label">总电池数</div>
            <div class="stat-change positive">+{{ newThisMonth }} 本月新增</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速导航 -->
    <div class="quick-nav-section">
      <div class="nav-grid">
        <div class="nav-card" @click="navigateTo('/battery-management/list')">
          <div class="nav-icon">
            <el-icon><List /></el-icon>
          </div>
          <div class="nav-content">
            <h3>电池列表</h3>
            <p>查看和管理所有电池信息</p>
          </div>
          <div class="nav-arrow">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>
        
        <div class="nav-card" @click="navigateTo('/battery-management/analysis')">
          <div class="nav-icon">
            <el-icon><TrendCharts /></el-icon>
          </div>
          <div class="nav-content">
            <h3>健康分析</h3>
            <p>电池健康度趋势和预测分析</p>
          </div>
          <div class="nav-arrow">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>
        
        <div class="nav-card" @click="navigateTo('/battery-management/maintenance')">
          <div class="nav-icon">
            <el-icon><Tools /></el-icon>
          </div>
          <div class="nav-content">
            <h3>维护记录</h3>
            <p>电池维护历史和计划管理</p>
          </div>
          <div class="nav-arrow">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>
        
        <div class="nav-card" @click="navigateTo('/battery-management/monitoring')">
          <div class="nav-icon">
            <el-icon><Monitor /></el-icon>
          </div>
          <div class="nav-content">
            <h3>实时监控</h3>
            <p>电池状态实时监控和预警</p>
          </div>
          <div class="nav-arrow">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 筛选和搜索 -->
      <div class="filter-section">
        <div class="filter-left">
          <el-input
            v-model="searchQuery"
            placeholder="搜索电池（ID、制造商、型号）"
            @input="handleSearch"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
        <div class="filter-right">
          <el-select v-model="statusFilter" placeholder="状态筛选" @change="handleFilter">
            <el-option label="全部状态" value="" />
            <el-option label="健康" value="good" />
            <el-option label="预警" value="warning" />
            <el-option label="异常" value="critical" />
          </el-select>
          <el-select v-model="manufacturerFilter" placeholder="制造商筛选" @change="handleFilter">
            <el-option label="全部制造商" value="" />
            <el-option label="宁德时代" value="宁德时代" />
            <el-option label="比亚迪" value="比亚迪" />
            <el-option label="国轩高科" value="国轩高科" />
            <el-option label="中航锂电" value="中航锂电" />
          </el-select>
        </div>
      </div>

      <!-- 电池列表 -->
      <div class="battery-list-section">
        <BatteryList 
          :batteries="filteredBatteries"
          :loading="loading"
          @view-detail="viewBatteryDetail"
          @edit-battery="editBattery"
          @delete-battery="deleteBattery"
          @view-maintenance="viewMaintenance"
        />
      </div>
    </div>

    <!-- 电池详情对话框 -->
    <BatteryDetail
      v-model="showDetailDialog"
      :battery-id="selectedBatteryId"
      @close="showDetailDialog = false"
    />

    <!-- 电池表单对话框 -->
    <BatteryForm
      v-model="showFormDialog"
      :battery-id="editingBatteryId"
      :mode="formMode"
      @success="handleFormSuccess"
      @close="showFormDialog = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Lightning, Plus, Refresh, Search, CircleCheck,
  Warning, CircleClose, List, TrendCharts, Tools,
  Monitor, ArrowRight
} from '@element-plus/icons-vue'
import { useBatteryStore } from '@/stores/battery'
import BatteryList from '@/components/BatteryList.vue'
import BatteryDetail from '@/components/BatteryDetail.vue'
import BatteryForm from '@/components/BatteryForm.vue'

// Router
const router = useRouter()

// Store
const batteryStore = useBatteryStore()

// 响应式数据
const loading = ref(false)
const searchQuery = ref('')
const statusFilter = ref('')
const manufacturerFilter = ref('')
const showDetailDialog = ref(false)
const showFormDialog = ref(false)
const selectedBatteryId = ref('')
const editingBatteryId = ref('')
const formMode = ref<'add' | 'edit'>('add')
const newThisMonth = ref(12)

// 计算属性
const batteryStats = computed(() => batteryStore.batteryStats)

const healthyRate = computed(() => {
  const total = batteryStats.value.total_batteries
  return total > 0 ? Math.round((batteryStats.value.healthy_batteries / total) * 100) : 0
})

const warningRate = computed(() => {
  const total = batteryStats.value.total_batteries
  return total > 0 ? Math.round((batteryStats.value.warning_batteries / total) * 100) : 0
})

const criticalRate = computed(() => {
  const total = batteryStats.value.total_batteries
  return total > 0 ? Math.round((batteryStats.value.critical_batteries / total) * 100) : 0
})

const filteredBatteries = computed(() => {
  let result = batteryStore.batteries
  
  // 搜索筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(battery => 
      battery.id.toLowerCase().includes(query) ||
      battery.manufacturer.toLowerCase().includes(query) ||
      battery.model.toLowerCase().includes(query)
    )
  }
  
  // 状态筛选
  if (statusFilter.value) {
    result = result.filter(battery => battery.health_status === statusFilter.value)
  }
  
  // 制造商筛选
  if (manufacturerFilter.value) {
    result = result.filter(battery => battery.manufacturer === manufacturerFilter.value)
  }
  
  return result
})

// 方法
const loadBatteries = async () => {
  loading.value = true
  try {
    await batteryStore.fetchBatteries()
  } catch (error) {
    ElMessage.error('加载电池数据失败')
  } finally {
    loading.value = false
  }
}

const navigateTo = (path: string) => {
  router.push(path)
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const handleFilter = () => {
  // 筛选逻辑已在计算属性中处理
}

const addBattery = () => {
  formMode.value = 'add'
  editingBatteryId.value = ''
  showFormDialog.value = true
}

const editBattery = (batteryId: string) => {
  formMode.value = 'edit'
  editingBatteryId.value = batteryId
  showFormDialog.value = true
}

const viewBatteryDetail = (batteryId: string) => {
  selectedBatteryId.value = batteryId
  showDetailDialog.value = true
}

const deleteBattery = async (batteryId: string) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个电池记录吗？此操作不可恢复。',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // await batteryStore.deleteBattery(batteryId)
    ElMessage.success('电池删除成功')
    await loadBatteries()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除电池失败')
    }
  }
}

const viewMaintenance = (batteryId: string) => {
  router.push(`/battery-management/maintenance?batteryId=${batteryId}`)
}

const refreshData = async () => {
  await loadBatteries()
  ElMessage.success('数据刷新成功')
}

const handleFormSuccess = async () => {
  showFormDialog.value = false
  await loadBatteries()
  ElMessage.success(formMode.value === 'add' ? '电池添加成功' : '电池更新成功')
}

// 生命周期
onMounted(() => {
  loadBatteries()
})
</script>

<style scoped>
.battery-management {
  padding: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  position: relative;
}

.battery-management::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 50%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
  pointer-events: none;
}

/* 页面头部 */
.page-header {
  position: relative;
  z-index: 1;
  padding: 32px 24px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  color: white;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 36px;
  color: #fbbf24;
}

.page-description {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 统计卡片 */
.stats-section {
  position: relative;
  z-index: 1;
  padding: 0 24px 32px;
  max-width: 1200px;
  margin: 0 auto;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.healthy {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.stat-icon.warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.stat-icon.critical {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.stat-icon.total {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: #111827;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 4px;
}

.stat-change {
  font-size: 12px;
  font-weight: 500;
}

.stat-change.positive {
  color: #10b981;
}

.stat-change.warning {
  color: #f59e0b;
}

.stat-change.critical {
  color: #ef4444;
}

/* 快速导航 */
.quick-nav-section {
  position: relative;
  z-index: 1;
  padding: 0 24px 32px;
  max-width: 1200px;
  margin: 0 auto;
}

.nav-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.nav-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.nav-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
}

.nav-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
}

.nav-content {
  flex: 1;
}

.nav-content h3 {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 4px 0;
}

.nav-content p {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.nav-arrow {
  font-size: 16px;
  color: #9ca3af;
}

/* 主要内容 */
.main-content {
  position: relative;
  z-index: 1;
  padding: 0 24px 32px;
  max-width: 1200px;
  margin: 0 auto;
}

.filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  gap: 16px;
}

.filter-left {
  flex: 1;
  max-width: 400px;
}

.filter-right {
  display: flex;
  gap: 12px;
}

.battery-list-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .header-actions {
    flex-direction: column;
    gap: 8px;
    width: 100%;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }

  .nav-grid {
    grid-template-columns: 1fr;
  }

  .filter-section {
    flex-direction: column;
    gap: 12px;
  }

  .filter-right {
    width: 100%;
    justify-content: space-between;
  }
}
</style>
