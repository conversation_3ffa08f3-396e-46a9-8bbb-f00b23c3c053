<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: white;
            color: #333;
        }
        h1 {
            color: #409eff;
        }
        .test-box {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        button {
            background: #409eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #66b1ff;
        }
    </style>
</head>
<body>
    <h1>电池健康度分析 - 静态测试页面</h1>
    <p>这是一个静态HTML页面，用于测试基本的页面显示功能。</p>
    
    <div class="test-box">
        <h2>页面信息</h2>
        <ul>
            <li>当前URL: <span id="current-url"></span></li>
            <li>页面加载时间: <span id="load-time"></span></li>
            <li>浏览器: <span id="browser-info"></span></li>
        </ul>
    </div>
    
    <div class="test-box">
        <h2>导航测试</h2>
        <button onclick="goToMonitoring()">前往监控页面</button>
        <button onclick="goToMaintenance()">前往维护页面</button>
        <button onclick="goBack()">返回上一页</button>
    </div>
    
    <div class="test-box">
        <h2>功能测试</h2>
        <p>计数器: <span id="counter">0</span></p>
        <button onclick="increment()">增加</button>
        <button onclick="decrement()">减少</button>
        <button onclick="reset()">重置</button>
    </div>

    <script>
        let counter = 0;
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('current-url').textContent = window.location.href;
            document.getElementById('load-time').textContent = new Date().toLocaleString();
            document.getElementById('browser-info').textContent = navigator.userAgent.split(' ')[0];
            console.log('静态测试页面已加载');
        });
        
        // 导航函数
        function goToMonitoring() {
            window.location.href = '/battery-management/monitoring';
        }
        
        function goToMaintenance() {
            window.location.href = '/battery-management/maintenance';
        }
        
        function goBack() {
            window.history.back();
        }
        
        // 计数器函数
        function increment() {
            counter++;
            document.getElementById('counter').textContent = counter;
        }
        
        function decrement() {
            counter--;
            document.getElementById('counter').textContent = counter;
        }
        
        function reset() {
            counter = 0;
            document.getElementById('counter').textContent = counter;
        }
    </script>
</body>
</html>
