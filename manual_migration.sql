-- 手动执行的数据库迁移脚本
-- 请在 Supabase 控制台的 SQL Editor 中执行此脚本

-- 创建 edge_devices 表
CREATE TABLE IF NOT EXISTS public.edge_devices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL CHECK (type IN ('sensor', 'controller', 'gateway', 'camera')),
    status VARCHAR(20) NOT NULL DEFAULT 'offline' CHECK (status IN ('online', 'offline', 'error')),
    location_lat DECIMAL(10, 8),
    location_lng DECIMAL(11, 8),
    location_address TEXT,
    capabilities JSONB DEFAULT '[]'::jsonb,
    last_heartbeat TIMESTAMPTZ DEFAULT NOW(),
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 创建 edge_device_data 表
CREATE TABLE IF NOT EXISTS public.edge_device_data (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    device_id UUID NOT NULL REFERENCES public.edge_devices(id) ON DELETE CASCADE,
    data_type VARCHAR(100) NOT NULL,
    payload JSONB NOT NULL,
    timestamp TIMESTAMPTZ DEFAULT NOW(),
    processed BOOLEAN DEFAULT FALSE,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 创建 edge_tasks 表
CREATE TABLE IF NOT EXISTS public.edge_tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL CHECK (type IN ('data_processing', 'ai_inference', 'real_time_analysis', 'alert_detection')),
    priority VARCHAR(20) NOT NULL DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'critical')),
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'completed', 'failed')),
    device_id UUID NOT NULL REFERENCES public.edge_devices(id) ON DELETE CASCADE,
    config JSONB DEFAULT '{}'::jsonb,
    result JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_edge_devices_status ON public.edge_devices(status);
CREATE INDEX IF NOT EXISTS idx_edge_devices_type ON public.edge_devices(type);
CREATE INDEX IF NOT EXISTS idx_edge_devices_last_heartbeat ON public.edge_devices(last_heartbeat);

CREATE INDEX IF NOT EXISTS idx_edge_device_data_device_id ON public.edge_device_data(device_id);
CREATE INDEX IF NOT EXISTS idx_edge_device_data_timestamp ON public.edge_device_data(timestamp);
CREATE INDEX IF NOT EXISTS idx_edge_device_data_processed ON public.edge_device_data(processed);

CREATE INDEX IF NOT EXISTS idx_edge_tasks_device_id ON public.edge_tasks(device_id);
CREATE INDEX IF NOT EXISTS idx_edge_tasks_status ON public.edge_tasks(status);
CREATE INDEX IF NOT EXISTS idx_edge_tasks_priority ON public.edge_tasks(priority);
CREATE INDEX IF NOT EXISTS idx_edge_tasks_created_at ON public.edge_tasks(created_at);

-- 启用行级安全策略 (RLS)
ALTER TABLE public.edge_devices ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.edge_device_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.edge_tasks ENABLE ROW LEVEL SECURITY;

-- 创建 RLS 策略（允许所有认证用户访问）
CREATE POLICY "Allow authenticated users to access edge_devices" ON public.edge_devices
    FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated users to access edge_device_data" ON public.edge_device_data
    FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated users to access edge_tasks" ON public.edge_tasks
    FOR ALL USING (auth.role() = 'authenticated');

-- 授予权限
GRANT ALL ON public.edge_devices TO authenticated;
GRANT ALL ON public.edge_device_data TO authenticated;
GRANT ALL ON public.edge_tasks TO authenticated;

GRANT ALL ON public.edge_devices TO service_role;
GRANT ALL ON public.edge_device_data TO service_role;
GRANT ALL ON public.edge_tasks TO service_role;

-- 插入初始数据
INSERT INTO public.edge_devices (id, name, type, status, location_lat, location_lng, location_address, capabilities, last_heartbeat) VALUES
('550e8400-e29b-41d4-a716-446655440001', '温度传感器-001', 'sensor', 'online', 39.9042, 116.4074, '北京市朝阳区', '["temperature", "humidity"]'::jsonb, NOW()),
('550e8400-e29b-41d4-a716-446655440002', '充电桩控制器-001', 'controller', 'online', 31.2304, 121.4737, '上海市浦东新区', '["power_control", "status_monitoring"]'::jsonb, NOW()),
('550e8400-e29b-41d4-a716-446655440003', '网关设备-001', 'gateway', 'online', 22.3193, 114.1694, '深圳市南山区', '["data_relay", "protocol_conversion"]'::jsonb, NOW()),
('550e8400-e29b-41d4-a716-446655440004', '监控摄像头-001', 'camera', 'online', 30.5728, 104.0668, '成都市高新区', '["video_capture", "motion_detection"]'::jsonb, NOW())
ON CONFLICT (id) DO NOTHING;

-- 插入一些示例数据
INSERT INTO public.edge_device_data (device_id, data_type, payload, timestamp) VALUES
('550e8400-e29b-41d4-a716-446655440001', 'sensor_reading', '{"temperature": 25.5, "humidity": 60.2, "pressure": 1013.25}'::jsonb, NOW() - INTERVAL '5 minutes'),
('550e8400-e29b-41d4-a716-446655440002', 'power_status', '{"voltage": 220.5, "current": 15.2, "power": 3351.6, "status": "charging"}'::jsonb, NOW() - INTERVAL '3 minutes'),
('550e8400-e29b-41d4-a716-446655440003', 'gateway_stats', '{"connected_devices": 12, "data_rate": 1024, "cpu_usage": 45.2, "memory_usage": 67.8}'::jsonb, NOW() - INTERVAL '2 minutes'),
('550e8400-e29b-41d4-a716-446655440004', 'camera_event', '{"motion_detected": true, "object_count": 3, "confidence": 0.95, "timestamp": "2024-01-20T10:30:00Z"}'::jsonb, NOW() - INTERVAL '1 minute')
ON CONFLICT DO NOTHING;

-- 插入一些示例任务
INSERT INTO public.edge_tasks (id, name, type, priority, status, device_id, config) VALUES
('660e8400-e29b-41d4-a716-446655440001', '数据处理任务-001', 'data_processing', 'medium', 'completed', '550e8400-e29b-41d4-a716-446655440001', '{"processing_type": "aggregation", "interval": 300}'::jsonb),
('660e8400-e29b-41d4-a716-446655440002', 'AI推理任务-001', 'ai_inference', 'high', 'running', '550e8400-e29b-41d4-a716-446655440004', '{"model": "object_detection", "threshold": 0.8}'::jsonb),
('660e8400-e29b-41d4-a716-446655440003', '实时分析任务-001', 'real_time_analysis', 'critical', 'pending', '550e8400-e29b-41d4-a716-446655440003', '{"analysis_type": "traffic_monitoring", "window_size": 60}'::jsonb)
ON CONFLICT (id) DO NOTHING;

COMMIT;

-- 验证表是否创建成功
SELECT 'edge_devices' as table_name, COUNT(*) as record_count FROM public.edge_devices
UNION ALL
SELECT 'edge_device_data' as table_name, COUNT(*) as record_count FROM public.edge_device_data
UNION ALL
SELECT 'edge_tasks' as table_name, COUNT(*) as record_count FROM public.edge_tasks;