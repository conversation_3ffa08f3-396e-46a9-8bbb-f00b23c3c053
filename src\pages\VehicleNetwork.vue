<template>
  <div class="vehicle-network-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">
        <el-icon><Connection /></el-icon>
        车联网交互中心
      </h1>
      <p class="page-subtitle">远程控制 · 故障诊断 · OTA升级 · 智能互联</p>
    </div>

    <!-- 车辆连接状态 -->
    <div class="connection-status">
      <ModernCard variant="glassmorphism" :hover-effect="true" class="status-card">
        <div class="status-content">
          <div class="status-indicator" :class="connectionStatus.status">
            <div class="pulse"></div>
          </div>
          <div class="status-info">
            <h3>{{ connectionStatus.text }}</h3>
            <p>{{ connectionStatus.description }}</p>
            <div class="status-details">
              <span>信号强度: {{ connectionStatus.signalStrength }}%</span>
              <span>延迟: {{ connectionStatus.latency }}ms</span>
              <span>在线时长: {{ connectionStatus.onlineTime }}</span>
            </div>
          </div>
          <div class="status-actions">
            <ModernButton variant="primary" size="small" @click="refreshConnection">
              刷新连接
            </ModernButton>
          </div>
        </div>
      </ModernCard>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧：远程控制和车辆状态 -->
      <div class="control-section">
        <!-- 远程控制面板 -->
        <ModernCard variant="glassmorphism" :hover-effect="true" class="remote-control-card">
          <template #header>
            <div class="card-header">
              <span>远程控制</span>
              <el-tag :type="getControlStatusType(remoteControl.enabled)" size="small">
                {{ remoteControl.enabled ? '已启用' : '已禁用' }}
              </el-tag>
            </div>
          </template>
          <div class="control-grid">
            <!-- 车门控制 -->
            <div class="control-group">
              <h4>车门控制</h4>
              <div class="control-buttons">
                <ModernButton 
                  :variant="doorStatus.frontLeft ? 'danger' : 'primary'"
                  size="small"
                  @click="toggleDoor('frontLeft')"
                >
                  <el-icon><Lock /></el-icon>
                  前左门
                </ModernButton>
                <ModernButton 
                  :variant="doorStatus.frontRight ? 'danger' : 'primary'"
                  size="small"
                  @click="toggleDoor('frontRight')"
                >
                  <el-icon><Lock /></el-icon>
                  前右门
                </ModernButton>
                <ModernButton 
                  :variant="doorStatus.rearLeft ? 'danger' : 'primary'"
                  size="small"
                  @click="toggleDoor('rearLeft')"
                >
                  <el-icon><Lock /></el-icon>
                  后左门
                </ModernButton>
                <ModernButton 
                  :variant="doorStatus.rearRight ? 'danger' : 'primary'"
                  size="small"
                  @click="toggleDoor('rearRight')"
                >
                  <el-icon><Lock /></el-icon>
                  后右门
                </ModernButton>
              </div>
            </div>

            <!-- 空调控制 -->
            <div class="control-group">
              <h4>空调控制</h4>
              <div class="climate-controls">
                <div class="climate-item">
                  <span>温度设置</span>
                  <div class="temperature-control">
                    <ModernButton size="small" @click="adjustTemperature(-1)">
                      <el-icon><Minus /></el-icon>
                    </ModernButton>
                    <span class="temperature-display">{{ climateControl.temperature }}°C</span>
                    <ModernButton size="small" @click="adjustTemperature(1)">
                      <el-icon><Plus /></el-icon>
                    </ModernButton>
                  </div>
                </div>
                <div class="climate-item">
                  <span>空调开关</span>
                  <el-switch v-model="climateControl.enabled" @change="toggleClimate" />
                </div>
                <div class="climate-item">
                  <span>风速等级</span>
                  <el-select v-model="climateControl.fanSpeed" size="small" style="width: 80px">
                    <el-option label="1" value="1" />
                    <el-option label="2" value="2" />
                    <el-option label="3" value="3" />
                    <el-option label="4" value="4" />
                  </el-select>
                </div>
              </div>
            </div>

            <!-- 车灯控制 -->
            <div class="control-group">
              <h4>车灯控制</h4>
              <div class="light-controls">
                <div class="light-item">
                  <span>前大灯</span>
                  <el-switch v-model="lightControl.headlights" @change="toggleLight('headlights')" />
                </div>
                <div class="light-item">
                  <span>雾灯</span>
                  <el-switch v-model="lightControl.fogLights" @change="toggleLight('fogLights')" />
                </div>
                <div class="light-item">
                  <span>转向灯</span>
                  <el-radio-group v-model="lightControl.turnSignal" @change="setTurnSignal">
                    <el-radio label="off">关闭</el-radio>
                    <el-radio label="left">左转</el-radio>
                    <el-radio label="right">右转</el-radio>
                  </el-radio-group>
                </div>
              </div>
            </div>

            <!-- 引擎控制 -->
            <div class="control-group">
              <h4>引擎控制</h4>
              <div class="engine-controls" style="position: relative;">
                <ModernLoader v-if="engineStatus.loading" />
                <ModernButton 
                  :variant="engineStatus.running ? 'danger' : 'success'"
                  @click="toggleEngine"
                  :disabled="engineStatus.loading"
                >
                  <el-icon><VideoPlay /></el-icon>
                  {{ engineStatus.running ? '熄火' : '启动' }}
                </ModernButton>
                <ModernButton variant="warning" @click="preHeat" :disabled="engineStatus.running">
                  <el-icon><Sunny /></el-icon>
                  预热
                </ModernButton>
              </div>
            </div>
          </div>
        </ModernCard>

        <!-- 车辆实时状态 -->
        <ModernCard variant="glassmorphism" :hover-effect="true" class="vehicle-status-card">
          <template #header>
            <div class="card-header">
              <span>车辆实时状态</span>
              <ModernButton variant="ghost" @click="refreshStatus">
                <el-icon><Refresh /></el-icon>
                刷新
              </ModernButton>
            </div>
          </template>
          <div class="status-grid">
            <div class="status-item">
              <div class="status-icon battery">
                <el-icon><Lightning /></el-icon>
              </div>
              <div class="status-info">
                <h4>{{ vehicleStatus.battery }}%</h4>
                <p>电池电量</p>
                <div class="battery-bar">
                  <div class="battery-fill" :style="{ width: vehicleStatus.battery + '%' }"></div>
                </div>
              </div>
            </div>

            <div class="status-item">
              <div class="status-icon location">
                <el-icon><LocationInformation /></el-icon>
              </div>
              <div class="status-info">
                <h4>{{ vehicleStatus.location }}</h4>
                <p>当前位置</p>
                <span class="location-time">{{ vehicleStatus.locationTime }}</span>
              </div>
            </div>

            <div class="status-item">
              <div class="status-icon mileage">
                <el-icon><Odometer /></el-icon>
              </div>
              <div class="status-info">
                <h4>{{ vehicleStatus.mileage }}</h4>
                <p>总里程(km)</p>
                <span class="mileage-today">今日: {{ vehicleStatus.todayMileage }}km</span>
              </div>
            </div>

            <div class="status-item">
              <div class="status-icon temperature">
                <el-icon><Thermometer /></el-icon>
              </div>
              <div class="status-info">
                <h4>{{ vehicleStatus.temperature }}°C</h4>
                <p>车内温度</p>
                <span class="temp-status">{{ vehicleStatus.tempStatus }}</span>
              </div>
            </div>
          </div>
        </ModernCard>
      </div>

      <!-- 右侧：故障诊断和OTA升级 -->
      <div class="diagnostic-section">
        <!-- 故障诊断 -->
        <ModernCard variant="glassmorphism" :hover-effect="true" class="diagnostic-card">
          <template #header>
            <div class="card-header">
              <span>故障诊断</span>
              <ModernButton variant="primary" size="small" @click="startDiagnostic">
                开始诊断
              </ModernButton>
            </div>
          </template>
          <div class="diagnostic-content">
            <!-- 诊断进度 -->
            <div v-if="diagnostic.running" class="diagnostic-progress">
              <el-progress 
                :percentage="diagnostic.progress" 
                :status="diagnostic.status"
                :stroke-width="8"
              />
              <p class="progress-text">{{ diagnostic.currentStep }}</p>
            </div>

            <!-- 诊断结果 -->
            <div v-else class="diagnostic-results">
              <div class="result-summary">
                <div class="summary-item">
                  <span class="summary-label">系统状态:</span>
                  <el-tag :type="getSystemStatusType(diagnostic.systemStatus)" size="small">
                    {{ diagnostic.systemStatus }}
                  </el-tag>
                </div>
                <div class="summary-item">
                  <span class="summary-label">故障数量:</span>
                  <span class="summary-value">{{ diagnostic.faultCount }}</span>
                </div>
                <div class="summary-item">
                  <span class="summary-label">上次诊断:</span>
                  <span class="summary-value">{{ formatDateTime(diagnostic.lastDiagnostic) }}</span>
                </div>
              </div>

              <!-- 故障列表 -->
              <div class="fault-list">
                <h5>检测到的问题</h5>
                <div v-for="fault in diagnostic.faults" :key="fault.id" class="fault-item">
                  <div class="fault-icon" :class="fault.severity">
                    <el-icon><Warning /></el-icon>
                  </div>
                  <div class="fault-info">
                    <h6>{{ fault.title }}</h6>
                    <p class="fault-description">{{ fault.description }}</p>
                    <div class="fault-details">
                      <span class="fault-code">错误代码: {{ fault.code }}</span>
                      <span class="fault-time">{{ formatDateTime(fault.timestamp) }}</span>
                    </div>
                  </div>
                  <div class="fault-actions">
                    <ModernButton variant="ghost" size="small" @click="viewFaultDetail(fault)">
                      详情
                    </ModernButton>
                    <ModernButton variant="ghost" size="small" @click="fixFault(fault)">
                      修复
                    </ModernButton>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ModernCard>

        <!-- OTA升级 -->
        <ModernCard variant="glassmorphism" :hover-effect="true" class="ota-card">
          <template #header>
            <div class="card-header">
              <span>OTA升级</span>
              <div style="position: relative;">
                <ModernLoader v-if="ota.checking" />
                <ModernButton variant="success" size="small" @click="checkUpdates" :disabled="ota.checking">
                  检查更新
                </ModernButton>
              </div>
            </div>
          </template>
          <div class="ota-content">
            <!-- 当前版本信息 -->
            <div class="version-info">
              <div class="version-item">
                <span class="version-label">系统版本:</span>
                <span class="version-value">{{ ota.currentVersion }}</span>
              </div>
              <div class="version-item">
                <span class="version-label">发布日期:</span>
                <span class="version-value">{{ ota.releaseDate }}</span>
              </div>
              <div class="version-item">
                <span class="version-label">更新状态:</span>
                <el-tag :type="getUpdateStatusType(ota.updateStatus)" size="small">
                  {{ ota.updateStatus }}
                </el-tag>
              </div>
            </div>

            <!-- 可用更新 -->
            <div v-if="ota.availableUpdate" class="available-update">
              <div class="update-header">
                <h5>可用更新: {{ ota.availableUpdate.version }}</h5>
                <el-tag type="success" size="small">{{ ota.availableUpdate.size }}</el-tag>
              </div>
              <div class="update-description">
                <p>{{ ota.availableUpdate.description }}</p>
                <div class="update-features">
                  <h6>更新内容:</h6>
                  <ul>
                    <li v-for="feature in ota.availableUpdate.features" :key="feature">
                      {{ feature }}
                    </li>
                  </ul>
                </div>
              </div>
              <div class="update-actions">
                <ModernButton 
                  variant="primary" 
                  @click="startUpdate" 
                  :loading="ota.updating"
                  :disabled="ota.updating"
                >
                  {{ ota.updating ? '更新中...' : '立即更新' }}
                </ModernButton>
                <ModernButton @click="scheduleUpdate">
                  定时更新
                </ModernButton>
              </div>
            </div>

            <!-- 更新进度 -->
            <div v-if="ota.updating" class="update-progress">
              <el-progress 
                :percentage="ota.progress" 
                :status="ota.progressStatus"
                :stroke-width="8"
              />
              <p class="progress-text">{{ ota.progressText }}</p>
              <div class="progress-details">
                <span>下载速度: {{ ota.downloadSpeed }}</span>
                <span>剩余时间: {{ ota.remainingTime }}</span>
              </div>
            </div>

            <!-- 更新历史 -->
            <div class="update-history">
              <h5>更新历史</h5>
              <div class="history-list">
                <div v-for="update in ota.updateHistory" :key="update.id" class="history-item">
                  <div class="history-info">
                    <span class="history-version">{{ update.version }}</span>
                    <span class="history-date">{{ formatDate(update.date) }}</span>
                  </div>
                  <div class="history-status">
                    <el-tag :type="getHistoryStatusType(update.status)" size="small">
                      {{ update.status }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ModernCard>

        <!-- 网络统计 -->
        <ModernCard variant="glassmorphism" :hover-effect="true" class="network-stats-card">
          <template #header>
            <span>网络统计</span>
          </template>
          <div class="stats-content">
            <div ref="networkChartRef" class="chart-container"></div>
            <div class="stats-summary">
              <div class="stat-item">
                <span class="stat-label">今日流量:</span>
                <span class="stat-value">{{ networkStats.todayTraffic }}MB</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">本月流量:</span>
                <span class="stat-value">{{ networkStats.monthlyTraffic }}GB</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">平均延迟:</span>
                <span class="stat-value">{{ networkStats.avgLatency }}ms</span>
              </div>
            </div>
          </div>
        </ModernCard>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import {
  Connection,
  Lock,
  Minus,
  Plus,
  VideoPlay,
  Sunny,
  Refresh,
  Lightning,
  LocationInformation,
  Odometer,
  Warning
} from '@element-plus/icons-vue'
import { Thermometer } from 'lucide-vue-next'
import { ElMessage, ElMessageBox } from 'element-plus'
import ModernButton from '@/components/ModernButton.vue'
import ModernCard from '@/components/ModernCard.vue'
import ModernLoader from '@/components/ModernLoader.vue'
import { useVehicleStore } from '@/stores/vehicle'

// 使用vehicle store
const vehicleStore = useVehicleStore()

// 连接状态 - 从API获取真实数据
const connectionStatus = ref({
  status: 'connected',
  text: '已连接',
  description: '车辆网络连接正常',
  signalStrength: 85,
  latency: 45,
  onlineTime: '2小时15分钟'
})

// 远程控制状态
const remoteControl = ref({
  enabled: true
})

// 车门状态
const doorStatus = ref({
  frontLeft: false,
  frontRight: false,
  rearLeft: false,
  rearRight: false
})

// 空调控制
const climateControl = ref({
  enabled: false,
  temperature: 22,
  fanSpeed: '2'
})

// 车灯控制
const lightControl = ref({
  headlights: false,
  fogLights: false,
  turnSignal: 'off'
})

// 引擎状态
const engineStatus = ref({
  running: false,
  loading: false
})

// 车辆状态 - 使用computed从store和API获取真实数据
const vehicleStatus = ref({
  battery: 78,
  location: '北京市朝阳区',
  locationTime: '2分钟前',
  mileage: 15680,
  todayMileage: 45,
  temperature: 23,
  tempStatus: '舒适'
})

// 获取车辆状态数据
const fetchVehicleStatus = async () => {
  try {
    const response = await fetch('/api/vehicles/status')
    const data = await response.json()
    if (data.success && data.data) {
      vehicleStatus.value = {
        battery: data.data.battery_level || vehicleStatus.value.battery,
        location: data.data.location || vehicleStatus.value.location,
        locationTime: data.data.location_time || vehicleStatus.value.locationTime,
        mileage: data.data.mileage || vehicleStatus.value.mileage,
        todayMileage: data.data.today_mileage || vehicleStatus.value.todayMileage,
        temperature: data.data.temperature || vehicleStatus.value.temperature,
        tempStatus: data.data.temp_status || vehicleStatus.value.tempStatus
      }
    }
  } catch (error) {
    console.error('获取车辆状态失败:', error)
  }
}

// 故障诊断 - 从API获取真实数据
const diagnostic = ref({
  running: false,
  progress: 0,
  status: 'success',
  currentStep: '等待开始诊断',
  systemStatus: '正常',
  faultCount: 0,
  lastDiagnostic: new Date(),
  faults: []
})

// 获取故障诊断数据
const fetchDiagnosticData = async () => {
  try {
    const response = await fetch('/api/vehicles/diagnostic')
    const data = await response.json()
    if (data.success && data.data) {
      diagnostic.value = {
        ...diagnostic.value,
        systemStatus: data.data.system_status || '正常',
        faultCount: data.data.fault_count || 0,
        lastDiagnostic: data.data.last_diagnostic ? new Date(data.data.last_diagnostic) : new Date(),
        faults: data.data.faults || []
      }
    }
  } catch (error) {
    console.error('获取故障诊断数据失败:', error)
    // 使用默认数据
    diagnostic.value.faults = [
      {
        id: 'F001',
        title: '轮胎气压不足',
        description: '左前轮胎气压低于标准值，建议及时充气',
        code: 'P0171',
        severity: 'warning',
        timestamp: new Date(Date.now() - 30 * 60 * 1000)
      },
      {
        id: 'F002',
        title: '空调滤芯需更换',
        description: '空调滤芯使用时间过长，影响空气质量',
        code: 'P0420',
        severity: 'info',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000)
      }
    ]
    diagnostic.value.faultCount = 2
    diagnostic.value.lastDiagnostic = new Date(Date.now() - 2 * 60 * 60 * 1000)
  }
}

// OTA升级 - 从API获取真实数据
const ota = ref({
  checking: false,
  currentVersion: 'v2.1.5',
  releaseDate: '2024-01-15',
  updateStatus: '最新版本',
  availableUpdate: null,
  updating: false,
  progress: 0,
  progressStatus: 'success',
  progressText: '准备更新...',
  downloadSpeed: '0MB/s',
  remainingTime: '0分钟',
  updateHistory: []
})

// 获取OTA升级数据
const fetchOTAData = async () => {
  try {
    const response = await fetch('/api/vehicles/ota')
    const data = await response.json()
    if (data.success && data.data) {
      ota.value = {
        ...ota.value,
        currentVersion: data.data.current_version || ota.value.currentVersion,
        releaseDate: data.data.release_date || ota.value.releaseDate,
        updateStatus: data.data.update_status || '最新版本',
        availableUpdate: data.data.available_update || null,
        updateHistory: data.data.update_history || []
      }
    }
  } catch (error) {
    console.error('获取OTA升级数据失败:', error)
    // 使用默认数据
    ota.value.availableUpdate = {
      version: 'v2.2.0',
      size: '156MB',
      description: '本次更新包含性能优化、新功能添加和安全补丁',
      features: [
        '优化电池管理算法',
        '新增智能泊车功能',
        '修复已知安全漏洞',
        '改进用户界面体验'
      ]
    }
    ota.value.updateHistory = [
      {
        id: 'U001',
        version: 'v2.1.5',
        date: new Date('2024-01-15'),
        status: '已安装'
      },
      {
        id: 'U002',
        version: 'v2.1.4',
        date: new Date('2023-12-20'),
        status: '已安装'
      },
      {
        id: 'U003',
        version: 'v2.1.3',
        date: new Date('2023-11-25'),
        status: '已安装'
      }
    ]
  }
}

// 网络统计 - 从API获取真实数据
const networkStats = ref({
  todayTraffic: 0,
  monthlyTraffic: 0,
  avgLatency: 0
})

// 获取网络统计数据
const fetchNetworkStats = async () => {
  try {
    const response = await fetch('/api/vehicles/network/stats')
    const data = await response.json()
    if (data.success && data.data) {
      networkStats.value = {
        todayTraffic: data.data.today_traffic || 125,
        monthlyTraffic: data.data.monthly_traffic || 3.2,
        avgLatency: data.data.avg_latency || 42
      }
    }
  } catch (error) {
    console.error('获取网络统计数据失败:', error)
    // 使用默认数据
    networkStats.value = {
      todayTraffic: 125,
      monthlyTraffic: 3.2,
      avgLatency: 42
    }
  }
}

// 图表相关
const networkChartRef = ref<HTMLElement>()
let networkChart: echarts.ECharts | null = null

// 方法
const getControlStatusType = (enabled: boolean) => {
  return enabled ? 'success' : 'danger'
}

const getSystemStatusType = (status: string) => {
  const types = {
    '正常': 'success',
    '警告': 'warning',
    '故障': 'danger'
  }
  return types[status] || 'info'
}

const getUpdateStatusType = (status: string) => {
  const types = {
    '最新版本': 'success',
    '有可用更新': 'warning',
    '更新中': 'info'
  }
  return types[status] || 'info'
}

const getHistoryStatusType = (status: string) => {
  const types = {
    '已安装': 'success',
    '安装失败': 'danger',
    '已回滚': 'warning'
  }
  return types[status] || 'info'
}

const refreshConnection = async () => {
  try {
    const response = await fetch('/api/vehicles/connection/status');
    const data = await response.json();
    if (data.success) {
      connectionStatus.value = {
        ...connectionStatus.value,
        ...data.data
      };
      ElMessage.success('连接状态已刷新');
    } else {
      ElMessage.error('刷新连接状态失败');
    }
  } catch (error) {
    console.error('刷新连接失败:', error);
    ElMessage.error('刷新连接失败');
  }
}

const toggleDoor = async (door: string) => {
  try {
    const action = doorStatus.value[door] ? 'lock' : 'unlock';
    const response = await fetch('/api/vehicles/control/door', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        door: door,
        action: action
      })
    });
    const data = await response.json();
    if (data.success) {
      doorStatus.value[door] = !doorStatus.value[door];
      const actionText = doorStatus.value[door] ? '解锁' : '锁定';
      ElMessage.success(`${door}已${actionText}`);
    } else {
      ElMessage.error(data.message || '车门控制失败');
    }
  } catch (error) {
    console.error('车门控制失败:', error);
    ElMessage.error('车门控制失败');
  }
}

const adjustTemperature = async (delta: number) => {
  const newTemp = climateControl.value.temperature + delta
  if (newTemp >= 16 && newTemp <= 30) {
    try {
      const response = await fetch('/api/vehicles/control/climate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'set_temperature',
          temperature: newTemp
        })
      });
      const data = await response.json();
      if (data.success) {
        climateControl.value.temperature = newTemp;
        ElMessage.success(`温度已调节至${newTemp}°C`);
      } else {
        ElMessage.error(data.message || '温度调节失败');
      }
    } catch (error) {
      console.error('温度调节失败:', error);
      ElMessage.error('温度调节失败');
    }
  }
}

const toggleClimate = async () => {
  try {
    const response = await fetch('/api/vehicles/control/climate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        action: climateControl.value.enabled ? 'turn_off' : 'turn_on',
        temperature: climateControl.value.temperature,
        fan_speed: climateControl.value.fanSpeed
      })
    });
    const data = await response.json();
    if (data.success) {
      const action = climateControl.value.enabled ? '关闭' : '开启';
      ElMessage.success(`空调已${action}`);
    } else {
      ElMessage.error(data.message || '空调控制失败');
    }
  } catch (error) {
    console.error('空调控制失败:', error);
    ElMessage.error('空调控制失败');
  }
}

const toggleLight = async (light: string) => {
  try {
    const response = await fetch('/api/vehicles/control/lights', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        light_type: light,
        action: lightControl.value[light] ? 'turn_off' : 'turn_on'
      })
    });
    const data = await response.json();
    if (data.success) {
      const action = lightControl.value[light] ? '关闭' : '开启';
      ElMessage.success(`${light}已${action}`);
    } else {
      ElMessage.error(data.message || '车灯控制失败');
    }
  } catch (error) {
    console.error('车灯控制失败:', error);
    ElMessage.error('车灯控制失败');
  }
}

const setTurnSignal = async () => {
  try {
    const response = await fetch('/api/vehicles/control/lights', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        light_type: 'turn_signal',
        action: lightControl.value.turnSignal
      })
    });
    const data = await response.json();
    if (data.success) {
      ElMessage.success(`转向灯设置为${lightControl.value.turnSignal}`);
    } else {
      ElMessage.error(data.message || '转向灯控制失败');
    }
  } catch (error) {
    console.error('转向灯控制失败:', error);
    ElMessage.error('转向灯控制失败');
  }
}

const toggleEngine = async () => {
  engineStatus.value.loading = true
  
  try {
    const response = await fetch('/api/vehicles/control/engine', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        action: engineStatus.value.running ? 'stop' : 'start'
      })
    });
    const data = await response.json();
    if (data.success) {
      engineStatus.value.running = !engineStatus.value.running;
      const action = engineStatus.value.running ? '启动' : '熄火';
      ElMessage.success(`引擎${action}成功`);
    } else {
      ElMessage.error(data.message || '引擎控制失败');
    }
  } catch (error) {
    console.error('引擎控制失败:', error);
    ElMessage.error('引擎控制失败');
  } finally {
    engineStatus.value.loading = false
  }
}

const preHeat = async () => {
  try {
    const response = await fetch('/api/vehicles/control/engine', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        action: 'preheat'
      })
    });
    const data = await response.json();
    if (data.success) {
      ElMessage.success('引擎预热已启动');
    } else {
      ElMessage.error(data.message || '引擎预热失败');
    }
  } catch (error) {
    console.error('引擎预热失败:', error);
    ElMessage.error('引擎预热失败');
  }
}

const refreshStatus = async () => {
  try {
    const response = await fetch('/api/vehicles/status/realtime');
    const data = await response.json();
    if (data.success) {
      vehicleStatus.value = {
        ...vehicleStatus.value,
        ...data.data
      };
      ElMessage.success('车辆状态已刷新');
    } else {
      ElMessage.error('刷新车辆状态失败');
    }
  } catch (error) {
    console.error('刷新状态失败:', error);
    ElMessage.error('刷新状态失败');
  }
}

const startDiagnostic = async () => {
  diagnostic.value.running = true
  diagnostic.value.progress = 0
  diagnostic.value.currentStep = '初始化诊断系统...'
  
  try {
    const response = await fetch('/api/vehicles/diagnostic/start', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    const data = await response.json();
    
    if (data.success) {
      const steps = [
        '检查引擎系统...',
        '检查电池系统...',
        '检查制动系统...',
        '检查传动系统...',
        '检查电子系统...',
        '生成诊断报告...'
      ]
      
      for (let i = 0; i < steps.length; i++) {
        diagnostic.value.currentStep = steps[i]
        diagnostic.value.progress = Math.round(((i + 1) / steps.length) * 100)
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
      
      // 获取诊断结果
      const resultResponse = await fetch('/api/vehicles/diagnostic/result');
      const resultData = await resultResponse.json();
      
      if (resultData.success) {
        diagnostic.value.systemStatus = resultData.data.systemStatus;
        diagnostic.value.faultCount = resultData.data.faultCount;
        diagnostic.value.faults = resultData.data.faults || [];
        diagnostic.value.lastDiagnostic = new Date();
      }
      
      diagnostic.value.running = false
      diagnostic.value.currentStep = '诊断完成'
      ElMessage.success('车辆诊断完成')
    } else {
      diagnostic.value.running = false
      ElMessage.error(data.message || '诊断启动失败');
    }
  } catch (error) {
    diagnostic.value.running = false
    console.error('诊断失败:', error);
    ElMessage.error('诊断失败');
  }
}

const viewFaultDetail = async (fault: any) => {
  try {
    const response = await fetch(`/api/vehicles/diagnostic/fault/${fault.id}`);
    const data = await response.json();
    
    if (data.success) {
      const faultDetail = data.data;
      ElMessageBox.alert(
        `<div style="text-align: left;">
          <h3>${fault.title}</h3>
          <p><strong>错误代码:</strong> ${fault.code}</p>
          <p><strong>严重程度:</strong> ${fault.severity}</p>
          <p><strong>描述:</strong> ${fault.description}</p>
          <p><strong>可能原因:</strong> ${faultDetail.possibleCauses || '分析中...'}</p>
          <p><strong>建议解决方案:</strong> ${faultDetail.solutions || '请联系技术支持'}</p>
          <p><strong>发生时间:</strong> ${formatDateTime(fault.timestamp)}</p>
        </div>`,
        '故障详情',
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确定'
        }
      );
    } else {
      ElMessageBox.alert(fault.description, fault.title, {
        confirmButtonText: '确定'
      });
    }
  } catch (error) {
    console.error('获取故障详情失败:', error);
    ElMessageBox.alert(fault.description, fault.title, {
      confirmButtonText: '确定'
    });
  }
}

const fixFault = async (fault: any) => {
  try {
    const response = await fetch('/api/vehicles/diagnostic/fix', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        fault_id: fault.id,
        fault_code: fault.code
      })
    });
    const data = await response.json();
    
    if (data.success) {
      ElMessage.success(`故障修复指令已发送: ${fault.title}`);
      // 重新获取故障列表
      await startDiagnostic();
    } else {
      ElMessage.error(data.message || '故障修复失败');
    }
  } catch (error) {
    console.error('故障修复失败:', error);
    ElMessage.error('故障修复失败');
  }
}

const checkUpdates = async () => {
  ota.value.checking = true
  
  try {
    const response = await fetch('/api/vehicles/ota/check-updates');
    const data = await response.json();
    
    if (data.success) {
      if (data.data.hasUpdate) {
        ota.value.updateStatus = '有可用更新';
        ota.value.availableUpdate = data.data.updateInfo;
        ElMessage.success('发现新版本更新');
      } else {
        ota.value.updateStatus = '最新版本';
        ElMessage.info('当前已是最新版本');
      }
    } else {
      ElMessage.error(data.message || '检查更新失败');
    }
  } catch (error) {
    console.error('检查更新失败:', error);
    ElMessage.error('检查更新失败');
  } finally {
    ota.value.checking = false
  }
}

const startUpdate = async () => {
  try {
    // 确认更新
    await ElMessageBox.confirm(
      `确定要更新到版本 ${ota.value.availableUpdate.version} 吗？\n\n更新内容：\n${ota.value.availableUpdate.features.join('\n')}\n\n更新大小：${ota.value.availableUpdate.size}`,
      '确认更新',
      {
        confirmButtonText: '开始更新',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    ota.value.updating = true
    ota.value.progress = 0
    ota.value.progressText = '开始下载更新包...'
    
    const response = await fetch('/api/vehicles/ota/start-update', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        version: ota.value.availableUpdate.version
      })
    });
    const data = await response.json();
    
    if (data.success) {
      const updateSteps = [
        { text: '下载更新包...', progress: 30 },
        { text: '验证更新包...', progress: 50 },
        { text: '安装更新...', progress: 80 },
        { text: '重启系统...', progress: 100 }
      ]
      
      for (const step of updateSteps) {
        ota.value.progressText = step.text
        ota.value.progress = step.progress
        ota.value.downloadSpeed = '2.5MB/s'
        ota.value.remainingTime = `${Math.ceil((100 - step.progress) / 10)}分钟`
        await new Promise(resolve => setTimeout(resolve, 1500))
      }
      
      // 更新历史记录
      ota.value.updateHistory.unshift({
        id: `U${Date.now()}`,
        version: ota.value.availableUpdate.version,
        date: new Date(),
        status: '已安装'
      });
      
      ota.value.updating = false
      ota.value.currentVersion = ota.value.availableUpdate.version
      ota.value.updateStatus = '最新版本'
      ota.value.availableUpdate = null
      ElMessage.success('系统更新完成')
    } else {
      ota.value.updating = false
      ElMessage.error(data.message || '更新启动失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      ota.value.updating = false
      console.error('更新失败:', error);
      ElMessage.error('更新失败');
    }
  }
}

const scheduleUpdate = async () => {
  try {
    const { value: scheduleTime } = await ElMessageBox.prompt(
      '请选择定时更新时间（24小时制，格式：HH:MM）',
      '定时更新',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/,
        inputErrorMessage: '请输入正确的时间格式（HH:MM）'
      }
    );
    
    const response = await fetch('/api/vehicles/ota/schedule-update', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        schedule_time: scheduleTime,
        version: ota.value.availableUpdate?.version
      })
    });
    const data = await response.json();
    
    if (data.success) {
      ElMessage.success(`定时更新已设置，将在 ${scheduleTime} 自动更新`);
    } else {
      ElMessage.error(data.message || '定时更新设置失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('定时更新设置失败:', error);
      ElMessage.error('定时更新设置失败');
    }
  }
}

const formatDateTime = (date: Date) => {
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatDate = (date: Date) => {
  return date.toLocaleDateString('zh-CN')
}

// 初始化网络统计图表
const initNetworkChart = () => {
  if (!networkChartRef.value) return
  
  networkChart = echarts.init(networkChartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00']
    },
    yAxis: {
      type: 'value',
      name: '流量(MB)'
    },
    series: [
      {
        name: '网络流量',
        type: 'line',
        data: [5, 8, 15, 25, 35, 28, 20],
        smooth: true,
        itemStyle: { color: '#409EFF' },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
              { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
            ]
          }
        }
      }
    ]
  }
  
  networkChart.setOption(option)
}

// 生命周期
onMounted(() => {
  initNetworkChart()
  
  // 初始化数据
  fetchVehicleStatus()
  fetchDiagnosticData()
  fetchOTAData()
  fetchNetworkStats()
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    networkChart?.resize()
  })
  
  // 定期刷新真实数据
  const updateInterval = setInterval(async () => {
    await Promise.all([
      fetchVehicleStatus(),
      fetchNetworkStats()
    ])
  }, 30000) // 每30秒刷新一次
  
  // 清理定时器
  onUnmounted(() => {
    clearInterval(updateInterval)
  })
})

onUnmounted(() => {
  networkChart?.dispose()
  window.removeEventListener('resize', () => {
    networkChart?.resize()
  })
})
</script>

<style scoped>
.vehicle-network-container {
  padding: 24px;
  background: #ffffff;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 32px;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.page-subtitle {
  font-size: 16px;
  color: #718096;
  margin: 12px 0 0 0;
}

.connection-status {
  margin-bottom: 30px;
}

.status-card {
  border-radius: 12px;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.status-content {
  display: flex;
  align-items: center;
  padding: 20px;
}

.status-indicator {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  position: relative;
}

.status-indicator.connected {
  background: #059669;
}

.status-indicator.disconnected {
  background: #dc2626;
}

.pulse {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: inherit;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.3);
    opacity: 0;
  }
}

.status-info {
  flex: 1;
}

.status-info h3 {
  font-size: 20px;
  font-weight: bold;
  margin: 0 0 5px 0;
  color: #2c3e50;
}

.status-info p {
  font-size: 14px;
  color: #7f8c8d;
  margin: 0 0 10px 0;
}

.status-details {
  display: flex;
  gap: 20px;
  font-size: 12px;
  color: #606266;
}

.main-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.control-section,
.diagnostic-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.remote-control-card,
.vehicle-status-card,
.diagnostic-card,
.ota-card,
.network-stats-card {
  border-radius: 12px;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.control-grid {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 10px 0;
}

.control-group h4 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 14px;
}

.control-buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.control-buttons .modern-button {
  min-height: 36px !important;
  padding: 8px 12px !important;
  white-space: nowrap !important;
  line-height: 1 !important;
}

.control-buttons .modern-button > span {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 6px !important;
}

.climate-controls,
.light-controls {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.climate-item,
.light-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8fafc;
  border-radius: 6px;
}

.temperature-control {
  display: flex;
  align-items: center;
  gap: 10px;
}

.temperature-display {
  min-width: 50px;
  text-align: center;
  font-weight: bold;
  color: #2c3e50;
}

.engine-controls {
  display: flex;
  gap: 10px;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  padding: 10px 0;
}

.status-item {
  display: flex;
  align-items: center;
  padding: 15px;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 4px solid #409EFF;
}

.status-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 18px;
  color: white;
}

.status-icon.battery {
  background: #059669;
}

.status-icon.location {
  background: #3b82f6;
}

.status-icon.mileage {
  background: #d97706;
}

.status-icon.temperature {
  background: #dc2626;
}

.status-info h4 {
  font-size: 18px;
  font-weight: bold;
  margin: 0;
  color: #2c3e50;
}

.status-info p {
  font-size: 12px;
  color: #7f8c8d;
  margin: 5px 0;
}

.battery-bar {
  width: 100%;
  height: 4px;
  background: #f0f0f0;
  border-radius: 2px;
  overflow: hidden;
  margin-top: 5px;
}

.battery-fill {
  height: 100%;
  background: #059669;
  transition: width 0.3s ease;
}

.location-time,
.mileage-today,
.temp-status {
  font-size: 10px;
  color: #909399;
}

.diagnostic-progress,
.update-progress {
  margin-bottom: 20px;
}

.progress-text {
  text-align: center;
  margin: 10px 0;
  color: #606266;
  font-size: 14px;
}

.progress-details {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #909399;
  margin-top: 10px;
}

.result-summary {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 20px;
  padding: 15px;
  background: #f8fafc;
  border-radius: 8px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-label {
  font-size: 14px;
  color: #606266;
}

.summary-value {
  font-size: 14px;
  font-weight: bold;
  color: #2c3e50;
}

.fault-list h5 {
  margin: 0 0 15px 0;
  color: #2c3e50;
}

.fault-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  margin-bottom: 10px;
}

.fault-icon {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 16px;
  color: white;
}

.fault-icon.warning {
  background: #E6A23C;
}

.fault-icon.info {
  background: #409EFF;
}

.fault-icon.danger {
  background: #F56C6C;
}

.fault-info {
  flex: 1;
}

.fault-info h6 {
  margin: 0 0 5px 0;
  color: #2c3e50;
  font-size: 14px;
}

.fault-description {
  margin: 0 0 8px 0;
  color: #606266;
  font-size: 12px;
}

.fault-details {
  display: flex;
  gap: 15px;
  font-size: 10px;
  color: #909399;
}

.version-info {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 20px;
  padding: 15px;
  background: #f8fafc;
  border-radius: 8px;
}

.version-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.version-label {
  font-size: 14px;
  color: #606266;
}

.version-value {
  font-size: 14px;
  font-weight: bold;
  color: #2c3e50;
}

.available-update {
  border: 1px solid #67C23A;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  background: #f0f9ff;
}

.update-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.update-header h5 {
  margin: 0;
  color: #2c3e50;
}

.update-description p {
  margin: 0 0 10px 0;
  color: #606266;
  font-size: 14px;
}

.update-features h6 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 14px;
}

.update-features ul {
  margin: 0;
  padding-left: 20px;
  color: #606266;
  font-size: 12px;
}

.update-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.update-history h5 {
  margin: 0 0 15px 0;
  color: #2c3e50;
}

.history-list {
  max-height: 150px;
  overflow-y: auto;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
}

.history-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.history-version {
  font-size: 14px;
  font-weight: bold;
  color: #2c3e50;
}

.history-date {
  font-size: 12px;
  color: #909399;
}

.chart-container {
  height: 200px;
  width: 100%;
  margin-bottom: 15px;
}

.stats-summary {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8fafc;
  border-radius: 6px;
}

.stat-label {
  font-size: 12px;
  color: #606266;
}

.stat-value {
  font-size: 12px;
  font-weight: bold;
  color: #2c3e50;
}

@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 1fr;
  }
  
  .control-buttons {
    grid-template-columns: 1fr;
  }
  
  .status-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .vehicle-network-container {
    padding: 15px;
  }
  
  .page-title {
    font-size: 24px;
  }
  
  .status-content {
    flex-direction: column;
    text-align: center;
  }
  
  .status-indicator {
    margin-right: 0;
    margin-bottom: 15px;
  }
  
  .status-details {
    justify-content: center;
  }
}
</style>