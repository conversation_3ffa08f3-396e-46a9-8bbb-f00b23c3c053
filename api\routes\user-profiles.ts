import { Router } from 'express'
import { db } from '../config/mysql'
import type { ApiResponse } from '../../shared/types'

const router = Router()

// 获取用户档案
router.get('/profile', async (req, res) => {
  try {
    const { user_id } = req.query
    
    // 模拟用户档案数据
    const profile = {
      user_id: user_id || 1,
      username: 'demo_user',
      email: '<EMAIL>',
      phone: '138****8888',
      avatar: '/avatars/default.png',
      created_at: '2024-01-01T00:00:00.000Z',
      preferences: {
        theme: 'auto',
        language: 'zh-CN',
        notifications: {
          email: true,
          sms: false,
          push: true
        },
        privacy: {
          shareLocation: true,
          shareDrivingData: false
        }
      },
      stats: {
        totalDriving: 15420, // km
        totalCharging: 245,
        carbonSaved: 2.8, // tons
        joinDays: 156
      }
    }
    
    res.json({
      success: true,
      data: profile
    } as ApiResponse)
  } catch (error) {
    console.error('获取用户档案异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取驾驶档案
router.get('/driving-profile', async (req, res) => {
  try {
    const { user_id } = req.query
    
    // 模拟驾驶档案数据
    const drivingProfile = {
      user_id: user_id || 1,
      drivingStyle: 'eco',
      safetyScore: 92,
      ecoScore: 88,
      totalDistance: 15420,
      totalTrips: 342,
      avgSpeed: 45.2,
      habits: {
        accelerationScore: 85,
        brakingScore: 90,
        corneringScore: 88,
        speedingIncidents: 3
      },
      monthlyStats: [
        { month: '2024-01', distance: 1200, trips: 28, score: 89 },
        { month: '2024-02', distance: 1350, trips: 32, score: 91 },
        { month: '2024-03', distance: 1180, trips: 26, score: 92 }
      ],
      achievements: [
        { id: 1, name: '节能达人', description: '连续30天保持节能驾驶', earned: true },
        { id: 2, name: '安全先锋', description: '安全评分超过90分', earned: true },
        { id: 3, name: '里程王者', description: '单月行驶超过2000公里', earned: false }
      ]
    }
    
    res.json({
      success: true,
      data: drivingProfile
    } as ApiResponse)
  } catch (error) {
    console.error('获取驾驶档案异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取生态档案
router.get('/eco-profile', async (req, res) => {
  try {
    const { user_id } = req.query
    
    // 模拟生态档案数据
    const ecoProfile = {
      user_id: user_id || 1,
      carbonCredits: 1250,
      carbonSaved: 2.8, // tons
      treesEquivalent: 12,
      ecoLevel: 5,
      ecoRank: 'Gold',
      activities: {
        greenDriving: {
          count: 156,
          credits: 780,
          description: '绿色出行次数'
        },
        efficientCharging: {
          count: 89,
          credits: 267,
          description: '高效充电次数'
        },
        ecoEvents: {
          count: 12,
          credits: 180,
          description: '参与环保活动'
        }
      },
      monthlyTrend: [
        { month: '2024-01', credits: 95, carbonSaved: 0.2 },
        { month: '2024-02', credits: 120, carbonSaved: 0.3 },
        { month: '2024-03', credits: 135, carbonSaved: 0.3 }
      ],
      rewards: [
        { id: 1, name: '充电优惠券', type: 'coupon', value: '5元', status: 'available' },
        { id: 2, name: '环保徽章', type: 'badge', value: 'Gold', status: 'earned' }
      ]
    }
    
    res.json({
      success: true,
      data: ecoProfile
    } as ApiResponse)
  } catch (error) {
    console.error('获取生态档案异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 更新用户偏好设置
router.put('/preferences', async (req, res) => {
  try {
    const { user_id, preferences } = req.body
    
    if (!user_id || !preferences) {
      return res.status(400).json({
        success: false,
        error: '用户ID和偏好设置为必填字段'
      } as ApiResponse)
    }
    
    // 模拟更新成功
    res.json({
      success: true,
      message: '偏好设置更新成功',
      data: {
        user_id,
        preferences,
        updated_at: new Date().toISOString()
      }
    } as ApiResponse)
  } catch (error) {
    console.error('更新用户偏好异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

export default router