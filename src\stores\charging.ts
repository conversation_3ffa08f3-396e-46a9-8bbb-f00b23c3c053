import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api, { apiWithRetry } from '@/utils/api'

// 充电站接口
export interface ChargingStation {
  id: string
  name: string
  address: string
  latitude: number
  longitude: number
  operator: string
  power_type: 'AC' | 'DC' | 'SUPERCHARGE'
  total_ports: number
  available_ports: number
  status: 'available' | 'busy' | 'maintenance' | 'offline'
  pricing: {
    peak_price: number
    off_peak_price: number
    service_fee: number
  }
  amenities: string[]
  distance?: number
  created_at: string
  updated_at: string
}

// 预约接口
export interface ChargingReservation {
  id: string
  user_id: string
  station_id: string
  vehicle_id: string
  start_time: string
  end_time: string
  duration: number
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed'
  notes?: string
  station_name?: string
  vehicle_info?: string
  created_at: string
  updated_at: string
}

// 充电会话接口
export interface ChargingSession {
  id: string
  user_id: string
  vehicle_id: string
  station_id: string
  reservation_id?: string
  start_time: string
  end_time?: string
  duration?: number
  energy_delivered: number
  current_power: number
  max_power: number
  start_soc: number
  end_soc?: number
  total_cost: number
  estimated_cost?: number
  payment_status: 'pending' | 'paid' | 'failed' | 'refunded'
  status: 'charging' | 'completed' | 'interrupted' | 'failed'
  station_name?: string
  vehicle_info?: string
  created_at: string
  updated_at: string
}

// 支付记录接口
export interface PaymentRecord {
  id: string
  session_id: string
  user_id: string
  amount: number
  payment_method: 'wechat' | 'alipay' | 'bankcard' | 'wallet'
  transaction_id?: string
  status: 'pending' | 'completed' | 'failed' | 'refunded'
  session_info?: string
  created_at: string
  updated_at: string
}

// 月度统计接口
export interface MonthlyStats {
  totalAmount: number
  sessionCount: number
  totalEnergy: number
  avgPrice: number
}

export const useChargingStore = defineStore('charging', () => {
  // 状态
  const stations = ref<ChargingStation[]>([])
  const reservations = ref<ChargingReservation[]>([])
  const sessions = ref<ChargingSession[]>([])
  const payments = ref<PaymentRecord[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const todayRevenue = computed(() => {
    const today = new Date().toISOString().split('T')[0]
    return payments.value
      .filter(p => p.created_at.startsWith(today) && p.status === 'completed')
      .reduce((sum, p) => sum + p.amount, 0)
  })

  const monthlyStats = computed((): MonthlyStats => {
    const currentMonth = new Date().toISOString().slice(0, 7)
    const monthlyPayments = payments.value.filter(p => 
      p.created_at.startsWith(currentMonth) && p.status === 'completed'
    )
    const monthlySessions = sessions.value.filter(s => 
      s.created_at.startsWith(currentMonth) && s.status === 'completed'
    )

    const totalAmount = monthlyPayments.reduce((sum, p) => sum + p.amount, 0)
    const sessionCount = monthlySessions.length
    const totalEnergy = monthlySessions.reduce((sum, s) => sum + s.energy_delivered, 0)
    const avgPrice = totalEnergy > 0 ? totalAmount / totalEnergy : 0

    return {
      totalAmount: Math.round(totalAmount * 100) / 100,
      sessionCount,
      totalEnergy: Math.round(totalEnergy * 100) / 100,
      avgPrice: Math.round(avgPrice * 100) / 100
    }
  })

  // 使用全局API配置

  // 充电站相关方法
  const fetchStations = async (params?: {
    latitude?: number
    longitude?: number
    radius?: number
    power_type?: string
    status?: string
  }) => {
    try {
      loading.value = true
      error.value = null
      const response = await apiWithRetry.get('/charging-stations', { params, retry: 3 })
      stations.value = response.data.data || []
    } catch (err: any) {
      error.value = err.message || '获取充电站列表失败'
      console.error('获取充电站失败:', err)
    } finally {
      loading.value = false
    }
  }

  const getStationById = async (id: string) => {
    try {
      const response = await apiWithRetry.get(`/charging-stations/${id}`, { retry: 2 })
      return response.data.data
    } catch (err: any) {
      error.value = err.message || '获取充电站详情失败'
      throw err
    }
  }

  const searchStations = async (query: string) => {
    try {
      loading.value = true
      const response = await apiWithRetry.get('/charging-stations', {
        params: { search: query },
        retry: 2
      })
      stations.value = response.data.data || []
    } catch (err: any) {
      error.value = err.message || '搜索充电站失败'
      console.error('搜索充电站失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 预约相关方法
  const fetchReservations = async (userId?: string) => {
    try {
      loading.value = true
      error.value = null
      const response = await apiWithRetry.get('/charging-sessions/reservations', {
        params: userId ? { user_id: userId } : {},
        retry: 2
      })
      reservations.value = response.data.data || []
    } catch (err: any) {
      error.value = err.message || '获取预约列表失败'
      console.error('获取预约失败:', err)
    } finally {
      loading.value = false
    }
  }

  const createReservation = async (reservationData: {
    stationId: string
    vehicleId: string
    startTime: string
    duration: string
  }) => {
    try {
      const response = await apiWithRetry.post('/charging-sessions/reservations', {
        station_id: reservationData.stationId,
        vehicle_id: reservationData.vehicleId,
        start_time: reservationData.startTime,
        duration: parseInt(reservationData.duration)
      }, { retry: 1 })
      
      // 更新本地预约列表
      await fetchReservations()
      return response.data.data
    } catch (err: any) {
      error.value = err.message || '创建预约失败'
      throw err
    }
  }

  const cancelReservation = async (id: string) => {
    try {
      await apiWithRetry.delete(`/charging-sessions/reservations/${id}`, { retry: 1 })
      // 更新本地预约列表
      await fetchReservations()
    } catch (err: any) {
      error.value = err.message || '取消预约失败'
      throw err
    }
  }

  // 充电会话相关方法
  const fetchSessions = async (params?: {
    user_id?: string
    vehicle_id?: string
    station_id?: string
    status?: string
    page?: number
    limit?: number
  }) => {
    try {
      loading.value = true
      error.value = null
      const response = await apiWithRetry.get('/charging-sessions', { params, retry: 2 })
      sessions.value = response.data.data || []
    } catch (err: any) {
      error.value = err.message || '获取充电会话失败'
      console.error('获取充电会话失败:', err)
    } finally {
      loading.value = false
    }
  }

  const getSessionById = async (id: string) => {
    try {
      const response = await apiWithRetry.get(`/charging-sessions/${id}`, { retry: 2 })
      return response.data.data
    } catch (err: any) {
      error.value = err.message || '获取充电会话详情失败'
      throw err
    }
  }

  const startCharging = async (sessionData: {
    stationId: string
    vehicleId: string
    reservationId?: string
  }) => {
    try {
      const response = await apiWithRetry.post('/charging-sessions', {
        station_id: sessionData.stationId,
        vehicle_id: sessionData.vehicleId,
        reservation_id: sessionData.reservationId
      }, { retry: 1 })
      
      // 更新本地会话列表
      await fetchSessions()
      return response.data.data
    } catch (err: any) {
      error.value = err.message || '开始充电失败'
      throw err
    }
  }

  const stopCharging = async (id: string) => {
    try {
      const response = await apiWithRetry.put(`/charging-sessions/${id}/end`, {}, { retry: 1 })
      // 更新本地会话列表
      await fetchSessions()
      return response.data.data
    } catch (err: any) {
      error.value = err.message || '停止充电失败'
      throw err
    }
  }

  const interruptCharging = async (id: string, reason: string) => {
    try {
      const response = await apiWithRetry.put(`/charging-sessions/${id}/interrupt`, {
        reason
      }, { retry: 1 })
      // 更新本地会话列表
      await fetchSessions()
      return response.data.data
    } catch (err: any) {
      error.value = err.message || '中断充电失败'
      throw err
    }
  }

  // 支付相关方法
  const fetchPayments = async (params?: {
    user_id?: string
    session_id?: string
    status?: string
    page?: number
    limit?: number
  }) => {
    try {
      loading.value = true
      error.value = null
      const response = await apiWithRetry.get('/charging-sessions/payments', { params, retry: 2 })
      payments.value = response.data.data || []
    } catch (err: any) {
      error.value = err.message || '获取支付记录失败'
      console.error('获取支付记录失败:', err)
    } finally {
      loading.value = false
    }
  }

  const payOrder = async (paymentId: string, paymentMethod: string = 'wechat') => {
    try {
      const response = await apiWithRetry.post(`/charging-sessions/payments/${paymentId}/pay`, {
        payment_method: paymentMethod
      }, { retry: 1 })
      
      // 更新本地支付记录
      await fetchPayments()
      return response.data.data
    } catch (err: any) {
      error.value = err.message || '支付失败'
      throw err
    }
  }

  const updatePaymentStatus = async (id: string, status: string) => {
    try {
      const response = await apiWithRetry.put(`/charging-sessions/${id}/payment`, {
        payment_status: status
      }, { retry: 1 })
      // 更新本地会话列表
      await fetchSessions()
      return response.data.data
    } catch (err: any) {
      error.value = err.message || '更新支付状态失败'
      throw err
    }
  }

  // 统计相关方法
  const getChargingStats = async (params?: {
    user_id?: string
    start_date?: string
    end_date?: string
    group_by?: 'day' | 'week' | 'month'
  }) => {
    try {
      const response = await apiWithRetry.get('/charging-sessions/stats', { params, retry: 2 })
      return response.data.data
    } catch (err: any) {
      error.value = err.message || '获取充电统计失败'
      throw err
    }
  }

  const getStationStats = async (stationId: string) => {
    try {
      const response = await apiWithRetry.get(`/charging-stations/${stationId}/stats`, { retry: 2 })
      return response.data.data
    } catch (err: any) {
      error.value = err.message || '获取充电站统计失败'
      throw err
    }
  }

  // 充电站管理方法
  const createStation = async (stationData: any) => {
    try {
      const response = await apiWithRetry.post('/charging-stations', stationData, { retry: 1 })
      await fetchStations()
      return response.data.data
    } catch (err: any) {
      error.value = err.message || '创建充电站失败'
      throw err
    }
  }

  const updateStation = async (id: string, stationData: any) => {
    try {
      const response = await apiWithRetry.put(`/charging-stations/${id}`, stationData, { retry: 1 })
      await fetchStations()
      return response.data.data
    } catch (err: any) {
      error.value = err.message || '更新充电站失败'
      throw err
    }
  }

  const deleteStation = async (id: string) => {
    try {
      await apiWithRetry.delete(`/charging-stations/${id}`, { retry: 1 })
      await fetchStations()
    } catch (err: any) {
      error.value = err.message || '删除充电站失败'
      throw err
    }
  }

  const stopSession = async (sessionId: string) => {
    try {
      const response = await apiWithRetry.put(`/charging-sessions/${sessionId}/stop`, {}, { retry: 1 })
      await fetchSessions()
      return response.data.data
    } catch (err: any) {
      error.value = err.message || '停止充电会话失败'
      throw err
    }
  }

  // 计算总收入
  const totalRevenue = computed(() => {
    return payments.value
      .filter(p => p.status === 'completed')
      .reduce((sum, p) => sum + p.amount, 0)
  })

  // 重置状态
  const resetState = () => {
    stations.value = []
    reservations.value = []
    sessions.value = []
    payments.value = []
    error.value = null
    loading.value = false
  }

  return {
    // 状态
    stations,
    reservations,
    sessions,
    payments,
    loading,
    error,
    
    // 计算属性
    todayRevenue,
    totalRevenue,
    monthlyStats,

    // 充电站方法
    fetchStations,
    getStationById,
    searchStations,
    createStation,
    updateStation,
    deleteStation,
    
    // 预约方法
    fetchReservations,
    createReservation,
    cancelReservation,
    
    // 充电会话方法
    fetchSessions,
    getSessionById,
    startCharging,
    stopCharging,
    stopSession,
    interruptCharging,
    
    // 支付方法
    fetchPayments,
    payOrder,
    updatePaymentStatus,
    
    // 统计方法
    getChargingStats,
    getStationStats,
    
    // 工具方法
    resetState
  }
})