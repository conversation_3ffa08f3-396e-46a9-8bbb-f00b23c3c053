/**
 * User authentication API routes
 * Handle user registration, login, token management, etc.
 */
import { Router, type Request, type Response } from 'express'
import bcrypt from 'bcrypt'
import jwt from 'jsonwebtoken'
import { pool } from '../config/mysql'
import type { ApiResponse } from '../../shared/types'

const router = Router()

// JWT密钥（在生产环境中应该使用环境变量）
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key'
const JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || 'your-refresh-secret-key'
const JWT_EXPIRES_IN = '24h'
const JWT_REFRESH_EXPIRES_IN = '7d'

// 用户接口
interface User {
  id: string
  email: string
  name: string
  phone?: string
  avatar_url?: string
  user_type: 'individual' | 'enterprise'
  password_hash: string
  created_at: string
  updated_at: string
}

// 生成JWT token
const generateTokens = (userId: string) => {
  const token = jwt.sign({ userId }, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN })
  const refreshToken = jwt.sign({ userId }, JWT_REFRESH_SECRET, { expiresIn: JWT_REFRESH_EXPIRES_IN })
  return { token, refreshToken }
}

// 验证JWT token
const verifyToken = (token: string): { userId: string } | null => {
  try {
    return jwt.verify(token, JWT_SECRET) as { userId: string }
  } catch (error) {
    return null
  }
}

/**
 * User Registration
 * POST /api/auth/register
 */
router.post('/register', async (req: Request, res: Response): Promise<void> => {
  try {
    const { email, name, phone, password } = req.body
    
    // 验证必填字段
    if (!email || !name || !password) {
      res.status(400).json({
        success: false,
        message: '邮箱、姓名和密码为必填字段'
      } as ApiResponse)
      return
    }
    
    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      res.status(400).json({
        success: false,
        message: '邮箱格式不正确'
      } as ApiResponse)
      return
    }
    
    // 验证密码长度
    if (password.length < 6) {
      res.status(400).json({
        success: false,
        message: '密码长度不能少于6位'
      } as ApiResponse)
      return
    }
    
    // 检查邮箱是否已存在
    if (!pool) {
      res.status(500).json({
        success: false,
        message: '数据库连接失败'
      } as ApiResponse)
      return
    }
    
    const [existingUsers] = await pool.execute(
      'SELECT id FROM users WHERE email = ?',
      [email]
    )
    
    if ((existingUsers as any[]).length > 0) {
      res.status(400).json({
        success: false,
        message: '该邮箱已被注册'
      } as ApiResponse)
      return
    }
    
    // 加密密码
    const saltRounds = 10
    const passwordHash = await bcrypt.hash(password, saltRounds)
    
    // 创建用户
    const userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    await pool.execute(
      'INSERT INTO users (user_id, username, email, password_hash, phone, role, status) VALUES (?, ?, ?, ?, ?, ?, ?)',
      [userId, name, email, passwordHash, phone || null, 'user', 'active']
    )
    
    // 获取新创建的用户信息
    const [newUsers] = await pool.execute(
      'SELECT id, user_id, username as name, email, phone, role as user_type, status, created_at, updated_at FROM users WHERE email = ?',
      [email]
    )
    
    const newUser = (newUsers as any[])[0]
    if (!newUser) {
      res.status(500).json({
        success: false,
        message: '注册失败，请重试'
      } as ApiResponse)
      return
    }
    
    res.status(201).json({
      success: true,
      message: '注册成功',
      data: newUser
    } as ApiResponse)
    
  } catch (error) {
    console.error('注册异常:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    } as ApiResponse)
  }
})

/**
 * User Login
 * POST /api/auth/login
 */
router.post('/login', async (req: Request, res: Response): Promise<void> => {
  try {
    const { email, password } = req.body
    
    // 验证必填字段
    if (!email || !password) {
      res.status(400).json({
        success: false,
        message: '邮箱和密码为必填字段'
      } as ApiResponse)
      return
    }
    
    // 查找用户
    if (!pool) {
      res.status(500).json({
        success: false,
        message: '数据库连接失败'
      } as ApiResponse)
      return
    }
    
    const [users] = await pool.execute(
      'SELECT * FROM users WHERE email = ?',
      [email]
    )
    
    const user = (users as any[])[0]
    if (!user) {
      res.status(401).json({
        success: false,
        message: '邮箱或密码错误'
      } as ApiResponse)
      return
    }
    
    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.password_hash)
    if (!isPasswordValid) {
      res.status(401).json({
        success: false,
        message: '邮箱或密码错误'
      } as ApiResponse)
      return
    }
    
    // 生成token
    const { token, refreshToken } = generateTokens(user.user_id || user.id)
    
    // 更新最后登录时间
    await pool.execute(
      'UPDATE users SET last_login = NOW(), updated_at = NOW() WHERE id = ?',
      [user.id]
    )
    
    // 返回用户信息和token（不包含密码）
    const { password_hash, ...userWithoutPassword } = user
    
    // 格式化用户信息以匹配前端期望的字段
    const formattedUser = {
      id: user.user_id || user.id,
      email: user.email,
      name: user.username || user.name,
      phone: user.phone,
      user_type: user.role || user.user_type,
      status: user.status,
      avatar_url: user.avatar_url,
      created_at: user.created_at,
      updated_at: user.updated_at
    }
    
    res.json({
      success: true,
      message: '登录成功',
      data: {
        user: formattedUser,
        token,
        refreshToken
      }
    } as ApiResponse)
    
  } catch (error) {
    console.error('登录异常:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    } as ApiResponse)
  }
})

/**
 * Validate Token
 * POST /api/auth/validate
 */
router.post('/validate', async (req: Request, res: Response): Promise<void> => {
  try {
    const authHeader = req.headers.authorization
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({
        success: false,
        message: '未提供有效的认证token'
      } as ApiResponse)
      return
    }
    
    const token = authHeader.substring(7)
    const decoded = verifyToken(token)
    
    if (!decoded) {
      res.status(401).json({
        success: false,
        message: 'Token无效或已过期'
      } as ApiResponse)
      return
    }
    
    // 验证用户是否存在
    if (!pool) {
      res.status(500).json({
        success: false,
        message: '数据库连接失败'
      } as ApiResponse)
      return
    }
    
    const [users] = await pool.execute(
      'SELECT id, user_id, username as name, email, phone, avatar_url, role as user_type, status, created_at, updated_at FROM users WHERE user_id = ? OR id = ?',
      [decoded.userId, decoded.userId]
    )
    
    const user = (users as any[])[0]
    if (!user) {
      res.status(401).json({
        success: false,
        message: '用户不存在'
      } as ApiResponse)
      return
    }
    
    // 格式化用户信息
    const formattedUser = {
      id: user.user_id || user.id,
      email: user.email,
      name: user.name,
      phone: user.phone,
      user_type: user.user_type,
      status: user.status,
      avatar_url: user.avatar_url,
      created_at: user.created_at,
      updated_at: user.updated_at
    }
    
    res.json({
      success: true,
      data: { user: formattedUser }
    } as ApiResponse)
    
  } catch (error) {
    console.error('验证token异常:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    } as ApiResponse)
  }
})

/**
 * Refresh Token
 * POST /api/auth/refresh
 */
router.post('/refresh', async (req: Request, res: Response): Promise<void> => {
  try {
    const { refreshToken } = req.body
    
    if (!refreshToken) {
      res.status(400).json({
        success: false,
        message: '未提供refresh token'
      } as ApiResponse)
      return
    }
    
    try {
      const decoded = jwt.verify(refreshToken, JWT_REFRESH_SECRET) as { userId: string }
      
      // 验证用户是否存在
      if (!pool) {
        res.status(500).json({
          success: false,
          message: '数据库连接失败'
        } as ApiResponse)
        return
      }
      
      const [users] = await pool.execute(
        'SELECT id, user_id FROM users WHERE user_id = ? OR id = ?',
        [decoded.userId, decoded.userId]
      )
      
      const user = (users as any[])[0]
      if (!user) {
        res.status(401).json({
          success: false,
          message: '用户不存在'
        } as ApiResponse)
        return
      }
      
      // 生成新的token
      const { token: newToken, refreshToken: newRefreshToken } = generateTokens(user.user_id || user.id)
      
      res.json({
        success: true,
        data: {
          token: newToken,
          refreshToken: newRefreshToken
        }
      } as ApiResponse)
      
    } catch (jwtError) {
      res.status(401).json({
        success: false,
        message: 'Refresh token无效或已过期'
      } as ApiResponse)
    }
    
  } catch (error) {
    console.error('刷新token异常:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    } as ApiResponse)
  }
})

/**
 * User Logout
 * POST /api/auth/logout
 */
router.post('/logout', async (req: Request, res: Response): Promise<void> => {
  try {
    // 在实际应用中，这里可以将token加入黑名单
    // 或者在数据库中记录登出时间
    
    res.json({
      success: true,
      message: '登出成功'
    } as ApiResponse)
    
  } catch (error) {
    console.error('登出异常:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    } as ApiResponse)
  }
})

/**
 * OAuth Callback Handler
 * POST /api/auth/oauth/callback
 */
router.post('/oauth/callback', async (req: Request, res: Response): Promise<void> => {
  try {
    const { provider, code, state } = req.body
    
    if (!provider || !code) {
      res.status(400).json({
        success: false,
        message: '缺少必要的OAuth参数'
      } as ApiResponse)
      return
    }
    
    let userInfo: any = null
    
    if (provider === 'github') {
      userInfo = await handleGitHubOAuth(code)
    } else if (provider === 'google') {
      userInfo = await handleGoogleOAuth(code)
    } else {
      res.status(400).json({
        success: false,
        message: '不支持的OAuth提供商'
      } as ApiResponse)
      return
    }
    
    if (!userInfo) {
      res.status(400).json({
        success: false,
        message: 'OAuth验证失败'
      } as ApiResponse)
      return
    }
    
    // 检查用户是否已存在
    if (!pool) {
      res.status(500).json({
        success: false,
        message: '数据库连接失败'
      } as ApiResponse)
      return
    }
    
    const [existingUsers] = await pool.execute(
      'SELECT * FROM users WHERE email = ?',
      [userInfo.email]
    )
    
    const existingUser = (existingUsers as any[])[0]
    let user: any
    
    if (existingUser) {
      // 用户已存在，更新信息
      await pool.execute(
        'UPDATE users SET username = ?, avatar_url = ?, updated_at = NOW() WHERE id = ?',
        [
          userInfo.name || existingUser.username,
          userInfo.avatar_url || existingUser.avatar_url,
          existingUser.id
        ]
      )
      
      // 获取更新后的用户信息
      const [updatedUsers] = await pool.execute(
        'SELECT * FROM users WHERE id = ?',
        [existingUser.id]
      )
      
      user = (updatedUsers as any[])[0]
    } else {
      // 创建新用户
      const userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      const userName = userInfo.name || userInfo.email.split('@')[0]
      
      await pool.execute(
        'INSERT INTO users (user_id, username, email, password_hash, avatar_url, role, status) VALUES (?, ?, ?, ?, ?, ?, ?)',
        [userId, userName, userInfo.email, '', userInfo.avatar_url || null, 'user', 'active']
      )
      
      // 获取新创建的用户信息
      const [newUsers] = await pool.execute(
        'SELECT * FROM users WHERE email = ?',
        [userInfo.email]
      )
      
      user = (newUsers as any[])[0]
      if (!user) {
        console.error('创建用户失败: 无法获取新用户信息')
        res.status(500).json({
          success: false,
          message: '创建用户失败'
        } as ApiResponse)
        return
      }
      
    }
    
    // 生成JWT tokens
    const { token, refreshToken } = generateTokens(user.user_id || user.id)
    
    // 格式化用户信息以匹配前端期望的字段
    const formattedUser = {
      id: user.user_id || user.id,
      email: user.email,
      name: user.username || user.name,
      phone: user.phone,
      user_type: user.role || user.user_type || 'individual',
      status: user.status,
      avatar_url: user.avatar_url,
      created_at: user.created_at,
      updated_at: user.updated_at
    }
    
    res.json({
      success: true,
      message: 'OAuth登录成功',
      data: {
        user: formattedUser,
        token,
        refreshToken
      }
    } as ApiResponse)
    
  } catch (error) {
    console.error('OAuth回调处理失败:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    } as ApiResponse)
  }
})

// GitHub OAuth处理
const handleGitHubOAuth = async (code: string) => {
  try {
    // 获取访问令牌
    const tokenResponse = await fetch('https://github.com/login/oauth/access_token', {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        client_id: process.env.VITE_GITHUB_CLIENT_ID,
        client_secret: process.env.GITHUB_CLIENT_SECRET,
        code
      })
    })
    
    const tokenData = await tokenResponse.json()
    
    if (!tokenData.access_token) {
      throw new Error('获取GitHub访问令牌失败')
    }
    
    // 获取用户信息
    const userResponse = await fetch('https://api.github.com/user', {
      headers: {
        'Authorization': `token ${tokenData.access_token}`,
        'User-Agent': 'EV-Management-System'
      }
    })
    
    const userData = await userResponse.json()
    
    // 获取用户邮箱（如果公开）
    const emailResponse = await fetch('https://api.github.com/user/emails', {
      headers: {
        'Authorization': `token ${tokenData.access_token}`,
        'User-Agent': 'EV-Management-System'
      }
    })
    
    const emailData = await emailResponse.json()
    const primaryEmail = emailData.find((email: any) => email.primary)?.email || userData.email
    
    return {
      email: primaryEmail,
      name: userData.name || userData.login,
      avatar_url: userData.avatar_url
    }
  } catch (error) {
    console.error('GitHub OAuth处理失败:', error)
    return null
  }
}

// Google OAuth处理
const handleGoogleOAuth = async (code: string) => {
  try {
    // 获取访问令牌
    const tokenResponse = await fetch('https://oauth2.googleapis.com/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: new URLSearchParams({
        client_id: process.env.VITE_GOOGLE_CLIENT_ID!,
        client_secret: process.env.GOOGLE_CLIENT_SECRET!,
        code,
        grant_type: 'authorization_code',
        redirect_uri: `${process.env.BASE_URL || 'http://localhost:5173'}/auth/callback/google`
      })
    })
    
    const tokenData = await tokenResponse.json()
    
    if (!tokenData.access_token) {
      throw new Error('获取Google访问令牌失败')
    }
    
    // 获取用户信息
    const userResponse = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
      headers: {
        'Authorization': `Bearer ${tokenData.access_token}`
      }
    })
    
    const userData = await userResponse.json()
    
    return {
      email: userData.email,
      name: userData.name,
      avatar_url: userData.picture
    }
  } catch (error) {
    console.error('Google OAuth处理失败:', error)
    return null
  }
}

export default router