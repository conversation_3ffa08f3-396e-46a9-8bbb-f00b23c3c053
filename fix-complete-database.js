const mysql = require('mysql2/promise')

async function fixCompleteDatabase() {
  let connection
  
  try {
    console.log('✅ 连接数据库...')
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '123456',
      database: 'ev_management'
    })
    
    console.log('🔧 修复数据库结构...')
    
    // 1. 修改 value 字段允许为空
    await connection.execute(`
      ALTER TABLE edge_device_data 
      MODIFY COLUMN value JSON NULL
    `)
    console.log('✅ value 字段修改成功')
    
    // 2. 检查并创建 edge_devices 表
    const [tables] = await connection.execute(`
      SHOW TABLES LIKE 'edge_devices'
    `)
    
    if (tables.length === 0) {
      console.log('🔧 创建 edge_devices 表...')
      await connection.execute(`
        CREATE TABLE edge_devices (
          id INT AUTO_INCREMENT PRIMARY KEY,
          device_id VARCHAR(50) UNIQUE NOT NULL,
          device_name VARCHAR(100) NOT NULL,
          device_type VARCHAR(50) NOT NULL,
          status ENUM('online', 'offline', 'maintenance') DEFAULT 'offline',
          location VARCHAR(200),
          ip_address VARCHAR(45),
          last_heartbeat TIMESTAMP NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_device_id (device_id),
          INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
      `)
      console.log('✅ edge_devices 表创建成功')
    } else {
      console.log('ℹ️  edge_devices 表已存在')
    }
    
    // 3. 插入测试设备
    console.log('🔧 插入测试设备...')
    const testDevices = [
      {
        device_id: 'EV001',
        device_name: '电动车001',
        device_type: 'electric_vehicle',
        status: 'online',
        location: '北京市朝阳区',
        ip_address: '*************'
      },
      {
        device_id: 'EV002', 
        device_name: '电动车002',
        device_type: 'electric_vehicle',
        status: 'online',
        location: '上海市浦东新区',
        ip_address: '*************'
      },
      {
        device_id: 'EV003',
        device_name: '电动车003',
        device_type: 'electric_vehicle', 
        status: 'offline',
        location: '广州市天河区',
        ip_address: '*************'
      },
      {
        device_id: 'CS001',
        device_name: '充电站001',
        device_type: 'charging_station',
        status: 'online',
        location: '深圳市南山区',
        ip_address: '*************'
      },
      {
        device_id: 'CS002',
        device_name: '充电站002', 
        device_type: 'charging_station',
        status: 'online',
        location: '杭州市西湖区',
        ip_address: '*************'
      }
    ]
    
    for (const device of testDevices) {
      try {
        await connection.execute(`
          INSERT INTO edge_devices 
          (device_id, device_name, device_type, status, location, ip_address, last_heartbeat) 
          VALUES (?, ?, ?, ?, ?, ?, NOW())
          ON DUPLICATE KEY UPDATE
          device_name = VALUES(device_name),
          status = VALUES(status),
          location = VALUES(location),
          ip_address = VALUES(ip_address),
          last_heartbeat = NOW()
        `, [
          device.device_id,
          device.device_name,
          device.device_type,
          device.status,
          device.location,
          device.ip_address
        ])
        console.log(`✅ 设备 ${device.device_id} 创建/更新成功`)
      } catch (error) {
        console.log(`⚠️  设备 ${device.device_id} 创建失败:`, error.message)
      }
    }
    
    // 4. 插入测试数据
    console.log('🔧 插入测试数据...')
    const testData = [
      {
        device_id: 'EV001',
        data_type: 'battery_level',
        value: JSON.stringify({ level: 85, voltage: 400.5, temperature: 25.8 }),
        text_value: '85%',
        unit: '%',
        quality: 95.5
      },
      {
        device_id: 'EV001',
        data_type: 'speed',
        value: JSON.stringify({ speed: 60, max_speed: 120 }),
        text_value: '60km/h',
        unit: 'km/h',
        quality: 98.0
      },
      {
        device_id: 'EV002', 
        data_type: 'charging_power',
        value: JSON.stringify({ power: 50.2, current: 125.5, voltage: 400 }),
        text_value: '50.2kW',
        unit: 'kW',
        quality: 98.0
      },
      {
        device_id: 'EV002',
        data_type: 'battery_level',
        value: JSON.stringify({ level: 92, voltage: 405.2, temperature: 23.5 }),
        text_value: '92%',
        unit: '%',
        quality: 97.8
      },
      {
        device_id: 'EV003',
        data_type: 'temperature',
        value: JSON.stringify({ temp: 25.8, status: 'normal', cooling: 'auto' }),
        text_value: '25.8°C',
        unit: '°C', 
        quality: 92.3
      },
      {
        device_id: 'CS001',
        data_type: 'station_status',
        value: JSON.stringify({ status: 'available', slots: 8, occupied: 3 }),
        text_value: '可用',
        unit: 'slots',
        quality: 100.0
      },
      {
        device_id: 'CS001',
        data_type: 'power_output',
        value: JSON.stringify({ total_power: 350, max_power: 500 }),
        text_value: '350kW',
        unit: 'kW',
        quality: 99.2
      },
      {
        device_id: 'CS002',
        data_type: 'utilization',
        value: JSON.stringify({ rate: 78.5, active_sessions: 6, total_slots: 10 }),
        text_value: '78.5%',
        unit: '%',
        quality: 96.8
      }
    ]
    
    for (const data of testData) {
      try {
        await connection.execute(`
          INSERT INTO edge_device_data 
          (device_id, data_type, value, text_value, unit, quality, timestamp) 
          VALUES (?, ?, ?, ?, ?, ?, NOW())
        `, [
          data.device_id,
          data.data_type, 
          data.value,
          data.text_value,
          data.unit,
          data.quality
        ])
        console.log(`✅ 数据插入成功: ${data.device_id} - ${data.data_type}`)
      } catch (error) {
        console.log(`⚠️  数据插入失败: ${data.device_id} - ${data.data_type}:`, error.message)
      }
    }
    
    // 5. 验证数据
    console.log('🔍 验证数据...')
    const [deviceRows] = await connection.execute(`
      SELECT device_id, device_name, device_type, status 
      FROM edge_devices 
      ORDER BY device_id
    `)
    
    console.log('设备列表:')
    deviceRows.forEach(row => {
      console.log(`  - ${row.device_id}: ${row.device_name} (${row.device_type}) - ${row.status}`)
    })
    
    const [dataRows] = await connection.execute(`
      SELECT device_id, data_type, text_value, unit, quality 
      FROM edge_device_data 
      ORDER BY created_at DESC 
      LIMIT 10
    `)
    
    console.log('最新数据:')
    dataRows.forEach(row => {
      console.log(`  - ${row.device_id}: ${row.data_type} = ${row.text_value} ${row.unit || ''} (质量: ${row.quality})`)
    })
    
    // 6. 创建一些车辆和充电站记录
    console.log('🔧 创建车辆和充电站记录...')
    
    // 检查并创建 vehicles 表
    const [vehicleTables] = await connection.execute(`SHOW TABLES LIKE 'vehicles'`)
    if (vehicleTables.length === 0) {
      await connection.execute(`
        CREATE TABLE vehicles (
          id INT AUTO_INCREMENT PRIMARY KEY,
          vehicle_id VARCHAR(50) UNIQUE NOT NULL,
          license_plate VARCHAR(20) NOT NULL,
          model VARCHAR(100) NOT NULL,
          battery_capacity DECIMAL(8,2) DEFAULT 0,
          current_battery_level DECIMAL(5,2) DEFAULT 0,
          status ENUM('online', 'offline', 'charging', 'maintenance') DEFAULT 'offline',
          location VARCHAR(200),
          owner_id INT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_vehicle_id (vehicle_id),
          INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
      `)
      console.log('✅ vehicles 表创建成功')
    }
    
    // 插入测试车辆
    const testVehicles = [
      { vehicle_id: 'EV001', license_plate: '京A12345', model: 'Tesla Model 3', battery_capacity: 75.0, current_battery_level: 85.0, status: 'online', location: '北京市朝阳区' },
      { vehicle_id: 'EV002', license_plate: '沪B67890', model: 'BYD Han EV', battery_capacity: 85.4, current_battery_level: 92.0, status: 'charging', location: '上海市浦东新区' },
      { vehicle_id: 'EV003', license_plate: '粤C11111', model: 'NIO ES8', battery_capacity: 100.0, current_battery_level: 45.0, status: 'offline', location: '广州市天河区' }
    ]
    
    for (const vehicle of testVehicles) {
      try {
        await connection.execute(`
          INSERT INTO vehicles 
          (vehicle_id, license_plate, model, battery_capacity, current_battery_level, status, location) 
          VALUES (?, ?, ?, ?, ?, ?, ?)
          ON DUPLICATE KEY UPDATE
          current_battery_level = VALUES(current_battery_level),
          status = VALUES(status),
          location = VALUES(location)
        `, [
          vehicle.vehicle_id, vehicle.license_plate, vehicle.model,
          vehicle.battery_capacity, vehicle.current_battery_level,
          vehicle.status, vehicle.location
        ])
        console.log(`✅ 车辆 ${vehicle.vehicle_id} 创建/更新成功`)
      } catch (error) {
        console.log(`⚠️  车辆 ${vehicle.vehicle_id} 创建失败:`, error.message)
      }
    }
    
    // 检查并创建 charging_stations 表
    const [stationTables] = await connection.execute(`SHOW TABLES LIKE 'charging_stations'`)
    if (stationTables.length === 0) {
      await connection.execute(`
        CREATE TABLE charging_stations (
          id INT AUTO_INCREMENT PRIMARY KEY,
          station_id VARCHAR(50) UNIQUE NOT NULL,
          station_name VARCHAR(100) NOT NULL,
          location VARCHAR(200) NOT NULL,
          total_slots INT DEFAULT 0,
          available_slots INT DEFAULT 0,
          power_capacity DECIMAL(8,2) DEFAULT 0,
          status ENUM('online', 'offline', 'maintenance') DEFAULT 'offline',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_station_id (station_id),
          INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
      `)
      console.log('✅ charging_stations 表创建成功')
    }
    
    // 插入测试充电站
    const testStations = [
      { station_id: 'CS001', station_name: '深圳南山充电站', location: '深圳市南山区科技园', total_slots: 8, available_slots: 5, power_capacity: 500.0, status: 'online' },
      { station_id: 'CS002', station_name: '杭州西湖充电站', location: '杭州市西湖区文三路', total_slots: 10, available_slots: 4, power_capacity: 600.0, status: 'online' }
    ]
    
    for (const station of testStations) {
      try {
        await connection.execute(`
          INSERT INTO charging_stations 
          (station_id, station_name, location, total_slots, available_slots, power_capacity, status) 
          VALUES (?, ?, ?, ?, ?, ?, ?)
          ON DUPLICATE KEY UPDATE
          available_slots = VALUES(available_slots),
          status = VALUES(status)
        `, [
          station.station_id, station.station_name, station.location,
          station.total_slots, station.available_slots,
          station.power_capacity, station.status
        ])
        console.log(`✅ 充电站 ${station.station_id} 创建/更新成功`)
      } catch (error) {
        console.log(`⚠️  充电站 ${station.station_id} 创建失败:`, error.message)
      }
    }
    
    console.log('🎉 数据库完整修复完成！')
    
  } catch (error) {
    console.error('❌ 修复失败:', error.message)
  } finally {
    if (connection) {
      await connection.end()
      console.log('🔌 数据库连接已关闭')
    }
  }
}

fixCompleteDatabase()