<template>
  <div class="about-us-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon><OfficeBuilding /></el-icon>
          关于我们
        </h1>
        <p class="page-subtitle">致力于新能源汽车智能化管理的创新企业</p>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 公司介绍 -->
      <div class="section">
        <el-card class="content-card">
          <template #header>
            <div class="card-header">
              <el-icon><OfficeBuilding /></el-icon>
              <span>公司介绍</span>
            </div>
          </template>
          <div class="company-intro">
            <div class="intro-content">
              <div class="intro-text">
                <h3>企业愿景</h3>
                <p>成为全球领先的新能源汽车智能管理解决方案提供商，推动绿色出行和智慧交通的发展。</p>
                
                <h3>企业使命</h3>
                <p>通过先进的技术和创新的产品，为新能源汽车用户提供全方位的智能化管理服务，提升用户体验，促进可持续发展。</p>
                
                <h3>核心价值观</h3>
                <div class="values-grid">
                  <div class="value-item">
                    <el-icon><Star /></el-icon>
                    <div class="value-content">
                      <h4>创新驱动</h4>
                      <p>持续技术创新，引领行业发展</p>
                    </div>
                  </div>
                  <div class="value-item">
                    <el-icon><UserFilled /></el-icon>
                    <div class="value-content">
                      <h4>用户至上</h4>
                      <p>以用户需求为中心，提供优质服务</p>
                    </div>
                  </div>
                  <div class="value-item">
                    <el-icon><Trophy /></el-icon>
                    <div class="value-content">
                      <h4>品质卓越</h4>
                      <p>追求卓越品质，打造精品解决方案</p>
                    </div>
                  </div>
                  <div class="value-item">
                    <el-icon><Connection /></el-icon>
                    <div class="value-content">
                      <h4>合作共赢</h4>
                      <p>开放合作，与合作伙伴共同发展</p>
                    </div>
                  </div>
                </div>
              </div>
              <div class="intro-stats">
                <h3>企业实力</h3>
                <div class="stats-grid">
                  <div class="stat-item">
                    <div class="stat-number">2018</div>
                    <div class="stat-label">成立年份</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-number">500+</div>
                    <div class="stat-label">员工数量</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-number">100+</div>
                    <div class="stat-label">合作伙伴</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-number">50万+</div>
                    <div class="stat-label">服务用户</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-number">20+</div>
                    <div class="stat-label">专利技术</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-number">99.9%</div>
                    <div class="stat-label">系统可用性</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 团队介绍 -->
      <div class="section">
        <el-card class="content-card">
          <template #header>
            <div class="card-header">
              <el-icon><Avatar /></el-icon>
              <span>核心团队</span>
            </div>
          </template>
          <div class="team-content">
            <div class="team-grid">
              <div class="team-member" v-for="member in teamMembers" :key="member.name">
                <div class="member-avatar">
                  <el-avatar :size="80" :src="member.avatar">
                    <el-icon><UserFilled /></el-icon>
                  </el-avatar>
                </div>
                <div class="member-info">
                  <h4>{{ member.name }}</h4>
                  <p class="member-position">{{ member.position }}</p>
                  <p class="member-description">{{ member.description }}</p>
                  <div class="member-skills">
                    <el-tag v-for="skill in member.skills" :key="skill" size="small">
                      {{ skill }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 发展历程 -->
      <div class="section">
        <el-card class="content-card">
          <template #header>
            <div class="card-header">
              <el-icon><Clock /></el-icon>
              <span>发展历程</span>
            </div>
          </template>
          <div class="timeline-content">
            <div class="timeline">
              <div class="timeline-item" v-for="(milestone, index) in milestones" :key="index">
                <div class="timeline-marker">
                  <div class="marker-dot"></div>
                  <div class="marker-line" v-if="index < milestones.length - 1"></div>
                </div>
                <div class="timeline-content-item">
                  <div class="timeline-date">{{ milestone.date }}</div>
                  <h4>{{ milestone.title }}</h4>
                  <p>{{ milestone.description }}</p>
                  <div class="milestone-achievements" v-if="milestone.achievements">
                    <el-tag v-for="achievement in milestone.achievements" :key="achievement" type="success" size="small">
                      {{ achievement }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 联系我们 -->
      <div class="section">
        <el-card class="content-card">
          <template #header>
            <div class="card-header">
              <el-icon><Phone /></el-icon>
              <span>联系我们</span>
            </div>
          </template>
          <div class="contact-content">
            <div class="contact-grid">
              <div class="contact-info">
                <h3>联系方式</h3>
                <div class="contact-item">
                  <el-icon><OfficeBuilding /></el-icon>
                  <div class="contact-details">
                    <h4>公司地址</h4>
                    <p>北京市海淀区中关村科技园区创新大厦A座15层</p>
                  </div>
                </div>
                <div class="contact-item">
                  <el-icon><Phone /></el-icon>
                  <div class="contact-details">
                    <h4>联系电话</h4>
                    <p>************</p>
                  </div>
                </div>
                <div class="contact-item">
                  <el-icon><Message /></el-icon>
                  <div class="contact-details">
                    <h4>邮箱地址</h4>
                    <p><EMAIL></p>
                  </div>
                </div>
                <div class="contact-item">
                  <el-icon><Link /></el-icon>
                  <div class="contact-details">
                    <h4>官方网站</h4>
                    <p>www.ev-smart.com</p>
                  </div>
                </div>
              </div>
              <div class="contact-form">
                <h3>在线留言</h3>
                <el-form :model="contactForm" :rules="contactRules" ref="contactFormRef" label-width="80px">
                  <el-form-item label="姓名" prop="name">
                    <el-input v-model="contactForm.name" placeholder="请输入您的姓名"></el-input>
                  </el-form-item>
                  <el-form-item label="邮箱" prop="email">
                    <el-input v-model="contactForm.email" placeholder="请输入您的邮箱"></el-input>
                  </el-form-item>
                  <el-form-item label="电话" prop="phone">
                    <el-input v-model="contactForm.phone" placeholder="请输入您的电话"></el-input>
                  </el-form-item>
                  <el-form-item label="主题" prop="subject">
                    <el-select v-model="contactForm.subject" placeholder="请选择咨询主题" style="width: 100%">
                      <el-option label="产品咨询" value="product"></el-option>
                      <el-option label="技术支持" value="support"></el-option>
                      <el-option label="商务合作" value="business"></el-option>
                      <el-option label="其他" value="other"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="留言" prop="message">
                    <el-input 
                      v-model="contactForm.message" 
                      type="textarea" 
                      :rows="4" 
                      placeholder="请输入您的留言内容"
                    ></el-input>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="submitContactForm" :loading="submitting">
                      提交留言
                    </el-button>
                    <el-button @click="resetContactForm">重置</el-button>
                  </el-form-item>
                </el-form>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 加入我们 -->
      <div class="section">
        <el-card class="content-card">
          <template #header>
            <div class="card-header">
              <el-icon><UserFilled /></el-icon>
              <span>加入我们</span>
            </div>
          </template>
          <div class="careers-content">
            <div class="careers-intro">
              <h3>为什么选择我们？</h3>
              <p>我们提供具有竞争力的薪酬福利、广阔的发展空间和优秀的团队文化，欢迎有志之士加入我们的团队！</p>
            </div>
            <div class="benefits-grid">
              <div class="benefit-item">
                <el-icon><Money /></el-icon>
                <h4>竞争薪酬</h4>
                <p>行业领先的薪酬水平和完善的绩效奖励机制</p>
              </div>
              <div class="benefit-item">
                <el-icon><TrendCharts /></el-icon>
                <h4>发展空间</h4>
                <p>清晰的职业发展路径和丰富的培训学习机会</p>
              </div>
              <div class="benefit-item">
                <el-icon><Coffee /></el-icon>
                <h4>工作环境</h4>
                <p>舒适的办公环境和灵活的工作时间安排</p>
              </div>
              <div class="benefit-item">
                <el-icon><Present /></el-icon>
                <h4>福利待遇</h4>
                <p>五险一金、带薪年假、节日福利等完善保障</p>
              </div>
            </div>
            <div class="job-positions">
              <h3>热招职位</h3>
              <div class="positions-grid">
                <div class="position-item" v-for="position in jobPositions" :key="position.title">
                  <div class="position-header">
                    <h4>{{ position.title }}</h4>
                    <el-tag :type="position.urgent ? 'danger' : 'primary'" size="small">
                      {{ position.urgent ? '急招' : '在招' }}
                    </el-tag>
                  </div>
                  <div class="position-details">
                    <p><strong>工作地点：</strong>{{ position.location }}</p>
                    <p><strong>薪资范围：</strong>{{ position.salary }}</p>
                    <p><strong>工作经验：</strong>{{ position.experience }}</p>
                    <p><strong>学历要求：</strong>{{ position.education }}</p>
                  </div>
                  <div class="position-requirements">
                    <h5>职位要求：</h5>
                    <ul>
                      <li v-for="requirement in position.requirements" :key="requirement">
                        {{ requirement }}
                      </li>
                    </ul>
                  </div>
                  <el-button type="primary" @click="applyJob(position.title)">
                    立即申请
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 合作伙伴 -->
      <div class="section">
        <el-card class="content-card">
          <template #header>
            <div class="card-header">
              <el-icon><Connection /></el-icon>
              <span>合作伙伴</span>
            </div>
          </template>
          <div class="partners-content">
            <div class="partners-intro">
              <h3>战略合作伙伴</h3>
              <p>我们与众多知名企业建立了深度合作关系，共同推动新能源汽车行业的发展。</p>
            </div>
            <div class="partners-categories">
              <div class="partner-category">
                <h4>汽车制造商</h4>
                <div class="partners-grid">
                  <div class="partner-item" v-for="partner in carManufacturers" :key="partner.name">
                    <div class="partner-logo">
                      <el-avatar :size="60" shape="square">
                        {{ partner.name.charAt(0) }}
                      </el-avatar>
                    </div>
                    <div class="partner-info">
                      <h5>{{ partner.name }}</h5>
                      <p>{{ partner.description }}</p>
                    </div>
                  </div>
                </div>
              </div>
              <div class="partner-category">
                <h4>技术服务商</h4>
                <div class="partners-grid">
                  <div class="partner-item" v-for="partner in techPartners" :key="partner.name">
                    <div class="partner-logo">
                      <el-avatar :size="60" shape="square">
                        {{ partner.name.charAt(0) }}
                      </el-avatar>
                    </div>
                    <div class="partner-info">
                      <h5>{{ partner.name }}</h5>
                      <p>{{ partner.description }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="partnership-cta">
              <h3>寻求合作？</h3>
              <p>如果您有意与我们建立合作关系，请联系我们的商务团队。</p>
              <el-button type="primary" size="large" @click="contactBusiness">
                商务合作
              </el-button>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import {
  OfficeBuilding,
  Star,
  UserFilled,
  Trophy,
  Connection,
  Avatar,
  Clock,
  Phone,
  Message,
  Link,
  Money,
  TrendCharts,
  Coffee,
  Present
} from '@element-plus/icons-vue'

// 响应式数据
const submitting = ref(false)
const contactFormRef = ref<FormInstance>()

// 联系表单
const contactForm = reactive({
  name: '',
  email: '',
  phone: '',
  subject: '',
  message: ''
})

// 表单验证规则
const contactRules: FormRules = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '姓名长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入电话号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  subject: [
    { required: true, message: '请选择咨询主题', trigger: 'change' }
  ],
  message: [
    { required: true, message: '请输入留言内容', trigger: 'blur' },
    { min: 10, max: 500, message: '留言内容长度在 10 到 500 个字符', trigger: 'blur' }
  ]
}

// 团队成员数据
const teamMembers = ref([
  {
    name: '张明',
    position: '首席执行官 (CEO)',
    description: '拥有15年新能源汽车行业经验，曾任职于多家知名汽车企业高管职位。',
    avatar: '',
    skills: ['战略规划', '团队管理', '商业拓展']
  },
  {
    name: '李华',
    position: '首席技术官 (CTO)',
    description: '计算机科学博士，专注于人工智能和物联网技术在汽车领域的应用。',
    avatar: '',
    skills: ['人工智能', '物联网', '系统架构']
  },
  {
    name: '王芳',
    position: '产品总监',
    description: '10年产品管理经验，擅长用户体验设计和产品策略规划。',
    avatar: '',
    skills: ['产品设计', '用户体验', '数据分析']
  },
  {
    name: '陈强',
    position: '研发总监',
    description: '资深软件工程师，负责系统架构设计和技术团队管理。',
    avatar: '',
    skills: ['软件架构', '团队管理', '技术创新']
  }
])

// 发展历程数据
const milestones = ref([
  {
    date: '2018年',
    title: '公司成立',
    description: '在北京中关村成立，专注于新能源汽车智能管理技术研发。',
    achievements: ['获得天使轮投资', '核心团队组建']
  },
  {
    date: '2019年',
    title: '产品发布',
    description: '推出第一代新能源汽车智能管理系统，获得市场认可。',
    achievements: ['首批客户签约', '技术专利申请']
  },
  {
    date: '2020年',
    title: '规模扩张',
    description: '完成A轮融资，团队规模扩大到100人，业务覆盖全国主要城市。',
    achievements: ['A轮融资完成', '全国业务布局']
  },
  {
    date: '2021年',
    title: '技术突破',
    description: '在人工智能和大数据分析方面取得重大突破，推出智能预测功能。',
    achievements: ['AI技术突破', '智能预测上线']
  },
  {
    date: '2022年',
    title: '战略合作',
    description: '与多家知名汽车制造商建立战略合作关系，市场份额快速增长。',
    achievements: ['战略合作签约', '市场份额提升']
  },
  {
    date: '2023年',
    title: '国际化发展',
    description: '启动国际化战略，产品开始向海外市场拓展。',
    achievements: ['海外市场进入', '国际认证获得']
  },
  {
    date: '2024年',
    title: '持续创新',
    description: '持续技术创新，推出新一代智能管理平台，服务用户超过50万。',
    achievements: ['新平台发布', '用户突破50万']
  }
])

// 招聘职位数据
const jobPositions = ref([
  {
    title: '高级前端工程师',
    location: '北京',
    salary: '20K-35K',
    experience: '3-5年',
    education: '本科及以上',
    urgent: true,
    requirements: [
      '熟练掌握Vue.js、React等前端框架',
      '具备TypeScript开发经验',
      '熟悉前端工程化和性能优化',
      '有移动端开发经验者优先'
    ]
  },
  {
    title: '后端开发工程师',
    location: '北京',
    salary: '18K-30K',
    experience: '2-4年',
    education: '本科及以上',
    urgent: false,
    requirements: [
      '熟练掌握Node.js、Java或Python',
      '熟悉MySQL、Redis等数据库',
      '具备微服务架构设计经验',
      '有云平台开发经验者优先'
    ]
  },
  {
    title: '产品经理',
    location: '北京',
    salary: '25K-40K',
    experience: '3-6年',
    education: '本科及以上',
    urgent: true,
    requirements: [
      '具备B端产品设计经验',
      '熟悉汽车行业或物联网领域',
      '优秀的沟通协调能力',
      '具备数据分析能力'
    ]
  },
  {
    title: 'UI/UX设计师',
    location: '北京',
    salary: '15K-25K',
    experience: '2-4年',
    education: '本科及以上',
    urgent: false,
    requirements: [
      '熟练使用Figma、Sketch等设计工具',
      '具备移动端和Web端设计经验',
      '良好的视觉设计和交互设计能力',
      '有汽车行业设计经验者优先'
    ]
  }
])

// 合作伙伴数据
const carManufacturers = ref([
  { name: '比亚迪', description: '新能源汽车领军企业' },
  { name: '蔚来汽车', description: '智能电动汽车品牌' },
  { name: '小鹏汽车', description: '智能汽车设计及制造商' },
  { name: '理想汽车', description: '豪华智能电动车品牌' }
])

const techPartners = ref([
  { name: '华为云', description: '云计算和AI技术服务' },
  { name: '阿里云', description: '云计算基础设施服务' },
  { name: '腾讯云', description: '云服务和大数据分析' },
  { name: '百度AI', description: '人工智能技术平台' }
])

// 方法
const submitContactForm = async () => {
  if (!contactFormRef.value) return
  
  try {
    await contactFormRef.value.validate()
    submitting.value = true
    
    // 模拟提交
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('留言提交成功，我们会尽快与您联系！')
    resetContactForm()
  } catch (error) {
    ElMessage.error('请检查表单信息是否填写正确')
  } finally {
    submitting.value = false
  }
}

const resetContactForm = () => {
  if (!contactFormRef.value) return
  contactFormRef.value.resetFields()
}

const applyJob = (jobTitle: string) => {
  ElMessageBox.confirm(
    `您确定要申请${jobTitle}职位吗？我们会将您跳转到招聘页面。`,
    '职位申请',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    }
  ).then(() => {
    ElMessage.success('正在跳转到招聘页面...')
    // 这里可以添加跳转到具体招聘页面的逻辑
    window.open('https://careers.ev-smart.com', '_blank')
  }).catch(() => {
    ElMessage.info('已取消申请')
  })
}

const contactBusiness = () => {
  ElMessage.info('商务合作功能正在完善中，请通过联系方式与我们取得联系')
}
</script>

<style scoped>
.about-us-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 40px 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

.header-content {
  max-width: 800px;
  margin: 0 auto;
}

.page-title {
  font-size: 3rem;
  font-weight: 700;
  color: white;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
}

.page-subtitle {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
}

.main-content {
  max-width: 1200px;
  margin: 0 auto;
}

.section {
  margin-bottom: 30px;
}

.content-card {
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border: none;
  overflow: hidden;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1.3rem;
  font-weight: 600;
  color: #2c3e50;
}

.company-intro {
  padding: 20px;
}

.intro-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 40px;
  align-items: start;
}

.intro-text h3 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 1.3rem;
}

.intro-text p {
  color: #5a6c7d;
  line-height: 1.6;
  margin-bottom: 20px;
}

.values-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-top: 20px;
}

.value-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 12px;
}

.value-content h4 {
  color: #2c3e50;
  margin: 0 0 5px 0;
  font-size: 1.1rem;
}

.value-content p {
  color: #6c757d;
  margin: 0;
  font-size: 0.9rem;
}

.intro-stats h3 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 1.3rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 15px;
  color: white;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.9;
}

.team-content {
  padding: 20px;
}

.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.team-member {
  text-align: center;
  padding: 25px;
  background: #f8f9fa;
  border-radius: 15px;
  transition: transform 0.3s ease;
}

.team-member:hover {
  transform: translateY(-5px);
}

.member-avatar {
  margin-bottom: 15px;
}

.member-info h4 {
  color: #2c3e50;
  margin: 10px 0 5px 0;
  font-size: 1.2rem;
}

.member-position {
  color: #667eea;
  font-weight: 600;
  margin-bottom: 10px;
}

.member-description {
  color: #6c757d;
  line-height: 1.5;
  margin-bottom: 15px;
}

.member-skills {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
}

.timeline-content {
  padding: 20px;
}

.timeline {
  position: relative;
  padding-left: 30px;
}

.timeline-item {
  position: relative;
  margin-bottom: 30px;
  display: flex;
  gap: 20px;
}

.timeline-marker {
  position: relative;
  flex-shrink: 0;
}

.marker-dot {
  width: 16px;
  height: 16px;
  background: #667eea;
  border-radius: 50%;
  border: 3px solid white;
  box-shadow: 0 0 0 3px #667eea;
}

.marker-line {
  position: absolute;
  left: 50%;
  top: 16px;
  width: 2px;
  height: 50px;
  background: #e9ecef;
  transform: translateX(-50%);
}

.timeline-content-item {
  flex: 1;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
}

.timeline-date {
  color: #667eea;
  font-weight: 600;
  font-size: 0.9rem;
  margin-bottom: 8px;
}

.timeline-content-item h4 {
  color: #2c3e50;
  margin: 0 0 10px 0;
  font-size: 1.2rem;
}

.timeline-content-item p {
  color: #6c757d;
  line-height: 1.5;
  margin-bottom: 15px;
}

.milestone-achievements {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.contact-content {
  padding: 20px;
}

.contact-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
}

.contact-info h3,
.contact-form h3 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 1.3rem;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 12px;
}

.contact-details h4 {
  color: #2c3e50;
  margin: 0 0 5px 0;
  font-size: 1.1rem;
}

.contact-details p {
  color: #6c757d;
  margin: 0;
}

.careers-content {
  padding: 20px;
}

.careers-intro {
  text-align: center;
  margin-bottom: 30px;
}

.careers-intro h3 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 1.5rem;
}

.careers-intro p {
  color: #6c757d;
  font-size: 1.1rem;
  line-height: 1.6;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.benefit-item {
  text-align: center;
  padding: 25px;
  background: #f8f9fa;
  border-radius: 15px;
  transition: transform 0.3s ease;
}

.benefit-item:hover {
  transform: translateY(-5px);
}

.benefit-item h4 {
  color: #2c3e50;
  margin: 15px 0 10px 0;
  font-size: 1.2rem;
}

.benefit-item p {
  color: #6c757d;
  line-height: 1.5;
}

.job-positions h3 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 1.4rem;
}

.positions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 25px;
}

.position-item {
  padding: 25px;
  background: #f8f9fa;
  border-radius: 15px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.position-item:hover {
  border-color: #667eea;
  transform: translateY(-3px);
}

.position-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.position-header h4 {
  color: #2c3e50;
  margin: 0;
  font-size: 1.2rem;
}

.position-details {
  margin-bottom: 15px;
}

.position-details p {
  color: #6c757d;
  margin: 5px 0;
  font-size: 0.95rem;
}

.position-requirements {
  margin-bottom: 20px;
}

.position-requirements h5 {
  color: #2c3e50;
  margin: 0 0 10px 0;
  font-size: 1rem;
}

.position-requirements ul {
  margin: 0;
  padding-left: 20px;
}

.position-requirements li {
  color: #6c757d;
  margin-bottom: 5px;
  font-size: 0.9rem;
}

.partners-content {
  padding: 20px;
}

.partners-intro {
  text-align: center;
  margin-bottom: 30px;
}

.partners-intro h3 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 1.5rem;
}

.partners-intro p {
  color: #6c757d;
  font-size: 1.1rem;
  line-height: 1.6;
}

.partners-categories {
  margin-bottom: 40px;
}

.partner-category {
  margin-bottom: 30px;
}

.partner-category h4 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 1.3rem;
}

.partners-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.partner-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  transition: transform 0.3s ease;
}

.partner-item:hover {
  transform: translateY(-3px);
}

.partner-info h5 {
  color: #2c3e50;
  margin: 0 0 5px 0;
  font-size: 1.1rem;
}

.partner-info p {
  color: #6c757d;
  margin: 0;
  font-size: 0.9rem;
}

.partnership-cta {
  text-align: center;
  padding: 30px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 15px;
  color: white;
}

.partnership-cta h3 {
  margin-bottom: 10px;
  font-size: 1.5rem;
}

.partnership-cta p {
  margin-bottom: 20px;
  font-size: 1.1rem;
  opacity: 0.9;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-title {
    font-size: 2rem;
  }
  
  .intro-content {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .contact-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .values-grid {
    grid-template-columns: 1fr;
  }
  
  .benefits-grid {
    grid-template-columns: 1fr;
  }
  
  .positions-grid {
    grid-template-columns: 1fr;
  }
  
  .partners-grid {
    grid-template-columns: 1fr;
  }
}
</style>