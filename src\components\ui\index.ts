// 现代化UI组件库
// 基于Uiverse.io设计理念的现代化组件集合

export { default as ModernButton } from './ModernButton.vue'
export { default as ModernLoader } from './ModernLoader.vue'
export { default as ModernCard } from './ModernCard.vue'
export { default as ModernInput } from './ModernInput.vue'
export { default as BikeLoader } from '../BikeLoader.vue'
export { default as RouteLoader } from '../RouteLoader.vue'

// 组件类型定义
export interface ModernButtonProps {
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'ghost' | 'neon'
  size?: 'small' | 'medium' | 'large'
  loading?: boolean
  disabled?: boolean
}

export interface ModernLoaderProps {
  type?: 'pulse' | 'quantum' | 'neon-spiral' | 'particle-wave' | 'hologram' | 'energy-ring' | 'modern'
  text?: string
  overlay?: boolean
}

export interface ModernCardProps {
  variant?: 'glass' | 'gradient' | 'neon' | 'minimal' | 'holographic'
  hoverable?: boolean
  clickable?: boolean
  loading?: boolean
}

export interface ModernInputProps {
  modelValue?: string | number
  type?: 'text' | 'password' | 'email' | 'number' | 'tel' | 'url' | 'search' | 'textarea'
  variant?: 'glass' | 'neon' | 'minimal' | 'gradient'
  label?: string
  placeholder?: string
  error?: string
  helpText?: string
  disabled?: boolean
  readonly?: boolean
  required?: boolean
  maxlength?: number
  rows?: number
  showClear?: boolean
  showCount?: boolean
}

// 主题配置
export const modernTheme = {
  colors: {
    primary: {
      gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      shadow: 'rgba(102, 126, 234, 0.4)'
    },
    secondary: {
      gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
      shadow: 'rgba(240, 147, 251, 0.4)'
    },
    success: {
      gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
      shadow: 'rgba(79, 172, 254, 0.4)'
    },
    warning: {
      gradient: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
      shadow: 'rgba(250, 112, 154, 0.4)'
    },
    danger: {
      gradient: 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)',
      shadow: 'rgba(255, 107, 107, 0.4)'
    },
    neon: {
      cyan: '#00f5ff',
      pink: '#ff6b6b',
      green: '#4ecdc4'
    }
  },
  effects: {
    glass: {
      background: 'rgba(255, 255, 255, 0.1)',
      border: 'rgba(255, 255, 255, 0.2)',
      backdrop: 'blur(20px)'
    },
    shadow: {
      small: '0 4px 16px 0',
      medium: '0 8px 32px 0',
      large: '0 16px 64px 0'
    },
    borderRadius: {
      small: '8px',
      medium: '12px',
      large: '20px'
    }
  },
  animations: {
    duration: {
      fast: '0.2s',
      normal: '0.3s',
      slow: '0.4s'
    },
    easing: {
      smooth: 'cubic-bezier(0.4, 0, 0.2, 1)',
      bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)'
    }
  }
}

// 工具函数
export const modernUtils = {
  // 生成渐变背景
  createGradient: (color1: string, color2: string, angle = 135) => {
    return `linear-gradient(${angle}deg, ${color1} 0%, ${color2} 100%)`
  },
  
  // 生成阴影
  createShadow: (color: string, opacity = 0.4, size = 'medium') => {
    const shadows = {
      small: '0 4px 16px 0',
      medium: '0 8px 32px 0',
      large: '0 16px 64px 0'
    }
    return `${shadows[size]} ${color.replace(')', `, ${opacity})`)}`
  },
  
  // 生成玻璃态效果
  createGlassEffect: (opacity = 0.1) => {
    return {
      background: `rgba(255, 255, 255, ${opacity})`,
      backdropFilter: 'blur(20px)',
      border: `1px solid rgba(255, 255, 255, ${opacity * 2})`
    }
  },
  
  // 生成霓虹效果
  createNeonEffect: (color: string, intensity = 1) => {
    return {
      boxShadow: `
        0 0 ${20 * intensity}px ${color},
        0 0 ${40 * intensity}px ${color},
        inset 0 0 ${20 * intensity}px ${color.replace(')', ', 0.1)')}
      `,
      textShadow: `0 0 ${10 * intensity}px ${color}`
    }
  }
}

// 响应式断点
export const breakpoints = {
  mobile: '768px',
  tablet: '1024px',
  desktop: '1200px',
  wide: '1600px'
}

// 组件默认配置
export const defaultConfig = {
  button: {
    variant: 'primary' as const,
    size: 'medium' as const
  },
  loader: {
    type: 'modern' as const
  },
  card: {
    variant: 'glass' as const,
    hoverable: true
  },
  input: {
    variant: 'glass' as const
  }
}