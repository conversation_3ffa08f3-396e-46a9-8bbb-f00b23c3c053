import { Router } from 'express'
import { db } from '../config/mysql'
import type { Vehicle, User, ApiResponse, PaginatedResponse } from '../../shared/types'

const router = Router()

// 获取车辆列表
router.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 10, user_id, brand, vehicle_type, status } = req.query
    const offset = (Number(page) - 1) * Number(limit)
    
    // 构建查询条件
    const conditions = []
    const params = []
    
    if (user_id) {
      conditions.push('v.owner_id = ?')
      params.push(user_id)
    }
    
    if (brand) {
      conditions.push('v.brand LIKE ?')
      params.push(`%${brand}%`)
    }
    
    if (vehicle_type) {
      conditions.push('v.vehicle_type = ?')
      params.push(vehicle_type)
    }
    
    if (status) {
      conditions.push('v.status = ?')
      params.push(status)
    }
    
    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : ''
    
    // 获取总数
    const countResult = await db.query(
      `SELECT COUNT(*) as total FROM vehicles v ${whereClause}`,
      params
    )
    const count = countResult[0]?.total || 0
    
    // 获取数据
    const dataResult = await db.query(
      `SELECT v.*, u.username as user_name, u.email as user_email,
              b.id as battery_id, b.battery_id as battery_serial, b.health_percentage as current_soh, b.current_charge as current_soc, b.status as battery_status
       FROM vehicles v
       LEFT JOIN users u ON v.owner_id = u.user_id
       LEFT JOIN batteries b ON v.vehicle_id = b.vehicle_id
       ${whereClause}
       ORDER BY v.created_at DESC
       LIMIT ? OFFSET ?`,
      [...params, Number(limit), Number(offset)]
    )
    const data = dataResult || []
    
    // 格式化数据
    const formattedData = data.map(record => ({
      ...record,
      users: record.user_name ? {
        name: record.user_name,
        email: record.user_email
      } : null,
      batteries: record.battery_id ? [{
        id: record.battery_id,
        battery_id: record.battery_serial,
        current_soh: record.current_soh,
        current_soc: record.current_soc,
        status: record.battery_status
      }] : [],
      // 移除冗余字段
      user_name: undefined,
      user_email: undefined,
      battery_serial: undefined,
      current_soh: undefined,
      current_soc: undefined,
      battery_status: undefined
    }))
    
    const response: PaginatedResponse<Vehicle> = {
      success: true,
      data: formattedData,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: count,
        totalPages: Math.ceil(count / Number(limit))
      }
    }
    
    res.json(response)
  } catch (error) {
    console.error('获取车辆列表异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取车辆统计数据
router.get('/status', async (req, res) => {
  try {
    // 获取车辆总数和在线车辆数
    const vehiclesResult = await db.query(
      'SELECT vehicle_id, status FROM vehicles'
    )
    const totalVehicles = vehiclesResult || []

    // 计算在线车辆数
    const onlineVehicles = totalVehicles.filter(v => v.status === 'active').length
    const totalCount = totalVehicles.length
    
    // 计算增长率（模拟数据，实际应该基于历史数据）
    const increaseRate = Math.round((onlineVehicles / Math.max(totalCount, 1)) * 100 * 0.12) // 模拟12%增长

    res.json({
      success: true,
      data: {
        total: onlineVehicles,
        increase: increaseRate,
        totalVehicles: totalCount,
        onlineRate: Math.round((onlineVehicles / Math.max(totalCount, 1)) * 100)
      }
    } as ApiResponse)
  } catch (error) {
    console.error('获取车辆统计异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取单个车辆信息
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params
    
    // 获取车辆基本信息和用户信息
    const vehicleResult = await db.query(
      `SELECT v.*, u.user_id as user_id, u.username as user_name, u.email as user_email, u.phone as user_phone
       FROM vehicles v
       LEFT JOIN users u ON v.owner_id = u.user_id
       WHERE v.vehicle_id = ?`,
      [id]
    )
    const vehicleData = vehicleResult[0]
    
    if (!vehicleData) {
      return res.status(404).json({
        success: false,
        error: '车辆不存在'
      } as ApiResponse)
    }
    
    // 获取电池信息
    const batteriesResult = await db.query(
      'SELECT * FROM batteries WHERE vehicle_id = ?',
      [id]
    )
    const batteries = batteriesResult || []
    
    // 获取充电会话信息
    const chargingSessionsResult = await db.query(
      `SELECT session_id, session_code, start_time, end_time, energy_delivered_kwh, total_cost, status
       FROM charging_sessions WHERE vehicle_id = ?`,
      [id]
    )
    const chargingSessions = chargingSessionsResult || []
    
    // 格式化数据
    const formattedData = {
      ...vehicleData,
      users: vehicleData.user_name ? {
        id: vehicleData.user_id,
        name: vehicleData.user_name,
        email: vehicleData.user_email,
        phone: vehicleData.user_phone
      } : null,
      batteries,
      charging_sessions: chargingSessions,
      // 移除冗余字段
      user_name: undefined,
      user_email: undefined,
      user_phone: undefined
    }
    
    res.json({
      success: true,
      data: formattedData
    } as ApiResponse<Vehicle>)
  } catch (error) {
    console.error('获取车辆信息异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 创建车辆
router.post('/', async (req, res) => {
  try {
    const {
      user_id,
      vin,
      license_plate,
      brand,
      model,
      year,
      color,
      vehicle_type = 'bev',
      battery_capacity,
      max_range
    } = req.body
    
    // 验证必填字段
    if (!user_id || !vin || !brand || !model) {
      return res.status(400).json({
        success: false,
        error: '用户ID、VIN码、品牌和型号为必填字段'
      } as ApiResponse)
    }
    
    // 检查VIN码是否已存在
    const [existingVehicleResult] = await db.execute(
      'SELECT id FROM vehicles WHERE vin = ?',
      [vin]
    )
    const existingVehicle = (existingVehicleResult as any[])[0]
    
    if (existingVehicle) {
      return res.status(400).json({
        success: false,
        error: 'VIN码已存在'
      } as ApiResponse)
    }
    
    // 验证用户是否存在
    const [userResult] = await db.execute(
      'SELECT id FROM users WHERE id = ?',
      [user_id]
    )
    const user = (userResult as any[])[0]
    
    if (!user) {
      return res.status(400).json({
        success: false,
        error: '用户不存在'
      } as ApiResponse)
    }
    
    // 插入车辆数据
    const [insertResult] = await db.execute(
      `INSERT INTO vehicles (user_id, vin, license_plate, brand, model, year, color, vehicle_type, battery_capacity, max_range, status, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', NOW(), NOW())`,
      [user_id, vin, license_plate, brand, model, year, color, vehicle_type, battery_capacity, max_range]
    )
    
    const insertId = (insertResult as any).insertId
    
    // 获取插入的记录
    const [vehicleResult] = await db.execute(
      'SELECT * FROM vehicles WHERE id = ?',
      [insertId]
    )
    const vehicleData = (vehicleResult as any[])[0]
    
    res.status(201).json({
      success: true,
      data: vehicleData,
      message: '车辆创建成功'
    } as ApiResponse<Vehicle>)
  } catch (error) {
    console.error('创建车辆异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 更新车辆信息
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params
    const {
      license_plate,
      brand,
      model,
      year,
      color,
      vehicle_type,
      battery_capacity,
      max_range,
      status,
      location
    } = req.body
    
    // 构建更新字段和参数
    const updateFields = []
    const updateParams = []
    
    if (license_plate !== undefined) {
      updateFields.push('license_plate = ?')
      updateParams.push(license_plate)
    }
    if (brand !== undefined) {
      updateFields.push('brand = ?')
      updateParams.push(brand)
    }
    if (model !== undefined) {
      updateFields.push('model = ?')
      updateParams.push(model)
    }
    if (year !== undefined) {
      updateFields.push('year = ?')
      updateParams.push(year)
    }
    if (color !== undefined) {
      updateFields.push('color = ?')
      updateParams.push(color)
    }
    if (vehicle_type !== undefined) {
      updateFields.push('vehicle_type = ?')
      updateParams.push(vehicle_type)
    }
    if (battery_capacity !== undefined) {
      updateFields.push('battery_capacity = ?')
      updateParams.push(battery_capacity)
    }
    if (max_range !== undefined) {
      updateFields.push('max_range = ?')
      updateParams.push(max_range)
    }
    if (status !== undefined) {
      updateFields.push('status = ?')
      updateParams.push(status)
    }
    if (location !== undefined) {
      updateFields.push('location = ?')
      updateParams.push(location)
    }
    
    updateFields.push('updated_at = NOW()')
    updateParams.push(id)
    
    if (updateFields.length === 1) {
      return res.status(400).json({
        success: false,
        error: '没有提供要更新的字段'
      } as ApiResponse)
    }
    
    // 执行更新
    await db.execute(
      `UPDATE vehicles SET ${updateFields.join(', ')} WHERE id = ?`,
      updateParams
    )
    
    // 获取更新后的记录
    const [vehicleResult] = await db.execute(
      'SELECT * FROM vehicles WHERE id = ?',
      [id]
    )
    const vehicleData = (vehicleResult as any[])[0]
    
    if (!vehicleData) {
      return res.status(404).json({
        success: false,
        error: '车辆不存在'
      } as ApiResponse)
    }
    
    res.json({
      success: true,
      data: vehicleData,
      message: '车辆信息更新成功'
    } as ApiResponse<Vehicle>)
  } catch (error) {
    console.error('更新车辆异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 删除车辆
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params
    
    // 检查车辆是否存在
    const [vehicleResult] = await db.execute(
      'SELECT id FROM vehicles WHERE id = ?',
      [id]
    )
    const vehicle = (vehicleResult as any[])[0]
    
    if (!vehicle) {
      return res.status(404).json({
        success: false,
        error: '车辆不存在'
      } as ApiResponse)
    }
    
    // 删除车辆
    await db.execute(
      'DELETE FROM vehicles WHERE id = ?',
      [id]
    )
    
    res.json({
      success: true,
      message: '车辆删除成功'
    } as ApiResponse)
  } catch (error) {
    console.error('删除车辆异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取车辆的电池信息
router.get('/:id/batteries', async (req, res) => {
  try {
    const { id } = req.params
    
    const [batteriesResult] = await db.execute(
      'SELECT * FROM batteries WHERE vehicle_id = ? ORDER BY created_at DESC',
      [id]
    )
    const batteries = batteriesResult as any[]
    
    res.json({
      success: true,
      data: batteries
    } as ApiResponse)
  } catch (error) {
    console.error('获取车辆电池信息异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取车辆的充电记录
router.get('/:id/charging-sessions', async (req, res) => {
  try {
    const { id } = req.params
    const { page = 1, limit = 10 } = req.query
    const offset = (Number(page) - 1) * Number(limit)
    
    // 获取总数
    const [countResult] = await db.execute(
      'SELECT COUNT(*) as total FROM charging_sessions WHERE vehicle_id = ?',
      [id]
    )
    const count = (countResult as any[])[0].total
    
    // 获取数据
    const [dataResult] = await db.execute(
      `SELECT cs.*, s.name as station_name, s.address as station_address
       FROM charging_sessions cs
       LEFT JOIN charging_stations s ON cs.station_id = s.id
       WHERE cs.vehicle_id = ?
       ORDER BY cs.start_time DESC
       LIMIT ? OFFSET ?`,
      [id, Number(limit), offset]
    )
    const data = dataResult as any[]
    
    // 格式化数据
    const formattedData = data.map(record => ({
      ...record,
      charging_stations: record.station_name ? {
        name: record.station_name,
        address: record.station_address
      } : null,
      // 移除冗余字段
      station_name: undefined,
      station_address: undefined
    }))
    
    const response: PaginatedResponse = {
      success: true,
      data: formattedData,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: count,
        totalPages: Math.ceil(count / Number(limit))
      }
    }
    
    res.json(response)
  } catch (error) {
    console.error('获取车辆充电记录异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取车辆统计数据
router.get('/stats', async (req, res) => {
  try {
    // 获取车辆总数和在线车辆数
    const [vehiclesResult] = await db.execute(
      'SELECT id, status FROM vehicles'
    )
    const totalVehicles = vehiclesResult as any[]

    // 计算在线车辆数
    const onlineVehicles = totalVehicles.filter(v => v.status === 'active').length
    const totalCount = totalVehicles.length
    
    // 计算增长率（模拟数据，实际应该基于历史数据）
    const increaseRate = Math.round((onlineVehicles / Math.max(totalCount, 1)) * 100 * 0.12) // 模拟12%增长

    res.json({
      success: true,
      data: {
        total: onlineVehicles,
        increase: increaseRate,
        totalVehicles: totalCount,
        onlineRate: Math.round((onlineVehicles / Math.max(totalCount, 1)) * 100)
      }
    } as ApiResponse)
  } catch (error) {
    console.error('获取车辆统计异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取车联网统计数据
router.get('/network/stats', async (req, res) => {
  try {
    // 获取车辆总数和在线车辆数
    const [vehiclesResult] = await db.execute(
      'SELECT id, status, created_at FROM vehicles'
    )
    const totalVehicles = vehiclesResult as any[]

    // 计算在线车辆数
    const onlineVehicles = totalVehicles.filter(v => v.status === 'active').length
    const totalCount = totalVehicles.length
    
    // 模拟网络统计数据
    const networkStats = {
      connectedVehicles: onlineVehicles,
      totalVehicles: totalCount,
      connectionRate: Math.round((onlineVehicles / Math.max(totalCount, 1)) * 100),
      dataTransmitted: Math.round(Math.random() * 1000 + 500), // MB
      averageLatency: Math.round(Math.random() * 50 + 20), // ms
      networkUptime: 99.8,
      activeConnections: onlineVehicles,
      peakConnections: Math.round(onlineVehicles * 1.2),
      bandwidthUsage: Math.round(Math.random() * 80 + 20), // %
      errorRate: Math.round(Math.random() * 2 * 100) / 100, // %
      lastUpdated: new Date().toISOString()
    }

    res.json({
      success: true,
      data: networkStats
    } as ApiResponse)
  } catch (error) {
    console.error('获取车联网统计异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取车辆诊断数据
router.get('/diagnostic', async (req, res) => {
  try {
    const { vehicle_id } = req.query
    
    // 如果指定了车辆ID，获取特定车辆的诊断数据
    if (vehicle_id) {
      const [vehicleResult] = await db.execute(
        'SELECT id, model, brand, status FROM vehicles WHERE id = ?',
        [vehicle_id]
      )
      const vehicle = (vehicleResult as any[])[0]
      
      if (!vehicle) {
        return res.status(404).json({
          success: false,
          error: '车辆不存在'
        } as ApiResponse)
      }
      
      // 模拟车辆诊断数据
      const diagnosticData = {
        vehicle_id: vehicle_id,
        vehicle_info: {
          model: vehicle.model,
          brand: vehicle.brand,
          status: vehicle.status
        },
        engine: {
          temperature: Math.round(Math.random() * 20 + 80), // 80-100°C
          rpm: Math.round(Math.random() * 2000 + 1000), // 1000-3000 RPM
          load: Math.round(Math.random() * 50 + 30), // 30-80%
          status: 'normal'
        },
        battery: {
          voltage: Math.round((Math.random() * 2 + 12) * 100) / 100, // 12-14V
          current: Math.round((Math.random() * 50 + 10) * 100) / 100, // 10-60A
          soc: Math.round(Math.random() * 40 + 60), // 60-100%
          temperature: Math.round(Math.random() * 15 + 25), // 25-40°C
          status: 'good'
        },
        transmission: {
          temperature: Math.round(Math.random() * 30 + 70), // 70-100°C
          pressure: Math.round(Math.random() * 20 + 40), // 40-60 PSI
          gear: Math.round(Math.random() * 5 + 1), // 1-6
          status: 'normal'
        },
        brakes: {
          frontPadWear: Math.round(Math.random() * 30 + 10), // 10-40%
          rearPadWear: Math.round(Math.random() * 25 + 15), // 15-40%
          fluidLevel: Math.round(Math.random() * 20 + 80), // 80-100%
          status: 'good'
        },
        tires: {
          frontLeft: Math.round(Math.random() * 10 + 30), // 30-40 PSI
          frontRight: Math.round(Math.random() * 10 + 30),
          rearLeft: Math.round(Math.random() * 10 + 30),
          rearRight: Math.round(Math.random() * 10 + 30),
          status: 'normal'
        },
        lastDiagnostic: new Date().toISOString(),
        overallStatus: 'healthy'
      }
      
      res.json({
        success: true,
        data: diagnosticData
      } as ApiResponse)
    } else {
      // 获取所有车辆的诊断概览
      const [vehiclesResult] = await db.execute(
        'SELECT id, model, brand, status FROM vehicles LIMIT 10'
      )
      const vehicles = vehiclesResult as any[]
      
      const diagnosticOverview = vehicles.map(vehicle => ({
        vehicle_id: vehicle.id,
        model: vehicle.model,
        brand: vehicle.brand,
        status: vehicle.status,
        overallHealth: Math.round(Math.random() * 20 + 80), // 80-100%
        lastDiagnostic: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
        issuesCount: Math.round(Math.random() * 3),
        criticalIssues: Math.round(Math.random() * 1)
      }))
      
      res.json({
        success: true,
        data: diagnosticOverview
      } as ApiResponse)
    }
  } catch (error) {
    console.error('获取车辆诊断数据异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取车辆OTA更新信息
router.get('/ota', async (req, res) => {
  try {
    const { vehicle_id } = req.query
    
    if (vehicle_id) {
      // 获取特定车辆的OTA信息
      const [vehicleResult] = await db.execute(
        'SELECT id, model, brand, status FROM vehicles WHERE id = ?',
        [vehicle_id]
      )
      const vehicle = (vehicleResult as any[])[0]
      
      if (!vehicle) {
        return res.status(404).json({
          success: false,
          error: '车辆不存在'
        } as ApiResponse)
      }
      
      // 模拟OTA更新数据
      const otaData = {
        vehicle_id: vehicle_id,
        vehicle_info: {
          model: vehicle.model,
          brand: vehicle.brand
        },
        currentVersion: '2.1.3',
        availableVersion: '2.2.0',
        updateAvailable: Math.random() > 0.5,
        updateSize: Math.round(Math.random() * 500 + 100), // 100-600 MB
        updateDescription: '系统性能优化，新增智能驾驶功能，修复已知问题',
        releaseDate: '2024-01-15',
        downloadProgress: 0,
        installProgress: 0,
        status: 'idle', // idle, downloading, installing, completed, failed
        lastUpdate: '2024-01-01',
        updateHistory: [
          {
            version: '2.1.3',
            date: '2024-01-01',
            description: '修复充电系统bug，优化电池管理'
          },
          {
            version: '2.1.2',
            date: '2023-12-15',
            description: '新增远程诊断功能，提升系统稳定性'
          },
          {
            version: '2.1.1',
            date: '2023-12-01',
            description: '优化用户界面，修复导航问题'
          }
        ]
      }
      
      res.json({
        success: true,
        data: otaData
      } as ApiResponse)
    } else {
      // 获取所有车辆的OTA概览
      const [vehiclesResult] = await db.execute(
        'SELECT id, model, brand, status FROM vehicles LIMIT 10'
      )
      const vehicles = vehiclesResult as any[]
      
      const otaOverview = vehicles.map(vehicle => ({
        vehicle_id: vehicle.id,
        model: vehicle.model,
        brand: vehicle.brand,
        currentVersion: '2.1.' + Math.round(Math.random() * 5),
        updateAvailable: Math.random() > 0.6,
        lastUpdate: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
        status: ['idle', 'downloading', 'installing'][Math.floor(Math.random() * 3)]
      }))
      
      res.json({
        success: true,
        data: otaOverview
      } as ApiResponse)
    }
  } catch (error) {
    console.error('获取OTA信息异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取车辆状态分布数据
router.get('/status-distribution', async (req, res) => {
  try {
    const [statusResult] = await db.execute(
      'SELECT status FROM vehicles'
    )
    const data = statusResult as any[]

    // 统计各状态的车辆数量
    const statusCount = data.reduce((acc: any, vehicle: any) => {
      acc[vehicle.status] = (acc[vehicle.status] || 0) + 1
      return acc
    }, {})

    // 转换为图表数据格式
    const chartData = Object.entries(statusCount).map(([status, count]) => ({
      name: status === 'active' ? '在线' : status === 'inactive' ? '离线' : status === 'maintenance' ? '维护中' : '其他',
      value: count
    }))

    res.json({
      success: true,
      data: chartData
    } as ApiResponse)
  } catch (error) {
    console.error('获取车辆状态分布异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取车辆位置信息
router.get('/:id/location', async (req, res) => {
  try {
    const { id } = req.params
    
    // 检查车辆是否存在
    const [vehicleResult] = await db.execute(
      'SELECT id, model, brand, status FROM vehicles WHERE id = ?',
      [id]
    )
    const vehicle = (vehicleResult as any[])[0]
    
    if (!vehicle) {
      return res.status(404).json({
        success: false,
        error: '车辆不存在'
      } as ApiResponse)
    }
    
    // 模拟获取车辆位置信息（实际项目中应该从GPS设备或车联网平台获取）
    const locationData = {
      vehicle_id: id,
      latitude: 39.9042 + (Math.random() - 0.5) * 0.1, // 北京市中心附近随机位置
      longitude: 116.4074 + (Math.random() - 0.5) * 0.1,
      address: '北京市朝阳区建国门外大街',
      speed: Math.round(Math.random() * 80), // 0-80 km/h
      direction: Math.round(Math.random() * 360), // 0-360度
      altitude: Math.round(Math.random() * 100 + 50), // 50-150米
      accuracy: Math.round(Math.random() * 10 + 5), // 5-15米精度
      updated_at: new Date().toISOString(),
      status: vehicle.status === 'active' ? 'online' : 'offline'
    }
    
    res.json({
      success: true,
      data: locationData
    } as ApiResponse)
  } catch (error) {
    console.error('获取车辆位置异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 车辆远程控制
router.post('/:id/control', async (req, res) => {
  try {
    const { id } = req.params
    const { action } = req.body
    
    // 验证控制指令
    const validActions = ['lock', 'unlock', 'start', 'stop', 'horn', 'flash']
    if (!action || !validActions.includes(action)) {
      return res.status(400).json({
        success: false,
        error: '无效的控制指令'
      } as ApiResponse)
    }
    
    // 检查车辆是否存在
    const [vehicleResult] = await db.execute(
      'SELECT id, model, brand, status FROM vehicles WHERE id = ?',
      [id]
    )
    const vehicle = (vehicleResult as any[])[0]
    
    if (!vehicle) {
      return res.status(404).json({
        success: false,
        error: '车辆不存在'
      } as ApiResponse)
    }
    
    // 检查车辆是否在线
    if (vehicle.status !== 'active') {
      return res.status(400).json({
        success: false,
        error: '车辆离线，无法执行控制指令'
      } as ApiResponse)
    }
    
    // 模拟执行控制指令（实际项目中应该发送到车联网平台）
    const actionResults = {
      lock: { message: '车辆已锁定', status: 'locked' },
      unlock: { message: '车辆已解锁', status: 'unlocked' },
      start: { message: '车辆已启动', status: 'running' },
      stop: { message: '车辆已熄火', status: 'stopped' },
      horn: { message: '鸣笛指令已执行', status: 'horn_activated' },
      flash: { message: '闪灯指令已执行', status: 'lights_flashed' }
    }
    
    const result = actionResults[action]
    
    // 记录控制日志（可选）
    try {
      await db.execute(
        'INSERT INTO vehicle_control_logs (vehicle_id, action, result, created_at) VALUES (?, ?, ?, NOW())',
        [id, action, result.message]
      )
    } catch (logError) {
      console.warn('记录控制日志失败:', logError)
    }
    
    res.json({
      success: true,
      data: {
        vehicle_id: id,
        action: action,
        result: result.message,
        status: result.status,
        timestamp: new Date().toISOString()
      },
      message: result.message
    } as ApiResponse)
  } catch (error) {
    console.error('车辆控制异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

export default router