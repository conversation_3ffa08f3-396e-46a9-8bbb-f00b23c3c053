# 新能源汽车智能综合管理系统 - 技术架构文档

## 1. 架构设计

```mermaid
graph TD
    A[车载终端/移动端] --> B[边缘计算节点]
    A --> C[Web前端应用]
    C --> D[API网关]
    B --> D
    D --> E[微服务集群]
    E --> F[消息队列]
    E --> G[缓存层]
    E --> H[数据库集群]
    E --> I[区块链网络]
    E --> J[AI模型服务]
    E --> K[时序数据库]
    
    subgraph "端侧层"
        A
        L[车载HMI]
        M[移动APP]
        N[Web管理端]
    end
    
    subgraph "边缘层"
        B
        O[边缘AI推理]
        P[本地数据缓存]
        Q[实时处理引擎]
    end
    
    subgraph "云端服务层"
        D
        E
        F
        G
    end
    
    subgraph "数据存储层"
        H
        I
        J
        K
        R[对象存储]
    end
    
    subgraph "外部服务"
        S[地图服务API]
        T[支付服务API]
        U[气象服务API]
        V[交通信息API]
    end
    
    E --> S
    E --> T
    E --> U
    E --> V
```

## 2. 技术描述

### 前端技术栈

* **Web端**: Vue 3 + TypeScript + Element Plus + Vite

* **移动端**: Flutter 3.0 + Dart

* **车载端**: Qt 6 + QML + C++

* **管理端**: React 18 + TypeScript + Ant Design + Webpack

### 后端技术栈

* **微服务框架**: Spring Boot 3.0 + Spring Cloud 2022

* **API网关**: Spring Cloud Gateway

* **服务发现**: Nacos

* **配置中心**: Nacos Config

* **负载均衡**: Ribbon + OpenFeign

* **熔断器**: Sentinel

### 数据存储

* **关系数据库**: PostgreSQL 15 (主数据)

* **时序数据库**: InfluxDB 2.0 (传感器数据)

* **缓存**: Redis 7.0 (会话缓存)

* **消息队列**: Apache Kafka 3.0

* **对象存储**: MinIO (文件存储)

* **区块链**: Hyperledger Fabric 2.4

### AI与大数据

* **机器学习**: Python 3.10 + TensorFlow 2.10 + PyTorch 1.13

* **数据处理**: Apache Spark 3.3 + Flink 1.16

* **模型服务**: TensorFlow Serving + ONNX Runtime

* **特征工程**: Apache Airflow 2.5

### 边缘计算

* **边缘框架**: KubeEdge + K3s

* **容器运行时**: Docker + containerd

* **ROS系统**: ROS 2 Humble

* **实时通信**: DDS (Data Distribution Service)

## 3. 路由定义

### Web管理端路由

| 路由         | 用途                    |
| ---------- | --------------------- |
| /dashboard | 系统总览仪表板，显示关键指标和实时状态   |
| /battery   | 智能电池管理中心，包含溯源、监测、预测功能 |
| /charging  | 智能充电服务平台，充电桩管理和订单处理   |
| /adas      | 高级驾驶辅助系统，安全监控和行为分析    |
| /vehicle   | 车联网交互中心，远程控制和故障诊断     |
| /ecosystem | 用户生态服务平台，用户画像和积分管理    |
| /security  | 系统安全运维中心，监控告警和权限管理    |
| /login     | 用户登录页面，支持多种认证方式       |
| /profile   | 用户个人中心，账户信息和偏好设置      |

### 移动端路由

| 路由        | 用途               |
| --------- | ---------------- |
| /home     | 首页，显示车辆状态和快捷功能入口 |
| /map      | 充电地图，查找附近充电桩和导航  |
| /charging | 充电服务，预约充电和支付管理   |
| /vehicle  | 车辆控制，远程操作和状态查看   |
| /profile  | 个人中心，用户信息和积分查看   |
| /settings | 设置页面，应用配置和账户管理   |

## 4. API定义

### 4.1 核心API

#### 电池管理相关API

```
POST /api/battery/trace
```

请求参数:

| 参数名称      | 参数类型   | 是否必需 | 描述      |
| --------- | ------ | ---- | ------- |
| batteryId | string | true | 电池唯一标识符 |
| traceData | object | true | 溯源数据对象  |
| timestamp | long   | true | 时间戳     |

响应参数:

| 参数名称      | 参数类型    | 描述     |
| --------- | ------- | ------ |
| success   | boolean | 操作是否成功 |
| traceId   | string  | 溯源记录ID |
| blockHash | string  | 区块链哈希值 |

示例:

```json
{
  "batteryId": "BAT20231201001",
  "traceData": {
    "manufacturer": "CATL",
    "productionDate": "2023-12-01",
    "materials": ["lithium", "cobalt", "nickel"]
  },
  "timestamp": 1701388800000
}
```

#### 充电服务相关API

```
POST /api/charging/reserve
```

请求参数:

| 参数名称      | 参数类型    | 是否必需 | 描述       |
| --------- | ------- | ---- | -------- |
| userId    | string  | true | 用户ID     |
| stationId | string  | true | 充电站ID    |
| startTime | string  | true | 预约开始时间   |
| duration  | integer | true | 预约时长(分钟) |

响应参数:

| 参数名称          | 参数类型    | 描述     |
| ------------- | ------- | ------ |
| success       | boolean | 预约是否成功 |
| reservationId | string  | 预约订单ID |
| queuePosition | integer | 排队位置   |

#### 车辆控制相关API

```
POST /api/vehicle/control
```

请求参数:

| 参数名称       | 参数类型   | 是否必需  | 描述     |
| ---------- | ------ | ----- | ------ |
| vehicleId  | string | true  | 车辆VIN码 |
| command    | string | true  | 控制命令   |
| parameters | object | false | 命令参数   |

响应参数:

| 参数名称      | 参数类型    | 描述     |
| --------- | ------- | ------ |
| success   | boolean | 控制是否成功 |
| commandId | string  | 命令执行ID |
| status    | string  | 执行状态   |

#### 用户认证相关API

```
POST /api/auth/login
```

请求参数:

| 参数名称     | 参数类型   | 是否必需 | 描述       |
| -------- | ------ | ---- | -------- |
| username | string | true | 用户名或手机号  |
| password | string | true | 密码(加密传输) |
| captcha  | string | true | 验证码      |
| deviceId | string | true | 设备标识     |

响应参数:

| 参数名称         | 参数类型    | 描述      |
| ------------ | ------- | ------- |
| success      | boolean | 登录是否成功  |
| token        | string  | JWT访问令牌 |
| refreshToken | string  | 刷新令牌    |
| userInfo     | object  | 用户基本信息  |

## 5. 服务架构图

```mermaid
graph TD
    A[客户端/前端] --> B[API网关层]
    B --> C[认证授权服务]
    B --> D[业务服务层]
    
    subgraph "业务服务层"
        D --> E[电池管理服务]
        D --> F[充电服务]
        D --> G[车辆控制服务]
        D --> H[用户服务]
        D --> I[支付服务]
        D --> J[消息通知服务]
    end
    
    subgraph "数据访问层"
        E --> K[电池数据仓库]
        F --> L[充电数据仓库]
        G --> M[车辆数据仓库]
        H --> N[用户数据仓库]
    end
    
    subgraph "基础设施层"
        O[服务注册中心]
        P[配置中心]
        Q[监控中心]
        R[日志中心]
    end
    
    D --> O
    D --> P
    D --> Q
    D --> R
```

## 6. 数据模型

### 6.1 数据模型定义

```mermaid
erDiagram
    USER ||--o{ VEHICLE : owns
    VEHICLE ||--o{ BATTERY : contains
    BATTERY ||--o{ BATTERY_TRACE : has
    VEHICLE ||--o{ CHARGING_SESSION : participates
    CHARGING_SESSION }o--|| CHARGING_STATION : uses
    USER ||--o{ CARBON_CREDIT : earns
    VEHICLE ||--o{ DRIVING_BEHAVIOR : generates
    USER ||--o{ FLEET_MANAGEMENT : manages
    
    USER {
        uuid id PK
        string username
        string email
        string phone
        string password_hash
        enum role
        timestamp created_at
        timestamp updated_at
    }
    
    VEHICLE {
        string vin PK
        uuid user_id FK
        string brand
        string model
        string color
        date manufacture_date
        enum status
        timestamp created_at
    }
    
    BATTERY {
        string battery_id PK
        string vehicle_vin FK
        string manufacturer
        float capacity
        float current_soc
        float health_score
        timestamp last_check
    }
    
    BATTERY_TRACE {
        uuid trace_id PK
        string battery_id FK
        string block_hash
        json trace_data
        timestamp created_at
    }
    
    CHARGING_STATION {
        string station_id PK
        string name
        float latitude
        float longitude
        integer total_ports
        integer available_ports
        enum status
    }
    
    CHARGING_SESSION {
        uuid session_id PK
        string vehicle_vin FK
        string station_id FK
        timestamp start_time
        timestamp end_time
        float energy_consumed
        decimal total_cost
        enum status
    }
    
    CARBON_CREDIT {
        uuid credit_id PK
        uuid user_id FK
        integer points
        string source
        timestamp earned_at
        timestamp expires_at
    }
    
    DRIVING_BEHAVIOR {
        uuid behavior_id PK
        string vehicle_vin FK
        float avg_speed
        float max_acceleration
        integer harsh_braking_count
        float eco_score
        date analysis_date
    }
```

### 6.2 数据定义语言

#### 用户表 (users)

```sql
-- 创建用户表
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    phone VARCHAR(20) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(20) DEFAULT 'user' CHECK (role IN ('user', 'admin', 'operator', 'fleet_manager')),
    avatar_url VARCHAR(500),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_phone ON users(phone);
CREATE INDEX idx_users_role ON users(role);
```

#### 车辆表 (vehicles)

```sql
-- 创建车辆表
CREATE TABLE vehicles (
    vin VARCHAR(17) PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(id),
    brand VARCHAR(50) NOT NULL,
    model VARCHAR(50) NOT NULL,
    color VARCHAR(30),
    manufacture_date DATE NOT NULL,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'maintenance', 'retired')),
    mileage INTEGER DEFAULT 0,
    last_location POINT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_vehicles_user_id ON vehicles(user_id);
CREATE INDEX idx_vehicles_brand_model ON vehicles(brand, model);
CREATE INDEX idx_vehicles_status ON vehicles(status);
```

#### 电池表 (batteries)

```sql
-- 创建电池表
CREATE TABLE batteries (
    battery_id VARCHAR(50) PRIMARY KEY,
    vehicle_vin VARCHAR(17) NOT NULL REFERENCES vehicles(vin),
    manufacturer VARCHAR(50) NOT NULL,
    capacity DECIMAL(8,2) NOT NULL,
    current_soc DECIMAL(5,2) CHECK (current_soc >= 0 AND current_soc <= 100),
    health_score DECIMAL(5,2) CHECK (health_score >= 0 AND health_score <= 100),
    temperature DECIMAL(5,2),
    voltage DECIMAL(8,3),
    current_amperage DECIMAL(8,3),
    cycle_count INTEGER DEFAULT 0,
    last_check TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_batteries_vehicle_vin ON batteries(vehicle_vin);
CREATE INDEX idx_batteries_health_score ON batteries(health_score);
CREATE INDEX idx_batteries_last_check ON batteries(last_check DESC);
```

#### 充电站表 (charging\_stations)

```sql
-- 创建充电站表
CREATE TABLE charging_stations (
    station_id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    operator_id UUID REFERENCES users(id),
    latitude DECIMAL(10,8) NOT NULL,
    longitude DECIMAL(11,8) NOT NULL,
    address TEXT NOT NULL,
    total_ports INTEGER NOT NULL CHECK (total_ports > 0),
    available_ports INTEGER NOT NULL CHECK (available_ports >= 0),
    power_rating DECIMAL(8,2) NOT NULL,
    connector_types TEXT[],
    pricing_model JSONB,
    status VARCHAR(20) DEFAULT 'online' CHECK (status IN ('online', 'offline', 'maintenance')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建地理位置索引
CREATE INDEX idx_charging_stations_location ON charging_stations USING GIST (point(longitude, latitude));
CREATE INDEX idx_charging_stations_status ON charging_stations(status);
CREATE INDEX idx_charging_stations_available_ports ON charging_stations(available_ports);
```

#### 充电会话表 (charging\_sessions)

```sql
-- 创建充电会话表
CREATE TABLE charging_sessions (
    session_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    vehicle_vin VARCHAR(17) NOT NULL REFERENCES vehicles(vin),
    station_id VARCHAR(50) NOT NULL REFERENCES charging_stations(station_id),
    port_number INTEGER NOT NULL,
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE,
    energy_consumed DECIMAL(8,3) DEFAULT 0,
    peak_power DECIMAL(8,2),
    total_cost DECIMAL(10,2) DEFAULT 0,
    payment_method VARCHAR(20),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'completed', 'interrupted', 'failed')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_charging_sessions_vehicle_vin ON charging_sessions(vehicle_vin);
CREATE INDEX idx_charging_sessions_station_id ON charging_sessions(station_id);
CREATE INDEX idx_charging_sessions_start_time ON charging_sessions(start_time DESC);
CREATE INDEX idx_charging_sessions_status ON charging_sessions(status);
```

#### 初始化数据

```sql
-- 插入系统管理员用户
INSERT INTO users (username, email, phone, password_hash, role) VALUES
('admin', '<EMAIL>', '13800000000', '$2b$12$hash_value_here', 'admin'),
('operator1', '<EMAIL>', '13800000001', '$2b$12$hash_value_here', 'operator');

-- 插入示例充电站
INSERT INTO charging_stations (station_id, name, latitude, longitude, address, total_ports, available_ports, power_rating, connector_types) VALUES
('CS001', '北京国贸充电站', 39.9042, 116.4074, '北京市朝阳区国贸中心', 8, 6, 120.0, ARRAY['CCS', 'CHAdeMO']),
('CS002', '上海陆家嘴充电站', 31.2304, 121.4737, '上海市浦东新区陆家嘴金融区', 12, 10, 150.0, ARRAY['CCS', 'GB/T']);

-- 插入示例车辆
INSERT INTO vehicles (vin, user_id, brand, model, color, manufacture_date) VALUES
('LNBSCPK39JG123456', (SELECT id FROM users WHERE username = 'admin'), 'Tesla', 'Model 3', 'White', '2023-01-15'),
('LNBSCPK39JG123457', (SELECT id FROM users WHERE username = 'admin'), 'BYD', 'Han EV', 'Black', '2023-02-20');
```

