import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface Vehicle {
  id: string
  user_id: string
  vin: string
  license_plate?: string
  brand: string
  model: string
  year?: number
  color?: string
  vehicle_type: 'bev' | 'phev' | 'fcev'
  battery_capacity?: number
  max_range?: number
  status: 'active' | 'maintenance' | 'retired'
  location?: any
  created_at: string
  updated_at: string
}

export const useVehicleStore = defineStore('vehicle', () => {
  const vehicles = ref<Vehicle[]>([])
  const currentVehicle = ref<Vehicle | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const activeVehicles = computed(() => 
    vehicles.value.filter(v => v.status === 'active')
  )

  const vehicleCount = computed(() => vehicles.value.length)

  // 获取车辆列表
  const fetchVehicles = async (userId?: string) => {
    loading.value = true
    error.value = null
    
    try {
      const params = new URLSearchParams()
      if (userId) params.append('user_id', userId)
      
      const response = await fetch(`/api/vehicles?${params}`)
      const data = await response.json()
      
      if (data.success) {
        vehicles.value = data.data
      } else {
        error.value = data.error || '获取车辆列表失败'
      }
    } catch (err) {
      error.value = '网络错误'
      console.error('获取车辆列表失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 获取单个车辆
  const fetchVehicle = async (id: string) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await fetch(`/api/vehicles/${id}`)
      const data = await response.json()
      
      if (data.success) {
        currentVehicle.value = data.data
        return data.data
      } else {
        error.value = data.error || '获取车辆信息失败'
        return null
      }
    } catch (err) {
      error.value = '网络错误'
      console.error('获取车辆信息失败:', err)
      return null
    } finally {
      loading.value = false
    }
  }

  // 创建车辆
  const createVehicle = async (vehicleData: Partial<Vehicle>) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await fetch('/api/vehicles', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(vehicleData)
      })
      
      const data = await response.json()
      
      if (data.success) {
        vehicles.value.push(data.data)
        return data.data
      } else {
        error.value = data.error || '创建车辆失败'
        return null
      }
    } catch (err) {
      error.value = '网络错误'
      console.error('创建车辆失败:', err)
      return null
    } finally {
      loading.value = false
    }
  }

  // 更新车辆
  const updateVehicle = async (id: string, vehicleData: Partial<Vehicle>) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await fetch(`/api/vehicles/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(vehicleData)
      })
      
      const data = await response.json()
      
      if (data.success) {
        const index = vehicles.value.findIndex(v => v.id === id)
        if (index !== -1) {
          vehicles.value[index] = data.data
        }
        if (currentVehicle.value?.id === id) {
          currentVehicle.value = data.data
        }
        return data.data
      } else {
        error.value = data.error || '更新车辆失败'
        return null
      }
    } catch (err) {
      error.value = '网络错误'
      console.error('更新车辆失败:', err)
      return null
    } finally {
      loading.value = false
    }
  }

  // 删除车辆
  const deleteVehicle = async (id: string) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await fetch(`/api/vehicles/${id}`, {
        method: 'DELETE'
      })
      
      const data = await response.json()
      
      if (data.success) {
        vehicles.value = vehicles.value.filter(v => v.id !== id)
        if (currentVehicle.value?.id === id) {
          currentVehicle.value = null
        }
        return true
      } else {
        error.value = data.error || '删除车辆失败'
        return false
      }
    } catch (err) {
      error.value = '网络错误'
      console.error('删除车辆失败:', err)
      return false
    } finally {
      loading.value = false
    }
  }

  // 设置当前车辆
  const setCurrentVehicle = (vehicle: Vehicle | null) => {
    currentVehicle.value = vehicle
  }

  // 清除错误
  const clearError = () => {
    error.value = null
  }

  // 重置状态
  const reset = () => {
    vehicles.value = []
    currentVehicle.value = null
    loading.value = false
    error.value = null
  }

  return {
    // 状态
    vehicles,
    currentVehicle,
    loading,
    error,
    
    // 计算属性
    activeVehicles,
    vehicleCount,
    
    // 方法
    fetchVehicles,
    fetchVehicle,
    createVehicle,
    updateVehicle,
    deleteVehicle,
    setCurrentVehicle,
    clearError,
    reset
  }
})