<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="aiGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="faceGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#f8fafc;stop-opacity:0.9" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆 -->
  <circle cx="50" cy="50" r="48" fill="url(#aiGradient)" stroke="#ffffff" stroke-width="2"/>
  
  <!-- AI助手脸部 -->
  <circle cx="50" cy="45" r="25" fill="url(#faceGradient)" stroke="#667eea" stroke-width="1"/>
  
  <!-- 眼睛 -->
  <circle cx="42" cy="40" r="3" fill="#4f46e5"/>
  <circle cx="58" cy="40" r="3" fill="#4f46e5"/>
  
  <!-- 嘴巴 -->
  <path d="M 40 52 Q 50 58 60 52" stroke="#4f46e5" stroke-width="2" fill="none" stroke-linecap="round"/>
  
  <!-- 装饰性电路图案 -->
  <g stroke="#ffffff" stroke-width="1.5" fill="none" opacity="0.6">
    <path d="M 20 25 L 30 25 L 35 20"/>
    <path d="M 65 20 L 70 25 L 80 25"/>
    <path d="M 20 75 L 30 75 L 35 80"/>
    <path d="M 65 80 L 70 75 L 80 75"/>
    <circle cx="35" cy="20" r="2" fill="#ffffff"/>
    <circle cx="65" cy="20" r="2" fill="#ffffff"/>
    <circle cx="35" cy="80" r="2" fill="#ffffff"/>
    <circle cx="65" cy="80" r="2" fill="#ffffff"/>
  </g>
  
  <!-- 中心装饰 -->
  <circle cx="50" cy="75" r="4" fill="#ffffff" opacity="0.8"/>
  <circle cx="50" cy="75" r="2" fill="#4f46e5"/>
</svg>
