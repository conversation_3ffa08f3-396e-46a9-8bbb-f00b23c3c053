<template>
  <div class="vehicle-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <el-icon class="title-icon"><Odometer /></el-icon>
            车辆管理
          </h1>
          <p class="page-description">全面管理您的电动车队，实时监控车辆状态</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="addVehicle">
            <el-icon><Plus /></el-icon>
            添加车辆
          </el-button>
          <el-button @click="refreshData">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon total">
            <el-icon><Odometer /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ vehicleStats.total }}</div>
            <div class="stat-label">总车辆数</div>
            <div class="stat-change positive">+{{ vehicleStats.newThisMonth }} 本月新增</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon active">
            <el-icon><SuccessFilled /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ vehicleStats.active }}</div>
            <div class="stat-label">在线车辆</div>
            <div class="stat-change positive">{{ vehicleStats.activeRate }}% 在线率</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon charging">
            <el-icon><Lightning /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ vehicleStats.charging }}</div>
            <div class="stat-label">充电中</div>
            <div class="stat-change">{{ vehicleStats.chargingRate }}% 充电率</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon maintenance">
            <el-icon><Warning /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ vehicleStats.maintenance }}</div>
            <div class="stat-label">维护中</div>
            <div class="stat-change">{{ vehicleStats.maintenanceRate }}% 维护率</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 筛选和搜索 -->
      <div class="filter-section">
        <div class="filter-left">
          <el-input
            v-model="searchQuery"
            placeholder="搜索车辆（车牌号、品牌、型号）"
            @input="handleSearch"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
        <div class="filter-right">
          <el-select v-model="statusFilter" placeholder="状态筛选" @change="handleFilter">
            <el-option label="全部状态" value="" />
            <el-option label="在线" value="active" />
            <el-option label="维护中" value="maintenance" />
            <el-option label="已退役" value="retired" />
          </el-select>
          <el-select v-model="brandFilter" placeholder="品牌筛选" @change="handleFilter">
            <el-option label="全部品牌" value="" />
            <el-option label="特斯拉" value="Tesla" />
            <el-option label="比亚迪" value="BYD" />
            <el-option label="蔚来" value="NIO" />
            <el-option label="小鹏" value="XPeng" />
            <el-option label="理想" value="Li Auto" />
          </el-select>
        </div>
      </div>

      <!-- 车辆列表 -->
      <div class="vehicle-list-section">
        <VehicleList 
          :vehicles="filteredVehicles"
          :loading="loading"
          @view-detail="viewVehicleDetail"
          @edit-vehicle="editVehicle"
          @delete-vehicle="deleteVehicle"
          @control-vehicle="controlVehicle"
        />
      </div>
    </div>

    <!-- 车辆详情对话框 -->
    <VehicleDetail
      v-model="showDetailDialog"
      :vehicle-id="selectedVehicleId"
      @close="showDetailDialog = false"
    />

    <!-- 车辆表单对话框 -->
    <VehicleForm
      v-model="showFormDialog"
      :vehicle-id="editingVehicleId"
      :mode="formMode"
      @success="handleFormSuccess"
      @close="showFormDialog = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Odometer, Plus, Refresh, Search, SuccessFilled,
  Lightning, Warning
} from '@element-plus/icons-vue'
import { useVehicleStore } from '@/stores/vehicle'
import VehicleList from '@/components/VehicleList.vue'
import VehicleDetail from '@/components/VehicleDetail.vue'
import VehicleForm from '@/components/VehicleForm.vue'

// Store
const vehicleStore = useVehicleStore()

// 响应式数据
const loading = ref(false)
const searchQuery = ref('')
const statusFilter = ref('')
const brandFilter = ref('')
const showDetailDialog = ref(false)
const showFormDialog = ref(false)
const selectedVehicleId = ref('')
const editingVehicleId = ref('')
const formMode = ref<'add' | 'edit'>('add')

// 统计数据
const vehicleStats = ref({
  total: 1234,
  active: 1156,
  charging: 89,
  maintenance: 12,
  newThisMonth: 45,
  activeRate: 94,
  chargingRate: 7,
  maintenanceRate: 1
})

// 计算属性
const filteredVehicles = computed(() => {
  let result = vehicleStore.vehicles
  
  // 搜索筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(vehicle => 
      vehicle.license_plate?.toLowerCase().includes(query) ||
      vehicle.brand.toLowerCase().includes(query) ||
      vehicle.model.toLowerCase().includes(query)
    )
  }
  
  // 状态筛选
  if (statusFilter.value) {
    result = result.filter(vehicle => vehicle.status === statusFilter.value)
  }
  
  // 品牌筛选
  if (brandFilter.value) {
    result = result.filter(vehicle => vehicle.brand === brandFilter.value)
  }
  
  return result
})

// 方法
const loadVehicles = async () => {
  loading.value = true
  try {
    await vehicleStore.fetchVehicles()
    updateStats()
  } catch (error) {
    ElMessage.error('加载车辆数据失败')
  } finally {
    loading.value = false
  }
}

const updateStats = () => {
  const vehicles = vehicleStore.vehicles
  vehicleStats.value = {
    total: vehicles.length,
    active: vehicles.filter(v => v.status === 'active').length,
    charging: Math.floor(vehicles.length * 0.07), // 模拟充电中的车辆
    maintenance: vehicles.filter(v => v.status === 'maintenance').length,
    newThisMonth: Math.floor(vehicles.length * 0.1), // 模拟本月新增
    activeRate: Math.round((vehicles.filter(v => v.status === 'active').length / vehicles.length) * 100),
    chargingRate: 7,
    maintenanceRate: Math.round((vehicles.filter(v => v.status === 'maintenance').length / vehicles.length) * 100)
  }
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const handleFilter = () => {
  // 筛选逻辑已在计算属性中处理
}

const addVehicle = () => {
  formMode.value = 'add'
  editingVehicleId.value = ''
  showFormDialog.value = true
}

const editVehicle = (vehicleId: string) => {
  formMode.value = 'edit'
  editingVehicleId.value = vehicleId
  showFormDialog.value = true
}

const viewVehicleDetail = (vehicleId: string) => {
  selectedVehicleId.value = vehicleId
  showDetailDialog.value = true
}

const deleteVehicle = async (vehicleId: string) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这辆车吗？此操作不可恢复。',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await vehicleStore.deleteVehicle(vehicleId)
    ElMessage.success('车辆删除成功')
    await loadVehicles()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除车辆失败')
    }
  }
}

const controlVehicle = (vehicleId: string) => {
  // 跳转到车辆网络控制页面
  window.open(`/vehicle-network?vehicleId=${vehicleId}`, '_blank')
}

const refreshData = async () => {
  await loadVehicles()
  ElMessage.success('数据刷新成功')
}

const handleFormSuccess = async () => {
  showFormDialog.value = false
  await loadVehicles()
  ElMessage.success(formMode.value === 'add' ? '车辆添加成功' : '车辆更新成功')
}

// 生命周期
onMounted(() => {
  loadVehicles()
})
</script>

<style scoped>
.vehicle-management {
  padding: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  position: relative;
}

.vehicle-management::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

/* 页面头部 */
.page-header {
  padding: 40px 32px 20px;
  position: relative;
  z-index: 1;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  max-width: 1400px;
  margin: 0 auto;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  color: white;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 36px;
}

.page-description {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 统计卡片 */
.stats-section {
  padding: 20px 32px 40px;
  position: relative;
  z-index: 1;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.stat-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  gap: 20px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.active {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.stat-icon.charging {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.stat-icon.maintenance {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: #111827;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
  margin: 4px 0;
}

.stat-change {
  font-size: 12px;
  font-weight: 500;
}

.stat-change.positive {
  color: #10b981;
}

/* 主要内容 */
.main-content {
  padding: 0 32px 40px;
  position: relative;
  z-index: 1;
}

/* 筛选区域 */
.filter-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
  max-width: 1400px;
  margin: 0 auto 24px auto;
}

.filter-left {
  flex: 1;
  max-width: 400px;
}

.filter-right {
  display: flex;
  gap: 12px;
}

/* 车辆列表区域 */
.vehicle-list-section {
  max-width: 1400px;
  margin: 0 auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .vehicle-management {
    padding: 0;
  }
  
  .page-header {
    padding: 24px 16px 16px;
  }
  
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .stats-section {
    padding: 16px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .main-content {
    padding: 0 16px 24px;
  }
  
  .filter-section {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .filter-right {
    justify-content: stretch;
  }
  
  .filter-right .el-select {
    flex: 1;
  }
}
</style>
