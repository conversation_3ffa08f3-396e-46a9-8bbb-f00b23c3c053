<template>
  <div class="oauth-callback-container">
    <div class="callback-content">
      <div class="loading-animation">
        <div class="spinner"></div>
      </div>
      <h2 class="callback-title">{{ statusMessage }}</h2>
      <p class="callback-description">{{ detailMessage }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const statusMessage = ref('正在处理登录...')
const detailMessage = ref('请稍候，我们正在验证您的身份')

// 处理OAuth回调
const handleOAuthCallback = async () => {
  try {
    const urlParams = new URLSearchParams(window.location.search)
    const code = urlParams.get('code')
    const state = urlParams.get('state')
    const error = urlParams.get('error')
    const provider = router.currentRoute.value.params.provider as string
    
    if (error) {
      statusMessage.value = '登录失败'
      detailMessage.value = `登录被取消或发生错误: ${error}`
      ElMessage.error('登录失败')
      
      setTimeout(() => {
        router.push('/login')
      }, 3000)
      return
    }
    
    if (!code || !provider) {
      statusMessage.value = '登录失败'
      detailMessage.value = '缺少必要的登录参数'
      ElMessage.error('登录参数错误')
      
      setTimeout(() => {
        router.push('/login')
      }, 3000)
      return
    }
    
    statusMessage.value = '正在验证身份...'
    detailMessage.value = `正在通过${provider.toUpperCase()}验证您的身份`
    
    // 调用后端OAuth回调接口
    const response = await fetch('/api/auth/oauth/callback', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        provider,
        code,
        state
      })
    })
    
    const result = await response.json()
    
    if (result.success && result.data) {
      statusMessage.value = '登录成功！'
      detailMessage.value = '正在跳转到系统...'
      
      // 保存用户信息到store
      authStore.user = result.data.user
      authStore.token = result.data.token
      
      // 保存到本地存储
      localStorage.setItem('auth_token', result.data.token)
      localStorage.setItem('auth_user', JSON.stringify(result.data.user))
      if (result.data.refreshToken) {
        localStorage.setItem('auth_refresh_token', result.data.refreshToken)
      }
      
      ElMessage.success('登录成功！')
      
      // 获取重定向URL
      const redirect = sessionStorage.getItem('oauth_redirect') || '/'
      sessionStorage.removeItem('oauth_provider')
      sessionStorage.removeItem('oauth_redirect')
      
      // 延迟跳转
      setTimeout(() => {
        router.push(redirect)
      }, 1500)
    } else {
      statusMessage.value = '登录失败'
      detailMessage.value = result.message || '身份验证失败，请重试'
      ElMessage.error(result.message || '登录失败')
      
      setTimeout(() => {
        router.push('/login')
      }, 3000)
    }
  } catch (error) {
    console.error('OAuth callback error:', error)
    statusMessage.value = '登录失败'
    detailMessage.value = '网络错误，请检查网络连接后重试'
    ElMessage.error('网络错误')
    
    setTimeout(() => {
      router.push('/login')
    }, 3000)
  }
}

onMounted(() => {
  handleOAuthCallback()
})
</script>

<style scoped>
.oauth-callback-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, 
    #1a1a2e 0%, 
    #16213e 25%, 
    #0f3460 50%, 
    #533483 75%, 
    #7209b7 100%
  );
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
}

.callback-content {
  text-align: center;
  color: white;
  max-width: 400px;
  padding: 40px;
}

.loading-animation {
  margin-bottom: 30px;
}

.spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(255, 255, 255, 0.1);
  border-left: 4px solid #34d399;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

.callback-title {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 15px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.callback-description {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}
</style>