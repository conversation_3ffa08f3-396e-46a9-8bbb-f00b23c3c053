import type { Directive, DirectiveBinding } from 'vue'
import { modernInteractions, type InteractionConfig } from '@/composables/useModernInteractions'

// 指令绑定值类型
interface ModernInteractionBinding {
  hover?: boolean | InteractionConfig['hover']
  click?: boolean | InteractionConfig['click']
  focus?: boolean | InteractionConfig['focus']
  error?: boolean | InteractionConfig['error']
  success?: boolean | InteractionConfig['success']
  disabled?: boolean
}

// 元素数据存储
interface ElementData {
  animationIds: string[]
  listeners: {
    mouseenter?: (e: Event) => void
    mouseleave?: (e: Event) => void
    click?: (e: MouseEvent) => void
    focus?: (e: Event) => void
    blur?: (e: Event) => void
  }
  config: ModernInteractionBinding
}

// 存储元素数据的WeakMap
const elementDataMap = new WeakMap<HTMLElement, ElementData>()

// 获取或创建元素数据
function getElementData(el: HTMLElement): ElementData {
  if (!elementDataMap.has(el)) {
    elementDataMap.set(el, {
      animationIds: [],
      listeners: {},
      config: {}
    })
  }
  return elementDataMap.get(el)!
}

// 清理元素数据
function cleanupElementData(el: HTMLElement) {
  const data = elementDataMap.get(el)
  if (data) {
    // 清理动画
    data.animationIds.forEach(id => {
      modernInteractions.removeFocus(id)
    })
    
    // 移除事件监听器
    Object.entries(data.listeners).forEach(([event, listener]) => {
      if (listener) {
        el.removeEventListener(event, listener)
      }
    })
    
    elementDataMap.delete(el)
  }
}

// 解析配置
function parseConfig(value: any): ModernInteractionBinding {
  if (typeof value === 'boolean') {
    return { hover: value, click: value }
  }
  
  if (typeof value === 'object' && value !== null) {
    return value
  }
  
  return {}
}

// 设置悬停效果
function setupHoverEffect(el: HTMLElement, config: InteractionConfig['hover'] | boolean) {
  const data = getElementData(el)
  
  const hoverConfig = typeof config === 'boolean' ? {} : config
  
  data.listeners.mouseenter = () => {
    if (data.config.disabled) return
    const animationId = modernInteractions.hover(el, hoverConfig)
    data.animationIds.push(animationId)
  }
  
  data.listeners.mouseleave = () => {
    if (data.config.disabled) return
    modernInteractions.removeHover(el)
    data.animationIds = []
  }
  
  el.addEventListener('mouseenter', data.listeners.mouseenter)
  el.addEventListener('mouseleave', data.listeners.mouseleave)
}

// 设置点击效果
function setupClickEffect(el: HTMLElement, config: InteractionConfig['click'] | boolean) {
  const data = getElementData(el)
  
  const clickConfig = typeof config === 'boolean' ? {} : config
  
  data.listeners.click = (e: MouseEvent) => {
    if (data.config.disabled) return
    modernInteractions.click(el, e, clickConfig)
  }
  
  el.addEventListener('click', data.listeners.click)
}

// 设置焦点效果
function setupFocusEffect(el: HTMLElement, config: InteractionConfig['focus'] | boolean) {
  const data = getElementData(el)
  
  const focusConfig = typeof config === 'boolean' ? {} : config
  
  data.listeners.focus = () => {
    if (data.config.disabled) return
    const animationId = modernInteractions.focus(el, focusConfig)
    data.animationIds.push(animationId)
  }
  
  data.listeners.blur = () => {
    if (data.config.disabled) return
    data.animationIds.forEach(id => {
      modernInteractions.removeFocus(id)
    })
    data.animationIds = []
  }
  
  el.addEventListener('focus', data.listeners.focus)
  el.addEventListener('blur', data.listeners.blur)
}

// 触发错误效果
function triggerErrorEffect(el: HTMLElement, config: InteractionConfig['error'] | boolean) {
  const errorConfig = typeof config === 'boolean' ? {} : config
  modernInteractions.error(el, errorConfig)
}

// 触发成功效果
function triggerSuccessEffect(el: HTMLElement, config: InteractionConfig['success'] | boolean) {
  const successConfig = typeof config === 'boolean' ? {} : config
  modernInteractions.success(el, successConfig)
}

// 现代化交互指令
export const vModernInteraction: Directive<HTMLElement, ModernInteractionBinding> = {
  mounted(el: HTMLElement, binding: DirectiveBinding<ModernInteractionBinding>) {
    const config = parseConfig(binding.value)
    const data = getElementData(el)
    data.config = config
    
    // 设置CSS样式以支持动画
    el.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
    el.style.cursor = el.style.cursor || 'pointer'
    
    // 根据配置设置效果
    if (config.hover) {
      setupHoverEffect(el, config.hover)
    }
    
    if (config.click) {
      setupClickEffect(el, config.click)
    }
    
    if (config.focus) {
      setupFocusEffect(el, config.focus)
    }
    
    // 立即触发的效果
    if (config.error) {
      triggerErrorEffect(el, config.error)
    }
    
    if (config.success) {
      triggerSuccessEffect(el, config.success)
    }
  },
  
  updated(el: HTMLElement, binding: DirectiveBinding<ModernInteractionBinding>) {
    const newConfig = parseConfig(binding.value)
    const data = getElementData(el)
    const oldConfig = data.config
    
    // 检查是否需要重新设置效果
    if (JSON.stringify(newConfig) !== JSON.stringify(oldConfig)) {
      // 清理旧的监听器
      Object.entries(data.listeners).forEach(([event, listener]) => {
        if (listener) {
          el.removeEventListener(event, listener)
        }
      })
      
      data.listeners = {}
      data.config = newConfig
      
      // 重新设置效果
      if (newConfig.hover) {
        setupHoverEffect(el, newConfig.hover)
      }
      
      if (newConfig.click) {
        setupClickEffect(el, newConfig.click)
      }
      
      if (newConfig.focus) {
        setupFocusEffect(el, newConfig.focus)
      }
      
      // 触发新的即时效果
      if (newConfig.error && !oldConfig.error) {
        triggerErrorEffect(el, newConfig.error)
      }
      
      if (newConfig.success && !oldConfig.success) {
        triggerSuccessEffect(el, newConfig.success)
      }
    }
  },
  
  unmounted(el: HTMLElement) {
    cleanupElementData(el)
  }
}

// 预设配置
export const interactionPresets = {
  // 按钮预设
  button: {
    hover: {
      scale: 1.02,
      lift: 2,
      glow: true,
      glowColor: '#667eea',
      duration: 200
    },
    click: {
      ripple: true,
      rippleColor: '#667eea',
      scale: 0.98,
      duration: 150
    }
  },
  
  // 卡片预设
  card: {
    hover: {
      scale: 1.01,
      lift: 4,
      glow: true,
      glowColor: '#667eea',
      duration: 300
    },
    click: {
      ripple: true,
      rippleColor: '#667eea',
      particles: false,
      scale: 0.99,
      duration: 200
    }
  },
  
  // 输入框预设
  input: {
    focus: {
      glow: true,
      glowColor: '#667eea',
      scale: 1.005,
      duration: 200
    },
    error: {
      shake: true,
      shakeIntensity: 3,
      glowColor: '#ff6b6b',
      duration: 300
    }
  },
  
  // 图标预设
  icon: {
    hover: {
      scale: 1.1,
      lift: 1,
      glow: true,
      glowColor: '#667eea',
      duration: 200
    },
    click: {
      ripple: false,
      particles: true,
      particleCount: 6,
      scale: 0.9,
      duration: 300
    }
  },
  
  // 列表项预设
  listItem: {
    hover: {
      scale: 1.005,
      lift: 1,
      glow: false,
      duration: 200
    },
    click: {
      ripple: true,
      rippleColor: '#667eea',
      scale: 0.995,
      duration: 150
    }
  },
  
  // 成功反馈预设
  success: {
    success: {
      bounce: true,
      glow: true,
      glowColor: '#4facfe',
      particles: true,
      duration: 600
    }
  },
  
  // 错误反馈预设
  error: {
    error: {
      shake: true,
      shakeIntensity: 5,
      glowColor: '#ff6b6b',
      duration: 300
    }
  },
  
  // 微妙交互预设
  subtle: {
    hover: {
      scale: 1.005,
      lift: 1,
      glow: false,
      duration: 300
    },
    click: {
      ripple: false,
      scale: 0.995,
      duration: 100
    }
  },
  
  // 强烈交互预设
  intense: {
    hover: {
      scale: 1.05,
      lift: 6,
      glow: true,
      glowColor: '#667eea',
      duration: 200
    },
    click: {
      ripple: true,
      rippleColor: '#667eea',
      particles: true,
      particleCount: 12,
      scale: 0.95,
      duration: 300
    }
  }
}

// 便捷函数
export function createInteractionConfig(preset: keyof typeof interactionPresets, overrides?: Partial<ModernInteractionBinding>): ModernInteractionBinding {
  const baseConfig = interactionPresets[preset]
  return {
    ...baseConfig,
    ...overrides
  }
}

// 导出类型
export type { ModernInteractionBinding, InteractionConfig }

// 默认导出指令
export default vModernInteraction