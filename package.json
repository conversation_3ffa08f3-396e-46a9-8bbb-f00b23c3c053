{"name": "trae-project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"client:dev": "vite", "server:dev": "nodemon", "dev": "concurrently \"npm run client:dev\" \"npm run server:dev\"", "build": "vue-tsc -b && vite build", "preview": "vite preview", "check": "vue-tsc -b", "lint": "eslint . --ext .ts,.vue", "lint:fix": "eslint . --ext .ts,.vue --fix"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@element-plus/icons-vue": "^2.3.2", "@pinia/nuxt": "^0.11.2", "@types/bcrypt": "^6.0.0", "@types/jsonwebtoken": "^9.0.10", "axios": "^1.11.0", "bcrypt": "^6.0.0", "clsx": "^2.1.1", "cors": "^2.8.5", "dotenv": "^17.2.1", "echarts": "^5.6.0", "element-plus": "^2.10.5", "express": "^4.21.2", "jsonwebtoken": "^9.0.2", "leaflet": "^1.9.4", "lucide-vue-next": "^0.511.0", "multer": "^2.0.2", "mysql2": "^3.14.3", "openai": "^5.13.1", "pdf-parse": "^1.1.1", "pinia": "^3.0.3", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.3.0", "vue": "^3.4.15", "vue-echarts": "^7.0.3", "vue-router": "^4.2.5"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^4.17.21", "@types/node": "^22.15.30", "@typescript-eslint/eslint-plugin": "^7.0.1", "@typescript-eslint/parser": "^7.0.1", "@vercel/node": "^5.3.6", "@vitejs/plugin-vue": "^5.0.3", "@vue/runtime-dom": "^3.4.15", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.17", "concurrently": "^9.2.0", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.20.1", "nodemon": "^3.1.10", "postcss": "^8.4.35", "sass-embedded": "^1.90.0", "tailwindcss": "^3.4.1", "tsx": "^4.20.3", "typescript": "~5.3.3", "unplugin-vue-dev-locator": "^1.0.0", "vite": "^5.0.12", "vite-plugin-trae-solo-badge": "^1.0.0", "vue-tsc": "^1.8.27"}}