<template>
  <div class="battery-analysis">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <el-icon class="title-icon"><TrendCharts /></el-icon>
            电池健康分析
          </h1>
          <p class="page-description">深度分析电池健康状态，预测性能趋势</p>
        </div>
        <div class="header-actions">
          <el-button @click="exportReport">
            <el-icon><Download /></el-icon>
            导出报告
          </el-button>
          <el-button @click="refreshData">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 导航按钮区域 -->
    <div class="navigation-tabs">
      <div class="nav-buttons">
        <el-button type="primary" plain disabled>
          <el-icon><TrendCharts /></el-icon>
          健康度分析
        </el-button>
        <el-button type="warning" plain @click="navigateToMaintenance">
          <el-icon><Tools /></el-icon>
          电池维护
        </el-button>
        <el-button type="info" plain @click="navigateToMonitoring">
          <el-icon><Monitor /></el-icon>
          实时监控
        </el-button>
      </div>
    </div>

    <!-- 分析概览 -->
    <div class="analysis-overview">
      <div class="overview-grid">
        <div class="overview-card">
          <div class="card-header">
            <h3>整体健康度</h3>
            <el-icon class="card-icon"><Lightning /></el-icon>
          </div>
          <div class="card-content">
            <div class="metric-value">{{ overallHealth }}%</div>
            <div class="metric-trend" :class="healthTrend.type">
              <el-icon><component :is="healthTrend.icon" /></el-icon>
              {{ healthTrend.text }}
            </div>
          </div>
        </div>

        <div class="overview-card">
          <div class="card-header">
            <h3>平均温度</h3>
            <el-icon class="card-icon"><Thermometer /></el-icon>
          </div>
          <div class="card-content">
            <div class="metric-value">{{ avgTemperature }}°C</div>
            <div class="metric-trend" :class="tempTrend.type">
              <el-icon><component :is="tempTrend.icon" /></el-icon>
              {{ tempTrend.text }}
            </div>
          </div>
        </div>

        <div class="overview-card">
          <div class="card-header">
            <h3>平均循环次数</h3>
            <el-icon class="card-icon"><Refresh /></el-icon>
          </div>
          <div class="card-content">
            <div class="metric-value">{{ avgCycles }}</div>
            <div class="metric-trend" :class="cycleTrend.type">
              <el-icon><component :is="cycleTrend.icon" /></el-icon>
              {{ cycleTrend.text }}
            </div>
          </div>
        </div>

        <div class="overview-card">
          <div class="card-header">
            <h3>预警电池</h3>
            <el-icon class="card-icon"><Warning /></el-icon>
          </div>
          <div class="card-content">
            <div class="metric-value">{{ warningCount }}</div>
            <div class="metric-trend warning">
              <el-icon><ArrowUp /></el-icon>
              需要关注
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表分析 -->
    <div class="charts-section">
      <div class="charts-grid">
        <!-- 健康度趋势图 -->
        <div class="chart-card">
          <div class="chart-header">
            <h3>健康度趋势</h3>
            <el-select v-model="healthPeriod" size="small">
              <el-option label="最近7天" value="7d" />
              <el-option label="最近30天" value="30d" />
              <el-option label="最近90天" value="90d" />
            </el-select>
          </div>
          <div class="chart-content">
            <div class="chart-placeholder">
              <el-icon><TrendCharts /></el-icon>
              <p>健康度趋势图表</p>
            </div>
          </div>
        </div>

        <!-- 温度分布图 -->
        <div class="chart-card">
          <div class="chart-header">
            <h3>温度分布</h3>
            <el-select v-model="tempPeriod" size="small">
              <el-option label="当前" value="current" />
              <el-option label="最近24小时" value="24h" />
              <el-option label="最近7天" value="7d" />
            </el-select>
          </div>
          <div class="chart-content">
            <div class="chart-placeholder">
              <el-icon><Histogram /></el-icon>
              <p>温度分布图表</p>
            </div>
          </div>
        </div>

        <!-- 循环次数分析 -->
        <div class="chart-card">
          <div class="chart-header">
            <h3>循环次数分析</h3>
            <el-select v-model="cyclePeriod" size="small">
              <el-option label="按制造商" value="manufacturer" />
              <el-option label="按型号" value="model" />
              <el-option label="按年份" value="year" />
            </el-select>
          </div>
          <div class="chart-content">
            <div class="chart-placeholder">
              <el-icon><PieChart /></el-icon>
              <p>循环次数分析图表</p>
            </div>
          </div>
        </div>

        <!-- 健康状态分布 -->
        <div class="chart-card">
          <div class="chart-header">
            <h3>健康状态分布</h3>
            <el-button-group size="small">
              <el-button :type="statusView === 'pie' ? 'primary' : ''" @click="statusView = 'pie'">
                饼图
              </el-button>
              <el-button :type="statusView === 'bar' ? 'primary' : ''" @click="statusView = 'bar'">
                柱图
              </el-button>
            </el-button-group>
          </div>
          <div class="chart-content">
            <div class="chart-placeholder">
              <el-icon><DataAnalysis /></el-icon>
              <p>健康状态分布图表</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 详细分析 -->
    <div class="detailed-analysis">
      <div class="analysis-tabs">
        <el-tabs v-model="activeTab" type="card">
          <el-tab-pane label="健康度分析" name="health">
            <div class="tab-content">
              <div class="analysis-item">
                <h4>健康度评估</h4>
                <p>基于SOH（State of Health）指标，当前电池组整体健康度为 {{ overallHealth }}%，处于良好状态。</p>
                <div class="health-breakdown">
                  <div class="breakdown-item">
                    <span class="label">优秀 (90%+):</span>
                    <span class="value">{{ healthBreakdown.excellent }} 块</span>
                    <div class="progress-bar">
                      <div class="progress" :style="{width: healthBreakdown.excellentPercent + '%'}"></div>
                    </div>
                  </div>
                  <div class="breakdown-item">
                    <span class="label">良好 (80-90%):</span>
                    <span class="value">{{ healthBreakdown.good }} 块</span>
                    <div class="progress-bar">
                      <div class="progress" :style="{width: healthBreakdown.goodPercent + '%'}"></div>
                    </div>
                  </div>
                  <div class="breakdown-item">
                    <span class="label">一般 (70-80%):</span>
                    <span class="value">{{ healthBreakdown.fair }} 块</span>
                    <div class="progress-bar">
                      <div class="progress" :style="{width: healthBreakdown.fairPercent + '%'}"></div>
                    </div>
                  </div>
                  <div class="breakdown-item">
                    <span class="label">较差 (<70%):</span>
                    <span class="value">{{ healthBreakdown.poor }} 块</span>
                    <div class="progress-bar">
                      <div class="progress" :style="{width: healthBreakdown.poorPercent + '%'}"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="温度分析" name="temperature">
            <div class="tab-content">
              <div class="analysis-item">
                <h4>温度状况分析</h4>
                <p>电池温度是影响性能和寿命的关键因素。当前平均温度为 {{ avgTemperature }}°C。</p>
                <div class="temp-alerts">
                  <el-alert
                    v-if="highTempBatteries > 0"
                    :title="`发现 ${highTempBatteries} 块电池温度过高 (>35°C)`"
                    type="warning"
                    :closable="false"
                  />
                  <el-alert
                    v-if="lowTempBatteries > 0"
                    :title="`发现 ${lowTempBatteries} 块电池温度过低 (<10°C)`"
                    type="info"
                    :closable="false"
                  />
                </div>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="预测分析" name="prediction">
            <div class="tab-content">
              <div class="analysis-item">
                <h4>性能预测</h4>
                <p>基于历史数据和当前趋势，预测未来电池性能变化。</p>
                <div class="predictions">
                  <div class="prediction-item">
                    <div class="prediction-label">预计6个月后平均健康度</div>
                    <div class="prediction-value">{{ predictedHealth6m }}%</div>
                    <div class="prediction-change" :class="healthChange6m.type">
                      {{ healthChange6m.text }}
                    </div>
                  </div>
                  <div class="prediction-item">
                    <div class="prediction-label">预计1年后平均健康度</div>
                    <div class="prediction-value">{{ predictedHealth1y }}%</div>
                    <div class="prediction-change" :class="healthChange1y.type">
                      {{ healthChange1y.text }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="建议措施" name="recommendations">
            <div class="tab-content">
              <div class="analysis-item">
                <h4>优化建议</h4>
                <div class="recommendations">
                  <div class="recommendation-item">
                    <el-icon class="rec-icon"><Warning /></el-icon>
                    <div class="rec-content">
                      <h5>温度管理</h5>
                      <p>建议加强高温电池的散热管理，避免长时间高温运行。</p>
                    </div>
                  </div>
                  <div class="recommendation-item">
                    <el-icon class="rec-icon"><Tools /></el-icon>
                    <div class="rec-content">
                      <h5>维护计划</h5>
                      <p>对健康度低于80%的电池制定专项维护计划。</p>
                    </div>
                  </div>
                  <div class="recommendation-item">
                    <el-icon class="rec-icon"><Monitor /></el-icon>
                    <div class="rec-content">
                      <h5>监控加强</h5>
                      <p>增加对预警电池的监控频率，及时发现异常。</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import {
  TrendCharts, Download, Refresh, Lightning, Thermometer,
  Warning, ArrowUp, Histogram, PieChart, DataAnalysis,
  Tools, Monitor
} from '@element-plus/icons-vue'
import { useBatteryStore } from '@/stores/battery'

// Router
const router = useRouter()

// Store
const batteryStore = useBatteryStore()

// 响应式数据
const healthPeriod = ref('30d')
const tempPeriod = ref('current')
const cyclePeriod = ref('manufacturer')
const statusView = ref('pie')
const activeTab = ref('health')

// 计算属性
const overallHealth = computed(() => {
  const stats = batteryStore.batteryStats
  return stats.avg_soh || 85
})

const avgTemperature = computed(() => {
  const batteries = batteryStore.batteries
  if (batteries.length === 0) return 25
  const total = batteries.reduce((sum, battery) => sum + battery.temperature, 0)
  return Math.round(total / batteries.length)
})

const avgCycles = computed(() => {
  const batteries = batteryStore.batteries
  if (batteries.length === 0) return 300
  const total = batteries.reduce((sum, battery) => sum + battery.cycle_count, 0)
  return Math.round(total / batteries.length)
})

const warningCount = computed(() => {
  return batteryStore.batteryStats.warning_batteries + batteryStore.batteryStats.critical_batteries
})

const healthTrend = computed(() => ({
  type: 'positive',
  icon: 'ArrowUp',
  text: '较上月提升2%'
}))

const tempTrend = computed(() => ({
  type: 'stable',
  icon: 'Minus',
  text: '温度稳定'
}))

const cycleTrend = computed(() => ({
  type: 'negative',
  icon: 'ArrowUp',
  text: '循环增加'
}))

const healthBreakdown = computed(() => {
  const batteries = batteryStore.batteries
  const total = batteries.length
  
  const excellent = batteries.filter(b => b.soh >= 90).length
  const good = batteries.filter(b => b.soh >= 80 && b.soh < 90).length
  const fair = batteries.filter(b => b.soh >= 70 && b.soh < 80).length
  const poor = batteries.filter(b => b.soh < 70).length
  
  return {
    excellent,
    good,
    fair,
    poor,
    excellentPercent: total > 0 ? (excellent / total) * 100 : 0,
    goodPercent: total > 0 ? (good / total) * 100 : 0,
    fairPercent: total > 0 ? (fair / total) * 100 : 0,
    poorPercent: total > 0 ? (poor / total) * 100 : 0
  }
})

const highTempBatteries = computed(() => {
  return batteryStore.batteries.filter(b => b.temperature > 35).length
})

const lowTempBatteries = computed(() => {
  return batteryStore.batteries.filter(b => b.temperature < 10).length
})

const predictedHealth6m = computed(() => {
  return Math.max(overallHealth.value - 3, 70)
})

const predictedHealth1y = computed(() => {
  return Math.max(overallHealth.value - 6, 65)
})

const healthChange6m = computed(() => ({
  type: 'negative',
  text: '预计下降3%'
}))

const healthChange1y = computed(() => ({
  type: 'negative',
  text: '预计下降6%'
}))

// 方法
const navigateToMaintenance = () => {
  console.log('点击电池维护按钮')
  router.push('/battery-management/maintenance')
}

const navigateToMonitoring = () => {
  console.log('点击实时监控按钮')
  router.push('/battery-management/monitoring')
}

const refreshData = async () => {
  try {
    await batteryStore.fetchBatteries()
    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败')
  }
}

const exportReport = () => {
  ElMessage.info('报告导出功能开发中')
}

// 生命周期
onMounted(() => {
  batteryStore.fetchBatteries()
})
</script>

<style scoped>
.battery-analysis {
  padding: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  position: relative;
}

.battery-analysis::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 50%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
  pointer-events: none;
}

/* 页面头部 */
.page-header {
  position: relative;
  z-index: 1;
  padding: 32px 24px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  color: white;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 36px;
  color: #fbbf24;
}

.page-description {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 分析概览 */
.analysis-overview {
  position: relative;
  z-index: 1;
  padding: 0 24px 32px;
  max-width: 1200px;
  margin: 0 auto;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.overview-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.overview-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.card-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.card-icon {
  font-size: 20px;
  color: #6b7280;
}

.card-content {
  text-align: center;
}

.metric-value {
  font-size: 32px;
  font-weight: 700;
  color: #111827;
  margin-bottom: 8px;
}

.metric-trend {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  font-size: 14px;
  font-weight: 500;
}

.metric-trend.positive {
  color: #10b981;
}

.metric-trend.negative {
  color: #ef4444;
}

.metric-trend.stable {
  color: #6b7280;
}

.metric-trend.warning {
  color: #f59e0b;
}

/* 图表区域 */
.charts-section {
  position: relative;
  z-index: 1;
  padding: 0 24px 32px;
  max-width: 1200px;
  margin: 0 auto;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.chart-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  overflow: hidden;
}

.chart-header {
  padding: 20px 24px;
  border-bottom: 1px solid #f3f4f6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.chart-content {
  padding: 24px;
  height: 300px;
}

.chart-placeholder {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  font-size: 48px;
}

.chart-placeholder p {
  margin-top: 16px;
  font-size: 14px;
  color: #6b7280;
}

/* 详细分析 */
.detailed-analysis {
  position: relative;
  z-index: 1;
  padding: 0 24px 32px;
  max-width: 1200px;
  margin: 0 auto;
}

.analysis-tabs {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  overflow: hidden;
}

.tab-content {
  padding: 24px;
}

.analysis-item {
  margin-bottom: 24px;
}

.analysis-item h4 {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 12px 0;
}

.analysis-item p {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.6;
  margin: 0 0 16px 0;
}

/* 健康度分解 */
.health-breakdown {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.breakdown-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.breakdown-item .label {
  min-width: 120px;
  font-size: 14px;
  color: #374151;
  font-weight: 500;
}

.breakdown-item .value {
  min-width: 60px;
  font-size: 14px;
  color: #111827;
  font-weight: 600;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background: #f3f4f6;
  border-radius: 4px;
  overflow: hidden;
}

.progress {
  height: 100%;
  background: linear-gradient(90deg, #10b981 0%, #059669 100%);
  transition: width 0.3s ease;
}

/* 温度警告 */
.temp-alerts {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 预测分析 */
.predictions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.prediction-item {
  background: #f9fafb;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
}

.prediction-label {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 8px;
}

.prediction-value {
  font-size: 24px;
  font-weight: 700;
  color: #111827;
  margin-bottom: 4px;
}

.prediction-change {
  font-size: 12px;
  font-weight: 500;
}

.prediction-change.positive {
  color: #10b981;
}

.prediction-change.negative {
  color: #ef4444;
}

/* 建议措施 */
.recommendations {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.recommendation-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: #f0f9ff;
  border-radius: 12px;
  border-left: 4px solid #3b82f6;
}

.rec-icon {
  font-size: 20px;
  color: #3b82f6;
  margin-top: 2px;
}

.rec-content h5 {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 4px 0;
}

.rec-content p {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .header-actions {
    flex-direction: column;
    gap: 8px;
    width: 100%;
  }

  .overview-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }

  .charts-grid {
    grid-template-columns: 1fr;
  }

  .predictions {
    grid-template-columns: 1fr;
  }

  .breakdown-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .breakdown-item .label,
  .breakdown-item .value {
    min-width: auto;
  }
}

/* 导航按钮样式 */
.navigation-tabs {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.nav-buttons {
  display: flex;
  gap: 16px;
  align-items: center;
}

.nav-buttons .el-button {
  height: 40px;
  padding: 0 20px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.nav-buttons .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.nav-buttons .el-button .el-icon {
  margin-right: 8px;
}

/* 当前页面按钮样式 */
.nav-buttons .el-button[disabled] {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  opacity: 1;
  cursor: default;
}
</style>
