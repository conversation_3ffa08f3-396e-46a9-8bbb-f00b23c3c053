<template>
  <div class="bill-management">
    <!-- 账单统计卡片 -->
    <div class="statistics-cards">
      <el-row :gutter="20">
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <ModernCard variant="glassmorphism" :hover-effect="true">
            <div class="stat-card">
              <div class="stat-icon total">
                <el-icon :size="24"><Wallet /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ formatAmount(statistics.totalAmount) }}</div>
                <div class="stat-label">总收入</div>
              </div>
            </div>
          </ModernCard>
        </el-col>
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <ModernCard variant="glassmorphism" :hover-effect="true">
            <div class="stat-card">
              <div class="stat-icon monthly">
                <el-icon :size="24"><Calendar /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ formatAmount(statistics.monthlyAmount) }}</div>
                <div class="stat-label">本月收入</div>
              </div>
            </div>
          </ModernCard>
        </el-col>
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <ModernCard variant="glassmorphism" :hover-effect="true">
            <div class="stat-card">
              <div class="stat-icon orders">
                <el-icon :size="24"><Document /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ statistics.totalOrders }}</div>
                <div class="stat-label">总订单数</div>
              </div>
            </div>
          </ModernCard>
        </el-col>
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <ModernCard variant="glassmorphism" :hover-effect="true">
            <div class="stat-card">
              <div class="stat-icon average">
                <el-icon :size="24"><TrendCharts /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ formatAmount(statistics.averageAmount) }}</div>
                <div class="stat-label">平均订单金额</div>
              </div>
            </div>
          </ModernCard>
        </el-col>
      </el-row>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <ModernCard variant="glassmorphism" :hover-effect="true">
        <el-row :gutter="20" align="middle">
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-select
              v-model="filterStatus"
              placeholder="选择状态"
              clearable
              style="width: 100%"
              @change="handleFilterChange"
            >
              <el-option label="全部状态" value="" />
              <el-option label="待支付" value="pending" />
              <el-option label="支付成功" value="success" />
              <el-option label="支付失败" value="failed" />
              <el-option label="已取消" value="cancelled" />
              <el-option label="已退款" value="refunded" />
            </el-select>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-select
              v-model="filterType"
              placeholder="选择类型"
              clearable
              style="width: 100%"
              @change="handleFilterChange"
            >
              <el-option label="全部类型" value="" />
              <el-option label="充电费用" value="charging" />
              <el-option label="服务费用" value="service" />
              <el-option label="维护费用" value="maintenance" />
              <el-option label="订阅费用" value="subscription" />
            </el-select>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
              @change="handleFilterChange"
            />
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <div style="position: relative;">
              <ModernLoader v-if="exportLoading" />
              <ModernButton variant="primary" @click="exportBills" :disabled="exportLoading">
                <el-icon><Download /></el-icon>
                导出账单
              </ModernButton>
            </div>
          </el-col>
        </el-row>
      </ModernCard>
    </div>

    <!-- 账单列表 -->
    <div class="bill-list">
      <ModernCard variant="glassmorphism" :hover-effect="true">
        <template #header>
          <div class="card-header">
            <span>账单列表</span>
            <div class="header-actions">
              <div style="position: relative;">
                <ModernLoader v-if="exportLoading" />
                <ModernButton
                  variant="secondary"
                  size="small"
                  :disabled="exportLoading"
                  @click="exportBills"
                >
                  <el-icon><Download /></el-icon>
                  导出账单
                </ModernButton>
              </div>
              <ModernButton
                variant="primary"
                size="small"
                @click="refreshBills"
              >
                <el-icon><Refresh /></el-icon>
                刷新
              </ModernButton>
            </div>
          </div>
        </template>

        <div style="position: relative;">
          <ModernLoader v-if="loading" />
          <el-table
            :data="filteredOrders"
            stripe
            style="width: 100%"
            :default-sort="{ prop: 'createTime', order: 'descending' }"
          >
          <el-table-column prop="orderNo" label="订单号" width="160" show-overflow-tooltip />
          
          <el-table-column label="类型" width="100">
            <template #default="{ row }" v-if="row">
              <el-tag :type="getOrderTypeTagType(row.type)" size="small">
                {{ getOrderTypeName(row.type) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="title" label="订单标题" min-width="150" show-overflow-tooltip />
          
          <el-table-column prop="description" label="描述" min-width="180" show-overflow-tooltip />
          
          <el-table-column label="金额" width="120" align="right">
            <template #default="{ row }" v-if="row">
              <div class="amount-cell">
                <span class="final-amount">{{ formatAmount(row.amount) }}</span>
                <span v-if="row.originalAmount && row.originalAmount > row.amount" class="original-amount">
                  {{ formatAmount(row.originalAmount) }}
                </span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="支付方式" width="100">
            <template #default="{ row }" v-if="row">
              <span v-if="row.paymentMethod">{{ getPaymentMethodName(row.paymentMethod) }}</span>
              <span v-else class="text-placeholder">-</span>
            </template>
            <template #default v-else>
              <span class="text-placeholder">-</span>
            </template>
          </el-table-column>

          <el-table-column label="状态" width="100">
            <template #default="{ row }" v-if="row">
              <el-tag :type="getStatusTagType(row.status)" size="small">
                {{ getPaymentStatusName(row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="创建时间" width="160">
            <template #default="{ row }" v-if="row">{{ formatDateTime(row.createTime) }}</template>
          </el-table-column>

          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }" v-if="row">
              <ModernButton variant="ghost" size="small" @click="viewOrderDetail(row)">
                详情
              </ModernButton>
              <ModernButton 
                v-if="row.status === 'success'" 
                variant="ghost" 
                size="small" 
                @click="requestRefund(row)"
              >
                退款
              </ModernButton>
            </template>
          </el-table-column>
        </el-table>
        </div>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="totalOrders"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </ModernCard>
    </div>

    <!-- 订单详情对话框 -->
    <el-dialog
      v-model="detailVisible"
      title="订单详情"
      width="600px"
    >
      <div v-if="selectedOrder" class="order-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="订单号">{{ selectedOrder.orderNo }}</el-descriptions-item>
          <el-descriptions-item label="订单类型">
            <el-tag :type="getOrderTypeTagType(selectedOrder.type)" size="small">
              {{ getOrderTypeName(selectedOrder.type) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="订单标题" :span="2">{{ selectedOrder.title }}</el-descriptions-item>
          <el-descriptions-item label="订单描述" :span="2">{{ selectedOrder.description }}</el-descriptions-item>
          <el-descriptions-item label="原价" v-if="selectedOrder.originalAmount">
            {{ formatAmount(selectedOrder.originalAmount) }}
          </el-descriptions-item>
          <el-descriptions-item label="优惠金额" v-if="selectedOrder.discount">
            {{ formatAmount(selectedOrder.discount) }}
          </el-descriptions-item>
          <el-descriptions-item label="实付金额">{{ formatAmount(selectedOrder.amount) }}</el-descriptions-item>
          <el-descriptions-item label="支付方式">
            {{ selectedOrder.paymentMethod ? getPaymentMethodName(selectedOrder.paymentMethod) : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="订单状态">
            <el-tag :type="getStatusTagType(selectedOrder.status)" size="small">
              {{ getPaymentStatusName(selectedOrder.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDateTime(selectedOrder.createTime) }}</el-descriptions-item>
          <el-descriptions-item label="支付时间" v-if="selectedOrder.payTime">
            {{ formatDateTime(selectedOrder.payTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="退款时间" v-if="selectedOrder.refundTime">
            {{ formatDateTime(selectedOrder.refundTime) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <ModernButton @click="detailVisible = false">关闭</ModernButton>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Wallet,
  Calendar,
  Document,
  TrendCharts,
  Download,
  Refresh
} from '@element-plus/icons-vue'
import {
  paymentService,
  PaymentStatus,
  OrderType,
  PaymentMethod,
  formatAmount,
  formatDateTime
} from '@/services/paymentService'
import type { PaymentOrder, BillStatistics } from '@/services/paymentService'
import ModernButton from '@/components/ModernButton.vue'
import ModernCard from '@/components/ModernCard.vue'
import ModernLoader from '@/components/ui/ModernLoader.vue'

// 响应式数据
const loading = ref(false)
const exportLoading = ref(false)
const orders = ref<PaymentOrder[]>([])
const statistics = ref<BillStatistics>({
  totalAmount: 0,
  totalOrders: 0,
  successOrders: 0,
  failedOrders: 0,
  refundAmount: 0,
  monthlyAmount: 0,
  yearlyAmount: 0,
  averageAmount: 0
})

// 筛选条件
const filterStatus = ref('')
const filterType = ref('')
const dateRange = ref<[string, string] | null>(null)

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const totalOrders = computed(() => filteredOrders.value.length)

// 订单详情
const detailVisible = ref(false)
const selectedOrder = ref<PaymentOrder | null>(null)

// 计算属性 - 过滤后的订单
const filteredOrders = computed(() => {
  let filtered = [...orders.value]

  // 状态筛选
  if (filterStatus.value) {
    filtered = filtered.filter(order => order.status === filterStatus.value)
  }

  // 类型筛选
  if (filterType.value) {
    filtered = filtered.filter(order => order.type === filterType.value)
  }

  // 日期范围筛选
  if (dateRange.value && dateRange.value.length === 2) {
    const [startDate, endDate] = dateRange.value
    filtered = filtered.filter(order => {
      const orderDate = order.createTime.toISOString().split('T')[0]
      return orderDate >= startDate && orderDate <= endDate
    })
  }

  // 分页
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filtered.slice(start, end)
})

// 生命周期
onMounted(() => {
  loadBills()
  loadStatistics()
})

// 加载账单数据
const loadBills = async () => {
  try {
    loading.value = true
    const result = await paymentService.getOrders('user-001')
    orders.value = result
  } catch (error) {
    console.error('加载账单失败:', error)
    ElMessage.error('加载账单失败')
  } finally {
    loading.value = false
  }
}

// 加载统计数据
const loadStatistics = async () => {
  try {
    const result = await paymentService.getBillStatistics('user-001')
    statistics.value = result
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 刷新账单
const refreshBills = () => {
  loadBills()
  loadStatistics()
}

// 处理筛选变化
const handleFilterChange = () => {
  currentPage.value = 1
}

// 处理分页大小变化
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
}

// 处理当前页变化
const handleCurrentChange = (page: number) => {
  currentPage.value = page
}

// 查看订单详情
const viewOrderDetail = (order: PaymentOrder) => {
  selectedOrder.value = order
  detailVisible.value = true
}

// 申请退款
const requestRefund = async (order: PaymentOrder) => {
  try {
    const { value: reason } = await ElMessageBox.prompt(
      '请输入退款原因',
      '申请退款',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPlaceholder: '请输入退款原因',
        inputValidator: (value) => {
          if (!value || value.trim().length === 0) {
            return '请输入退款原因'
          }
          return true
        }
      }
    )

    const success = await paymentService.requestRefund(order.id, reason)
    if (success) {
      ElMessage.success('退款申请提交成功')
      refreshBills()
    } else {
      ElMessage.error('退款申请失败')
    }
  } catch (error) {
    // 用户取消操作
  }
}

// 导出账单
const exportBills = async () => {
  try {
    exportLoading.value = true
    
    // 模拟导出过程
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('账单导出成功')
  } catch (error) {
    ElMessage.error('账单导出失败')
  } finally {
    exportLoading.value = false
  }
}

// 获取状态标签类型
const getStatusTagType = (status: PaymentStatus): string => {
  const typeMap = {
    [PaymentStatus.PENDING]: 'warning',
    [PaymentStatus.SUCCESS]: 'success',
    [PaymentStatus.FAILED]: 'danger',
    [PaymentStatus.CANCELLED]: 'info',
    [PaymentStatus.REFUNDED]: 'warning'
  }
  return typeMap[status] || 'info'
}

// 获取订单类型标签类型
const getOrderTypeTagType = (type: OrderType): string => {
  const typeMap = {
    [OrderType.CHARGING]: 'primary',
    [OrderType.SERVICE]: 'success',
    [OrderType.MAINTENANCE]: 'warning',
    [OrderType.SUBSCRIPTION]: 'info'
  }
  return typeMap[type] || 'info'
}

// 获取支付状态名称
const getPaymentStatusName = (status: PaymentStatus): string => {
  return paymentService.getPaymentStatusName(status)
}

// 获取订单类型名称
const getOrderTypeName = (type: OrderType): string => {
  return paymentService.getOrderTypeName(type)
}

// 获取支付方式名称
const getPaymentMethodName = (method: PaymentMethod): string => {
  return paymentService.getPaymentMethodName(method)
}
</script>

<style scoped>
.bill-management {
  padding: 20px;

  .statistics-cards {
    margin-bottom: 20px;

    .stat-card {
      display: flex;
      align-items: center;
      padding: 20px;
      background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease, box-shadow 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      }

      .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;

        &.total {
          background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
          color: white;
        }

        &.monthly {
          background: linear-gradient(135deg, #10b981 0%, #059669 100%);
          color: white;
        }

        &.orders {
          background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
          color: white;
        }

        &.average {
          background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
          color: white;
        }
      }

      .stat-content {
        flex: 1;

        .stat-value {
          font-size: 24px;
          font-weight: 700;
          color: #1f2937;
          margin-bottom: 4px;
        }

        .stat-label {
          font-size: 14px;
          color: #6b7280;
        }
      }
    }
  }

  .filter-section {
    margin-bottom: 20px;

    :deep(.el-card__body) {
      padding: 20px;
    }
  }

  .bill-list {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 600;
      color: #1f2937;
    }

    .amount-cell {
      .final-amount {
        font-weight: 600;
        color: #1f2937;
      }

      .original-amount {
        display: block;
        font-size: 12px;
        color: #9ca3af;
        text-decoration: line-through;
        margin-top: 2px;
      }
    }

    .text-placeholder {
      color: #9ca3af;
    }

    .pagination-wrapper {
      display: flex;
      justify-content: center;
      margin-top: 20px;
    }
  }

  .order-detail {
    :deep(.el-descriptions) {
      .el-descriptions__label {
        font-weight: 600;
        color: #374151;
      }
    }
  }
}

@media (max-width: 768px) {
  .bill-management {
    padding: 10px;

    .statistics-cards {
      .stat-card {
        padding: 16px;

        .stat-icon {
          width: 40px;
          height: 40px;
          margin-right: 12px;
        }

        .stat-content {
          .stat-value {
            font-size: 20px;
          }
        }
      }
    }
  }
}
</style>