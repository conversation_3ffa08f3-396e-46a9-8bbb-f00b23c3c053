<template>
  <div 
    ref="inputRef"
    :class="[
      'modern-input', 
      `modern-input--${variant}`, 
      { 
        'modern-input--focused': isFocused, 
        'modern-input--error': error 
      },
      inputClasses
    ]"
    :style="inputStyles"
  >
    <!-- 浮动标签 -->
    <label v-if="label" :class="['modern-input__label', { 'modern-input__label--float': isFocused || modelValue }]">
      {{ label }}
      <span v-if="required" class="modern-input__required">*</span>
    </label>
    
    <!-- 输入框容器 -->
    <div class="modern-input__container">
      <!-- 前缀图标 -->
      <div v-if="$slots.prefix" class="modern-input__prefix">
        <slot name="prefix"></slot>
      </div>
      
      <!-- 输入框 -->
      <input
        v-if="type !== 'textarea'"
        :type="type"
        :value="modelValue"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :maxlength="maxlength"
        class="modern-input__field"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
        @keydown="handleKeydown"
      />
      
      <!-- 文本域 -->
      <textarea
        v-else
        :value="modelValue"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :maxlength="maxlength"
        :rows="rows"
        class="modern-input__field modern-input__textarea"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
      ></textarea>
      
      <!-- 后缀图标 -->
      <div v-if="$slots.suffix || showClear" class="modern-input__suffix">
        <button
          v-if="showClear && modelValue"
          class="modern-input__clear"
          @click="handleClear"
          type="button"
        >
          ✕
        </button>
        <slot name="suffix"></slot>
      </div>
    </div>
    
    <!-- 错误信息 -->
    <div v-if="error" class="modern-input__error">{{ error }}</div>
    
    <!-- 帮助文本 -->
    <div v-if="helpText && !error" class="modern-input__help">{{ helpText }}</div>
    
    <!-- 字符计数 -->
    <div v-if="maxlength && showCount" class="modern-input__count">
      {{ (modelValue || '').length }} / {{ maxlength }}
    </div>
    
    <!-- 发光效果 -->
    <div class="modern-input__glow"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useBreakpoint, useResponsiveValue, useDeviceFeatures } from '@/composables/useResponsive'
import type { ResponsiveValue } from '@/utils/responsive'

interface Props {
  modelValue?: string | number
  type?: 'text' | 'password' | 'email' | 'number' | 'tel' | 'url' | 'search' | 'textarea'
  variant?: 'glass' | 'neon' | 'minimal' | 'gradient'
  label?: string
  placeholder?: string
  error?: string
  helpText?: string
  disabled?: boolean
  readonly?: boolean
  required?: boolean
  maxlength?: number
  rows?: number
  showClear?: boolean
  showCount?: boolean
  size?: ResponsiveValue<'small' | 'medium' | 'large'>
  borderRadius?: ResponsiveValue<string>
  fullWidth?: boolean
  customStyles?: Record<string, any>
}

interface Emits {
  'update:modelValue': [value: string | number]
  focus: [event: FocusEvent]
  blur: [event: FocusEvent]
  keydown: [event: KeyboardEvent]
  clear: []
}

const props = withDefaults(defineProps<Props>(), {
  type: 'text',
  variant: 'glass',
  disabled: false,
  readonly: false,
  required: false,
  rows: 3,
  showClear: false,
  showCount: false,
  size: 'medium',
  borderRadius: '12px',
  fullWidth: false
})

const emit = defineEmits<Emits>()

const inputRef = ref<HTMLElement>()
const isFocused = ref(false)

// 响应式功能
const { currentBreakpoint, isMobileDevice, isTabletDevice } = useBreakpoint()
const { reducedMotion, isTouch, highContrast } = useDeviceFeatures()

// 响应式值计算
const responsiveSize = useResponsiveValue(props.size)
const responsiveBorderRadius = useResponsiveValue(props.borderRadius)

// 计算样式
const inputStyles = computed(() => ({
  borderRadius: responsiveBorderRadius,
  width: props.fullWidth ? '100%' : 'auto',
  ...props.customStyles
}))

// 计算类名
const inputClasses = computed(() => ({
  [`modern-input--${responsiveSize}`]: true,
  'modern-input--mobile': isMobileDevice,
  'modern-input--tablet': isTabletDevice,
  'modern-input--touch': isTouch,
  'modern-input--reduced-motion': reducedMotion,
  'modern-input--high-contrast': highContrast,
  'modern-input--full-width': props.fullWidth
}))

const handleInput = (event: Event) => {
  const target = event.target as HTMLInputElement | HTMLTextAreaElement
  emit('update:modelValue', target.value)
}

const handleFocus = (event: FocusEvent) => {
  isFocused.value = true
  emit('focus', event)
}

const handleBlur = (event: FocusEvent) => {
  isFocused.value = false
  emit('blur', event)
}

const handleKeydown = (event: KeyboardEvent) => {
  emit('keydown', event)
}

const handleClear = () => {
  emit('update:modelValue', '')
  emit('clear')
}
</script>

<style scoped>
.modern-input {
  position: relative;
  margin-bottom: 24px;
  
  /* CSS变量 */
  --input-padding: 16px;
  --input-font-size: 16px;
  --input-border-radius: 12px;
  --label-font-size: 16px;
  --label-float-size: 12px;
}

.modern-input--focused .modern-input__glow {
  opacity: 1;
}

.modern-input--error .modern-input__container {
  border-color: #ff6b6b !important;
  box-shadow: 0 0 20px rgba(255, 107, 107, 0.3) !important;
}

/* 响应式尺寸修饰符 */
.modern-input--small {
  --input-padding: 12px;
  --input-font-size: 14px;
  --input-border-radius: 8px;
  --label-font-size: 14px;
  --label-float-size: 11px;
}

.modern-input--medium {
  --input-padding: 16px;
  --input-font-size: 16px;
  --input-border-radius: 12px;
  --label-font-size: 16px;
  --label-float-size: 12px;
}

.modern-input--large {
  --input-padding: 20px;
  --input-font-size: 18px;
  --input-border-radius: 16px;
  --label-font-size: 18px;
  --label-float-size: 14px;
}

.modern-input--mobile {
  --input-padding: 14px;
  --input-font-size: 16px; /* 防止iOS缩放 */
  --input-border-radius: 10px;
  --label-font-size: 14px;
  --label-float-size: 11px;
}

.modern-input--tablet {
  --input-padding: 15px;
  --input-font-size: 16px;
  --input-border-radius: 11px;
  --label-font-size: 15px;
  --label-float-size: 11px;
}

.modern-input--touch {
  min-height: 44px;
}

.modern-input--full-width {
  width: 100%;
}

.modern-input--reduced-motion .modern-input__label,
.modern-input--reduced-motion .modern-input__container,
.modern-input--reduced-motion .modern-input__glow {
  transition: none;
}

.modern-input--high-contrast .modern-input__container {
  border-width: 2px;
}

.modern-input--high-contrast .modern-input__field,
.modern-input--high-contrast .modern-input__label {
  color: var(--text-primary);
}

/* 标签样式 */
.modern-input__label {
  position: absolute;
  top: var(--input-padding);
  left: var(--input-padding);
  color: rgba(255, 255, 255, 0.7);
  font-size: var(--label-font-size);
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
  z-index: 2;
}

.modern-input__label--float {
  top: -8px;
  left: 12px;
  font-size: var(--label-float-size);
  color: #667eea;
  background: rgba(0, 0, 0, 0.8);
  padding: 0 8px;
  border-radius: 4px;
}

.modern-input__required {
  color: #ff6b6b;
  margin-left: 2px;
}

/* 输入框容器 */
.modern-input__container {
  position: relative;
  display: flex;
  align-items: center;
  border-radius: var(--input-border-radius);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(20px);
  overflow: hidden;
}

/* 玻璃态变体 */
.modern-input--glass .modern-input__container {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

.modern-input--glass.modern-input--focused .modern-input__container {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(102, 126, 234, 0.5);
  box-shadow: 0 0 30px rgba(102, 126, 234, 0.3);
}

/* 霓虹变体 */
.modern-input--neon .modern-input__container {
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid #00f5ff;
  box-shadow: 0 0 20px rgba(0, 245, 255, 0.3);
}

.modern-input--neon.modern-input--focused .modern-input__container {
  box-shadow: 
    0 0 30px rgba(0, 245, 255, 0.5),
    inset 0 0 20px rgba(0, 245, 255, 0.1);
}

.modern-input--neon .modern-input__label--float {
  color: #00f5ff;
  background: rgba(0, 0, 0, 0.9);
}

/* 极简变体 */
.modern-input--minimal .modern-input__container {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
}

.modern-input--minimal.modern-input--focused .modern-input__container {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.15);
}

/* 渐变变体 */
.modern-input--gradient .modern-input__container {
  background: linear-gradient(135deg, 
    rgba(102, 126, 234, 0.1) 0%, 
    rgba(118, 75, 162, 0.1) 100%);
  border: 1px solid rgba(102, 126, 234, 0.3);
  box-shadow: 0 8px 32px 0 rgba(102, 126, 234, 0.2);
}

.modern-input--gradient.modern-input--focused .modern-input__container {
  background: linear-gradient(135deg, 
    rgba(102, 126, 234, 0.2) 0%, 
    rgba(118, 75, 162, 0.2) 100%);
  border-color: rgba(102, 126, 234, 0.5);
  box-shadow: 0 0 30px rgba(102, 126, 234, 0.4);
}

/* 输入框字段 */
.modern-input__field {
  flex: 1;
  background: transparent;
  border: none;
  outline: none;
  padding: var(--input-padding);
  color: var(--text-primary);
  font-size: var(--input-font-size);
  font-weight: 500;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  z-index: 2;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-input__field::placeholder {
  color: rgba(255, 255, 255, 0.5);
  transition: color 0.3s ease;
}

.modern-input__field:focus::placeholder {
  color: rgba(255, 255, 255, 0.3);
}

.modern-input__field:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.modern-input__textarea {
  resize: vertical;
  min-height: 80px;
  line-height: 1.5;
}

/* 前缀和后缀 */
.modern-input__prefix,
.modern-input__suffix {
  display: flex;
  align-items: center;
  color: rgba(255, 255, 255, 0.7);
  padding: 0 var(--input-padding);
  z-index: 2;
}

.modern-input__clear {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  transition: all 0.2s ease;
  font-size: 14px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modern-input__clear:hover {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
}

/* 错误信息 */
.modern-input__error {
  margin-top: 8px;
  color: #ff6b6b;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
}

.modern-input__error::before {
  content: '⚠';
  font-size: 12px;
}

/* 帮助文本 */
.modern-input__help {
  margin-top: 8px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
}

/* 字符计数 */
.modern-input__count {
  position: absolute;
  bottom: -20px;
  right: 0;
  color: rgba(255, 255, 255, 0.5);
  font-size: 12px;
}

/* 发光效果 */
.modern-input__glow {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, 
    transparent 30%, 
    rgba(102, 126, 234, 0.2) 50%, 
    transparent 70%);
  border-radius: 14px;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: -1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modern-input:not(.modern-input--mobile):not(.modern-input--tablet) {
    --input-padding: 14px;
    --input-font-size: 16px; /* 防止iOS缩放 */
    --input-border-radius: 10px;
    --label-font-size: 14px;
    --label-float-size: 11px;
    margin-bottom: 20px;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .modern-input:not(.modern-input--mobile):not(.modern-input--tablet) {
    --input-padding: 15px;
    --input-font-size: 16px;
    --input-border-radius: 11px;
    --label-font-size: 15px;
    --label-float-size: 11px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .modern-input__field {
    color: var(--text-primary);
  }
  
  .modern-input__field::placeholder {
    color: var(--text-muted);
  }
  
  .modern-input__label {
    color: rgba(255, 255, 255, 0.6);
  }
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
  .modern-input__label,
  .modern-input__container,
  .modern-input__glow {
    transition: none;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .modern-input:not(.modern-input--high-contrast) .modern-input__container {
    border-width: 2px;
  }
  
  .modern-input:not(.modern-input--high-contrast) .modern-input__field,
  .modern-input:not(.modern-input--high-contrast) .modern-input__label {
    color: var(--text-primary);
  }
}
</style>