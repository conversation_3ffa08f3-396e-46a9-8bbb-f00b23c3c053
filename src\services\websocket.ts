import { io, Socket } from 'socket.io-client';
import { ref, reactive } from 'vue';

// WebSocket连接状态
export const wsStatus = ref<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected');

// 实时数据存储
export const realtimeData = reactive({
  vehicleStatus: {
    online: 0,
    charging: 0,
    idle: 0,
    maintenance: 0
  },
  batteryData: {
    averageHealth: 0,
    totalCapacity: 0,
    chargingPower: 0,
    temperature: 0
  },
  chargingStations: {
    total: 0,
    available: 0,
    occupied: 0,
    offline: 0
  },
  alerts: [] as Array<{
    id: string;
    type: 'warning' | 'error' | 'info';
    message: string;
    timestamp: string;
    vehicleId?: string;
  }>,
  systemMetrics: {
    cpuUsage: 0,
    memoryUsage: 0,
    networkLatency: 0,
    activeConnections: 0
  }
});

class WebSocketService {
  private socket: Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectInterval = 3000;

  constructor() {
    this.connect();
  }

  private connect() {
    try {
      wsStatus.value = 'connecting';
      
      // 在实际项目中，这里应该是真实的WebSocket服务器地址
      this.socket = io('ws://localhost:3001', {
        transports: ['websocket'],
        timeout: 5000,
        forceNew: true
      });

      this.setupEventListeners();
    } catch (error) {
      console.error('WebSocket连接失败:', error);
      wsStatus.value = 'error';
      this.handleReconnect();
    }
  }

  private setupEventListeners() {
    if (!this.socket) return;

    // 连接成功
    this.socket.on('connect', () => {
      console.log('WebSocket连接成功');
      wsStatus.value = 'connected';
      this.reconnectAttempts = 0;
      
      // 请求初始数据
      this.socket?.emit('request_initial_data');
    });

    // 连接断开
    this.socket.on('disconnect', (reason) => {
      console.log('WebSocket连接断开:', reason);
      wsStatus.value = 'disconnected';
      
      if (reason === 'io server disconnect') {
        // 服务器主动断开，需要重新连接
        this.handleReconnect();
      }
    });

    // 连接错误
    this.socket.on('connect_error', (error) => {
      console.error('WebSocket连接错误:', error);
      wsStatus.value = 'error';
      this.handleReconnect();
    });

    // 接收车辆状态数据
    this.socket.on('vehicle_status_update', (data) => {
      Object.assign(realtimeData.vehicleStatus, data);
    });

    // 接收电池数据
    this.socket.on('battery_data_update', (data) => {
      Object.assign(realtimeData.batteryData, data);
    });

    // 接收充电站数据
    this.socket.on('charging_stations_update', (data) => {
      Object.assign(realtimeData.chargingStations, data);
    });

    // 接收告警信息
    this.socket.on('new_alert', (alert) => {
      realtimeData.alerts.unshift({
        ...alert,
        timestamp: new Date().toLocaleString()
      });
      
      // 保持告警列表不超过100条
      if (realtimeData.alerts.length > 100) {
        realtimeData.alerts = realtimeData.alerts.slice(0, 100);
      }
    });

    // 接收系统指标
    this.socket.on('system_metrics_update', (data) => {
      Object.assign(realtimeData.systemMetrics, data);
    });

    // 接收初始数据
    this.socket.on('initial_data', (data) => {
      Object.assign(realtimeData, data);
    });
  }

  private handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      
      setTimeout(() => {
        this.connect();
      }, this.reconnectInterval * this.reconnectAttempts);
    } else {
      console.error('WebSocket重连失败，已达到最大重试次数');
      wsStatus.value = 'error';
    }
  }

  // 发送消息
  public emit(event: string, data?: any) {
    if (this.socket && wsStatus.value === 'connected') {
      this.socket.emit(event, data);
    } else {
      console.warn('WebSocket未连接，无法发送消息');
    }
  }

  // 手动重连
  public reconnect() {
    this.disconnect();
    this.reconnectAttempts = 0;
    this.connect();
  }

  // 断开连接
  public disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    wsStatus.value = 'disconnected';
  }

  // 获取连接状态
  public getStatus() {
    return wsStatus.value;
  }

  // 获取实时数据
  public getRealtimeData() {
    return realtimeData;
  }
}

// 创建WebSocket服务实例
export const websocketService = new WebSocketService();

// 模拟数据生成器（用于开发测试）
export class MockDataGenerator {
  private intervals: NodeJS.Timeout[] = [];

  start() {
    // 模拟车辆状态更新
    this.intervals.push(setInterval(() => {
      realtimeData.vehicleStatus = {
        online: Math.floor(Math.random() * 100) + 150,
        charging: Math.floor(Math.random() * 50) + 20,
        idle: Math.floor(Math.random() * 80) + 100,
        maintenance: Math.floor(Math.random() * 10) + 5
      };
    }, 3000));

    // 模拟电池数据更新
    this.intervals.push(setInterval(() => {
      realtimeData.batteryData = {
        averageHealth: Math.floor(Math.random() * 20) + 80,
        totalCapacity: Math.floor(Math.random() * 1000) + 5000,
        chargingPower: Math.floor(Math.random() * 500) + 200,
        temperature: Math.floor(Math.random() * 20) + 25
      };
    }, 2000));

    // 模拟充电站数据更新
    this.intervals.push(setInterval(() => {
      const total = 150;
      const occupied = Math.floor(Math.random() * 80) + 30;
      const offline = Math.floor(Math.random() * 10) + 2;
      const available = total - occupied - offline;
      
      realtimeData.chargingStations = {
        total,
        available,
        occupied,
        offline
      };
    }, 4000));

    // 模拟系统指标更新
    this.intervals.push(setInterval(() => {
      realtimeData.systemMetrics = {
        cpuUsage: Math.floor(Math.random() * 40) + 30,
        memoryUsage: Math.floor(Math.random() * 30) + 50,
        networkLatency: Math.floor(Math.random() * 50) + 10,
        activeConnections: Math.floor(Math.random() * 100) + 200
      };
    }, 1500));

    // 模拟告警生成
    this.intervals.push(setInterval(() => {
      if (Math.random() > 0.7) { // 30%概率生成告警
        const alertTypes = ['warning', 'error', 'info'] as const;
        const messages = [
          '车辆电池温度过高',
          '充电站离线',
          '系统负载过高',
          '网络连接异常',
          '车辆定位信号丢失',
          '充电功率异常'
        ];
        
        const alert = {
          id: Date.now().toString(),
          type: alertTypes[Math.floor(Math.random() * alertTypes.length)],
          message: messages[Math.floor(Math.random() * messages.length)],
          timestamp: new Date().toLocaleString(),
          vehicleId: `V${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`
        };
        
        realtimeData.alerts.unshift(alert);
        
        if (realtimeData.alerts.length > 100) {
          realtimeData.alerts = realtimeData.alerts.slice(0, 100);
        }
      }
    }, 5000));
  }

  stop() {
    this.intervals.forEach(interval => clearInterval(interval));
    this.intervals = [];
  }
}

// 创建模拟数据生成器实例
export const mockDataGenerator = new MockDataGenerator();

// 移除模拟数据生成器，改为使用真实API
// 在开发环境下也使用真实数据
if (import.meta.env.DEV) {
  console.log('开发环境：使用真实API数据');
}