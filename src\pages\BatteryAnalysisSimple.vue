<template>
  <div class="battery-analysis">
    <!-- 导航按钮区域 -->
    <div class="navigation-tabs">
      <div class="nav-buttons">
        <el-button type="primary" plain disabled>
          <el-icon><TrendCharts /></el-icon>
          健康度分析
        </el-button>
        <router-link to="/battery-management/maintenance" custom v-slot="{ navigate }">
          <el-button type="warning" plain @click="navigate">
            <el-icon><Tools /></el-icon>
            电池维护
          </el-button>
        </router-link>
        <router-link to="/battery-management/monitoring" custom v-slot="{ navigate }">
          <el-button type="info" plain @click="navigate">
            <el-icon><Monitor /></el-icon>
            实时监控
          </el-button>
        </router-link>
      </div>
    </div>

    <!-- 页面标题 -->
    <div class="page-header">
      <h1>电池健康度分析</h1>
      <p>分析电池性能指标，评估电池健康状态</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <el-card class="stat-card health-card">
        <div class="stat-content">
          <div class="stat-icon health-icon">
            <el-icon><Battery /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ overallStats.averageHealth }}%</div>
            <div class="stat-label">平均健康度</div>
            <div class="stat-trend" :class="{ positive: overallStats.healthTrend > 0, negative: overallStats.healthTrend < 0 }">
              <el-icon><TrendCharts /></el-icon>
              {{ overallStats.healthTrend > 0 ? '+' : '' }}{{ overallStats.healthTrend }}%
            </div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card warning-card">
        <div class="stat-content">
          <div class="stat-icon warning-icon">
            <el-icon><Warning /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ overallStats.warningCount }}</div>
            <div class="stat-label">需要关注</div>
            <div class="stat-detail">健康度 < 80%</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card success-card">
        <div class="stat-content">
          <div class="stat-icon success-icon">
            <el-icon><CircleCheck /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ overallStats.healthyCount }}</div>
            <div class="stat-label">状态良好</div>
            <div class="stat-detail">健康度 ≥ 90%</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card temperature-card">
        <div class="stat-content">
          <div class="stat-icon temperature-icon">
            <el-icon><Thermometer /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ overallStats.avgTemperature }}°C</div>
            <div class="stat-label">平均温度</div>
            <div class="stat-detail">范围: {{ overallStats.tempRange }}</div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 健康度分布图表 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>健康度分布</span>
                <el-tag type="info">实时数据</el-tag>
              </div>
            </template>
            <div class="chart-container">
              <div class="health-distribution">
                <div class="health-bar">
                  <div class="health-segment excellent" :style="{ width: healthDistribution.excellent + '%' }">
                    <span>优秀 ({{ healthDistribution.excellent }}%)</span>
                  </div>
                  <div class="health-segment good" :style="{ width: healthDistribution.good + '%' }">
                    <span>良好 ({{ healthDistribution.good }}%)</span>
                  </div>
                  <div class="health-segment warning" :style="{ width: healthDistribution.warning + '%' }">
                    <span>警告 ({{ healthDistribution.warning }}%)</span>
                  </div>
                  <div class="health-segment danger" :style="{ width: healthDistribution.danger + '%' }">
                    <span>危险 ({{ healthDistribution.danger }}%)</span>
                  </div>
                </div>
                <div class="health-legend">
                  <div class="legend-item">
                    <span class="legend-color excellent"></span>
                    <span>优秀 (≥95%)</span>
                  </div>
                  <div class="legend-item">
                    <span class="legend-color good"></span>
                    <span>良好 (80-94%)</span>
                  </div>
                  <div class="legend-item">
                    <span class="legend-color warning"></span>
                    <span>警告 (60-79%)</span>
                  </div>
                  <div class="legend-item">
                    <span class="legend-color danger"></span>
                    <span>危险 (<60%)</span>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>温度监控</span>
                <el-button size="small" @click="refreshTemperatureData">刷新</el-button>
              </div>
            </template>
            <div class="chart-container">
              <div class="temperature-chart">
                <div class="temp-gauge">
                  <div class="gauge-container">
                    <div class="gauge-arc" :style="{ transform: `rotate(${getTemperatureAngle(overallStats.avgTemperature)}deg)` }"></div>
                    <div class="gauge-center">
                      <div class="temp-value">{{ overallStats.avgTemperature }}°C</div>
                      <div class="temp-status" :class="getTemperatureStatus(overallStats.avgTemperature)">
                        {{ getTemperatureStatusText(overallStats.avgTemperature) }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 电池列表 -->
    <el-card class="battery-list-card">
      <template #header>
        <div class="card-header">
          <span>电池健康状态列表</span>
          <el-button type="primary" @click="refreshData">刷新数据</el-button>
        </div>
      </template>

      <el-table :data="batteryData" style="width: 100%" stripe>
        <el-table-column prop="id" label="电池ID" width="120" />
        <el-table-column prop="name" label="电池名称" width="150" />
        <el-table-column prop="health" label="健康度" width="120">
          <template #default="scope">
            <div class="health-cell">
              <el-progress
                :percentage="scope.row.health"
                :color="getHealthColor(scope.row.health)"
                :stroke-width="8"
                text-inside
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="voltage" label="电压(V)" width="100" />
        <el-table-column prop="temperature" label="温度(°C)" width="100">
          <template #default="scope">
            <span :class="getTemperatureClass(scope.row.temperature)">
              {{ scope.row.temperature }}°C
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="capacity" label="容量(%)" width="100">
          <template #default="scope">
            <el-tag :type="getCapacityType(scope.row.capacity)">
              {{ scope.row.capacity }}%
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="cycleCount" label="循环次数" width="100" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button size="small" @click="viewDetails(scope.row)">详情</el-button>
            <el-button size="small" type="warning" @click="scheduleMaintenance(scope.row)">维护</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  TrendCharts,
  Tools,
  Monitor,
  Battery,
  Warning,
  CircleCheck,
  Thermometer
} from '@element-plus/icons-vue'

// 电池数据
const batteryData = ref([
  {
    id: 'BAT001',
    name: '主电池组A',
    health: 96,
    voltage: 48.2,
    temperature: 25,
    status: '正常',
    capacity: 100,
    cycleCount: 1250,
    lastMaintenance: '2024-08-15'
  },
  {
    id: 'BAT002',
    name: '主电池组B',
    health: 88,
    voltage: 47.8,
    temperature: 27,
    status: '正常',
    capacity: 95,
    cycleCount: 1580,
    lastMaintenance: '2024-08-10'
  },
  {
    id: 'BAT003',
    name: '备用电池组',
    health: 75,
    voltage: 46.5,
    temperature: 32,
    status: '警告',
    capacity: 85,
    cycleCount: 2100,
    lastMaintenance: '2024-07-28'
  },
  {
    id: 'BAT004',
    name: '应急电池组',
    health: 98,
    voltage: 48.5,
    temperature: 23,
    status: '正常',
    capacity: 100,
    cycleCount: 850,
    lastMaintenance: '2024-08-18'
  },
  {
    id: 'BAT005',
    name: '辅助电池组',
    health: 82,
    voltage: 47.2,
    temperature: 29,
    status: '正常',
    capacity: 90,
    cycleCount: 1750,
    lastMaintenance: '2024-08-05'
  },
  {
    id: 'BAT006',
    name: '储能电池组',
    health: 65,
    voltage: 45.8,
    temperature: 35,
    status: '警告',
    capacity: 75,
    cycleCount: 2450,
    lastMaintenance: '2024-07-20'
  }
])

// 计算总体统计数据
const overallStats = computed(() => {
  const totalBatteries = batteryData.value.length
  const totalHealth = batteryData.value.reduce((sum, battery) => sum + battery.health, 0)
  const averageHealth = Math.round(totalHealth / totalBatteries)

  const warningCount = batteryData.value.filter(battery => battery.health < 80).length
  const healthyCount = batteryData.value.filter(battery => battery.health >= 90).length

  const totalTemp = batteryData.value.reduce((sum, battery) => sum + battery.temperature, 0)
  const avgTemperature = Math.round(totalTemp / totalBatteries)

  const temperatures = batteryData.value.map(b => b.temperature)
  const minTemp = Math.min(...temperatures)
  const maxTemp = Math.max(...temperatures)

  return {
    averageHealth,
    healthTrend: 2.3, // 模拟趋势数据
    warningCount,
    healthyCount,
    avgTemperature,
    tempRange: `${minTemp}-${maxTemp}°C`
  }
})

// 健康度分布计算
const healthDistribution = computed(() => {
  const total = batteryData.value.length
  const excellent = batteryData.value.filter(b => b.health >= 95).length
  const good = batteryData.value.filter(b => b.health >= 80 && b.health < 95).length
  const warning = batteryData.value.filter(b => b.health >= 60 && b.health < 80).length
  const danger = batteryData.value.filter(b => b.health < 60).length

  return {
    excellent: Math.round((excellent / total) * 100),
    good: Math.round((good / total) * 100),
    warning: Math.round((warning / total) * 100),
    danger: Math.round((danger / total) * 100)
  }
})

// 获取健康度标签类型
const getHealthType = (health: number) => {
  if (health >= 90) return 'success'
  if (health >= 80) return 'warning'
  return 'danger'
}

// 获取健康度颜色
const getHealthColor = (health: number) => {
  if (health >= 95) return '#67c23a'
  if (health >= 90) return '#95d475'
  if (health >= 80) return '#e6a23c'
  if (health >= 60) return '#f56c6c'
  return '#f56c6c'
}

// 获取温度样式类
const getTemperatureClass = (temp: number) => {
  if (temp < 20) return 'temp-cold'
  if (temp > 35) return 'temp-hot'
  return 'temp-normal'
}

// 获取容量标签类型
const getCapacityType = (capacity: number) => {
  if (capacity >= 95) return 'success'
  if (capacity >= 85) return 'warning'
  return 'danger'
}

// 获取状态标签类型
const getStatusType = (status: string) => {
  switch (status) {
    case '正常': return 'success'
    case '警告': return 'warning'
    case '故障': return 'danger'
    default: return 'info'
  }
}

// 安排维护
const scheduleMaintenance = (battery: any) => {
  ElMessage.success(`已为 ${battery.name} 安排维护计划`)
}

// 温度相关函数
const getTemperatureAngle = (temp: number) => {
  // 将温度映射到0-180度的角度范围 (10°C到50°C)
  const minTemp = 10
  const maxTemp = 50
  const normalizedTemp = Math.max(minTemp, Math.min(maxTemp, temp))
  return ((normalizedTemp - minTemp) / (maxTemp - minTemp)) * 180
}

const getTemperatureStatus = (temp: number) => {
  if (temp < 20) return 'cold'
  if (temp > 35) return 'hot'
  return 'normal'
}

const getTemperatureStatusText = (temp: number) => {
  if (temp < 20) return '偏低'
  if (temp > 35) return '偏高'
  return '正常'
}

// 刷新温度数据
const refreshTemperatureData = () => {
  // 模拟温度数据更新
  batteryData.value.forEach(battery => {
    battery.temperature += (Math.random() - 0.5) * 2
    battery.temperature = Math.round(battery.temperature * 10) / 10
  })
  ElMessage.success('温度数据已刷新')
}

// 刷新数据
const refreshData = () => {
  // 模拟数据更新
  batteryData.value.forEach(battery => {
    battery.health += (Math.random() - 0.5) * 2
    battery.health = Math.max(0, Math.min(100, Math.round(battery.health)))
    battery.voltage += (Math.random() - 0.5) * 0.5
    battery.voltage = Math.round(battery.voltage * 10) / 10
  })
  ElMessage.success('数据已刷新')
}

// 查看详情
const viewDetails = (battery: any) => {
  ElMessage.info(`查看 ${battery.name} 的详细信息`)
}

onMounted(() => {
  console.log('电池健康度分析页面已加载')
})
</script>

<style scoped>
.battery-analysis {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

/* 导航按钮样式 */
.navigation-tabs {
  margin-bottom: 20px;
}

.nav-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.nav-buttons .el-button {
  display: flex;
  align-items: center;
  gap: 5px;
}

/* 页面标题 */
.page-header {
  margin-bottom: 30px;
  text-align: center;
}

.page-header h1 {
  color: #303133;
  font-size: 28px;
  margin-bottom: 10px;
}

.page-header p {
  color: #606266;
  font-size: 16px;
}

/* 统计卡片网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
}

.health-icon {
  background: linear-gradient(135deg, #67c23a, #95d475);
}

.warning-icon {
  background: linear-gradient(135deg, #e6a23c, #f7ba2a);
}

.success-icon {
  background: linear-gradient(135deg, #409eff, #66b1ff);
}

.temperature-icon {
  background: linear-gradient(135deg, #f56c6c, #ff8a8a);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.stat-label {
  color: #909399;
  font-size: 14px;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  margin-top: 4px;
}

.stat-trend.positive {
  color: #67c23a;
}

.stat-trend.negative {
  color: #f56c6c;
}

.stat-detail {
  color: #c0c4cc;
  font-size: 12px;
  margin-top: 2px;
}

/* 电池列表卡片 */
.battery-list-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header span {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

/* 图表样式 */
.charts-section {
  margin-bottom: 30px;
}

.chart-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.chart-container {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 健康度分布图 */
.health-distribution {
  width: 100%;
}

.health-bar {
  display: flex;
  height: 30px;
  border-radius: 15px;
  overflow: hidden;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.health-segment {
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  font-weight: bold;
  transition: all 0.3s ease;
}

.health-segment:hover {
  transform: scaleY(1.1);
}

.health-segment.excellent {
  background: linear-gradient(135deg, #67c23a, #95d475);
}

.health-segment.good {
  background: linear-gradient(135deg, #409eff, #66b1ff);
}

.health-segment.warning {
  background: linear-gradient(135deg, #e6a23c, #f7ba2a);
}

.health-segment.danger {
  background: linear-gradient(135deg, #f56c6c, #ff8a8a);
}

.health-legend {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  gap: 10px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
  color: #606266;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-color.excellent {
  background: #67c23a;
}

.legend-color.good {
  background: #409eff;
}

.legend-color.warning {
  background: #e6a23c;
}

.legend-color.danger {
  background: #f56c6c;
}

/* 温度计样式 */
.temperature-chart {
  width: 100%;
  display: flex;
  justify-content: center;
}

.temp-gauge {
  position: relative;
}

.gauge-container {
  width: 120px;
  height: 120px;
  position: relative;
  border-radius: 50%;
  background: conic-gradient(from 0deg, #67c23a 0deg 60deg, #e6a23c 60deg 120deg, #f56c6c 120deg 180deg);
  padding: 10px;
}

.gauge-container::before {
  content: '';
  position: absolute;
  top: 10px;
  left: 10px;
  right: 10px;
  bottom: 10px;
  background: white;
  border-radius: 50%;
}

.gauge-arc {
  position: absolute;
  top: 5px;
  left: 50%;
  width: 2px;
  height: 50px;
  background: #303133;
  transform-origin: bottom center;
  z-index: 10;
}

.gauge-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: 10;
}

.temp-value {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.temp-status {
  font-size: 12px;
  margin-top: 2px;
}

.temp-status.normal {
  color: #67c23a;
}

.temp-status.hot {
  color: #f56c6c;
}

.temp-status.cold {
  color: #409eff;
}

/* 表格样式增强 */
.health-cell {
  padding: 5px 0;
}

.temp-normal {
  color: #67c23a;
  font-weight: bold;
}

.temp-hot {
  color: #f56c6c;
  font-weight: bold;
}

.temp-cold {
  color: #409eff;
  font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .battery-analysis {
    padding: 10px;
  }

  .nav-buttons {
    flex-direction: column;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .page-header h1 {
    font-size: 24px;
  }

  .charts-section .el-col {
    margin-bottom: 20px;
  }

  .health-legend {
    justify-content: center;
  }

  .gauge-container {
    width: 100px;
    height: 100px;
  }

  .temp-value {
    font-size: 16px;
  }
}
</style>
