<template>
  <div class="battery-analysis">
    <!-- 导航按钮区域 -->
    <div class="navigation-tabs">
      <div class="nav-buttons">
        <el-button type="primary" plain disabled>
          <el-icon><TrendCharts /></el-icon>
          健康度分析
        </el-button>
        <router-link to="/battery-management/maintenance" custom v-slot="{ navigate }">
          <el-button type="warning" plain @click="navigate">
            <el-icon><Tools /></el-icon>
            电池维护
          </el-button>
        </router-link>
        <router-link to="/battery-management/monitoring" custom v-slot="{ navigate }">
          <el-button type="info" plain @click="navigate">
            <el-icon><Monitor /></el-icon>
            实时监控
          </el-button>
        </router-link>
      </div>
    </div>

    <!-- 页面标题 -->
    <div class="page-header">
      <h1>电池健康度分析</h1>
      <p>分析电池性能指标，评估电池健康状态</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon">
            <el-icon><Battery /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">85%</div>
            <div class="stat-label">平均健康度</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon">
            <el-icon><Warning /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">3</div>
            <div class="stat-label">需要关注</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon">
            <el-icon><CircleCheck /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">12</div>
            <div class="stat-label">状态良好</div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 电池列表 -->
    <el-card class="battery-list-card">
      <template #header>
        <div class="card-header">
          <span>电池健康状态列表</span>
          <el-button type="primary" @click="refreshData">刷新数据</el-button>
        </div>
      </template>

      <el-table :data="batteryData" style="width: 100%">
        <el-table-column prop="id" label="电池ID" width="120" />
        <el-table-column prop="name" label="电池名称" width="150" />
        <el-table-column prop="health" label="健康度" width="100">
          <template #default="scope">
            <el-tag :type="getHealthType(scope.row.health)">
              {{ scope.row.health }}%
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="voltage" label="电压(V)" width="100" />
        <el-table-column prop="temperature" label="温度(°C)" width="100" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button size="small" @click="viewDetails(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  TrendCharts,
  Tools,
  Monitor,
  Battery,
  Warning,
  CircleCheck
} from '@element-plus/icons-vue'

// 电池数据
const batteryData = ref([
  {
    id: 'BAT001',
    name: '主电池组A',
    health: 92,
    voltage: 48.2,
    temperature: 25,
    status: '正常'
  },
  {
    id: 'BAT002',
    name: '主电池组B',
    health: 88,
    voltage: 47.8,
    temperature: 27,
    status: '正常'
  },
  {
    id: 'BAT003',
    name: '备用电池组',
    health: 75,
    voltage: 46.5,
    temperature: 32,
    status: '警告'
  },
  {
    id: 'BAT004',
    name: '应急电池组',
    health: 95,
    voltage: 48.5,
    temperature: 23,
    status: '正常'
  }
])

// 获取健康度标签类型
const getHealthType = (health: number) => {
  if (health >= 90) return 'success'
  if (health >= 80) return 'warning'
  return 'danger'
}

// 获取状态标签类型
const getStatusType = (status: string) => {
  switch (status) {
    case '正常': return 'success'
    case '警告': return 'warning'
    case '故障': return 'danger'
    default: return 'info'
  }
}

// 刷新数据
const refreshData = () => {
  ElMessage.success('数据已刷新')
}

// 查看详情
const viewDetails = (battery: any) => {
  ElMessage.info(`查看 ${battery.name} 的详细信息`)
}

onMounted(() => {
  console.log('电池健康度分析页面已加载')
})
</script>

<style scoped>
.battery-analysis {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

/* 导航按钮样式 */
.navigation-tabs {
  margin-bottom: 20px;
}

.nav-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.nav-buttons .el-button {
  display: flex;
  align-items: center;
  gap: 5px;
}

/* 页面标题 */
.page-header {
  margin-bottom: 30px;
  text-align: center;
}

.page-header h1 {
  color: #303133;
  font-size: 28px;
  margin-bottom: 10px;
}

.page-header p {
  color: #606266;
  font-size: 16px;
}

/* 统计卡片网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #409eff, #67c23a);
  color: white;
  font-size: 24px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.stat-label {
  color: #909399;
  font-size: 14px;
}

/* 电池列表卡片 */
.battery-list-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header span {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .battery-analysis {
    padding: 10px;
  }

  .nav-buttons {
    flex-direction: column;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .page-header h1 {
    font-size: 24px;
  }
}
</style>
