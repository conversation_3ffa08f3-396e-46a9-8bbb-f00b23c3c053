<template>
  <div class="battery-analysis-simple">
    <h1>电池健康分析 - 简化版</h1>
    <p>这是一个简化版的健康度分析页面，用于测试导航功能。</p>
    
    <div style="margin: 20px 0;">
      <el-button @click="goBack">返回监控页面</el-button>
      <el-button @click="goToMaintenance">前往维护页面</el-button>
    </div>
    
    <div style="padding: 20px; background: #f5f5f5; border-radius: 8px;">
      <h2>测试信息</h2>
      <p>当前路由: {{ $route.path }}</p>
      <p>页面加载时间: {{ loadTime }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const loadTime = ref('')

onMounted(() => {
  loadTime.value = new Date().toLocaleString()
  console.log('简化版健康度分析页面已加载')
})

const goBack = () => {
  router.push('/battery-management/monitoring')
}

const goToMaintenance = () => {
  router.push('/battery-management/maintenance')
}
</script>

<style scoped>
.battery-analysis-simple {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
  background: white;
  min-height: 100vh;
  color: #333;
}

h1 {
  color: #409eff !important;
  margin-bottom: 20px;
  font-size: 24px;
  font-weight: bold;
}

h2 {
  color: #333 !important;
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
}

p {
  color: #666 !important;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 10px;
}

.el-button {
  margin-right: 10px;
}

div[style*="background: #f5f5f5"] {
  background: #f5f5f5 !important;
  padding: 20px;
  border-radius: 8px;
  margin-top: 20px;
}
</style>
