<template>
  <div class="battery-analysis-simple">
    <h1>电池健康分析 - 简化版</h1>
    <p>这是一个简化版的健康度分析页面，用于测试导航功能。</p>
    
    <div style="margin: 20px 0;">
      <el-button @click="goBack">返回监控页面</el-button>
      <el-button @click="goToMaintenance">前往维护页面</el-button>
    </div>
    
    <div style="padding: 20px; background: #f5f5f5; border-radius: 8px;">
      <h2>测试信息</h2>
      <p>当前路由: {{ $route.path }}</p>
      <p>页面加载时间: {{ loadTime }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const loadTime = ref('')

onMounted(() => {
  loadTime.value = new Date().toLocaleString()
  console.log('简化版健康度分析页面已加载')
})

const goBack = () => {
  router.push('/battery-management/monitoring')
}

const goToMaintenance = () => {
  router.push('/battery-management/maintenance')
}
</script>

<style scoped>
.battery-analysis-simple {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

h1 {
  color: #409eff;
  margin-bottom: 20px;
}

.el-button {
  margin-right: 10px;
}
</style>
