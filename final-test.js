const axios = require('axios');

async function finalTest() {
  console.log('🎯 最终功能测试...\n');
  
  const tests = [
    { question: '电池健康度如何计算？', expectKnowledge: true },
    { question: '如何查找充电站？', expectKnowledge: true },
    { question: 'ADAS功能如何开启？', expectKnowledge: true },
    { question: '系统有什么功能？', expectKnowledge: false }
  ];
  
  for (const test of tests) {
    try {
      const response = await axios.post('http://localhost:3001/api/ai-enhanced/chat', {
        message: test.question
      });
      
      if (response.data.success) {
        const hasKnowledge = response.data.response.includes('【') && response.data.response.includes('】');
        const status = hasKnowledge ? '✅ 知识库回答' : '📝 通用回答';
        
        console.log(`❓ ${test.question}`);
        console.log(`${status}`);
        console.log(`🤖 ${response.data.response.substring(0, 80)}...\n`);
      }
    } catch (error) {
      console.log(`❌ ${test.question} - 错误: ${error.message}\n`);
    }
  }
  
  console.log('🎉 测试完成！AI助手功能正常运行。');
}

finalTest();
