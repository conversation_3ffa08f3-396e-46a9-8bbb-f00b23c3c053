<template>
  <div class="adas-system">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">
        <Shield class="w-8 h-8" />
        高级驾驶辅助系统
      </h1>
      <p class="page-description">智能环境感知，主动安全预警，自动泊车辅助，驾驶行为分析</p>
    </div>

    <!-- 系统状态卡片 -->
    <div class="status-grid">
      <div class="status-card">
        <div class="status-icon">
          <Eye class="w-6 h-6 text-blue-500" />
        </div>
        <div class="status-content">
          <div class="status-value">{{ systemStatus.perception }}</div>
          <div class="status-label">环境感知</div>
        </div>
        <div class="status-indicator" :class="getStatusClass(systemStatus.perception)"></div>
      </div>
      
      <div class="status-card">
        <div class="status-icon">
          <AlertTriangle class="w-6 h-6 text-orange-500" />
        </div>
        <div class="status-content">
          <div class="status-value">{{ systemStatus.warning }}</div>
          <div class="status-label">安全预警</div>
        </div>
        <div class="status-indicator" :class="getStatusClass(systemStatus.warning)"></div>
      </div>
      
      <div class="status-card">
        <div class="status-icon">
          <Car class="w-6 h-6 text-green-500" />
        </div>
        <div class="status-content">
          <div class="status-value">{{ systemStatus.parking }}</div>
          <div class="status-label">自动泊车</div>
        </div>
        <div class="status-indicator" :class="getStatusClass(systemStatus.parking)"></div>
      </div>
      
      <div class="status-card">
        <div class="status-icon">
          <BarChart3 class="w-6 h-6 text-purple-500" />
        </div>
        <div class="status-content">
          <div class="status-value">{{ systemStatus.analysis }}</div>
          <div class="status-label">行为分析</div>
        </div>
        <div class="status-indicator" :class="getStatusClass(systemStatus.analysis)"></div>
      </div>
    </div>

    <!-- 功能模块 -->
    <div class="modules-grid">
      <!-- 环境感知模块 -->
      <div class="module-card perception-module">
        <div class="module-header">
          <Radar class="w-5 h-5" />
          <h3>环境感知</h3>
          <div class="header-actions">
            <ModernButton size="small" @click="refreshPerception">
              <RotateCcw class="w-4 h-4" />
            </ModernButton>
            <ModernButton size="small" @click="togglePerceptionMode">
              <Settings class="w-4 h-4" />
            </ModernButton>
          </div>
        </div>
        <div class="module-content">
          <div class="perception-display">
            <div class="vehicle-view">
              <div class="vehicle-icon">
                <Car class="w-16 h-16 text-blue-600" />
              </div>
              
              <!-- 前方检测区域 -->
              <div class="detection-zone front">
                <div class="zone-label">前方检测</div>
                <div class="detected-objects">
                  <div 
                    v-for="obj in frontObjects" 
                    :key="obj.id"
                    class="object-marker"
                    :class="getObjectTypeClass(obj.type)"
                    :style="{ left: obj.x + '%', top: obj.y + '%' }"
                  >
                    <div class="object-info">
                      <span class="object-type">{{ getObjectTypeName(obj.type) }}</span>
                      <span class="object-distance">{{ obj.distance }}m</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- 侧方检测区域 -->
              <div class="detection-zone left">
                <div class="zone-label">左侧检测</div>
                <div class="detected-objects">
                  <div 
                    v-for="obj in leftObjects" 
                    :key="obj.id"
                    class="object-marker"
                    :class="getObjectTypeClass(obj.type)"
                  >
                    <span class="object-distance">{{ obj.distance }}m</span>
                  </div>
                </div>
              </div>
              
              <div class="detection-zone right">
                <div class="zone-label">右侧检测</div>
                <div class="detected-objects">
                  <div 
                    v-for="obj in rightObjects" 
                    :key="obj.id"
                    class="object-marker"
                    :class="getObjectTypeClass(obj.type)"
                  >
                    <span class="object-distance">{{ obj.distance }}m</span>
                  </div>
                </div>
              </div>
              
              <!-- 后方检测区域 -->
              <div class="detection-zone rear">
                <div class="zone-label">后方检测</div>
                <div class="detected-objects">
                  <div 
                    v-for="obj in rearObjects" 
                    :key="obj.id"
                    class="object-marker"
                    :class="getObjectTypeClass(obj.type)"
                  >
                    <span class="object-distance">{{ obj.distance }}m</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="perception-stats">
            <div class="stat-item">
              <span class="stat-label">检测对象</span>
              <span class="stat-value">{{ totalDetectedObjects }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">最近距离</span>
              <span class="stat-value">{{ nearestDistance }}m</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">检测精度</span>
              <span class="stat-value">{{ perceptionAccuracy }}%</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 安全预警模块 -->
      <div class="module-card">
        <div class="module-header">
          <AlertCircle class="w-5 h-5" />
          <h3>安全预警</h3>
        </div>
        <div class="module-content">
          <div class="warning-settings">
            <h4 class="mb-3">预警设置</h4>
            <div class="setting-list">
              <div class="setting-item">
                <span class="setting-label">前碰撞预警</span>
                <el-switch v-model="warningSettings.fcw" />
              </div>
              <div class="setting-item">
                <span class="setting-label">车道偏离预警</span>
                <el-switch v-model="warningSettings.ldw" />
              </div>
              <div class="setting-item">
                <span class="setting-label">盲点监测</span>
                <el-switch v-model="warningSettings.bsm" />
              </div>
              <div class="setting-item">
                <span class="setting-label">疲劳驾驶预警</span>
                <el-switch v-model="warningSettings.ddw" />
              </div>
              <div class="setting-item">
                <span class="setting-label">超速预警</span>
                <el-switch v-model="warningSettings.spw" />
              </div>
            </div>
          </div>
          
          <div class="active-warnings">
            <h4 class="mb-3">当前预警</h4>
            <div class="warning-list">
              <div 
                v-for="warning in activeWarnings" 
                :key="warning.id"
                class="warning-item"
                :class="getWarningLevelClass(warning.level)"
              >
                <div class="warning-icon">
                  <component :is="getWarningIcon(warning.type)" class="w-5 h-5" />
                </div>
                <div class="warning-content">
                  <div class="warning-title">{{ warning.title }}</div>
                  <div class="warning-description">{{ warning.description }}</div>
                  <div class="warning-time">{{ formatTime(warning.timestamp) }}</div>
                </div>
                <div class="warning-level">
                  <el-tag 
                    :type="getWarningTagType(warning.level)"
                    size="small"
                  >
                    {{ getWarningLevelText(warning.level) }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
          
          <div class="warning-history">
            <h4 class="mb-3">预警历史</h4>
            <div class="history-stats">
              <div class="history-stat">
                <span class="stat-label">今日预警</span>
                <span class="stat-value">{{ todayWarnings }}</span>
              </div>
              <div class="history-stat">
                <span class="stat-label">本周预警</span>
                <span class="stat-value">{{ weekWarnings }}</span>
              </div>
              <div class="history-stat">
                <span class="stat-label">预警率</span>
                <span class="stat-value">{{ warningRate }}%</span>
              </div>
            </div>
            <ModernButton @click="viewWarningHistory" class="w-full mt-3">
              查看详细历史
            </ModernButton>
          </div>
        </div>
      </div>

      <!-- 自动泊车模块 -->
      <div class="module-card">
        <div class="module-header">
          <Navigation class="w-5 h-5" />
          <h3>自动泊车</h3>
        </div>
        <div class="module-content">
          <div class="parking-control">
            <h4 class="mb-3">泊车控制</h4>
            <div class="parking-status">
              <div class="status-display">
                <div class="status-icon">
                  <component :is="getParkingStatusIcon()" class="w-8 h-8" :class="getParkingStatusColor()" />
                </div>
                <div class="status-text">
                  <div class="status-title">{{ parkingStatus.title }}</div>
                  <div class="status-description">{{ parkingStatus.description }}</div>
                </div>
              </div>
              
              <div class="parking-progress" v-if="parkingStatus.inProgress">
                <el-progress 
                  :percentage="parkingProgress" 
                  :status="parkingProgress === 100 ? 'success' : 'active'"
                />
                <div class="progress-text">{{ parkingStatus.step }}</div>
              </div>
            </div>
            
            <div class="parking-actions">
              <ModernButton 
                v-if="!parkingStatus.inProgress"
                variant="primary" 
                @click="startParking"
                :disabled="!canStartParking"
              >
                <Play class="w-4 h-4 mr-2" />
                开始泊车
              </ModernButton>
              
              <ModernButton 
                v-if="parkingStatus.inProgress"
                variant="danger" 
                @click="stopParking"
              >
                <Square class="w-4 h-4 mr-2" />
                停止泊车
              </ModernButton>
              
              <ModernButton 
                v-if="parkingStatus.inProgress"
                @click="pauseParking"
              >
                <Pause class="w-4 h-4 mr-2" />
                暂停
              </ModernButton>
            </div>
          </div>
          
          <div class="parking-spaces">
            <h4 class="mb-3">可用车位</h4>
            <div class="space-list">
              <div 
                v-for="space in availableSpaces" 
                :key="space.id"
                class="space-item"
                :class="{ 'selected': selectedSpace?.id === space.id }"
                @click="selectParkingSpace(space)"
              >
                <div class="space-info">
                  <div class="space-type">{{ getSpaceTypeName(space.type) }}</div>
                  <div class="space-size">{{ space.length }}×{{ space.width }}m</div>
                  <div class="space-difficulty">难度: {{ space.difficulty }}/5</div>
                </div>
                <div class="space-status">
                  <el-tag 
                    :type="getSpaceStatusType(space.status)"
                    size="small"
                  >
                    {{ getSpaceStatusText(space.status) }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
          
          <div class="parking-settings">
            <h4 class="mb-3">泊车设置</h4>
            <el-form label-width="100px" size="small">
              <el-form-item label="泊车模式">
                <el-select v-model="parkingSettings.mode">
                  <el-option label="自动模式" value="auto" />
                  <el-option label="辅助模式" value="assist" />
                  <el-option label="手动模式" value="manual" />
                </el-select>
              </el-form-item>
              
              <el-form-item label="速度设置">
                <el-slider 
                  v-model="parkingSettings.speed" 
                  :min="1" 
                  :max="5" 
                  :marks="{ 1: '慢', 3: '中', 5: '快' }"
                />
              </el-form-item>
              
              <el-form-item label="声音提示">
                <el-switch v-model="parkingSettings.soundAlert" />
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>

      <!-- 行为分析模块 -->
      <div class="module-card">
        <div class="module-header">
          <TrendingUp class="w-5 h-5" />
          <h3>行为分析</h3>
        </div>
        <div class="module-content">
          <div class="behavior-overview">
            <h4 class="mb-3">驾驶评分</h4>
            <div class="score-display">
              <div class="score-circle">
                <div class="score-value">{{ drivingScore }}</div>
                <div class="score-label">综合评分</div>
              </div>
              <div class="score-breakdown">
                <div class="score-item">
                  <span class="score-category">安全性</span>
                  <div class="score-bar">
                    <div class="score-fill" :style="{ width: behaviorScores.safety + '%' }"></div>
                  </div>
                  <span class="score-number">{{ behaviorScores.safety }}</span>
                </div>
                <div class="score-item">
                  <span class="score-category">经济性</span>
                  <div class="score-bar">
                    <div class="score-fill" :style="{ width: behaviorScores.economy + '%' }"></div>
                  </div>
                  <span class="score-number">{{ behaviorScores.economy }}</span>
                </div>
                <div class="score-item">
                  <span class="score-category">舒适性</span>
                  <div class="score-bar">
                    <div class="score-fill" :style="{ width: behaviorScores.comfort + '%' }"></div>
                  </div>
                  <span class="score-number">{{ behaviorScores.comfort }}</span>
                </div>
                <div class="score-item">
                  <span class="score-category">环保性</span>
                  <div class="score-bar">
                    <div class="score-fill" :style="{ width: behaviorScores.eco + '%' }"></div>
                  </div>
                  <span class="score-number">{{ behaviorScores.eco }}</span>
                </div>
              </div>
            </div>
          </div>
          
          <div class="behavior-metrics">
            <h4 class="mb-3">行为指标</h4>
            <div class="metrics-grid">
              <div class="metric-card">
                <div class="metric-icon">
                  <Gauge class="w-5 h-5 text-blue-500" />
                </div>
                <div class="metric-content">
                  <div class="metric-value">{{ behaviorMetrics.avgSpeed }}km/h</div>
                  <div class="metric-label">平均速度</div>
                </div>
              </div>
              
              <div class="metric-card">
                <div class="metric-icon">
                  <Zap class="w-5 h-5 text-orange-500" />
                </div>
                <div class="metric-content">
                  <div class="metric-value">{{ behaviorMetrics.harshEvents }}</div>
                  <div class="metric-label">急加速/急刹车</div>
                </div>
              </div>
              
              <div class="metric-card">
                <div class="metric-icon">
                  <Clock class="w-5 h-5 text-green-500" />
                </div>
                <div class="metric-content">
                  <div class="metric-value">{{ behaviorMetrics.drivingTime }}h</div>
                  <div class="metric-label">驾驶时长</div>
                </div>
              </div>
              
              <div class="metric-card">
                <div class="metric-icon">
                  <MapPin class="w-5 h-5 text-purple-500" />
                </div>
                <div class="metric-content">
                  <div class="metric-value">{{ behaviorMetrics.distance }}km</div>
                  <div class="metric-label">行驶里程</div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="behavior-trends">
            <h4 class="mb-3">趋势分析</h4>
            <div class="trend-chart">
              <div class="chart-placeholder">
                <BarChart3 class="w-16 h-16 text-gray-400" />
                <p class="text-gray-500 mt-2">驾驶行为趋势图</p>
                <p class="text-sm text-gray-400">显示最近30天的驾驶行为变化</p>
              </div>
            </div>
          </div>
          
          <ModernButton @click="viewDetailedAnalysis" class="w-full mt-4">
            查看详细分析报告
          </ModernButton>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { 
  Shield, 
  Eye, 
  AlertTriangle, 
  Car, 
  BarChart3, 
  Radar, 
  RotateCcw, 
  Settings, 
  AlertCircle, 
  Navigation, 
  Play, 
  Square, 
  Pause, 
  TrendingUp, 
  Gauge, 
  Zap, 
  Clock, 
  MapPin,
  CheckCircle 
} from 'lucide-vue-next'
import { ElMessage } from 'element-plus'
import { useAdasStore } from '@/stores/adas'
import { ModernButton } from '@/components/ui'

const adasStore = useAdasStore()

// 响应式数据
const systemStatus = ref({
  perception: '正常',
  warning: '正常',
  parking: '待机',
  analysis: '运行中'
})

const warningSettings = ref({
  fcw: true,
  ldw: true,
  bsm: true,
  ddw: true,
  spw: true
})

const parkingSettings = ref({
  mode: 'auto',
  speed: 3,
  soundAlert: true
})

const selectedSpace = ref(null)
const parkingProgress = ref(0)

// 计算属性
const frontObjects = computed(() => adasStore.detectedObjects.filter(obj => obj.zone === 'front'))
const leftObjects = computed(() => adasStore.detectedObjects.filter(obj => obj.zone === 'left'))
const rightObjects = computed(() => adasStore.detectedObjects.filter(obj => obj.zone === 'right'))
const rearObjects = computed(() => adasStore.detectedObjects.filter(obj => obj.zone === 'rear'))

const totalDetectedObjects = computed(() => adasStore.detectedObjects.length)
const nearestDistance = computed(() => {
  const distances = adasStore.detectedObjects.map(obj => obj.distance)
  return distances.length > 0 ? Math.min(...distances) : 0
})
const perceptionAccuracy = computed(() => adasStore.perceptionAccuracy)

const activeWarnings = computed(() => adasStore.warnings.filter(w => w.active))
const todayWarnings = computed(() => adasStore.todayWarnings)
const weekWarnings = computed(() => adasStore.weekWarnings)
const warningRate = computed(() => adasStore.warningRate)

const parkingStatus = computed(() => adasStore.parkingStatus)
const canStartParking = computed(() => selectedSpace.value && !parkingStatus.value.inProgress)
const availableSpaces = computed(() => adasStore.parkingSpaces.filter(s => s.status === 'available'))

const drivingScore = computed(() => adasStore.drivingScore)
const behaviorScores = computed(() => adasStore.behaviorScores)
const behaviorMetrics = computed(() => adasStore.behaviorMetrics)

// 方法
const getStatusClass = (status: string) => {
  const classes = {
    '正常': 'status-normal',
    '警告': 'status-warning',
    '错误': 'status-error',
    '待机': 'status-standby',
    '运行中': 'status-running'
  }
  return classes[status] || 'status-normal'
}

const getObjectTypeClass = (type: string) => {
  const classes = {
    'vehicle': 'object-vehicle',
    'pedestrian': 'object-pedestrian',
    'cyclist': 'object-cyclist',
    'obstacle': 'object-obstacle'
  }
  return classes[type] || 'object-unknown'
}

const getObjectTypeName = (type: string) => {
  const names = {
    'vehicle': '车辆',
    'pedestrian': '行人',
    'cyclist': '骑行者',
    'obstacle': '障碍物'
  }
  return names[type] || '未知'
}

const getWarningIcon = (type: string) => {
  const icons = {
    'fcw': AlertTriangle,
    'ldw': Navigation,
    'bsm': Eye,
    'ddw': Clock,
    'spw': Gauge
  }
  return icons[type] || AlertCircle
}

const getWarningLevelClass = (level: string) => {
  const classes = {
    'low': 'warning-low',
    'medium': 'warning-medium',
    'high': 'warning-high',
    'critical': 'warning-critical'
  }
  return classes[level] || 'warning-low'
}

const getWarningTagType = (level: string) => {
  const types = {
    'low': 'info',
    'medium': 'warning',
    'high': 'danger',
    'critical': 'danger'
  }
  return types[level] || 'info'
}

const getWarningLevelText = (level: string) => {
  const texts = {
    'low': '低',
    'medium': '中',
    'high': '高',
    'critical': '紧急'
  }
  return texts[level] || '未知'
}

const getParkingStatusIcon = () => {
  if (parkingStatus.value.inProgress) return Play
  if (parkingStatus.value.completed) return CheckCircle
  return Car
}

const getParkingStatusColor = () => {
  if (parkingStatus.value.inProgress) return 'text-blue-500'
  if (parkingStatus.value.completed) return 'text-green-500'
  return 'text-gray-500'
}

const getSpaceTypeName = (type: string) => {
  const names = {
    'parallel': '平行泊车',
    'perpendicular': '垂直泊车',
    'angle': '斜角泊车'
  }
  return names[type] || '未知'
}

const getSpaceStatusType = (status: string) => {
  const types = {
    'available': 'success',
    'occupied': 'danger',
    'reserved': 'warning'
  }
  return types[status] || 'info'
}

const getSpaceStatusText = (status: string) => {
  const texts = {
    'available': '可用',
    'occupied': '占用',
    'reserved': '预留'
  }
  return texts[status] || '未知'
}

const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleTimeString('zh-CN')
}

const refreshPerception = async () => {
  try {
    const response = await fetch('/api/adas/perception/refresh', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    })
    
    if (response.ok) {
      adasStore.refreshPerception()
      ElMessage.success('环境感知已刷新')
    } else {
      throw new Error('刷新失败')
    }
  } catch (error) {
    console.error('刷新感知数据失败:', error)
    ElMessage.error('刷新感知数据失败，请重试')
  }
}

const togglePerceptionMode = async () => {
  try {
    const response = await fetch('/api/adas/perception/mode', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        mode: 'enhanced' // 可以根据当前模式切换
      })
    })
    
    if (response.ok) {
      const data = await response.json()
      ElMessage.success(`已切换到${data.mode}模式`)
    } else {
      throw new Error('切换失败')
    }
  } catch (error) {
    console.error('切换感知模式失败:', error)
    ElMessage.error('切换感知模式失败，请重试')
  }
}

const startParking = async () => {
  if (!selectedSpace.value) {
    ElMessage.warning('请先选择车位')
    return
  }
  
  try {
    await adasStore.startParking(selectedSpace.value.id, parkingSettings.value)
    ElMessage.success('开始自动泊车')
  } catch (error) {
    ElMessage.error('启动泊车失败')
  }
}

const stopParking = async () => {
  try {
    await adasStore.stopParking()
    ElMessage.success('泊车已停止')
  } catch (error) {
    ElMessage.error('停止泊车失败')
  }
}

const pauseParking = async () => {
  try {
    await adasStore.pauseParking()
    ElMessage.success('泊车已暂停')
  } catch (error) {
    ElMessage.error('暂停泊车失败')
  }
}

const selectParkingSpace = (space: any) => {
  selectedSpace.value = space
  ElMessage.success(`已选择车位: ${getSpaceTypeName(space.type)}`)
}

const viewWarningHistory = async () => {
  try {
    const response = await fetch('/api/adas/warnings/history')
    
    if (response.ok) {
      const data = await response.json()
      // 这里可以打开一个对话框显示历史数据
      console.log('预警历史数据:', data)
      ElMessage.success('预警历史数据已加载')
    } else {
      throw new Error('获取历史数据失败')
    }
  } catch (error) {
    console.error('获取预警历史失败:', error)
    ElMessage.error('获取预警历史失败，请重试')
  }
}

const viewDetailedAnalysis = async () => {
  try {
    const response = await fetch('/api/adas/analysis/detailed')
    
    if (response.ok) {
      const data = await response.json()
      // 这里可以打开一个对话框显示详细分析报告
      console.log('详细分析报告:', data)
      ElMessage.success('分析报告已生成')
    } else {
      throw new Error('生成分析报告失败')
    }
  } catch (error) {
    console.error('获取详细分析失败:', error)
    ElMessage.error('获取详细分析失败，请重试')
  }
}

// 生命周期
onMounted(async () => {
  await Promise.all([
    adasStore.fetchDetectedObjects(),
    adasStore.fetchWarnings(),
    adasStore.fetchParkingSpaces(),
    adasStore.fetchBehaviorData()
  ])
  
  // 模拟实时数据更新
  setInterval(async () => {
    // 定期刷新真实数据
    if (Math.random() < 0.2) {
      await Promise.all([
        adasStore.fetchDetectedObjects(),
        adasStore.fetchWarnings(),
        adasStore.fetchBehaviorData()
      ])
    }
  }, 5000)
})
</script>

<style scoped>
.adas-system {
  @apply p-6 space-y-6;
}

.page-header {
  @apply text-center mb-8;
}

.page-title {
  @apply flex items-center justify-center gap-3 text-3xl font-bold text-gray-800 mb-2;
}

.page-description {
  @apply text-gray-600 text-lg;
}

.status-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8;
}

.status-card {
  @apply bg-white rounded-lg shadow-md p-6 flex items-center space-x-4 hover:shadow-lg transition-shadow relative;
}

.status-icon {
  @apply flex-shrink-0;
}

.status-content {
  @apply flex-1;
}

.status-value {
  @apply text-xl font-bold text-gray-800;
}

.status-label {
  @apply text-sm text-gray-600 mt-1;
}

.status-indicator {
  @apply absolute top-2 right-2 w-3 h-3 rounded-full;
}

.status-normal {
  @apply bg-green-500;
}

.status-warning {
  @apply bg-yellow-500;
}

.status-error {
  @apply bg-red-500;
}

.status-standby {
  @apply bg-gray-500;
}

.status-running {
  @apply bg-blue-500;
}

.modules-grid {
  @apply grid grid-cols-1 lg:grid-cols-2 gap-6;
}

.module-card {
  @apply bg-white rounded-lg shadow-md overflow-hidden;
}

.module-card.perception-module {
  @apply lg:col-span-2;
}

.module-header {
  @apply flex items-center gap-2 px-6 py-4 bg-gray-50 border-b;
}

.module-header h3 {
  @apply text-lg font-semibold text-gray-800 flex-1;
}

.header-actions {
  @apply flex gap-2;
}

.module-content {
  @apply p-6;
}

.perception-display {
  @apply mb-6;
}

.vehicle-view {
  @apply relative bg-gray-100 rounded-lg p-8 min-h-96 flex items-center justify-center;
}

.vehicle-icon {
  @apply z-10;
}

.detection-zone {
  @apply absolute border-2 border-dashed border-blue-300 bg-blue-50 bg-opacity-50;
}

.detection-zone.front {
  @apply top-4 left-1/2 transform -translate-x-1/2 w-32 h-24;
}

.detection-zone.left {
  @apply left-4 top-1/2 transform -translate-y-1/2 w-24 h-32;
}

.detection-zone.right {
  @apply right-4 top-1/2 transform -translate-y-1/2 w-24 h-32;
}

.detection-zone.rear {
  @apply bottom-4 left-1/2 transform -translate-x-1/2 w-32 h-24;
}

.zone-label {
  @apply absolute -top-6 left-1/2 transform -translate-x-1/2 text-xs text-gray-600 bg-white px-2 py-1 rounded;
}

.detected-objects {
  @apply relative w-full h-full;
}

.object-marker {
  @apply absolute w-4 h-4 rounded-full flex items-center justify-center text-xs text-white font-bold;
}

.object-vehicle {
  @apply bg-red-500;
}

.object-pedestrian {
  @apply bg-orange-500;
}

.object-cyclist {
  @apply bg-yellow-500;
}

.object-obstacle {
  @apply bg-gray-500;
}

.object-info {
  @apply absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded whitespace-nowrap;
}

.object-distance {
  @apply block;
}

.perception-stats {
  @apply flex justify-around bg-gray-50 rounded-lg p-4;
}

.stat-item {
  @apply text-center;
}

.stat-label {
  @apply block text-sm text-gray-600;
}

.stat-value {
  @apply block text-lg font-bold text-gray-800;
}

.warning-settings h4,
.active-warnings h4,
.warning-history h4,
.parking-control h4,
.parking-spaces h4,
.parking-settings h4,
.behavior-overview h4,
.behavior-metrics h4,
.behavior-trends h4 {
  @apply text-lg font-semibold text-gray-800;
}

.setting-list {
  @apply space-y-3;
}

.setting-item {
  @apply flex items-center justify-between p-3 bg-gray-50 rounded-lg;
}

.setting-label {
  @apply text-gray-700;
}

.warning-list {
  @apply space-y-3 max-h-64 overflow-y-auto;
}

.warning-item {
  @apply flex items-start gap-3 p-3 rounded-lg border-l-4;
}

.warning-low {
  @apply bg-blue-50 border-blue-400;
}

.warning-medium {
  @apply bg-yellow-50 border-yellow-400;
}

.warning-high {
  @apply bg-orange-50 border-orange-400;
}

.warning-critical {
  @apply bg-red-50 border-red-400;
}

.warning-icon {
  @apply flex-shrink-0 mt-1;
}

.warning-content {
  @apply flex-1;
}

.warning-title {
  @apply font-semibold text-gray-800;
}

.warning-description {
  @apply text-sm text-gray-600 mt-1;
}

.warning-time {
  @apply text-xs text-gray-500 mt-1;
}

.warning-level {
  @apply flex-shrink-0;
}

.history-stats {
  @apply grid grid-cols-3 gap-4;
}

.history-stat {
  @apply text-center p-3 bg-gray-50 rounded-lg;
}

.parking-status {
  @apply mb-4;
}

.status-display {
  @apply flex items-center gap-4 p-4 bg-gray-50 rounded-lg mb-4;
}

.status-text {
  @apply flex-1;
}

.status-title {
  @apply font-semibold text-gray-800;
}

.status-description {
  @apply text-sm text-gray-600;
}
</style>