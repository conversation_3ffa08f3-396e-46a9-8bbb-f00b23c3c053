<template>
  <div 
    ref="containerRef"
    class="swipe-container"
    :class="{
      'swiping': isSwiping,
      'transitioning': isTransitioning
    }"
    @touchstart="handleTouchStart"
    @touchmove="handleTouchMove"
    @touchend="handleTouchEnd"
    @touchcancel="handleTouchCancel"
  >
    <div 
      class="swipe-wrapper"
      :style="wrapperStyle"
    >
      <slot></slot>
    </div>
    
    <!-- 滑动指示器 -->
    <div v-if="showIndicators" class="swipe-indicators">
      <div 
        v-for="(item, index) in itemCount"
        :key="index"
        class="indicator"
        :class="{ 'indicator-active': index === currentIndex }"
        @click="goToSlide(index)"
      ></div>
    </div>
    
    <!-- 滑动提示 -->
    <div v-if="showHint && !hasInteracted" class="swipe-hint">
      <div class="hint-arrow">
        <ChevronLeft class="hint-icon" />
        <span class="hint-text">滑动切换</span>
        <ChevronRight class="hint-icon" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { ChevronLeft, ChevronRight } from 'lucide-vue-next'
import { debounce } from '../../utils/mobile'

interface Props {
  itemCount?: number
  initialIndex?: number
  autoplay?: boolean
  autoplayInterval?: number
  loop?: boolean
  threshold?: number
  showIndicators?: boolean
  showHint?: boolean
  direction?: 'horizontal' | 'vertical'
  resistance?: number
}

const props = withDefaults(defineProps<Props>(), {
  itemCount: 1,
  initialIndex: 0,
  autoplay: false,
  autoplayInterval: 3000,
  loop: false,
  threshold: 50,
  showIndicators: true,
  showHint: true,
  direction: 'horizontal',
  resistance: 0.3
})

const emit = defineEmits<{
  change: [index: number]
  swipeStart: []
  swipeEnd: []
  swipeLeft: []
  swipeRight: []
  swipeUp: []
  swipeDown: []
}>()

const containerRef = ref<HTMLElement>()
const currentIndex = ref(props.initialIndex)
const isSwiping = ref(false)
const isTransitioning = ref(false)
const hasInteracted = ref(false)

// 触摸状态
const touchState = ref({
  startX: 0,
  startY: 0,
  currentX: 0,
  currentY: 0,
  deltaX: 0,
  deltaY: 0,
  startTime: 0,
  isScrolling: false
})

// 自动播放定时器
let autoplayTimer: NodeJS.Timeout | null = null

// 包装器样式
const wrapperStyle = computed(() => {
  const isHorizontal = props.direction === 'horizontal'
  const translateValue = isHorizontal 
    ? -currentIndex.value * 100 + (isSwiping.value ? (touchState.value.deltaX / containerRef.value!.offsetWidth) * 100 : 0)
    : -currentIndex.value * 100 + (isSwiping.value ? (touchState.value.deltaY / containerRef.value!.offsetHeight) * 100 : 0)
  
  const transform = isHorizontal 
    ? `translateX(${translateValue}%)`
    : `translateY(${translateValue}%)`
  
  return {
    transform,
    transition: isTransitioning.value ? 'transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)' : 'none',
    display: 'flex',
    flexDirection: isHorizontal ? 'row' as const : 'column' as const,
    width: isHorizontal ? `${props.itemCount * 100}%` : '100%',
    height: isHorizontal ? '100%' : `${props.itemCount * 100}%`
  } as const
})

// 触摸开始
const handleTouchStart = (e: TouchEvent) => {
  if (isTransitioning.value) return
  
  const touch = e.touches[0]
  touchState.value = {
    startX: touch.clientX,
    startY: touch.clientY,
    currentX: touch.clientX,
    currentY: touch.clientY,
    deltaX: 0,
    deltaY: 0,
    startTime: Date.now(),
    isScrolling: false
  }
  
  isSwiping.value = true
  hasInteracted.value = true
  stopAutoplay()
  emit('swipeStart')
}

// 触摸移动
const handleTouchMove = (e: TouchEvent) => {
  if (!isSwiping.value || isTransitioning.value) return
  
  const touch = e.touches[0]
  touchState.value.currentX = touch.clientX
  touchState.value.currentY = touch.clientY
  touchState.value.deltaX = touch.clientX - touchState.value.startX
  touchState.value.deltaY = touch.clientY - touchState.value.startY
  
  const isHorizontal = props.direction === 'horizontal'
  const primaryDelta = isHorizontal ? Math.abs(touchState.value.deltaX) : Math.abs(touchState.value.deltaY)
  const secondaryDelta = isHorizontal ? Math.abs(touchState.value.deltaY) : Math.abs(touchState.value.deltaX)
  
  // 判断滑动方向
  if (!touchState.value.isScrolling && primaryDelta > 10) {
    touchState.value.isScrolling = primaryDelta > secondaryDelta
  }
  
  // 如果是主方向滑动，阻止默认行为
  if (touchState.value.isScrolling) {
    e.preventDefault()
    
    // 应用阻力效果
    const delta = isHorizontal ? touchState.value.deltaX : touchState.value.deltaY
    const maxDelta = isHorizontal ? containerRef.value!.offsetWidth : containerRef.value!.offsetHeight
    
    if ((currentIndex.value === 0 && delta > 0) || 
        (currentIndex.value === props.itemCount - 1 && delta < 0)) {
      if (!props.loop) {
        const resistance = props.resistance
        if (isHorizontal) {
          touchState.value.deltaX = delta * resistance
        } else {
          touchState.value.deltaY = delta * resistance
        }
      }
    }
  }
}

// 触摸结束
const handleTouchEnd = (e: TouchEvent) => {
  if (!isSwiping.value) return
  
  const isHorizontal = props.direction === 'horizontal'
  const delta = isHorizontal ? touchState.value.deltaX : touchState.value.deltaY
  const velocity = Math.abs(delta) / (Date.now() - touchState.value.startTime)
  const threshold = props.threshold
  
  let shouldSwipe = false
  let direction = ''
  
  // 判断是否应该切换
  if (Math.abs(delta) > threshold || velocity > 0.3) {
    shouldSwipe = true
    if (isHorizontal) {
      direction = delta > 0 ? 'right' : 'left'
    } else {
      direction = delta > 0 ? 'down' : 'up'
    }
  }
  
  if (shouldSwipe) {
    if (isHorizontal) {
      if (direction === 'left' && (currentIndex.value < props.itemCount - 1 || props.loop)) {
        next()
        emit('swipeLeft')
      } else if (direction === 'right' && (currentIndex.value > 0 || props.loop)) {
        prev()
        emit('swipeRight')
      }
    } else {
      if (direction === 'up' && (currentIndex.value < props.itemCount - 1 || props.loop)) {
        next()
        emit('swipeUp')
      } else if (direction === 'down' && (currentIndex.value > 0 || props.loop)) {
        prev()
        emit('swipeDown')
      }
    }
  }
  
  // 重置状态
  isSwiping.value = false
  isTransitioning.value = true
  
  setTimeout(() => {
    isTransitioning.value = false
    startAutoplay()
  }, 300)
  
  emit('swipeEnd')
}

// 触摸取消
const handleTouchCancel = () => {
  isSwiping.value = false
  isTransitioning.value = true
  
  setTimeout(() => {
    isTransitioning.value = false
    startAutoplay()
  }, 300)
}

// 下一页
const next = () => {
  if (currentIndex.value < props.itemCount - 1) {
    currentIndex.value++
  } else if (props.loop) {
    currentIndex.value = 0
  }
  emit('change', currentIndex.value)
}

// 上一页
const prev = () => {
  if (currentIndex.value > 0) {
    currentIndex.value--
  } else if (props.loop) {
    currentIndex.value = props.itemCount - 1
  }
  emit('change', currentIndex.value)
}

// 跳转到指定页面
const goToSlide = (index: number) => {
  if (index >= 0 && index < props.itemCount && index !== currentIndex.value) {
    currentIndex.value = index
    isTransitioning.value = true
    
    setTimeout(() => {
      isTransitioning.value = false
    }, 300)
    
    emit('change', currentIndex.value)
  }
}

// 开始自动播放
const startAutoplay = () => {
  if (props.autoplay && props.itemCount > 1) {
    autoplayTimer = setInterval(() => {
      next()
    }, props.autoplayInterval)
  }
}

// 停止自动播放
const stopAutoplay = () => {
  if (autoplayTimer) {
    clearInterval(autoplayTimer)
    autoplayTimer = null
  }
}

// 处理窗口大小变化
const handleResize = debounce(() => {
  // 重新计算位置
  nextTick(() => {
    isTransitioning.value = true
    setTimeout(() => {
      isTransitioning.value = false
    }, 100)
  })
}, 250)

// 暴露方法给父组件
defineExpose({
  next,
  prev,
  goToSlide,
  getCurrentIndex: () => currentIndex.value,
  startAutoplay,
  stopAutoplay
})

onMounted(() => {
  startAutoplay()
  window.addEventListener('resize', handleResize)
  
  // 隐藏提示
  if (props.showHint) {
    setTimeout(() => {
      hasInteracted.value = true
    }, 5000)
  }
})

onUnmounted(() => {
  stopAutoplay()
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.swipe-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  touch-action: pan-y; /* 允许垂直滚动，禁止水平滚动 */
}

.swipe-container.swiping {
  user-select: none;
}

.swipe-wrapper {
  width: 100%;
  height: 100%;
  will-change: transform;
}

.swipe-indicators {
  position: absolute;
  bottom: 16px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8px;
  z-index: 10;
}

.indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
}

.indicator-active {
  background: #667eea;
  transform: scale(1.2);
}

.swipe-hint {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 12px 20px;
  border-radius: 20px;
  font-size: 14px;
  z-index: 20;
  animation: hint-fade-in 0.5s ease, hint-fade-out 0.5s ease 4.5s forwards;
  pointer-events: none;
}

.hint-arrow {
  display: flex;
  align-items: center;
  gap: 8px;
}

.hint-icon {
  width: 16px;
  height: 16px;
  animation: hint-bounce 1s ease-in-out infinite alternate;
}

.hint-text {
  font-weight: 500;
}

@keyframes hint-fade-in {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

@keyframes hint-fade-out {
  from {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  to {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
}

@keyframes hint-bounce {
  from {
    transform: translateX(-2px);
  }
  to {
    transform: translateX(2px);
  }
}

/* 垂直方向适配 */
.swipe-container[data-direction="vertical"] {
  touch-action: pan-x;
}

.swipe-container[data-direction="vertical"] .swipe-indicators {
  bottom: auto;
  right: 16px;
  top: 50%;
  left: auto;
  transform: translateY(-50%);
  flex-direction: column;
}

/* 暗色模式适配 */
@media (prefers-color-scheme: dark) {
  .indicator {
    background: rgba(255, 255, 255, 0.3);
  }
  
  .swipe-hint {
    background: rgba(255, 255, 255, 0.9);
    color: #333;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .swipe-wrapper,
  .indicator,
  .swipe-hint {
    transition: none;
    animation: none;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .indicator {
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid white;
  }
  
  .indicator-active {
    background: white;
    border-color: black;
  }
}
</style>