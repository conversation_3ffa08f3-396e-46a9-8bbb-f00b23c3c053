<template>
  <div class="battery-maintenance">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <el-icon class="title-icon"><Tools /></el-icon>
            电池维护记录
          </h1>
          <p class="page-description">管理电池维护历史，制定维护计划</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="addMaintenanceRecord">
            <el-icon><Plus /></el-icon>
            添加记录
          </el-button>
          <el-button @click="exportRecords">
            <el-icon><Download /></el-icon>
            导出记录
          </el-button>
          <el-button @click="refreshData">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 导航按钮区域 -->
    <div class="navigation-tabs">
      <div class="nav-buttons">
        <router-link to="/battery-management/analysis" class="nav-button">
          <el-button type="primary" plain>
            <el-icon><TrendCharts /></el-icon>
            健康度分析
          </el-button>
        </router-link>
        <el-button type="warning" plain disabled>
          <el-icon><Tools /></el-icon>
          电池维护
        </el-button>
        <router-link to="/battery-management/monitoring" class="nav-button">
          <el-button type="info" plain>
            <el-icon><Monitor /></el-icon>
            实时监控
          </el-button>
        </router-link>
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="stats-section">
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon maintenance">
            <el-icon><Tools /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ totalRecords }}</div>
            <div class="stat-label">总维护记录</div>
            <div class="stat-change positive">+{{ newRecordsThisMonth }} 本月新增</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon pending">
            <el-icon><Clock /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ pendingRecords }}</div>
            <div class="stat-label">待处理</div>
            <div class="stat-change warning">需要关注</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon cost">
            <el-icon><Money /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">¥{{ totalCost }}</div>
            <div class="stat-label">总维护成本</div>
            <div class="stat-change neutral">本月 ¥{{ monthlyCost }}</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon schedule">
            <el-icon><Calendar /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ upcomingMaintenance }}</div>
            <div class="stat-label">即将到期</div>
            <div class="stat-change warning">7天内</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <div class="filter-left">
        <el-input
          v-model="searchQuery"
          placeholder="搜索维护记录（电池ID、技师、描述）"
          @input="handleSearch"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
      <div class="filter-right">
        <el-select v-model="typeFilter" placeholder="维护类型" @change="handleFilter">
          <el-option label="全部类型" value="" />
          <el-option label="定期检查" value="inspection" />
          <el-option label="部件更换" value="replacement" />
          <el-option label="系统校准" value="calibration" />
          <el-option label="故障维修" value="repair" />
        </el-select>
        <el-select v-model="statusFilter" placeholder="状态筛选" @change="handleFilter">
          <el-option label="全部状态" value="" />
          <el-option label="已完成" value="completed" />
          <el-option label="进行中" value="pending" />
          <el-option label="已取消" value="cancelled" />
        </el-select>
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleFilter"
        />
      </div>
    </div>

    <!-- 维护记录列表 -->
    <div class="maintenance-list">
      <div class="list-header">
        <h3>维护记录</h3>
        <div class="view-controls">
          <el-button-group size="small">
            <el-button 
              :type="viewMode === 'timeline' ? 'primary' : ''"
              @click="viewMode = 'timeline'"
            >
              <el-icon><Clock /></el-icon>
              时间线
            </el-button>
            <el-button 
              :type="viewMode === 'table' ? 'primary' : ''"
              @click="viewMode = 'table'"
            >
              <el-icon><List /></el-icon>
              表格
            </el-button>
          </el-button-group>
        </div>
      </div>

      <!-- 时间线视图 -->
      <div v-if="viewMode === 'timeline'" class="timeline-view">
        <el-timeline>
          <el-timeline-item
            v-for="record in filteredRecords"
            :key="record.id"
            :timestamp="formatDate(record.date)"
            :type="getTimelineType(record.status)"
            :icon="getTimelineIcon(record.type)"
            placement="top"
          >
            <div class="timeline-card">
              <div class="card-header">
                <div class="card-title">
                  <h4>{{ record.title }}</h4>
                  <el-tag :type="getStatusType(record.status)" size="small">
                    {{ getStatusText(record.status) }}
                  </el-tag>
                </div>
                <div class="card-meta">
                  <span class="battery-id">电池: {{ record.battery_id }}</span>
                  <span class="technician">技师: {{ record.technician }}</span>
                  <span class="cost">费用: ¥{{ record.cost }}</span>
                </div>
              </div>
              <div class="card-content">
                <p>{{ record.description }}</p>
              </div>
              <div class="card-actions">
                <el-button size="small" @click="viewRecord(record)">
                  详情
                </el-button>
                <el-button size="small" @click="editRecord(record)">
                  编辑
                </el-button>
                <el-button 
                  size="small" 
                  type="danger" 
                  @click="deleteRecord(record.id)"
                >
                  删除
                </el-button>
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>

      <!-- 表格视图 -->
      <div v-else class="table-view">
        <el-table 
          :data="filteredRecords" 
          style="width: 100%" 
          v-loading="loading"
          element-loading-text="加载中..."
        >
          <el-table-column prop="date" label="日期" width="120" />
          <el-table-column prop="battery_id" label="电池ID" width="120" />
          <el-table-column prop="title" label="维护项目" width="150" />
          <el-table-column prop="type" label="类型" width="100">
            <template #default="{ row }">
              <el-tag size="small">{{ getTypeText(row.type) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="technician" label="技师" width="100" />
          <el-table-column prop="cost" label="费用" width="100">
            <template #default="{ row }">
              ¥{{ row.cost }}
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)" size="small">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="description" label="描述" min-width="200" />
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <div class="table-actions">
                <el-button size="small" @click="viewRecord(row)">
                  详情
                </el-button>
                <el-button size="small" @click="editRecord(row)">
                  编辑
                </el-button>
                <el-button 
                  size="small" 
                  type="danger" 
                  @click="deleteRecord(row.id)"
                >
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 空状态 -->
      <div v-if="filteredRecords.length === 0 && !loading" class="empty-state">
        <el-empty description="暂无维护记录">
          <el-button type="primary" @click="addMaintenanceRecord">
            添加第一条记录
          </el-button>
        </el-empty>
      </div>
    </div>

    <!-- 维护记录表单对话框 -->
    <!-- <MaintenanceForm
      v-model="showFormDialog"
      :record-id="editingRecordId"
      :mode="formMode"
      @success="handleFormSuccess"
      @close="showFormDialog = false"
    /> -->

    <!-- 维护记录详情对话框 -->
    <!-- <MaintenanceDetail
      v-model="showDetailDialog"
      :record-id="selectedRecordId"
      @close="showDetailDialog = false"
    /> -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Tools, Plus, Download, Refresh, Clock, Money, Calendar,
  Search, List, TrendCharts, Monitor
} from '@element-plus/icons-vue'
import { useBatteryStore } from '@/stores/battery'
// import MaintenanceForm from '@/components/MaintenanceForm.vue'
// import MaintenanceDetail from '@/components/MaintenanceDetail.vue'

// Store
const batteryStore = useBatteryStore()

// 响应式数据
const loading = ref(false)
const searchQuery = ref('')
const typeFilter = ref('')
const statusFilter = ref('')
const dateRange = ref<[Date, Date] | null>(null)
const viewMode = ref<'timeline' | 'table'>('timeline')
const showFormDialog = ref(false)
const showDetailDialog = ref(false)
const selectedRecordId = ref('')
const editingRecordId = ref('')
const formMode = ref<'add' | 'edit'>('add')

// 模拟数据
const maintenanceRecords = ref([
  {
    id: '1',
    battery_id: 'battery-001',
    type: 'inspection',
    title: '定期健康检查',
    description: '检查电池各项参数，测试充放电性能，一切正常',
    date: '2024-01-15',
    cost: 200,
    technician: '李师傅',
    status: 'completed',
    created_at: '2024-01-15T10:00:00Z'
  },
  {
    id: '2',
    battery_id: 'battery-002',
    type: 'replacement',
    title: '电芯模块更换',
    description: '更换老化的电芯模块，提升整体性能',
    date: '2023-12-08',
    cost: 1500,
    technician: '王工程师',
    status: 'completed',
    created_at: '2023-12-08T14:30:00Z'
  },
  {
    id: '3',
    battery_id: 'battery-003',
    type: 'calibration',
    title: 'BMS系统校准',
    description: '重新校准电池管理系统，优化充放电策略',
    date: '2023-11-20',
    cost: 300,
    technician: '张技师',
    status: 'completed',
    created_at: '2023-11-20T16:45:00Z'
  },
  {
    id: '4',
    battery_id: 'battery-004',
    type: 'repair',
    title: '温度传感器维修',
    description: '修复温度传感器故障，恢复正常监控',
    date: '2024-01-20',
    cost: 500,
    technician: '赵师傅',
    status: 'pending',
    created_at: '2024-01-20T09:00:00Z'
  }
])

// 计算属性
const totalRecords = computed(() => maintenanceRecords.value.length)
const newRecordsThisMonth = computed(() => 2)
const pendingRecords = computed(() => 
  maintenanceRecords.value.filter(r => r.status === 'pending').length
)
const totalCost = computed(() => 
  maintenanceRecords.value.reduce((sum, r) => sum + r.cost, 0)
)
const monthlyCost = computed(() => 700)
const upcomingMaintenance = computed(() => 3)

const filteredRecords = computed(() => {
  let result = maintenanceRecords.value
  
  // 搜索筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(record => 
      record.battery_id.toLowerCase().includes(query) ||
      record.technician.toLowerCase().includes(query) ||
      record.description.toLowerCase().includes(query) ||
      record.title.toLowerCase().includes(query)
    )
  }
  
  // 类型筛选
  if (typeFilter.value) {
    result = result.filter(record => record.type === typeFilter.value)
  }
  
  // 状态筛选
  if (statusFilter.value) {
    result = result.filter(record => record.status === statusFilter.value)
  }
  
  // 日期筛选
  if (dateRange.value) {
    const [start, end] = dateRange.value
    result = result.filter(record => {
      const recordDate = new Date(record.date)
      return recordDate >= start && recordDate <= end
    })
  }
  
  return result.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
})

// 方法
const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const handleFilter = () => {
  // 筛选逻辑已在计算属性中处理
}

const addMaintenanceRecord = () => {
  formMode.value = 'add'
  editingRecordId.value = ''
  showFormDialog.value = true
}

const editRecord = (record: any) => {
  formMode.value = 'edit'
  editingRecordId.value = record.id
  showFormDialog.value = true
}

const viewRecord = (record: any) => {
  selectedRecordId.value = record.id
  showDetailDialog.value = true
}

const deleteRecord = async (recordId: string) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这条维护记录吗？此操作不可恢复。',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 删除记录
    const index = maintenanceRecords.value.findIndex(r => r.id === recordId)
    if (index !== -1) {
      maintenanceRecords.value.splice(index, 1)
      ElMessage.success('维护记录删除成功')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除维护记录失败')
    }
  }
}

const exportRecords = () => {
  ElMessage.info('记录导出功能开发中')
}

const refreshData = () => {
  ElMessage.success('数据刷新成功')
}

const handleFormSuccess = () => {
  showFormDialog.value = false
  ElMessage.success(formMode.value === 'add' ? '维护记录添加成功' : '维护记录更新成功')
}

// 辅助方法
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    'completed': 'success',
    'pending': 'warning',
    'cancelled': 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    'completed': '已完成',
    'pending': '进行中',
    'cancelled': '已取消'
  }
  return texts[status] || '未知'
}

const getTypeText = (type: string) => {
  const texts: Record<string, string> = {
    'inspection': '定期检查',
    'replacement': '部件更换',
    'calibration': '系统校准',
    'repair': '故障维修'
  }
  return texts[type] || '其他'
}

const getTimelineType = (status: string) => {
  const types: Record<string, string> = {
    'completed': 'success',
    'pending': 'warning',
    'cancelled': 'danger'
  }
  return types[status] || 'primary'
}

const getTimelineIcon = (type: string) => {
  // 返回对应的图标组件
  return Tools
}

// 生命周期
onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
.battery-maintenance {
  padding: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  position: relative;
}

.battery-maintenance::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 50%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
  pointer-events: none;
}

/* 页面头部 */
.page-header {
  position: relative;
  z-index: 1;
  padding: 32px 24px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  color: white;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 36px;
  color: #fbbf24;
}

.page-description {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 统计卡片 */
.stats-section {
  position: relative;
  z-index: 1;
  padding: 0 24px 32px;
  max-width: 1200px;
  margin: 0 auto;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.maintenance {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

.stat-icon.pending {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.stat-icon.cost {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.stat-icon.schedule {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: #111827;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 4px;
}

.stat-change {
  font-size: 12px;
  font-weight: 500;
}

.stat-change.positive {
  color: #10b981;
}

.stat-change.warning {
  color: #f59e0b;
}

.stat-change.neutral {
  color: #6b7280;
}

/* 筛选区域 */
.filter-section {
  position: relative;
  z-index: 1;
  padding: 0 24px 24px;
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.filter-left {
  flex: 1;
  max-width: 400px;
}

.filter-right {
  display: flex;
  gap: 12px;
}

/* 维护记录列表 */
.maintenance-list {
  position: relative;
  z-index: 1;
  padding: 0 24px 32px;
  max-width: 1200px;
  margin: 0 auto;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.list-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: white;
  margin: 0;
}

/* 时间线视图 */
.timeline-view {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 24px;
}

.timeline-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  margin-bottom: 16px;
}

.card-header {
  margin-bottom: 12px;
}

.card-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.card-title h4 {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.card-meta {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: #6b7280;
}

.card-content p {
  font-size: 14px;
  color: #374151;
  line-height: 1.5;
  margin: 0 0 16px 0;
}

.card-actions {
  display: flex;
  gap: 8px;
}

/* 表格视图 */
.table-view {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  overflow: hidden;
}

.table-actions {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

/* 空状态 */
.empty-state {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 60px 24px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .header-actions {
    flex-direction: column;
    gap: 8px;
    width: 100%;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }

  .filter-section {
    flex-direction: column;
    gap: 12px;
  }

  .filter-right {
    width: 100%;
    flex-wrap: wrap;
    justify-content: space-between;
  }

  .list-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .card-meta {
    flex-direction: column;
    gap: 4px;
  }

  .table-actions {
    flex-direction: column;
    gap: 4px;
  }
}

/* 导航按钮样式 */
.navigation-tabs {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.nav-buttons {
  display: flex;
  gap: 16px;
  align-items: center;
}

.nav-button {
  text-decoration: none;
}

.nav-button .el-button {
  height: 40px;
  padding: 0 20px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.nav-button .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.nav-button .el-button .el-icon {
  margin-right: 8px;
}

/* 当前页面按钮样式 */
.nav-buttons .el-button[disabled] {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  opacity: 1;
  cursor: default;
}
</style>
