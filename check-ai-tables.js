const mysql = require('mysql2/promise');

async function checkTables() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: '123456',
    database: 'ev_management'
  });
  
  try {
    console.log('检查ai_chat_logs表结构:');
    const [rows] = await connection.execute('DESCRIBE ai_chat_logs');
    console.table(rows);
    
    console.log('\n检查是否存在ai_assistant_config表:');
    const [tables] = await connection.execute('SHOW TABLES LIKE "ai_assistant_config"');
    console.log('ai_assistant_config表存在:', tables.length > 0);
    
    if (tables.length === 0) {
      console.log('\n创建ai_assistant_config表...');
      await connection.execute(`
        CREATE TABLE ai_assistant_config (
          id INT AUTO_INCREMENT PRIMARY KEY,
          name VARCHAR(100) NOT NULL DEFAULT '小E',
          full_name VARCHAR(200) NOT NULL DEFAULT 'EVAdmin Pro AI Assistant',
          description TEXT NOT NULL,
          avatar_url VARCHAR(500) DEFAULT '/images/ai-avatar.svg',
          model VARCHAR(100) DEFAULT 'gpt-4o',
          temperature DECIMAL(3,2) DEFAULT 0.7,
          max_tokens INT DEFAULT 2000,
          capabilities JSON,
          system_prompt TEXT,
          is_active BOOLEAN DEFAULT TRUE,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      `);
      
      await connection.execute(`
        INSERT INTO ai_assistant_config (
          name, 
          full_name, 
          description, 
          avatar_url, 
          capabilities,
          system_prompt
        ) VALUES (
          '小E',
          'EVAdmin Pro AI Assistant',
          '我是EVAdmin Pro的专属AI助手，专门为新能源汽车管理系统提供智能服务。我可以帮您解答系统使用问题、分析数据、处理文件，还能识别图片和视频内容。',
          '/images/ai-avatar.svg',
          JSON_ARRAY(
            '文本对话和问答',
            '文件内容分析', 
            '图片和视频识别',
            '系统功能指导',
            '数据分析建议',
            '故障诊断帮助'
          ),
          '你是EVAdmin Pro新能源汽车智能综合管理系统的专属AI助手，名字叫"小E"。你需要以友好、专业的态度帮助用户使用系统，回答问题，分析数据。'
        )
      `);
      
      console.log('✅ ai_assistant_config表创建成功');
    }
    
  } catch (error) {
    console.error('检查失败:', error.message);
  } finally {
    await connection.end();
  }
}

checkTables();
