import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api, { apiWithRetry } from '@/utils/api'

export interface Battery {
  id: string
  vehicle_id?: string
  serial_number: string
  manufacturer: string
  model: string
  battery_type: string
  capacity: number
  voltage: number
  soc: number
  soh: number
  cycle_count: number
  temperature: number
  health_status: 'good' | 'warning' | 'critical'
  last_maintenance?: string
  installation_date: string
  warranty_expiry: string
  created_at: string
  updated_at: string
  vehicle?: {
    id: string
    brand: string
    model: string
    vin: string
  }
}

interface BatteryTrace {
  id: string
  battery_id: string
  event_type: string
  event_time: string
  location: string
  operator: string
  description: string
  metadata: Record<string, any>
  created_at: string
  serial_number: string
  health_status: string
  trace_events: Array<{
    id: string
    event_type: string
    event_time: string
    location: string
    operator: string
    description: string
  }>
}

interface BatteryStats {
  total_batteries: number
  healthy_batteries: number
  warning_batteries: number
  critical_batteries: number
  avg_soc: number
  avg_soh: number
  avg_temperature: number
}

interface CreateBatteryData {
  vehicle_id: string
  serial_number: string
  manufacturer: string
  model: string
  battery_type: string
  capacity: number
  voltage: number
  installation_date: string
  warranty_expiry: string
}

interface UpdateBatteryData {
  soc?: number
  soh?: number
  cycle_count?: number
  temperature?: number
  health_status?: 'good' | 'warning' | 'critical'
  last_maintenance?: string
}

// 使用全局API实例，不需要单独的BASE_URL

export const useBatteryStore = defineStore('battery', () => {
  // 状态
  const batteries = ref<Battery[]>([])
  const currentBattery = ref<Battery | null>(null)
  const batteryTraces = ref<BatteryTrace[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 初始化模拟数据
  const initializeMockData = () => {
    if (batteries.value.length === 0) {
      loadMockBatteries()
    }
  }

  // 计算属性
  const batteryStats = computed((): BatteryStats => {
    const total = batteries.value.length
    const healthy = batteries.value.filter(b => b.health_status === 'good').length
    const warning = batteries.value.filter(b => b.health_status === 'warning').length
    const critical = batteries.value.filter(b => b.health_status === 'critical').length
    
    const avgSoc = total > 0 
      ? batteries.value.reduce((sum, b) => sum + (b.soc || 0), 0) / total 
      : 0
    
    const avgSoh = total > 0 
      ? batteries.value.reduce((sum, b) => sum + (b.soh || 0), 0) / total 
      : 0
    
    const avgTemperature = total > 0 
      ? batteries.value.reduce((sum, b) => sum + (b.temperature || 0), 0) / total 
      : 0

    return {
      total_batteries: total,
      healthy_batteries: healthy,
      warning_batteries: warning,
      critical_batteries: critical,
      avg_soc: Math.round(avgSoc * 100) / 100,
      avg_soh: Math.round(avgSoh * 100) / 100,
      avg_temperature: Math.round(avgTemperature * 100) / 100
    }
  })

  const healthyBatteries = computed(() => 
    batteries.value.filter(b => b.health_status === 'good')
  )

  const warningBatteries = computed(() => 
    batteries.value.filter(b => b.health_status === 'warning')
  )

  const criticalBatteries = computed(() => 
    batteries.value.filter(b => b.health_status === 'critical')
  )

  // 操作方法
  const setLoading = (value: boolean) => {
    loading.value = value
  }

  const setError = (message: string | null) => {
    error.value = message
  }

  const clearError = () => {
    error.value = null
  }

  // API 调用方法
  const fetchBatteries = async (params?: {
    vehicle_id?: string
    manufacturer?: string
    battery_type?: string
    health_status?: string
    capacity_min?: number
    capacity_max?: number
    page?: number
    limit?: number
  }) => {
    setLoading(true)
    clearError()

    try {
      const response = await apiWithRetry.get('/batteries', { params, retry: 3 })

      if (response.data.success) {
        batteries.value = response.data.data
      } else {
        throw new Error(response.data.message || '获取电池列表失败')
      }
    } catch (err: any) {
      const message = err.message || '获取电池列表失败'
      setError(message)
      // 加载模拟数据作为后备
      loadMockBatteries()
    } finally {
      setLoading(false)
    }
  }

  // 加载模拟数据
  const loadMockBatteries = () => {
    batteries.value = [
      {
        id: 'battery-001',
        vehicle_id: 'vehicle-001',
        serial_number: 'CATL-NCM811-001',
        manufacturer: '宁德时代',
        model: 'NCM811-75kWh',
        battery_type: 'NCM',
        capacity: 75.5,
        voltage: 385.2,
        soc: 85,
        soh: 96,
        cycle_count: 245,
        temperature: 25,
        health_status: 'good',
        last_maintenance: '2024-01-15',
        installation_date: '2023-02-01',
        warranty_expiry: '2031-02-01',
        created_at: '2023-02-01T08:00:00Z',
        updated_at: new Date().toISOString(),
        vehicle: {
          id: 'vehicle-001',
          brand: 'Tesla',
          model: 'Model 3',
          vin: 'TSLA123456789'
        }
      },
      {
        id: 'battery-002',
        vehicle_id: 'vehicle-002',
        serial_number: 'BYD-LFP-002',
        manufacturer: '比亚迪',
        model: 'LFP-60kWh',
        battery_type: 'LFP',
        capacity: 60.0,
        voltage: 375.8,
        soc: 65,
        soh: 88,
        cycle_count: 456,
        temperature: 32,
        health_status: 'warning',
        last_maintenance: '2023-12-08',
        installation_date: '2022-09-05',
        warranty_expiry: '2030-09-05',
        created_at: '2022-09-05T10:30:00Z',
        updated_at: new Date().toISOString(),
        vehicle: {
          id: 'vehicle-002',
          brand: 'BYD',
          model: 'Han EV',
          vin: 'BYD987654321'
        }
      },
      {
        id: 'battery-003',
        vehicle_id: 'vehicle-003',
        serial_number: 'GOTION-NCM523-003',
        manufacturer: '国轩高科',
        model: 'NCM523-55kWh',
        battery_type: 'NCM',
        capacity: 55.2,
        voltage: 365.4,
        soc: 45,
        soh: 72,
        cycle_count: 789,
        temperature: 38,
        health_status: 'critical',
        last_maintenance: '2023-11-20',
        installation_date: '2022-01-15',
        warranty_expiry: '2030-01-15',
        created_at: '2022-01-15T14:20:00Z',
        updated_at: new Date().toISOString(),
        vehicle: {
          id: 'vehicle-003',
          brand: 'NIO',
          model: 'ES6',
          vin: 'NIO555666777'
        }
      },
      {
        id: 'battery-004',
        serial_number: 'AVIC-NCM622-004',
        manufacturer: '中航锂电',
        model: 'NCM622-80kWh',
        battery_type: 'NCM',
        capacity: 80.1,
        voltage: 390.6,
        soc: 92,
        soh: 94,
        cycle_count: 123,
        temperature: 28,
        health_status: 'good',
        last_maintenance: '2024-01-10',
        installation_date: '2023-06-15',
        warranty_expiry: '2031-06-15',
        created_at: '2023-06-15T09:15:00Z',
        updated_at: new Date().toISOString()
      }
    ]
  }

  const fetchBatteryById = async (id: string) => {
    setLoading(true)
    clearError()

    try {
      const response = await apiWithRetry.get(`/batteries/${id}`, { retry: 2 })

      if (response.data.success) {
        currentBattery.value = response.data.data
        return response.data.data
      } else {
        throw new Error(response.data.message || '获取电池信息失败')
      }
    } catch (err: any) {
      const message = err.message || '获取电池信息失败'
      setError(message)
      // 从本地数据中查找
      const battery = batteries.value.find(b => b.id === id)
      if (battery) {
        currentBattery.value = battery
        return battery
      }
      throw new Error(message)
    } finally {
      setLoading(false)
    }
  }

  const createBattery = async (data: CreateBatteryData) => {
    setLoading(true)
    clearError()
    
    try {
      const response = await apiWithRetry.post('/batteries', data, { retry: 1 })
      
      if (response.data.success) {
        const newBattery = response.data.data
        batteries.value.push(newBattery)
        return newBattery
      } else {
        throw new Error(response.data.message || '创建电池记录失败')
      }
    } catch (err: any) {
      const message = err.message || '创建电池记录失败'
      setError(message)
      throw new Error(message)
    } finally {
      setLoading(false)
    }
  }

  const updateBattery = async (id: string, data: UpdateBatteryData) => {
    setLoading(true)
    clearError()
    
    try {
      const response = await apiWithRetry.put(`/batteries/${id}`, data, { retry: 1 })
      
      if (response.data.success) {
        const updatedBattery = response.data.data
        const index = batteries.value.findIndex(b => b.id === id)
        if (index !== -1) {
          batteries.value[index] = updatedBattery
        }
        if (currentBattery.value?.id === id) {
          currentBattery.value = updatedBattery
        }
        return updatedBattery
      } else {
        throw new Error(response.data.message || '更新电池信息失败')
      }
    } catch (err: any) {
      const message = err.message || '更新电池信息失败'
      setError(message)
      throw new Error(message)
    } finally {
      setLoading(false)
    }
  }

  const deleteBattery = async (id: string) => {
    setLoading(true)
    clearError()
    
    try {
      const response = await apiWithRetry.delete(`/batteries/${id}`, { retry: 1 })
      
      if (response.data.success) {
        batteries.value = batteries.value.filter(b => b.id !== id)
        if (currentBattery.value?.id === id) {
          currentBattery.value = null
        }
      } else {
        throw new Error(response.data.message || '删除电池记录失败')
      }
    } catch (err: any) {
      const message = err.message || '删除电池记录失败'
      setError(message)
      throw new Error(message)
    } finally {
      setLoading(false)
    }
  }

  const getBatteryTrace = async (query: string) => {
    setLoading(true)
    clearError()
    
    try {
      const response = await apiWithRetry.get('/batteries/trace', {
        params: { query },
        retry: 2
      })
      
      if (response.data.success) {
        return response.data.data
      } else {
        throw new Error(response.data.message || '获取电池溯源信息失败')
      }
    } catch (err: any) {
      const message = err.message || '获取电池溯源信息失败'
      setError(message)
      throw new Error(message)
    } finally {
      setLoading(false)
    }
  }

  const fetchBatteryTraces = async (batteryId: string, params?: {
    event_type?: string
    start_date?: string
    end_date?: string
    page?: number
    limit?: number
  }) => {
    setLoading(true)
    clearError()
    
    try {
      const response = await apiWithRetry.get(`/batteries/${batteryId}/trace`, { params, retry: 2 })
      
      if (response.data.success) {
        batteryTraces.value = response.data.data
        return response.data.data
      } else {
        throw new Error(response.data.message || '获取电池溯源记录失败')
      }
    } catch (err: any) {
      const message = err.message || '获取电池溯源记录失败'
      setError(message)
      throw new Error(message)
    } finally {
      setLoading(false)
    }
  }

  const addBatteryTrace = async (batteryId: string, data: {
    event_type: string
    location: string
    operator: string
    description: string
    metadata?: Record<string, any>
  }) => {
    setLoading(true)
    clearError()
    
    try {
      const response = await apiWithRetry.post(`/batteries/${batteryId}/trace`, data, { retry: 1 })
      
      if (response.data.success) {
        const newTrace = response.data.data
        batteryTraces.value.push(newTrace)
        return newTrace
      } else {
        throw new Error(response.data.message || '添加溯源记录失败')
      }
    } catch (err: any) {
      const message = err.message || '添加溯源记录失败'
      setError(message)
      throw new Error(message)
    } finally {
      setLoading(false)
    }
  }

  const getBatteryHealthStats = async (batteryId: string) => {
    setLoading(true)
    clearError()

    try {
      const response = await apiWithRetry.get(`/batteries/${batteryId}/health-stats`, { retry: 2 })

      if (response.data.success) {
        return response.data.data
      } else {
        throw new Error(response.data.message || '获取电池健康统计失败')
      }
    } catch (err: any) {
      const message = err.message || '获取电池健康统计失败'
      setError(message)
      throw new Error(message)
    } finally {
      setLoading(false)
    }
  }

  const batchUpdateBatteries = async (updates: Array<{
    id: string
    data: UpdateBatteryData
  }>) => {
    setLoading(true)
    clearError()

    try {
      const response = await apiWithRetry.put('/batteries/batch', { updates }, { retry: 1 })

      if (response.data.success) {
        // 更新本地状态
        updates.forEach(update => {
          const index = batteries.value.findIndex(b => b.id === update.id)
          if (index !== -1) {
            batteries.value[index] = { ...batteries.value[index], ...update.data }
          }
        })
        return response.data.data
      } else {
        throw new Error(response.data.message || '批量更新电池状态失败')
      }
    } catch (err: any) {
      const message = err.message || '批量更新电池状态失败'
      setError(message)
      throw new Error(message)
    } finally {
      setLoading(false)
    }
  }

  // 重置状态
  const resetState = () => {
    batteries.value = []
    currentBattery.value = null
    batteryTraces.value = []
    loading.value = false
    error.value = null
  }

  return {
    // 状态
    batteries,
    currentBattery,
    batteryTraces,
    loading,
    error,

    // 计算属性
    batteryStats,
    healthyBatteries,
    warningBatteries,
    criticalBatteries,

    // 方法
    setLoading,
    setError,
    clearError,
    fetchBatteries,
    fetchBatteryById,
    createBattery,
    updateBattery,
    deleteBattery,
    getBatteryTrace,
    fetchBatteryTraces,
    addBatteryTrace,
    getBatteryHealthStats,
    batchUpdateBatteries,
    loadMockBatteries,
    initializeMockData,
    resetState
  }
})