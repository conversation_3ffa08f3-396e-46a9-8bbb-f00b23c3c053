import { Router } from 'express'
import { db } from '../config/mysql'
import type { ApiResponse } from '../../shared/types'

const router = Router()

// 获取系统状态
router.get('/status', async (req, res) => {
  try {
    // 检查数据库连接状态
    let databaseStatus = false
    try {
      await db.query('SELECT 1')
      databaseStatus = true
    } catch (error) {
      console.error('数据库连接检查失败:', error)
      databaseStatus = false
    }

    // 检查API状态（当前API正在运行，所以为true）
    const apiStatus = true

    // 检查队列状态（简单模拟，实际项目中可以检查Redis或其他队列服务）
    const queueStatus = true

    const systemStatus = {
      api: apiStatus,
      database: databaseStatus,
      queue: queueStatus,
      timestamp: new Date().toISOString(),
      uptime: process.uptime(), // 服务运行时间（秒）
      memory: {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024), // MB
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024) // MB
      },
      cpu: {
        usage: Math.round(Math.random() * 100) // 模拟CPU使用率，实际项目中可以使用os模块获取
      }
    }

    res.json({
      success: true,
      data: systemStatus
    } as ApiResponse)
  } catch (error) {
    console.error('获取系统状态异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取系统健康检查
router.get('/health', async (req, res) => {
  try {
    // 基本健康检查
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      uptime: process.uptime()
    }

    res.json({
      success: true,
      data: health
    } as ApiResponse)
  } catch (error) {
    console.error('健康检查异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取系统统计信息
router.get('/stats', async (req, res) => {
  try {
    // 获取各模块的统计信息
    const vehicleCount = await db.query('SELECT COUNT(*) as count FROM vehicles')
    const userCount = await db.query('SELECT COUNT(*) as count FROM users')
    const stationCount = await db.query('SELECT COUNT(*) as count FROM charging_stations')
    const sessionCount = await db.query('SELECT COUNT(*) as count FROM charging_sessions')

    const stats = {
      vehicles: vehicleCount[0]?.count || 0,
      users: userCount[0]?.count || 0,
      chargingStations: stationCount[0]?.count || 0,
      chargingSessions: sessionCount[0]?.count || 0,
      timestamp: new Date().toISOString()
    }

    res.json({
      success: true,
      data: stats
    } as ApiResponse)
  } catch (error) {
    console.error('获取系统统计异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

export default router