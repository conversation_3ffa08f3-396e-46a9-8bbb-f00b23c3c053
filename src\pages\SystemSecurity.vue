<template>
  <div class="system-security-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">
        <el-icon><Lock /></el-icon>
        系统安全运维
      </h1>
      <p class="page-subtitle">用户权限管理 · 系统监控 · 审计日志 · 安全防护</p>
    </div>

    <!-- 安全概览 -->
    <div class="security-overview">
      <div class="overview-grid">
        <ModernCard variant="glassmorphism" :hover-effect="true" class="overview-card">
          <div class="overview-item">
            <div class="overview-icon security">
              <el-icon><Shield /></el-icon>
            </div>
            <div class="overview-info">
              <h3>{{ securityStats.securityLevel }}</h3>
              <p>安全等级</p>
            </div>
            <div class="overview-status">
              <el-tag :type="getSecurityLevelType(securityStats.securityLevel)" size="small">
                {{ getSecurityLevelText(securityStats.securityLevel) }}
              </el-tag>
            </div>
          </div>
        </ModernCard>
        
        <ModernCard variant="glassmorphism" :hover-effect="true" class="overview-card">
          <div class="overview-item">
            <div class="overview-icon users">
              <el-icon><UserFilled /></el-icon>
            </div>
            <div class="overview-info">
              <h3>{{ securityStats.activeUsers }}</h3>
              <p>在线用户</p>
            </div>
            <div class="overview-status">
              <span class="status-indicator online"></span>
            </div>
          </div>
        </ModernCard>
        
        <ModernCard variant="glassmorphism" :hover-effect="true" class="overview-card">
          <div class="overview-item">
            <div class="overview-icon threats">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="overview-info">
              <h3>{{ securityStats.threatCount }}</h3>
              <p>威胁检测</p>
            </div>
            <div class="overview-status">
              <el-tag :type="securityStats.threatCount > 0 ? 'danger' : 'success'" size="small">
                {{ securityStats.threatCount > 0 ? '有威胁' : '安全' }}
              </el-tag>
            </div>
          </div>
        </ModernCard>
        
        <ModernCard variant="glassmorphism" :hover-effect="true" class="overview-card">
          <div class="overview-item">
            <div class="overview-icon logs">
              <el-icon><Document /></el-icon>
            </div>
            <div class="overview-info">
              <h3>{{ securityStats.todayLogs }}</h3>
              <p>今日日志</p>
            </div>
            <div class="overview-status">
              <span class="log-count">{{ securityStats.todayLogs }}</span>
            </div>
          </div>
        </ModernCard>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧：用户权限管理 -->
      <div class="permissions-section">
        <!-- 用户管理 -->
        <ModernCard variant="glassmorphism" :hover-effect="true" class="user-management-card">
          <template #header>
            <div class="card-header">
              <span>用户权限管理</span>
              <div class="header-actions">
                <ModernButton variant="primary" size="small" @click="addUser">
                  <el-icon><Plus /></el-icon>
                  添加用户
                </ModernButton>
                <ModernButton variant="ghost" @click="refreshUsers">
                  <el-icon><Refresh /></el-icon>
                  刷新
                </ModernButton>
              </div>
            </div>
          </template>
          <div class="user-management-content">
            <!-- 搜索和筛选 -->
            <div class="search-filters">
              <ModernInput
                v-model="userSearch"
                placeholder="搜索用户名或邮箱"
                prefix-icon="Search"
                size="small"
                style="width: 200px;"
                variant="glass"
                @input="filterUsers"
              />
              <el-select v-model="roleFilter" placeholder="角色筛选" size="small" style="width: 120px;" @change="filterUsers">
                <el-option label="全部" value="" />
                <el-option label="管理员" value="admin" />
                <el-option label="操作员" value="operator" />
                <el-option label="用户" value="user" />
              </el-select>
              <el-select v-model="statusFilter" placeholder="状态筛选" size="small" style="width: 120px;" @change="filterUsers">
                <el-option label="全部" value="" />
                <el-option label="活跃" value="active" />
                <el-option label="禁用" value="disabled" />
                <el-option label="锁定" value="locked" />
              </el-select>
            </div>

            <!-- 用户列表 -->
            <div class="user-list">
              <el-table :data="filteredUsers" style="width: 100%" size="small">
                <el-table-column prop="avatar" label="头像" width="60">
                  <template #default="{ row }">
                    <el-avatar v-if="row" :size="30" :src="row.avatar">
                      <el-icon><User /></el-icon>
                    </el-avatar>
                  </template>
                </el-table-column>
                <el-table-column prop="username" label="用户名" width="120" />
                <el-table-column prop="email" label="邮箱" width="180" />
                <el-table-column prop="role" label="角色" width="100">
                  <template #default="{ row }">
                    <el-tag v-if="row" :type="getRoleType(row.role)" size="small">
                      {{ getRoleText(row.role) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" width="80">
                  <template #default="{ row }">
                    <el-tag v-if="row" :type="getStatusType(row.status)" size="small">
                      {{ getStatusText(row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="lastLogin" label="最后登录" width="140" />
                <el-table-column label="操作" width="180">
                  <template #default="{ row }">
                    <template v-if="row">
                      <ModernButton variant="ghost" size="small" @click="editUser(row)">
                        编辑
                      </ModernButton>
                      <ModernButton variant="ghost" size="small" @click="viewPermissions(row)">
                        权限
                      </ModernButton>
                      <ModernButton 
                        :variant="row.status === 'active' ? 'danger' : 'success'" 
                        size="small" 
                        @click="toggleUserStatus(row)"
                      >
                        {{ row.status === 'active' ? '禁用' : '启用' }}
                      </ModernButton>
                    </template>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <!-- 分页 -->
            <div class="pagination">
              <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :page-sizes="[10, 20, 50, 100]"
                :total="totalUsers"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </div>
        </ModernCard>

        <!-- 角色权限配置 -->
        <ModernCard variant="glassmorphism" :hover-effect="true" class="role-permissions-card">
          <template #header>
            <div class="card-header">
              <span>角色权限配置</span>
              <ModernButton variant="primary" size="small" @click="addRole">
                <el-icon><Plus /></el-icon>
                添加角色
              </ModernButton>
            </div>
          </template>
          <div class="role-permissions-content">
            <div class="role-list">
              <div v-for="role in roles" :key="role.id" class="role-item">
                <div class="role-header">
                  <div class="role-info">
                    <h5>{{ role.name }}</h5>
                    <p>{{ role.description }}</p>
                  </div>
                  <div class="role-actions">
                    <ModernButton variant="ghost" size="small" @click="editRole(role)">
                      编辑
                    </ModernButton>
                    <ModernButton variant="danger" size="small" @click="deleteRole(role)">
                      删除
                    </ModernButton>
                  </div>
                </div>
                <div class="role-permissions">
                  <div class="permission-group" v-for="group in role.permissions" :key="group.module">
                    <h6>{{ group.module }}</h6>
                    <div class="permission-tags">
                      <el-tag 
                        v-for="permission in group.actions" 
                        :key="permission"
                        size="small"
                        type="info"
                      >
                        {{ permission }}
                      </el-tag>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ModernCard>
      </div>

      <!-- 右侧：系统监控和审计日志 -->
      <div class="monitoring-section">
        <!-- 系统监控 -->
        <ModernCard variant="glassmorphism" :hover-effect="true" class="system-monitoring-card">
          <template #header>
            <div class="card-header">
              <span>系统监控</span>
              <div class="monitoring-status">
                <span class="status-dot" :class="systemStatus.overall"></span>
                <span class="status-text">{{ getSystemStatusText(systemStatus.overall) }}</span>
              </div>
            </div>
          </template>
          <div class="monitoring-content">
            <!-- 系统指标 -->
            <div class="system-metrics">
              <div class="metric-item">
                <div class="metric-header">
                  <span class="metric-label">CPU使用率</span>
                  <span class="metric-value">{{ systemMetrics.cpu }}%</span>
                </div>
                <div class="metric-bar">
                  <div class="metric-fill" :style="{ width: systemMetrics.cpu + '%' }" :class="getMetricClass(systemMetrics.cpu)"></div>
                </div>
              </div>
              
              <div class="metric-item">
                <div class="metric-header">
                  <span class="metric-label">内存使用率</span>
                  <span class="metric-value">{{ systemMetrics.memory }}%</span>
                </div>
                <div class="metric-bar">
                  <div class="metric-fill" :style="{ width: systemMetrics.memory + '%' }" :class="getMetricClass(systemMetrics.memory)"></div>
                </div>
              </div>
              
              <div class="metric-item">
                <div class="metric-header">
                  <span class="metric-label">磁盘使用率</span>
                  <span class="metric-value">{{ systemMetrics.disk }}%</span>
                </div>
                <div class="metric-bar">
                  <div class="metric-fill" :style="{ width: systemMetrics.disk + '%' }" :class="getMetricClass(systemMetrics.disk)"></div>
                </div>
              </div>
              
              <div class="metric-item">
                <div class="metric-header">
                  <span class="metric-label">网络流量</span>
                  <span class="metric-value">{{ systemMetrics.network }}MB/s</span>
                </div>
                <div class="metric-bar">
                  <div class="metric-fill normal" :style="{ width: (systemMetrics.network / 100) * 100 + '%' }"></div>
                </div>
              </div>
            </div>

            <!-- 系统状态图表 -->
            <div class="system-chart">
              <h5>系统性能趋势</h5>
              <div ref="systemChartRef" class="chart-container"></div>
            </div>

            <!-- 服务状态 -->
            <div class="service-status">
              <h5>服务状态</h5>
              <div class="service-list">
                <div v-for="service in services" :key="service.name" class="service-item">
                  <div class="service-info">
                    <span class="service-name">{{ service.name }}</span>
                    <span class="service-description">{{ service.description }}</span>
                  </div>
                  <div class="service-status-indicator">
                    <span class="status-dot" :class="service.status"></span>
                    <span class="status-text">{{ getServiceStatusText(service.status) }}</span>
                  </div>
                  <div class="service-actions">
                    <ModernButton variant="ghost" size="small" @click="restartService(service)">
                      重启
                    </ModernButton>
                    <ModernButton variant="ghost" size="small" @click="viewServiceLogs(service)">
                      日志
                    </ModernButton>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ModernCard>

        <!-- 审计日志 -->
        <ModernCard variant="glassmorphism" :hover-effect="true" class="audit-logs-card">
          <template #header>
            <div class="card-header">
              <span>审计日志</span>
              <div class="log-filters">
                <el-select v-model="logLevel" placeholder="日志级别" size="small" style="width: 100px;" @change="filterLogs">
                  <el-option label="全部" value="" />
                  <el-option label="信息" value="info" />
                  <el-option label="警告" value="warning" />
                  <el-option label="错误" value="error" />
                  <el-option label="严重" value="critical" />
                </el-select>
                <el-date-picker
                  v-model="logDateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  size="small"
                  style="width: 200px;"
                  @change="filterLogs"
                />
              </div>
            </div>
          </template>
          <div class="audit-logs-content">
            <!-- 日志统计 -->
            <div class="log-stats">
              <div class="log-stat-item">
                <span class="stat-label">今日日志</span>
                <span class="stat-value">{{ logStats.today }}</span>
              </div>
              <div class="log-stat-item">
                <span class="stat-label">错误日志</span>
                <span class="stat-value error">{{ logStats.errors }}</span>
              </div>
              <div class="log-stat-item">
                <span class="stat-label">警告日志</span>
                <span class="stat-value warning">{{ logStats.warnings }}</span>
              </div>
              <div class="log-stat-item">
                <span class="stat-label">安全事件</span>
                <span class="stat-value critical">{{ logStats.security }}</span>
              </div>
            </div>

            <!-- 日志列表 -->
            <div class="log-list">
              <div v-for="log in filteredLogs" :key="log.id" class="log-item" :class="log.level">
                <div class="log-header">
                  <div class="log-level">
                    <el-tag :type="getLogLevelType(log.level)" size="small">
                      {{ getLogLevelText(log.level) }}
                    </el-tag>
                  </div>
                  <div class="log-time">{{ log.timestamp }}</div>
                  <div class="log-user">{{ log.user }}</div>
                </div>
                <div class="log-content">
                  <div class="log-action">{{ log.action }}</div>
                  <div class="log-details">{{ log.details }}</div>
                </div>
                <div class="log-meta">
                  <span class="log-ip">IP: {{ log.ip }}</span>
                  <span class="log-module">模块: {{ log.module }}</span>
                  <ModernButton variant="ghost" size="small" @click="viewLogDetail(log)">
                    详情
                  </ModernButton>
                </div>
              </div>
            </div>

            <!-- 日志分页 -->
            <div class="log-pagination">
              <el-pagination
                v-model:current-page="logCurrentPage"
                v-model:page-size="logPageSize"
                :page-sizes="[20, 50, 100]"
                :total="totalLogs"
                layout="total, sizes, prev, pager, next"
                @size-change="handleLogSizeChange"
                @current-change="handleLogCurrentChange"
              />
            </div>
          </div>
        </ModernCard>
      </div>
    </div>

    <!-- 安全告警 -->
    <div class="security-alerts">
      <ModernCard variant="glassmorphism" :hover-effect="true" class="alerts-card">
        <template #header>
          <div class="card-header">
            <span>安全告警</span>
            <el-tag :type="alerts.length > 0 ? 'danger' : 'success'" size="small">
              {{ alerts.length }}个告警
            </el-tag>
          </div>
        </template>
        <div class="alerts-content">
          <div v-if="alerts.length === 0" class="no-alerts">
            <el-icon class="no-alerts-icon"><CircleCheck /></el-icon>
            <p>暂无安全告警</p>
          </div>
          <div v-else class="alert-list">
            <div v-for="alert in alerts" :key="alert.id" class="alert-item" :class="alert.severity">
              <div class="alert-icon">
                <el-icon><component :is="getAlertIcon(alert.type)" /></el-icon>
              </div>
              <div class="alert-content">
                <div class="alert-header">
                  <h6>{{ alert.title }}</h6>
                  <el-tag :type="getAlertSeverityType(alert.severity)" size="small">
                    {{ getAlertSeverityText(alert.severity) }}
                  </el-tag>
                </div>
                <p class="alert-description">{{ alert.description }}</p>
                <div class="alert-meta">
                  <span class="alert-time">{{ alert.timestamp }}</span>
                  <span class="alert-source">来源: {{ alert.source }}</span>
                </div>
              </div>
              <div class="alert-actions">
                <ModernButton variant="primary" size="small" @click="handleAlert(alert)">
                  处理
                </ModernButton>
                <ModernButton variant="ghost" size="small" @click="dismissAlert(alert)">
                  忽略
                </ModernButton>
              </div>
            </div>
          </div>
        </div>
      </ModernCard>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import {
  Lock,
  UserFilled,
  Warning,
  Document,
  Plus,
   Refresh,
   Search,
   User,
   CircleCheck,
   Monitor
 } from '@element-plus/icons-vue'
 import { Shield } from 'lucide-vue-next'
import { ElMessage, ElMessageBox } from 'element-plus'
import ModernButton from '@/components/ModernButton.vue'
import ModernCard from '@/components/ModernCard.vue'
import ModernInput from '@/components/ui/ModernInput.vue'

// 安全统计
const securityStats = ref({
  securityLevel: 'high',
  activeUsers: 156,
  threatCount: 0,
  todayLogs: 2847
})

// API调用函数
const fetchSecurityStats = async () => {
  try {
    const response = await fetch('/api/security/stats')
    if (response.ok) {
      const data = await response.json()
      if (data.success) {
        securityStats.value = data.data
      }
    }
  } catch (error) {
    console.error('获取安全统计失败:', error)
    // 保持默认数据作为回退
    securityStats.value = {
      securityLevel: 'high',
      activeUsers: 156,
      threatCount: 0,
      todayLogs: 2847
    }
  }
}

// 用户管理
const userSearch = ref('')
const roleFilter = ref('')
const statusFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const totalUsers = ref(0)

const users = ref([
  {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    role: 'admin',
    status: 'active',
    lastLogin: '2024-01-15 10:30',
    avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=admin%20user%20avatar&image_size=square'
  },
  {
    id: 2,
    username: 'operator1',
    email: '<EMAIL>',
    role: 'operator',
    status: 'active',
    lastLogin: '2024-01-15 09:45',
    avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=operator%20user%20avatar&image_size=square'
  },
  {
    id: 3,
    username: 'user1',
    email: '<EMAIL>',
    role: 'user',
    status: 'disabled',
    lastLogin: '2024-01-14 16:20',
    avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=regular%20user%20avatar&image_size=square'
  }
])

const fetchUsers = async () => {
  try {
    const response = await fetch('/api/security/users')
    if (response.ok) {
      const data = await response.json()
      if (data.success) {
        users.value = data.data
        totalUsers.value = data.total || data.data.length
      }
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    // 保持默认数据作为回退
    users.value = [
      {
        id: 1,
        username: 'admin',
        email: '<EMAIL>',
        role: 'admin',
        status: 'active',
        lastLogin: '2024-01-15 10:30',
        avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=admin%20user%20avatar&image_size=square'
      },
      {
        id: 2,
        username: 'operator1',
        email: '<EMAIL>',
        role: 'operator',
        status: 'active',
        lastLogin: '2024-01-15 09:45',
        avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=operator%20user%20avatar&image_size=square'
      },
      {
        id: 3,
        username: 'user1',
        email: '<EMAIL>',
        role: 'user',
        status: 'disabled',
        lastLogin: '2024-01-14 16:20',
        avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=regular%20user%20avatar&image_size=square'
      }
    ]
  }
}

const filteredUsers = computed(() => {
  let result = users.value
  
  if (userSearch.value) {
    result = result.filter(user => 
      user.username.toLowerCase().includes(userSearch.value.toLowerCase()) ||
      user.email.toLowerCase().includes(userSearch.value.toLowerCase())
    )
  }
  
  if (roleFilter.value) {
    result = result.filter(user => user.role === roleFilter.value)
  }
  
  if (statusFilter.value) {
    result = result.filter(user => user.status === statusFilter.value)
  }
  
  totalUsers.value = result.length
  return result.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
})

// 角色权限
const roles = ref([
  {
    id: 1,
    name: '系统管理员',
    description: '拥有系统所有权限',
    permissions: [
      {
        module: '用户管理',
        actions: ['查看', '创建', '编辑', '删除']
      },
      {
        module: '系统设置',
        actions: ['查看', '编辑']
      },
      {
        module: '日志管理',
        actions: ['查看', '导出']
      }
    ]
  },
  {
    id: 2,
    name: '操作员',
    description: '拥有日常操作权限',
    permissions: [
      {
        module: '车辆管理',
        actions: ['查看', '编辑']
      },
      {
        module: '充电管理',
        actions: ['查看', '操作']
      }
    ]
  },
  {
    id: 3,
    name: '普通用户',
    description: '基础查看权限',
    permissions: [
      {
        module: '个人信息',
        actions: ['查看', '编辑']
      },
      {
        module: '车辆信息',
        actions: ['查看']
      }
    ]
  }
])

// 系统监控
const systemStatus = ref({
  overall: 'normal'
})

const systemMetrics = ref({
  cpu: 45,
  memory: 68,
  disk: 32,
  network: 15.6
})

const services = ref([
  {
    name: 'Web服务',
    description: '前端Web应用服务',
    status: 'running'
  },
  {
    name: 'API服务',
    description: '后端API接口服务',
    status: 'running'
  },
  {
    name: '数据库',
    description: 'MySQL数据库服务',
    status: 'running'
  },
  {
    name: '缓存服务',
    description: 'Redis缓存服务',
    status: 'warning'
  }
])

const fetchSystemMetrics = async () => {
  try {
    const response = await fetch('/api/security/system-metrics')
    if (response.ok) {
      const data = await response.json()
      if (data.success) {
        systemMetrics.value = data.data
        systemStatus.value.overall = data.data.overall || 'normal'
      }
    }
  } catch (error) {
    console.error('获取系统指标失败:', error)
    // 保持默认数据作为回退
    systemMetrics.value = {
      cpu: 45,
      memory: 68,
      disk: 32,
      network: 15.6
    }
  }
}

const fetchServices = async () => {
  try {
    const response = await fetch('/api/security/services')
    if (response.ok) {
      const data = await response.json()
      if (data.success) {
        services.value = data.data
      }
    }
  } catch (error) {
    console.error('获取服务状态失败:', error)
    // 保持默认数据作为回退
    services.value = [
      {
        name: 'Web服务',
        description: '前端Web应用服务',
        status: 'running'
      },
      {
        name: 'API服务',
        description: '后端API接口服务',
        status: 'running'
      },
      {
        name: '数据库',
        description: 'MySQL数据库服务',
        status: 'running'
      },
      {
        name: '缓存服务',
        description: 'Redis缓存服务',
        status: 'warning'
      }
    ]
  }
}

// 审计日志
const logLevel = ref('')
const logDateRange = ref([])
const logCurrentPage = ref(1)
const logPageSize = ref(20)
const totalLogs = ref(0)

const logStats = ref({
  today: 2847,
  errors: 12,
  warnings: 45,
  security: 3
})

const logs = ref([
  {
    id: 1,
    level: 'info',
    timestamp: '2024-01-15 10:30:25',
    user: 'admin',
    action: '用户登录',
    details: '管理员用户成功登录系统',
    ip: '*************',
    module: '认证模块'
  },
  {
    id: 2,
    level: 'warning',
    timestamp: '2024-01-15 10:25:18',
    user: 'user1',
    action: '登录失败',
    details: '用户密码错误，登录失败',
    ip: '*************',
    module: '认证模块'
  },
  {
    id: 3,
    level: 'error',
    timestamp: '2024-01-15 10:20:12',
    user: 'system',
    action: '数据库连接',
    details: '数据库连接超时',
    ip: '127.0.0.1',
    module: '数据库模块'
  },
  {
    id: 4,
    level: 'critical',
    timestamp: '2024-01-15 10:15:05',
    user: 'unknown',
    action: '异常访问',
    details: '检测到可疑的API访问行为',
    ip: '***********',
    module: '安全模块'
  }
])

const fetchLogs = async () => {
  try {
    const response = await fetch('/api/security/logs')
    if (response.ok) {
      const data = await response.json()
      if (data.success) {
        logs.value = data.data
        logStats.value = data.stats || {
          today: 2847,
          errors: 12,
          warnings: 45,
          security: 3
        }
        totalLogs.value = data.total || data.data.length
      }
    }
  } catch (error) {
    console.error('获取审计日志失败:', error)
    // 保持默认数据作为回退
    logs.value = [
      {
        id: 1,
        level: 'info',
        timestamp: '2024-01-15 10:30:25',
        user: 'admin',
        action: '用户登录',
        details: '管理员用户成功登录系统',
        ip: '*************',
        module: '认证模块'
      },
      {
        id: 2,
        level: 'warning',
        timestamp: '2024-01-15 10:25:18',
        user: 'user1',
        action: '登录失败',
        details: '用户密码错误，登录失败',
        ip: '*************',
        module: '认证模块'
      },
      {
        id: 3,
        level: 'error',
        timestamp: '2024-01-15 10:20:12',
        user: 'system',
        action: '数据库连接',
        details: '数据库连接超时',
        ip: '127.0.0.1',
        module: '数据库模块'
      },
      {
        id: 4,
        level: 'critical',
        timestamp: '2024-01-15 10:15:05',
        user: 'unknown',
        action: '异常访问',
        details: '检测到可疑的API访问行为',
        ip: '***********',
        module: '安全模块'
      }
    ]
  }
}

const filteredLogs = computed(() => {
  let result = logs.value
  
  if (logLevel.value) {
    result = result.filter(log => log.level === logLevel.value)
  }
  
  totalLogs.value = result.length
  return result.slice((logCurrentPage.value - 1) * logPageSize.value, logCurrentPage.value * logPageSize.value)
})

// 安全告警
const alerts = ref([
  {
    id: 1,
    type: 'security',
    title: '异常登录检测',
    description: '检测到来自异常IP地址的登录尝试',
    severity: 'high',
    timestamp: '2024-01-15 10:15:05',
    source: '安全监控系统'
  },
  {
    id: 2,
    type: 'system',
    title: '系统资源告警',
    description: '内存使用率超过70%阈值',
    severity: 'medium',
    timestamp: '2024-01-15 10:10:30',
    source: '系统监控'
  }
])

const fetchAlerts = async () => {
  try {
    const response = await fetch('/api/security/alerts')
    if (response.ok) {
      const data = await response.json()
      if (data.success) {
        alerts.value = data.data
      }
    }
  } catch (error) {
    console.error('获取安全告警失败:', error)
    // 保持默认数据作为回退
    alerts.value = [
      {
        id: 1,
        type: 'security',
        title: '异常登录检测',
        description: '检测到来自异常IP地址的登录尝试',
        severity: 'high',
        timestamp: '2024-01-15 10:15:05',
        source: '安全监控系统'
      },
      {
        id: 2,
        type: 'system',
        title: '系统资源告警',
        description: '内存使用率超过70%阈值',
        severity: 'medium',
        timestamp: '2024-01-15 10:10:30',
        source: '系统监控'
      }
    ]
  }
}

// 图表相关
const systemChartRef = ref<HTMLElement>()
let systemChart: echarts.ECharts | null = null

// 方法
const getSecurityLevelType = (level: string) => {
  const types = {
    'high': 'success',
    'medium': 'warning',
    'low': 'danger'
  }
  return types[level] || 'info'
}

const getSecurityLevelText = (level: string) => {
  const texts = {
    'high': '高',
    'medium': '中',
    'low': '低'
  }
  return texts[level] || '未知'
}

const getRoleType = (role: string) => {
  const types = {
    'admin': 'danger',
    'operator': 'warning',
    'user': 'info'
  }
  return types[role] || 'info'
}

const getRoleText = (role: string) => {
  const texts = {
    'admin': '管理员',
    'operator': '操作员',
    'user': '用户'
  }
  return texts[role] || '未知'
}

const getStatusType = (status: string) => {
  const types = {
    'active': 'success',
    'disabled': 'warning',
    'locked': 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts = {
    'active': '活跃',
    'disabled': '禁用',
    'locked': '锁定'
  }
  return texts[status] || '未知'
}

const getSystemStatusText = (status: string) => {
  const texts = {
    'normal': '正常',
    'warning': '警告',
    'error': '错误'
  }
  return texts[status] || '未知'
}

const getMetricClass = (value: number) => {
  if (value >= 80) return 'critical'
  if (value >= 60) return 'warning'
  return 'normal'
}

const getServiceStatusText = (status: string) => {
  const texts = {
    'running': '运行中',
    'stopped': '已停止',
    'warning': '警告',
    'error': '错误'
  }
  return texts[status] || '未知'
}

const getLogLevelType = (level: string) => {
  const types = {
    'info': 'info',
    'warning': 'warning',
    'error': 'danger',
    'critical': 'danger'
  }
  return types[level] || 'info'
}

const getLogLevelText = (level: string) => {
  const texts = {
    'info': '信息',
    'warning': '警告',
    'error': '错误',
    'critical': '严重'
  }
  return texts[level] || '未知'
}

const getAlertIcon = (type: string) => {
  const icons = {
    'security': 'Warning',
    'system': 'Warning',
    'network': 'Warning'
  }
  return icons[type] || 'Warning'
}

const getAlertSeverityType = (severity: string) => {
  const types = {
    'low': 'info',
    'medium': 'warning',
    'high': 'danger',
    'critical': 'danger'
  }
  return types[severity] || 'info'
}

const getAlertSeverityText = (severity: string) => {
  const texts = {
    'low': '低',
    'medium': '中',
    'high': '高',
    'critical': '严重'
  }
  return texts[severity] || '未知'
}

// 用户管理方法
const addUser = () => {
  ElMessage.info('添加用户功能开发中')
}

const refreshUsers = async () => {
  await fetchUsers()
  ElMessage.success('用户列表已刷新')
}

const filterUsers = () => {
  currentPage.value = 1
}

const editUser = (user: any) => {
  ElMessage.info(`编辑用户: ${user.username}`)
}

const viewPermissions = (user: any) => {
  ElMessage.info(`查看用户权限: ${user.username}`)
}

const toggleUserStatus = async (user: any) => {
  const action = user.status === 'active' ? '禁用' : '启用'
  ElMessageBox.confirm(
    `确定要${action}用户 ${user.username} 吗？`,
    '确认操作',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      const response = await fetch(`/api/security/users/${user.id}/toggle-status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          status: user.status === 'active' ? 'disabled' : 'active'
        })
      })
      
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          user.status = user.status === 'active' ? 'disabled' : 'active'
          ElMessage.success(`用户已${action}`)
        } else {
          ElMessage.error(data.message || '操作失败')
        }
      } else {
        ElMessage.error('操作失败，请重试')
      }
    } catch (error) {
      console.error('切换用户状态失败:', error)
      ElMessage.error('操作失败，请重试')
    }
  }).catch(() => {
    ElMessage.info('操作已取消')
  })
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
}

// 角色管理方法
const addRole = () => {
  ElMessage.info('添加角色功能开发中')
}

const editRole = (role: any) => {
  ElMessage.info(`编辑角色: ${role.name}`)
}

const deleteRole = (role: any) => {
  ElMessageBox.confirm(
    `确定要删除角色 ${role.name} 吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ElMessage.success('角色已删除')
  }).catch(() => {
    ElMessage.info('删除已取消')
  })
}

// 系统监控方法
const restartService = async (service: any) => {
  ElMessageBox.confirm(
    `确定要重启服务 ${service.name} 吗？`,
    '确认重启',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      const response = await fetch(`/api/security/services/${service.name}/restart`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })
      
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          ElMessage.success(`服务 ${service.name} 重启成功`)
          // 刷新服务状态
          await fetchServices()
        } else {
          ElMessage.error(data.message || '重启失败')
        }
      } else {
        ElMessage.error('重启失败，请重试')
      }
    } catch (error) {
      console.error('重启服务失败:', error)
      ElMessage.error('重启失败，请重试')
    }
  }).catch(() => {
    ElMessage.info('重启已取消')
  })
}

const viewServiceLogs = (service: any) => {
  ElMessage.info(`查看服务日志: ${service.name}`)
}

// 日志管理方法
const filterLogs = () => {
  logCurrentPage.value = 1
}

const viewLogDetail = (log: any) => {
  ElMessage.info(`查看日志详情: ${log.action}`)
}

const handleLogSizeChange = (size: number) => {
  logPageSize.value = size
  logCurrentPage.value = 1
}

const handleLogCurrentChange = (page: number) => {
  logCurrentPage.value = page
}

// 告警处理方法
const handleAlert = (alert: any) => {
  ElMessage.info(`处理告警: ${alert.title}`)
}

const dismissAlert = async (alert: any) => {
  ElMessageBox.confirm(
    `确定要忽略告警 ${alert.title} 吗？`,
    '确认忽略',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      const response = await fetch(`/api/security/alerts/${alert.id}/dismiss`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        }
      })
      
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          const index = alerts.value.findIndex(a => a.id === alert.id)
          if (index > -1) {
            alerts.value.splice(index, 1)
          }
          ElMessage.success('告警已忽略')
        } else {
          ElMessage.error(data.message || '操作失败')
        }
      } else {
        ElMessage.error('操作失败，请重试')
      }
    } catch (error) {
      console.error('忽略告警失败:', error)
      ElMessage.error('操作失败，请重试')
    }
  }).catch(() => {
    ElMessage.info('操作已取消')
  })
}

// 初始化系统监控图表
const initSystemChart = () => {
  if (!systemChartRef.value) return
  
  systemChart = echarts.init(systemChartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['CPU', '内存', '磁盘']
    },
    xAxis: {
      type: 'category',
      data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00']
    },
    yAxis: {
      type: 'value',
      name: '使用率(%)',
      max: 100
    },
    series: [
      {
        name: 'CPU',
        type: 'line',
        data: [25, 30, 45, 55, 48, 42, 35],
        itemStyle: { color: '#409EFF' }
      },
      {
        name: '内存',
        type: 'line',
        data: [45, 50, 68, 72, 65, 58, 52],
        itemStyle: { color: '#67C23A' }
      },
      {
        name: '磁盘',
        type: 'line',
        data: [28, 30, 32, 35, 33, 31, 30],
        itemStyle: { color: '#E6A23C' }
      }
    ]
  }
  
  systemChart.setOption(option)
}

// 数据初始化函数
const initializeData = async () => {
  await Promise.all([
    fetchSecurityStats(),
    fetchUsers(),
    fetchSystemMetrics(),
    fetchServices(),
    fetchLogs(),
    fetchAlerts()
  ])
}

// 生命周期
let refreshInterval: NodeJS.Timeout | null = null

onMounted(async () => {
  // 初始化数据
  await initializeData()
  
  // 初始化图表
  initSystemChart()
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    systemChart?.resize()
  })
  
  // 定期刷新数据（每60秒）
  refreshInterval = setInterval(async () => {
    await Promise.all([
      fetchSecurityStats(),
      fetchSystemMetrics(),
      fetchServices(),
      fetchLogs(),
      fetchAlerts()
    ])
  }, 60000)
})

onUnmounted(() => {
  // 清除定时器
  if (refreshInterval) {
    clearInterval(refreshInterval)
  }
  
  // 销毁图表
  systemChart?.dispose()
  
  // 移除事件监听器
  window.removeEventListener('resize', () => {
    systemChart?.resize()
  })
})
</script>

<style scoped>
.system-security-container {
  padding: 24px;
  background: #ffffff;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 32px;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.page-subtitle {
  font-size: 16px;
  color: #718096;
  margin: 12px 0 0 0;
}

.security-overview {
  margin-bottom: var(--space-8);
  position: relative;
  z-index: 1;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--space-5);
}

.overview-card {
  border-radius: var(--radius-lg);
  background: #ffffff;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all var(--duration-normal) var(--ease-smooth);
  position: relative;
  overflow: hidden;
}



.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.overview-item {
  display: flex;
  align-items: center;
  padding: var(--space-5);
  gap: var(--space-4);
  border-radius: var(--radius-lg);
}

.overview-icon {
  width: 50px;
  height: 50px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-xl);
  color: white;
  position: relative;
  transition: all var(--duration-normal) var(--ease-smooth);
}

.overview-icon::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: var(--radius-lg);
  background: inherit;
  filter: blur(8px);
  opacity: 0.6;
  z-index: -1;
}

.overview-icon:hover {
  transform: scale(1.1) rotate(5deg);
  filter: drop-shadow(0 0 20px rgba(255, 255, 255, 0.5));
}

.overview-icon.security {
  background: #059669;
}

.overview-icon.users {
  background: #3b82f6;
}

.overview-icon.threats {
  background: #dc2626;
}

.overview-icon.logs {
  background: #d97706;
}

.overview-info {
  flex: 1;
}

.overview-info h3 {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  margin: 0;
  color: var(--gray-800);
  text-shadow: 0 0 10px rgba(44, 62, 80, 0.3);
}

.overview-info p {
  font-size: var(--text-sm);
  color: var(--gray-600);
  margin: var(--space-1) 0 0 0;
  text-shadow: 0 0 5px rgba(127, 140, 141, 0.3);
}

.overview-status {
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--success-500);
  box-shadow: 0 0 10px rgba(103, 194, 58, 0.6);
  animation: modernPulse 2s ease-in-out infinite;
}

.status-indicator.online {
  background: var(--success-500);
  box-shadow: 0 0 10px rgba(103, 194, 58, 0.6);
}

.log-count {
  font-size: var(--text-xs);
  color: var(--gray-500);
  text-shadow: 0 0 5px rgba(96, 98, 102, 0.3);
}

.main-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-5);
  margin-bottom: var(--space-8);
  position: relative;
  z-index: 1;
}

.permissions-section,
.monitoring-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-5);
}

.user-management-card,
.role-permissions-card,
.system-monitoring-card,
.audit-logs-card {
  border-radius: var(--radius-lg);
  background: #ffffff;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all var(--duration-normal) var(--ease-smooth);
  position: relative;
  overflow: hidden;
}





.user-management-card:hover,
.role-permissions-card:hover,
.system-monitoring-card:hover,
.audit-logs-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: var(--font-bold);
  padding: var(--space-5);
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.card-header h3 {
  font-size: var(--text-lg);
  color: #1f2937;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: var(--space-3);
  align-items: center;
}

.search-filters {
  display: flex;
  gap: var(--space-3);
  margin-bottom: var(--space-5);
  align-items: center;
  padding: var(--space-4);
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--radius-md);
}

.user-list {
  margin-bottom: var(--space-5);
  padding: var(--space-4);
}

.pagination,
.log-pagination {
  display: flex;
  justify-content: center;
  padding: var(--space-4);
}

.role-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
  padding: var(--space-4);
}

.role-item {
  border: 1px solid #e5e7eb;
  border-radius: var(--radius-md);
  padding: var(--space-4);
  background: #f9fafb;
  transition: all var(--duration-normal) var(--ease-smooth);
}

.role-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-color: #3b82f6;
}

.role-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-4);
}

.role-info h5 {
  margin: 0 0 var(--space-1) 0;
  color: var(--gray-800);
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  text-shadow: 0 0 10px rgba(44, 62, 80, 0.3);
}

.role-info p {
  margin: 0;
  color: var(--gray-600);
  font-size: var(--text-sm);
  text-shadow: 0 0 5px rgba(96, 98, 102, 0.3);
}

.role-actions {
  display: flex;
  gap: var(--space-3);
}

.role-permissions {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.permission-group h6 {
  margin: 0 0 var(--space-2) 0;
  color: var(--gray-800);
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  text-shadow: 0 0 10px rgba(44, 62, 80, 0.3);
}

.permission-tags {
  display: flex;
  gap: var(--space-1);
  flex-wrap: wrap;
}

.monitoring-status {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: modernPulse 2s ease-in-out infinite;
}

.status-dot.normal {
  background: var(--success-500);
  box-shadow: 0 0 10px rgba(103, 194, 58, 0.6);
}

.status-dot.warning {
  background: var(--warning-500);
  box-shadow: 0 0 10px rgba(230, 162, 60, 0.6);
}

.status-dot.error {
  background: var(--danger-500);
  box-shadow: 0 0 10px rgba(245, 108, 108, 0.6);
}

.status-dot.running {
  background: var(--success-500);
  box-shadow: 0 0 10px rgba(103, 194, 58, 0.6);
}

.status-dot.stopped {
  background: var(--gray-500);
  box-shadow: 0 0 10px rgba(144, 147, 153, 0.6);
}

.status-text {
  font-size: var(--text-xs);
  color: var(--gray-600);
  text-shadow: 0 0 5px rgba(96, 98, 102, 0.3);
}

.system-metrics {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
  margin-bottom: var(--space-6);
  padding: var(--space-4);
}

.metric-item {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
  padding: var(--space-3);
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-glass);
  transition: all var(--duration-normal) var(--ease-smooth);
}

.metric-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--modern-shadow-md) var(--primary-shadow);
  background: rgba(255, 255, 255, 0.1);
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.metric-label {
  font-size: var(--text-sm);
  color: var(--gray-600);
  font-weight: var(--font-medium);
  text-shadow: 0 0 5px rgba(96, 98, 102, 0.3);
}

.metric-value {
  font-size: var(--text-sm);
  font-weight: var(--font-bold);
  color: var(--gray-800);
  text-shadow: 0 0 10px rgba(44, 62, 80, 0.3);
}

.metric-bar {
  height: 6px;
  background: var(--gray-200);
  border-radius: var(--radius-sm);
  overflow: hidden;
  position: relative;
}

.metric-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: modernShimmer 2s ease-in-out infinite;
}

.metric-fill {
  height: 100%;
  transition: width var(--duration-normal) var(--ease-smooth);
  position: relative;
  z-index: 1;
}

.metric-fill.normal {
  background: linear-gradient(90deg, var(--success-500) 0%, var(--success-400) 100%);
  box-shadow: 0 0 10px rgba(103, 194, 58, 0.4);
}

.metric-fill.warning {
  background: linear-gradient(90deg, var(--warning-500) 0%, var(--warning-400) 100%);
  box-shadow: 0 0 10px rgba(230, 162, 60, 0.4);
}

.metric-fill.critical {
  background: linear-gradient(90deg, var(--danger-500) 0%, var(--danger-400) 100%);
  box-shadow: 0 0 10px rgba(245, 108, 108, 0.4);
}



.system-chart h5,
.service-status h5 {
  margin: 0 0 var(--space-4) 0;
  color: var(--gray-800);
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  text-shadow: 0 0 10px rgba(44, 62, 80, 0.3);
}

.chart-container {
  height: 200px;
  width: 100%;
  margin-bottom: var(--space-5);
  background: var(--bg-glass);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-glass);
  padding: var(--space-4);
  transition: all var(--duration-normal) var(--ease-smooth);
}

.chart-container:hover {
  transform: translateY(-2px);
  box-shadow: var(--modern-shadow-lg) var(--primary-shadow);
}

.service-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  padding: var(--space-4);
}

.service-item {
  display: flex;
  align-items: center;
  padding: var(--space-3) var(--space-4);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-md);
  background: var(--bg-glass);
  backdrop-filter: var(--glass-backdrop-sm);
  transition: all var(--duration-normal) var(--ease-smooth);
}

.service-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--modern-shadow-md) var(--primary-shadow);
  border-color: var(--primary-400);
}

.service-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.service-name {
  font-size: var(--text-sm);
  font-weight: var(--font-bold);
  color: var(--gray-800);
  text-shadow: 0 0 10px rgba(44, 62, 80, 0.3);
}

.service-description {
  font-size: var(--text-xs);
  color: var(--gray-600);
  text-shadow: 0 0 5px rgba(96, 98, 102, 0.3);
}

.service-status-indicator {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  margin-right: var(--space-4);
}

.service-actions {
  display: flex;
  gap: var(--space-1);
}

.log-filters {
  display: flex;
  gap: var(--space-3);
  align-items: center;
  padding: var(--space-4);
  background: #f9fafb;
  border-radius: var(--radius-md);
  border: 1px solid #e5e7eb;
  margin-bottom: var(--space-4);
}

.log-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--space-4);
  margin-bottom: var(--space-5);
  padding: var(--space-4);
}

.log-stat-item {
  text-align: center;
  padding: var(--space-4);
  background: #ffffff;
  border-radius: var(--radius-md);
  border: 1px solid #e5e7eb;
  transition: all var(--duration-normal) var(--ease-smooth);
}

.log-stat-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-label {
  display: block;
  font-size: var(--text-xs);
  color: var(--gray-600);
  margin-bottom: var(--space-1);
  font-weight: var(--font-medium);
  text-shadow: 0 0 5px rgba(96, 98, 102, 0.3);
}

.stat-value {
  display: block;
  font-size: var(--text-lg);
  font-weight: var(--font-bold);
  color: var(--gray-800);
  text-shadow: 0 0 10px rgba(44, 62, 80, 0.3);
}

.stat-value.error {
  color: var(--danger-500);
  text-shadow: 0 0 10px rgba(245, 108, 108, 0.5);
}

.stat-value.warning {
  color: var(--warning-500);
  text-shadow: 0 0 10px rgba(230, 162, 60, 0.5);
}

.stat-value.critical {
  color: var(--danger-500);
  text-shadow: 0 0 10px rgba(245, 108, 108, 0.5);
}

.log-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  margin-bottom: var(--space-5);
  max-height: 400px;
  overflow-y: auto;
  padding: var(--space-4);
  background: #f9fafb;
  border-radius: var(--radius-lg);
  border: 1px solid #e5e7eb;
}

.log-item {
  border: 1px solid #e5e7eb;
  border-radius: var(--radius-md);
  padding: var(--space-3) var(--space-4);
  background: #ffffff;
  transition: all var(--duration-normal) var(--ease-smooth);
}

.log-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.log-item.warning {
  border-left: 4px solid var(--warning-500);
  box-shadow: -4px 0 10px rgba(230, 162, 60, 0.3);
}

.log-item.error,
.log-item.critical {
  border-left: 4px solid var(--danger-500);
  box-shadow: -4px 0 10px rgba(245, 108, 108, 0.3);
}

.log-header {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-2);
}

.log-level {
  min-width: 60px;
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: var(--font-bold);
  color: white;
  background: var(--gray-500);
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.log-time {
  font-size: var(--text-xs);
  color: var(--gray-600);
  min-width: 120px;
  text-shadow: 0 0 5px rgba(96, 98, 102, 0.3);
}

.log-user {
  font-size: var(--text-xs);
  color: var(--primary-500);
  font-weight: var(--font-bold);
  text-shadow: 0 0 5px rgba(64, 158, 255, 0.3);
}

.log-content {
  margin-bottom: var(--space-2);
}

.log-action {
  font-size: var(--text-sm);
  font-weight: var(--font-bold);
  color: var(--gray-800);
  margin-bottom: var(--space-1);
  text-shadow: 0 0 10px rgba(44, 62, 80, 0.3);
}

.log-details {
  font-size: var(--text-xs);
  color: var(--gray-600);
  text-shadow: 0 0 5px rgba(96, 98, 102, 0.3);
}

.log-meta {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  font-size: var(--text-xs);
  color: var(--gray-500);
  text-shadow: 0 0 5px rgba(144, 147, 153, 0.3);
}

.log-ip,
.log-module {
  font-size: var(--text-xs);
  color: var(--gray-500);
  text-shadow: 0 0 5px rgba(144, 147, 153, 0.3);
}

.security-alerts {
  margin-top: var(--space-5);
}

.alerts-card {
  border-radius: var(--radius-lg);
  background: #ffffff;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all var(--duration-normal) var(--ease-smooth);
}

.alerts-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.no-alerts {
  text-align: center;
  padding: var(--space-10);
  color: var(--success-500);
}

.no-alerts-icon {
  font-size: 48px;
  margin-bottom: var(--space-3);
  text-shadow: 0 0 20px rgba(103, 194, 58, 0.5);
  animation: modernPulse 2s ease-in-out infinite;
}

.no-alerts p {
  margin: 0;
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  text-shadow: 0 0 10px rgba(103, 194, 58, 0.3);
}

.alert-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
  padding: var(--space-4);
}

.alert-item {
  display: flex;
  align-items: center;
  padding: var(--space-4);
  border: 1px solid #e5e7eb;
  border-radius: var(--radius-md);
  background: #ffffff;
  transition: all var(--duration-normal) var(--ease-smooth);
}

.alert-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.alert-item.high {
  border-left: 4px solid var(--danger-500);
  background: rgba(245, 108, 108, 0.1);
  box-shadow: -4px 0 15px rgba(245, 108, 108, 0.3);
}

.alert-item.medium {
  border-left: 4px solid var(--warning-500);
  background: rgba(230, 162, 60, 0.1);
  box-shadow: -4px 0 15px rgba(230, 162, 60, 0.3);
}

.alert-item.low {
  border-left: 4px solid var(--primary-500);
  background: rgba(64, 158, 255, 0.1);
  box-shadow: -4px 0 15px rgba(64, 158, 255, 0.3);
}

.alert-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--space-4);
  font-size: var(--text-lg);
  color: white;
  background: var(--danger-500);
  box-shadow: 0 4px 12px rgba(245, 108, 108, 0.4);
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.alert-content {
  flex: 1;
}

.alert-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-2);
}

.alert-header h6 {
  margin: 0;
  color: var(--gray-800);
  font-size: var(--text-sm);
  font-weight: var(--font-bold);
  text-shadow: 0 0 10px rgba(44, 62, 80, 0.3);
}

.alert-description {
  margin: 0 0 var(--space-2) 0;
  color: var(--gray-600);
  font-size: var(--text-xs);
  text-shadow: 0 0 5px rgba(96, 98, 102, 0.3);
}

.alert-meta {
  display: flex;
  gap: var(--space-4);
  font-size: var(--text-xs);
  color: var(--gray-500);
  text-shadow: 0 0 5px rgba(144, 147, 153, 0.3);
}

.alert-time,
.alert-source {
  font-size: var(--text-xs);
  color: var(--gray-500);
  text-shadow: 0 0 5px rgba(144, 147, 153, 0.3);
}

.alert-actions {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.modern-button.danger {
  color: var(--danger-500) !important;
}

.modern-button.success {
  color: var(--success-500) !important;
}

@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 1fr;
  }
  
  .overview-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .log-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .system-security-container {
    padding: 15px;
  }
  
  .page-title {
    font-size: 24px;
  }
  
  .overview-grid {
    grid-template-columns: 1fr;
  }
  
  .search-filters {
    flex-direction: column;
    align-items: stretch;
  }
  
  .log-filters {
    flex-direction: column;
    align-items: stretch;
  }
  
  .log-stats {
    grid-template-columns: 1fr;
  }
  
  .alert-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .alert-actions {
    flex-direction: row;
    width: 100%;
    justify-content: flex-end;
  }
}
</style>