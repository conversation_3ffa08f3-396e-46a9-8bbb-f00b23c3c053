<template>
  <div class="ai-assistant-container">
    <!-- 页面头部 -->
    <div class="ai-header">
      <div class="ai-header-content">
        <div class="ai-avatar">
          <el-icon :size="32" color="#4f46e5">
            <ChatDotRound />
          </el-icon>
        </div>
        <div class="ai-info">
          <h2>智能AI助手</h2>
          <p>我是您的专属AI助手，可以回答关于新能源汽车管理系统的任何问题</p>
        </div>
        <div class="ai-status">
          <el-tag :type="isOnline ? 'success' : 'danger'" size="small">
            {{ isOnline ? '在线' : '离线' }}
          </el-tag>
        </div>
      </div>
    </div>

    <!-- 对话区域 -->
    <div class="chat-container" ref="chatContainer">
      <div class="chat-messages">
        <!-- 欢迎消息 -->
        <div v-if="messages.length === 0" class="welcome-message">
          <div class="welcome-card">
            <el-icon :size="48" color="#4f46e5">
              <ChatDotRound />
            </el-icon>
            <h3>欢迎使用智能AI助手</h3>
            <p>您可以询问我关于以下内容的问题：</p>
            <div class="quick-questions">
              <el-button 
                v-for="question in quickQuestions" 
                :key="question"
                type="primary" 
                plain 
                size="small"
                @click="sendQuickQuestion(question)"
                class="quick-btn"
              >
                {{ question }}
              </el-button>
            </div>
          </div>
        </div>

        <!-- 消息列表 -->
        <div 
          v-for="(message, index) in messages" 
          :key="index" 
          class="message-item"
          :class="message.type"
        >
          <div class="message-avatar">
            <el-avatar 
              v-if="message.type === 'user'"
              :size="32"
              :src="userAvatar"
              icon="User"
            />
            <el-icon v-else :size="32" color="#4f46e5">
              <ChatDotRound />
            </el-icon>
          </div>
          <div class="message-content">
            <div class="message-bubble">
              <div v-if="message.type === 'ai' && message.isTyping" class="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
              <div v-else v-html="formatMessage(message.content)"></div>
            </div>
            <div class="message-time">
              {{ formatTime(message.timestamp) }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="chat-input-container">
      <div class="input-wrapper">
        <el-input
          v-model="inputMessage"
          type="textarea"
          :rows="1"
          :autosize="{ minRows: 1, maxRows: 4 }"
          placeholder="请输入您的问题..."
          @keydown.enter.prevent="handleEnterKey"
          :disabled="isLoading"
          class="message-input"
        />
        <div class="input-actions">
          <el-button
            type="primary"
            :icon="Promotion"
            @click="sendMessage"
            :loading="isLoading"
            :disabled="!inputMessage.trim()"
            class="send-btn"
          >
            发送
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { ChatDotRound, Promotion, User } from '@element-plus/icons-vue'
import { useAuthStore } from '../stores/auth'

interface Message {
  id: string
  type: 'user' | 'ai'
  content: string
  timestamp: Date
  isTyping?: boolean
}

const authStore = useAuthStore()
const chatContainer = ref<HTMLElement>()
const inputMessage = ref('')
const isLoading = ref(false)
const isOnline = ref(true)
const messages = ref<Message[]>([])

const userAvatar = computed(() => authStore.user?.avatar || '')

const quickQuestions = [
  '如何查看电池状态？',
  '充电站在哪里？',
  '如何设置充电计划？',
  '车辆保养提醒',
  '碳积分如何获得？',
  '系统功能介绍'
]

// 发送快速问题
const sendQuickQuestion = (question: string) => {
  inputMessage.value = question
  sendMessage()
}

// 处理回车键
const handleEnterKey = (event: KeyboardEvent) => {
  if (!event.shiftKey) {
    sendMessage()
  }
}

// 发送消息
const sendMessage = async () => {
  if (!inputMessage.value.trim() || isLoading.value) return

  const userMessage: Message = {
    id: Date.now().toString(),
    type: 'user',
    content: inputMessage.value.trim(),
    timestamp: new Date()
  }

  messages.value.push(userMessage)
  const question = inputMessage.value.trim()
  inputMessage.value = ''
  isLoading.value = true

  // 添加AI正在输入的消息
  const aiTypingMessage: Message = {
    id: (Date.now() + 1).toString(),
    type: 'ai',
    content: '',
    timestamp: new Date(),
    isTyping: true
  }
  messages.value.push(aiTypingMessage)

  await scrollToBottom()

  try {
    // 调用AI API
    const response = await fetch('/api/ai/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authStore.token}`
      },
      body: JSON.stringify({
        message: question,
        context: messages.value.slice(-10) // 发送最近10条消息作为上下文
      })
    })

    if (!response.ok) {
      throw new Error('AI服务暂时不可用')
    }

    const data = await response.json()
    
    // 移除正在输入的消息
    messages.value.pop()
    
    // 添加AI回复
    const aiMessage: Message = {
      id: (Date.now() + 2).toString(),
      type: 'ai',
      content: data.response,
      timestamp: new Date()
    }
    messages.value.push(aiMessage)

  } catch (error) {
    console.error('AI对话错误:', error)
    
    // 移除正在输入的消息
    messages.value.pop()
    
    // 添加错误消息
    const errorMessage: Message = {
      id: (Date.now() + 2).toString(),
      type: 'ai',
      content: '抱歉，我暂时无法回答您的问题，请稍后再试。',
      timestamp: new Date()
    }
    messages.value.push(errorMessage)
    
    ElMessage.error('AI服务连接失败')
  } finally {
    isLoading.value = false
    await scrollToBottom()
  }
}

// 滚动到底部
const scrollToBottom = async () => {
  await nextTick()
  if (chatContainer.value) {
    chatContainer.value.scrollTop = chatContainer.value.scrollHeight
  }
}

// 格式化消息内容
const formatMessage = (content: string) => {
  // 简单的markdown格式化
  return content
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/\n/g, '<br>')
    .replace(/`(.*?)`/g, '<code>$1</code>')
}

// 格式化时间
const formatTime = (timestamp: Date) => {
  return timestamp.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 初始化
onMounted(() => {
  // 检查AI服务状态
  checkAIStatus()
})

// 检查AI服务状态
const checkAIStatus = async () => {
  try {
    const response = await fetch('/api/ai/status')
    isOnline.value = response.ok
  } catch (error) {
    isOnline.value = false
  }
}
</script>

<style scoped>
.ai-assistant-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #ffffff;
}

.ai-header {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ai-header-content {
  display: flex;
  align-items: center;
  gap: 16px;
  max-width: 1200px;
  margin: 0 auto;
}

.ai-avatar {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}

.ai-info {
  flex: 1;
}

.ai-info h2 {
  margin: 0 0 4px 0;
  color: #1a202c;
  font-size: 24px;
  font-weight: 600;
}

.ai-info p {
  margin: 0;
  color: #718096;
  font-size: 14px;
}

.ai-status {
  margin-left: auto;
}

.chat-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.chat-messages {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.welcome-message {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.welcome-card {
  background: white;
  padding: 40px;
  border-radius: 16px;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  max-width: 500px;
}

.welcome-card h3 {
  margin: 16px 0 8px 0;
  color: #1e293b;
  font-size: 20px;
}

.welcome-card p {
  color: #64748b;
  margin-bottom: 24px;
}

.quick-questions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
}

.quick-btn {
  border-radius: 20px !important;
  font-size: 12px !important;
}

.message-item {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.message-item.user {
  flex-direction: row-reverse;
}

.message-avatar {
  flex-shrink: 0;
}

.message-content {
  flex: 1;
  max-width: 70%;
}

.message-item.user .message-content {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.message-bubble {
  background: white;
  padding: 12px 16px;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  word-wrap: break-word;
}

.message-item.user .message-bubble {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
}

.message-item.ai .message-bubble {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
}

.message-time {
  font-size: 12px;
  color: #94a3b8;
  margin-top: 4px;
}

.typing-indicator {
  display: flex;
  gap: 4px;
  align-items: center;
}

.typing-indicator span {
  width: 6px;
  height: 6px;
  background: #94a3b8;
  border-radius: 50%;
  animation: typing 1.4s infinite;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
}

.chat-input-container {
  background: white;
  border-top: 1px solid #e2e8f0;
  padding: 20px;
}

.input-wrapper {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  gap: 12px;
  align-items: flex-end;
}

.message-input {
  flex: 1;
}

:deep(.message-input .el-textarea__inner) {
  border-radius: 12px !important;
  border: 2px solid #e2e8f0 !important;
  padding: 12px 16px !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  resize: none !important;
  transition: all 0.3s ease !important;
}

:deep(.message-input .el-textarea__inner:focus) {
  border-color: #4f46e5 !important;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1) !important;
}

.send-btn {
  border-radius: 12px !important;
  padding: 12px 20px !important;
  height: auto !important;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%) !important;
  border: none !important;
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3) !important;
  transition: all 0.3s ease !important;
}

.send-btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 16px rgba(79, 70, 229, 0.4) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ai-header {
    padding: 16px;
  }
  
  .ai-header-content {
    gap: 12px;
  }
  
  .ai-info h2 {
    font-size: 20px;
  }
  
  .chat-container {
    padding: 16px;
  }
  
  .message-content {
    max-width: 85%;
  }
  
  .welcome-card {
    padding: 24px;
    margin: 0 16px;
  }
  
  .chat-input-container {
    padding: 16px;
  }
  
  .input-wrapper {
    flex-direction: column;
    gap: 8px;
  }
  
  .send-btn {
    width: 100% !important;
  }
}
</style>