<template>
  <button
    :class="[
      'modern-button',
      variant,
      size,
      {
        'loading': loading,
        'disabled': disabled,
        'icon-only': iconOnly
      }
    ]"
    :disabled="disabled || loading"
    @click="handleClick"
  >
    <div class="button-content">
      <div v-if="loading" class="loading-spinner"></div>
      <component v-else-if="icon" :is="icon" class="button-icon" />
      <span v-if="!iconOnly && $slots.default" class="button-text">
        <slot></slot>
      </span>
    </div>
    <div class="button-glow"></div>
  </button>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'info'
  size?: 'small' | 'medium' | 'large'
  loading?: boolean
  disabled?: boolean
  icon?: any
  iconOnly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'primary',
  size: 'medium',
  loading: false,
  disabled: false,
  iconOnly: false
})

const emit = defineEmits<{
  click: [event: MouseEvent]
}>()

const handleClick = (event: MouseEvent) => {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}
</script>

<style scoped>
.modern-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: var(--border-radius-lg);
  font-family: var(--font-family);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  overflow: hidden;
  transition: all var(--transition-duration) var(--transition-timing);
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: var(--text-light);
  box-shadow: var(--shadow-md);
  backdrop-filter: blur(var(--blur-sm));
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.modern-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.modern-button:hover::before {
  left: 100%;
}

.modern-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.modern-button:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

/* Sizes */
.modern-button.small {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-sm);
  min-height: 32px;
}

.modern-button.medium {
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-base);
  min-height: 40px;
}

.modern-button.large {
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: var(--font-size-lg);
  min-height: 48px;
}

.modern-button.icon-only {
  width: 40px;
  height: 40px;
  padding: 0;
}

.modern-button.icon-only.small {
  width: 32px;
  height: 32px;
}

.modern-button.icon-only.large {
  width: 48px;
  height: 48px;
}

/* Variants */
.modern-button.secondary {
  background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-dark) 100%);
}

.modern-button.success {
  background: linear-gradient(135deg, var(--success-color) 0%, var(--success-dark) 100%);
}

.modern-button.warning {
  background: linear-gradient(135deg, var(--warning-color) 0%, var(--warning-dark) 100%);
}

.modern-button.danger {
  background: linear-gradient(135deg, var(--danger-color) 0%, var(--danger-dark) 100%);
}

.modern-button.info {
  background: linear-gradient(135deg, var(--info-color) 0%, var(--info-dark) 100%);
}

/* States */
.modern-button.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.modern-button.loading {
  cursor: wait;
}

.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  position: relative;
  z-index: 1;
}

.button-icon {
  width: 18px;
  height: 18px;
  flex-shrink: 0;
}

.button-text {
  white-space: nowrap;
  font-weight: var(--font-weight-medium);
  text-shadow: var(--text-shadow);
}

.loading-spinner {
  width: 18px;
  height: 18px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.button-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: inherit;
  border-radius: inherit;
  opacity: 0;
  filter: blur(8px);
  transition: opacity var(--transition-duration);
  z-index: -1;
}

.modern-button:hover .button-glow {
  opacity: 0.6;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Neon glow effect for primary buttons */
.modern-button.primary {
  box-shadow: 
    var(--shadow-md),
    0 0 20px rgba(var(--primary-rgb), 0.3);
}

.modern-button.primary:hover {
  box-shadow: 
    var(--shadow-lg),
    0 0 30px rgba(var(--primary-rgb), 0.5),
    0 0 60px rgba(var(--primary-rgb), 0.2);
}

/* Glassmorphism effect */
.modern-button {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.modern-button.primary {
  background: linear-gradient(135deg, 
    rgba(var(--primary-rgb), 0.8) 0%, 
    rgba(var(--primary-rgb), 0.6) 100%);
}

.modern-button.secondary {
  background: linear-gradient(135deg, 
    rgba(var(--secondary-rgb), 0.8) 0%, 
    rgba(var(--secondary-rgb), 0.6) 100%);
}

.modern-button.success {
  background: linear-gradient(135deg, 
    rgba(var(--success-rgb), 0.8) 0%, 
    rgba(var(--success-rgb), 0.6) 100%);
}

.modern-button.warning {
  background: linear-gradient(135deg, 
    rgba(var(--warning-rgb), 0.8) 0%, 
    rgba(var(--warning-rgb), 0.6) 100%);
}

.modern-button.danger {
  background: linear-gradient(135deg, 
    rgba(var(--danger-rgb), 0.8) 0%, 
    rgba(var(--danger-rgb), 0.6) 100%);
}

.modern-button.info {
  background: linear-gradient(135deg, 
    rgba(var(--info-rgb), 0.8) 0%, 
    rgba(var(--info-rgb), 0.6) 100%);
}
</style>