/**
 * This is a API server
 */

import express, { type Request, type Response, type NextFunction }  from 'express';
import cors from 'cors';
import path from 'path';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import authRoutes from './routes/auth.js';
import usersRoutes from './routes/users.js';
import vehiclesRoutes from './routes/vehicles.js';
import batteriesRoutes from './routes/batteries.js';
import chargingStationsRoutes from './routes/charging-stations.js';
import chargingSessionsRoutes from './routes/charging-sessions.js';
import carbonCreditsRoutes from './routes/carbon-credits.js';
import drivingBehaviorRoutes from './routes/driving-behavior.js';
import fleetManagementRoutes from './routes/fleet-management.js';
import edgeDevicesRoutes from './routes/edge-devices.js';
import alertsRoutes from './routes/alerts.js';
import systemRoutes from './routes/system.js';
import aiAssistantRoutes from './routes/ai-assistant.js';
import aiEnhancedRoutes from './routes/ai-enhanced.js';
import aiSimpleRoutes from './routes/ai-simple.js';
import aiMultiRoutes from './routes/ai-multi.js';
import adasRoutes from './routes/adas.js';
import vehicleNetworkRoutes from './routes/vehicle-network.js';
import userProfilesRoutes from './routes/user-profiles.js';

// for esm mode
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// load env
dotenv.config();


const app: express.Application = express();

app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

/**
 * API Routes
 */
app.use('/api/auth', authRoutes);
app.use('/api/users', usersRoutes);
app.use('/api/vehicles', vehiclesRoutes);
app.use('/api/batteries', batteriesRoutes);
app.use('/api/charging-stations', chargingStationsRoutes);
app.use('/api/charging-sessions', chargingSessionsRoutes);
app.use('/api/carbon-credits', carbonCreditsRoutes);
app.use('/api/driving-behavior', drivingBehaviorRoutes);
app.use('/api/fleet-management', fleetManagementRoutes);
app.use('/api/edge-devices', edgeDevicesRoutes);
app.use('/api/alerts', alertsRoutes);
app.use('/api/system', systemRoutes);
app.use('/api/ai', aiAssistantRoutes);
app.use('/api/ai-enhanced', aiMultiRoutes);
app.use('/api/adas', adasRoutes);
app.use('/api/vehicle-network', vehicleNetworkRoutes);
app.use('/api/carbon', carbonCreditsRoutes); // 添加 /api/carbon 别名
app.use('/api/fleet', fleetManagementRoutes); // 添加 /api/fleet 别名
app.use('/api/users', userProfilesRoutes); // 添加用户档案路由

/**
 * health
 */
app.use('/api/health', (req: Request, res: Response, next: NextFunction): void => {
  res.status(200).json({
    success: true,
    message: 'ok'
  });
});

/**
 * error handler middleware
 */
app.use((error: Error, req: Request, res: Response, next: NextFunction) => {
  res.status(500).json({
    success: false,
    error: 'Server internal error'
  });
});

/**
 * 404 handler
 */
app.use((req: Request, res: Response) => {
  res.status(404).json({
    success: false,
    error: 'API not found'
  });
});

export default app;