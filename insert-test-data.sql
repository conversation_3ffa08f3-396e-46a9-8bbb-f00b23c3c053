-- =============================================
-- 新能源汽车智能综合管理系统 - 测试数据插入脚本
-- =============================================

USE ev_management;

-- 清空现有数据（可选）
-- DELETE FROM edge_devices;
-- DELETE FROM fleet_management;
-- DELETE FROM driving_behavior;
-- DELETE FROM carbon_credit;
-- DELETE FROM charging_sessions;
-- DELETE FROM charging_stations;
-- DELETE FROM batteries;
-- DELETE FROM vehicles;
-- DELETE FROM users;

-- 1. 插入用户数据
INSERT INTO users (user_id, username, email, password_hash, phone, role, status, avatar_url) VALUES
('user_001', '张三', 'z<PERSON><PERSON>@example.com', '$2b$10$rOzJqKqQQxQQxQQxQQxQQeJ7J7J7J7J7J7J7J7J7J7J7J7J7J7J7J7', '13800138001', 'user', 'active', 'https://example.com/avatar1.jpg'),
('user_002', '李四', '<EMAIL>', '$2b$10$rOzJqKqQQxQQxQQxQQxQQeJ7J7J7J7J7J7J7J7J7J7J7J7J7J7J7J7', '13800138002', 'user', 'active', 'https://example.com/avatar2.jpg'),
('user_003', '王五', '<EMAIL>', '$2b$10$rOzJqKqQQxQQxQQxQQxQQeJ7J7J7J7J7J7J7J7J7J7J7J7J7J7J7J7', '13800138003', 'user', 'active', 'https://example.com/avatar3.jpg'),
('user_004', '赵六', '<EMAIL>', '$2b$10$rOzJqKqQQxQQxQQxQQxQQeJ7J7J7J7J7J7J7J7J7J7J7J7J7J7J7J7', '13800138004', 'operator', 'active', 'https://example.com/avatar4.jpg'),
('user_005', '管理员', '<EMAIL>', '$2b$10$rOzJqKqQQxQQxQQxQQxQQeJ7J7J7J7J7J7J7J7J7J7J7J7J7J7J7J7', '13800138000', 'admin', 'active', 'https://example.com/avatar5.jpg');

-- 2. 插入车辆数据
INSERT INTO vehicles (vehicle_id, owner_id, make, model, year, vin, license_plate, color, battery_capacity, range_km, status, latitude, longitude) VALUES
('vehicle_001', 'user_001', '特斯拉', 'Model 3', 2023, 'TSLA123456789001', '京A12345', '白色', 75.0, 550, 'active', 39.9042, 116.4074),
('vehicle_002', 'user_002', '比亚迪', '汉EV', 2023, 'BYD1234567890002', '京B67890', '黑色', 85.4, 605, 'active', 39.9142, 116.4174),
('vehicle_003', 'user_003', '蔚来', 'ES8', 2023, 'NIO1234567890003', '京C11111', '蓝色', 100.0, 500, 'active', 39.9242, 116.4274),
('vehicle_004', 'user_001', '小鹏', 'P7', 2022, 'XPEV123456789004', '京D22222', '红色', 80.9, 586, 'maintenance', 39.9342, 116.4374),
('vehicle_005', 'user_002', '理想', 'ONE', 2023, 'LI12345678900005', '京E33333', '银色', 40.5, 180, 'active', 39.9442, 116.4474);

-- 3. 插入电池数据
INSERT INTO batteries (battery_id, vehicle_id, manufacturer, model, capacity_kwh, voltage, chemistry, manufacture_date, warranty_years, current_soh, current_soc, temperature, cycle_count, status) VALUES
('battery_001', 'vehicle_001', '松下', 'NCR18650B', 75.0, 400.0, 'NCM', '2023-01-15', 8, 98.5, 85.2, 25.5, 150, 'normal'),
('battery_002', 'vehicle_002', '比亚迪', 'Blade Battery', 85.4, 400.0, 'LFP', '2023-02-20', 8, 99.2, 92.1, 23.8, 120, 'normal'),
('battery_003', 'vehicle_003', '宁德时代', 'NCM811', 100.0, 400.0, 'NCM', '2023-03-10', 8, 97.8, 78.5, 26.2, 180, 'normal'),
('battery_004', 'vehicle_004', '国轩高科', 'LFP180', 80.9, 355.2, 'LFP', '2022-12-05', 8, 96.5, 45.3, 28.1, 250, 'warning'),
('battery_005', 'vehicle_005', '宁德时代', 'NCM523', 40.5, 355.2, 'NCM', '2023-04-18', 8, 99.1, 88.7, 24.9, 95, 'normal');

-- 4. 插入充电站数据
INSERT INTO charging_stations (station_id, name, operator, address, latitude, longitude, total_ports, available_ports, power_kw, pricing_per_kwh, amenities, status) VALUES
('station_001', '北京国贸充电站', '国家电网', '北京市朝阳区建国门外大街1号', 39.9088, 116.4317, 8, 6, 120.0, 1.2, '餐厅,WiFi,休息区', 'active'),
('station_002', '上海陆家嘴充电站', '特来电', '上海市浦东新区陆家嘴环路1000号', 31.2397, 121.4997, 12, 8, 150.0, 1.1, '便利店,咖啡厅', 'active'),
('station_003', '深圳科技园充电站', '星星充电', '深圳市南山区科技园南区', 22.5431, 113.9344, 6, 4, 60.0, 1.0, 'WiFi,洗手间', 'active'),
('station_004', '广州天河充电站', '小鹏汽车', '广州市天河区天河路208号', 23.1291, 113.3185, 10, 7, 180.0, 1.3, '餐厅,购物中心', 'active'),
('station_005', '杭州西湖充电站', '蔚来汽车', '杭州市西湖区文三路259号', 30.2741, 120.1551, 4, 2, 90.0, 1.15, '景区,休息区', 'maintenance');

-- 5. 插入充电会话数据
INSERT INTO charging_sessions (session_id, vehicle_id, station_id, user_id, start_time, end_time, energy_delivered_kwh, cost, payment_method, status, start_battery_level, end_battery_level) VALUES
('session_001', 'vehicle_001', 'station_001', 'user_001', '2024-01-15 09:30:00', '2024-01-15 10:45:00', 45.5, 54.60, '微信支付', 'completed', 20.5, 85.2),
('session_002', 'vehicle_002', 'station_002', 'user_002', '2024-01-16 14:20:00', '2024-01-16 15:50:00', 52.3, 57.53, '支付宝', 'completed', 35.8, 92.1),
('session_003', 'vehicle_003', 'station_003', 'user_003', '2024-01-17 11:15:00', '2024-01-17 12:30:00', 38.2, 38.20, '银行卡', 'completed', 40.3, 78.5),
('session_004', 'vehicle_001', 'station_004', 'user_001', '2024-01-18 16:45:00', NULL, 25.8, 33.54, '微信支付', 'active', 55.2, NULL),
('session_005', 'vehicle_005', 'station_001', 'user_002', '2024-01-19 08:00:00', '2024-01-19 08:45:00', 18.5, 22.20, '支付宝', 'completed', 70.2, 88.7);

-- 6. 插入碳积分数据
INSERT INTO carbon_credit (user_id, vehicle_id, credit_type, amount, description, earned_date, expiry_date, status) VALUES
('user_001', 'vehicle_001', 'driving', 125.50, '绿色出行奖励', '2024-01-15', '2025-01-15', 'active'),
('user_002', 'vehicle_002', 'charging', 89.30, '清洁能源充电奖励', '2024-01-16', '2025-01-16', 'active'),
('user_003', 'vehicle_003', 'sharing', 45.80, '车辆共享奖励', '2024-01-17', '2025-01-17', 'active'),
('user_001', 'vehicle_004', 'recycling', 200.00, '电池回收奖励', '2024-01-18', '2025-01-18', 'active'),
('user_002', 'vehicle_005', 'referral', 150.00, '推荐新用户奖励', '2024-01-19', '2025-01-19', 'active');

-- 7. 插入驾驶行为数据
INSERT INTO driving_behavior (vehicle_id, user_id, trip_id, start_time, end_time, distance_km, avg_speed_kmh, max_speed_kmh, energy_consumption_kwh, efficiency_km_per_kwh, harsh_braking_count, rapid_acceleration_count, speeding_duration_minutes, eco_score, route_data) VALUES
('vehicle_001', 'user_001', 'trip_001', '2024-01-15 08:00:00', '2024-01-15 09:15:00', 45.2, 65.5, 95.0, 8.5, 5.32, 2, 1, 5, 85.5, '{"start_location": "北京市朝阳区", "end_location": "北京市海淀区"}'),
('vehicle_002', 'user_002', 'trip_002', '2024-01-16 13:30:00', '2024-01-16 14:45:00', 38.7, 58.2, 88.0, 7.2, 5.38, 1, 0, 2, 92.3, '{"start_location": "上海市浦东新区", "end_location": "上海市徐汇区"}'),
('vehicle_003', 'user_003', 'trip_003', '2024-01-17 10:00:00', '2024-01-17 11:30:00', 52.1, 72.8, 110.0, 9.8, 5.32, 3, 2, 8, 78.9, '{"start_location": "深圳市南山区", "end_location": "深圳市福田区"}'),
('vehicle_004', 'user_001', 'trip_004', '2024-01-18 15:20:00', '2024-01-18 16:40:00', 29.5, 45.3, 75.0, 5.8, 5.09, 0, 0, 0, 95.8, '{"start_location": "广州市天河区", "end_location": "广州市越秀区"}'),
('vehicle_005', 'user_002', 'trip_005', '2024-01-19 07:30:00', '2024-01-19 08:15:00', 22.8, 55.2, 80.0, 4.2, 5.43, 1, 1, 3, 88.7, '{"start_location": "杭州市西湖区", "end_location": "杭州市拱墅区"}');

-- 8. 插入车队管理数据
INSERT INTO fleet_management (fleet_id, fleet_name, company_name, manager_id, total_vehicles, active_vehicles, fleet_type, status, contact_email, contact_phone, address) VALUES
('fleet_001', '绿色出行车队', '北京绿色出行科技有限公司', 'user_004', 25, 23, 'rental', 'active', '<EMAIL>', '************', '北京市朝阳区望京SOHO'),
('fleet_002', '智慧物流车队', '上海智慧物流有限公司', 'user_005', 50, 48, 'logistics', 'active', '<EMAIL>', '************', '上海市浦东新区张江高科技园区'),
('fleet_003', '企业通勤车队', '深圳科技企业服务公司', 'user_004', 15, 14, 'corporate', 'active', '<EMAIL>', '************', '深圳市南山区科技园南区'),
('fleet_004', '共享出行车队', '广州共享出行平台', 'user_005', 80, 75, 'sharing', 'active', '<EMAIL>', '************', '广州市天河区珠江新城'),
('fleet_005', '城市配送车队', '杭州城市配送服务公司', 'user_004', 35, 32, 'logistics', 'active', '<EMAIL>', '************', '杭州市西湖区文三路');

-- 9. 插入边缘设备数据
INSERT INTO edge_devices (device_id, name, type, status, location, ip_address, firmware_version, last_heartbeat, configuration, metadata) VALUES
('edge_001', '北京国贸边缘计算节点', 'gateway', 'online', '北京市朝阳区建国门外大街1号', '*************', 'v2.1.5', NOW(), '{"cpu_cores": 8, "memory_gb": 16, "storage_gb": 512}', '{"deployment_date": "2024-01-01", "maintenance_contact": "<EMAIL>"}'),
('edge_002', '上海陆家嘴数据处理器', 'processor', 'online', '上海市浦东新区陆家嘴环路1000号', '*************', 'v2.1.3', NOW(), '{"cpu_cores": 16, "memory_gb": 32, "storage_gb": 1024}', '{"deployment_date": "2024-01-05", "maintenance_contact": "<EMAIL>"}'),
('edge_003', '深圳科技园传感器集群', 'sensor', 'online', '深圳市南山区科技园南区', '*************', 'v1.8.2', NOW(), '{"sensor_count": 50, "data_rate": "1MB/s"}', '{"deployment_date": "2024-01-10", "sensor_types": ["temperature", "humidity", "air_quality"]}'),
('edge_004', '广州天河监控节点', 'monitor', 'maintenance', '广州市天河区天河路208号', '192.168.1.104', 'v2.0.8', DATE_SUB(NOW(), INTERVAL 2 HOUR), '{"camera_count": 12, "storage_days": 30}', '{"deployment_date": "2024-01-12", "maintenance_scheduled": "2024-01-20"}'),
('edge_005', '杭州西湖智能控制器', 'controller', 'online', '杭州市西湖区文三路259号', '192.168.1.105', 'v2.2.1', NOW(), '{"control_points": 25, "response_time_ms": 50}', '{"deployment_date": "2024-01-15", "control_systems": ["lighting", "charging", "traffic"]}');

-- 显示插入结果统计
SELECT 'users' as table_name, COUNT(*) as record_count FROM users
UNION ALL
SELECT 'vehicles', COUNT(*) FROM vehicles
UNION ALL
SELECT 'batteries', COUNT(*) FROM batteries
UNION ALL
SELECT 'charging_stations', COUNT(*) FROM charging_stations
UNION ALL
SELECT 'charging_sessions', COUNT(*) FROM charging_sessions
UNION ALL
SELECT 'carbon_credit', COUNT(*) FROM carbon_credit
UNION ALL
SELECT 'driving_behavior', COUNT(*) FROM driving_behavior
UNION ALL
SELECT 'fleet_management', COUNT(*) FROM fleet_management
UNION ALL
SELECT 'edge_devices', COUNT(*) FROM edge_devices;

SELECT '✅ 测试数据插入完成！' as status;