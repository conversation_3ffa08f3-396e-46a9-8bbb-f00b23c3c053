<template>
  <div class="charging-station-map">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <el-icon class="title-icon"><Location /></el-icon>
            充电站地图
          </h1>
          <p class="page-description">充电站位置导航、实时状态、路径规划</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="locateMe">
            <el-icon><Aim /></el-icon>
            定位我的位置
          </el-button>
          <el-button @click="showNearbyStations">
            <el-icon><Search /></el-icon>
            附近充电站
          </el-button>
          <el-button @click="refreshData">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
    </div>

    <!-- 地图控制面板 -->
    <div class="map-controls">
      <div class="controls-content">
        <div class="search-section">
          <el-input
            v-model="searchLocation"
            placeholder="搜索地址或充电站"
            style="width: 300px"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
            <template #append>
              <el-button @click="searchStations">搜索</el-button>
            </template>
          </el-input>
        </div>
        
        <div class="filter-section">
          <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 120px">
            <el-option label="全部状态" value="" />
            <el-option label="可用" value="available" />
            <el-option label="使用中" value="busy" />
            <el-option label="维护中" value="maintenance" />
            <el-option label="离线" value="offline" />
          </el-select>
          
          <el-select v-model="powerFilter" placeholder="功率筛选" style="width: 120px">
            <el-option label="全部功率" value="" />
            <el-option label="快充(>50kW)" value="fast" />
            <el-option label="慢充(≤50kW)" value="slow" />
          </el-select>
          
          <el-slider
            v-model="radiusFilter"
            :min="1"
            :max="20"
            :step="1"
            style="width: 150px"
            show-input
          />
          <span style="margin-left: 8px">km</span>
        </div>
      </div>
    </div>

    <!-- 地图和侧边栏 -->
    <div class="map-container">
      <div class="map-section">
        <!-- 地图区域 -->
        <div class="map-wrapper">
          <div class="map-placeholder">
            <div class="map-content">
              <el-icon class="map-icon"><Location /></el-icon>
              <h3>充电站地图</h3>
              <p>显示所有充电站位置和实时状态</p>
              <div class="map-stats">
                <div class="stat-item">
                  <span class="stat-value">{{ visibleStations.length }}</span>
                  <span class="stat-label">可见充电站</span>
                </div>
                <div class="stat-item">
                  <span class="stat-value">{{ availableStations }}</span>
                  <span class="stat-label">可用充电站</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 充电站列表侧边栏 -->
      <div class="sidebar">
        <div class="sidebar-header">
          <h3>充电站列表</h3>
          <div class="list-controls">
            <el-button size="small" @click="sortByDistance">
              <el-icon><Sort /></el-icon>
              按距离排序
            </el-button>
          </div>
        </div>
        
        <div class="stations-list">
          <div
            v-for="station in visibleStations"
            :key="station.id"
            class="station-item"
            :class="{ selected: selectedStation?.id === station.id }"
            @click="selectStation(station)"
          >
            <div class="station-header">
              <div class="station-name">{{ station.name }}</div>
              <div class="station-status">
                <el-tag :type="getStatusType(station.status)" size="small">
                  {{ getStatusText(station.status) }}
                </el-tag>
              </div>
            </div>
            
            <div class="station-info">
              <div class="info-row">
                <el-icon><Location /></el-icon>
                <span class="address">{{ station.address }}</span>
              </div>
              <div class="info-row">
                <el-icon><User /></el-icon>
                <span>{{ station.operator }}</span>
              </div>
              <div class="info-row">
                <el-icon><Connection /></el-icon>
                <span>{{ station.available_ports }}/{{ station.total_ports }} 可用</span>
              </div>
              <div class="info-row">
                <el-icon><Lightning /></el-icon>
                <span>{{ station.power }} kW</span>
              </div>
            </div>
            
            <div class="station-actions">
              <el-button size="small" @click.stop="navigateToStation(station)">
                <el-icon><Guide /></el-icon>
                导航
              </el-button>
              <el-button size="small" type="primary" @click.stop="makeReservation(station)">
                <el-icon><Clock /></el-icon>
                预约
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 充电站详情弹窗 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="充电站详情"
      width="600px"
    >
      <div v-if="selectedStation" class="station-detail">
        <div class="detail-header">
          <h2>{{ selectedStation.name }}</h2>
          <el-tag :type="getStatusType(selectedStation.status)">
            {{ getStatusText(selectedStation.status) }}
          </el-tag>
        </div>
        
        <div class="detail-content">
          <div class="detail-grid">
            <div class="detail-item">
              <label>地址:</label>
              <span>{{ selectedStation.address }}</span>
            </div>
            <div class="detail-item">
              <label>运营商:</label>
              <span>{{ selectedStation.operator }}</span>
            </div>
            <div class="detail-item">
              <label>充电桩总数:</label>
              <span>{{ selectedStation.total_ports }}</span>
            </div>
            <div class="detail-item">
              <label>可用充电桩:</label>
              <span>{{ selectedStation.available_ports }}</span>
            </div>
            <div class="detail-item">
              <label>充电功率:</label>
              <span>{{ selectedStation.power }} kW</span>
            </div>
            <div class="detail-item">
              <label>充电价格:</label>
              <span>¥{{ selectedStation.price }}/kWh</span>
            </div>
          </div>
        </div>
        
        <div class="detail-actions">
          <el-button @click="navigateToStation(selectedStation)">
            <el-icon><Guide /></el-icon>
            导航到此站
          </el-button>
          <el-button type="primary" @click="makeReservation(selectedStation)">
            <el-icon><Clock /></el-icon>
            立即预约
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useChargingStore } from '@/stores/charging'
import {
  Location,
  Aim,
  Search,
  Refresh,
  Sort,
  User,
  Connection,
  Lightning,
  Guide,
  Clock
} from '@element-plus/icons-vue'

const chargingStore = useChargingStore()

// 响应式数据
const searchLocation = ref('')
const statusFilter = ref('')
const powerFilter = ref('')
const radiusFilter = ref(10)
const selectedStation = ref(null)
const detailDialogVisible = ref(false)

// 计算属性
const visibleStations = computed(() => {
  let filtered = chargingStore.stations
  
  if (statusFilter.value) {
    if (statusFilter.value === 'available') {
      filtered = filtered.filter(s => s.status === 'active' && s.available_ports > 0)
    } else if (statusFilter.value === 'busy') {
      filtered = filtered.filter(s => s.status === 'active' && s.available_ports === 0)
    } else {
      filtered = filtered.filter(s => s.status === statusFilter.value)
    }
  }
  
  if (powerFilter.value) {
    if (powerFilter.value === 'fast') {
      filtered = filtered.filter(s => s.power > 50)
    } else if (powerFilter.value === 'slow') {
      filtered = filtered.filter(s => s.power <= 50)
    }
  }
  
  return filtered
})

const availableStations = computed(() => 
  visibleStations.value.filter(s => s.status === 'active' && s.available_ports > 0).length
)

// 方法
const refreshData = async () => {
  try {
    await chargingStore.fetchStations()
    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败')
  }
}

const locateMe = () => {
  ElMessage.info('正在获取您的位置...')
  // 这里可以实现地理定位功能
}

const showNearbyStations = () => {
  ElMessage.info('正在搜索附近充电站...')
  // 这里可以实现附近充电站搜索
}

const searchStations = () => {
  if (!searchLocation.value.trim()) {
    ElMessage.warning('请输入搜索内容')
    return
  }
  ElMessage.info(`正在搜索: ${searchLocation.value}`)
  // 这里可以实现搜索功能
}

const sortByDistance = () => {
  ElMessage.info('按距离排序功能开发中...')
  // 这里可以实现距离排序
}

const selectStation = (station: any) => {
  selectedStation.value = station
  detailDialogVisible.value = true
}

const getStatusType = (status: string) => {
  const types = {
    active: 'success',
    maintenance: 'warning',
    offline: 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts = {
    active: '正常',
    maintenance: '维护中',
    offline: '离线'
  }
  return texts[status] || '未知'
}

const navigateToStation = (station: any) => {
  ElMessage.success(`正在导航到 ${station.name}`)
  // 这里可以实现导航功能
}

const makeReservation = (station: any) => {
  ElMessage.info(`正在为 ${station.name} 创建预约`)
  // 这里可以跳转到预约页面或打开预约对话框
}

// 生命周期
onMounted(async () => {
  await refreshData()
})
</script>

<style scoped>
.charging-station-map {
  padding: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.page-header {
  margin-bottom: 32px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 36px;
  color: #667eea;
}

.page-description {
  font-size: 16px;
  color: #718096;
  margin: 8px 0 0 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.map-controls {
  margin-bottom: 24px;
}

.controls-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.filter-section {
  display: flex;
  gap: 16px;
  align-items: center;
}

.map-container {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 24px;
  height: calc(100vh - 280px);
}

.map-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.map-wrapper {
  height: 100%;
  position: relative;
}

.map-placeholder {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
}

.map-content {
  text-align: center;
  color: #4a5568;
}

.map-icon {
  font-size: 64px;
  color: #667eea;
  margin-bottom: 16px;
}

.map-content h3 {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #1a202c;
}

.map-content p {
  font-size: 16px;
  color: #718096;
  margin: 0 0 24px 0;
}

.map-stats {
  display: flex;
  gap: 32px;
  justify-content: center;
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 28px;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #718096;
}

.sidebar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 24px;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sidebar-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: #1a202c;
  margin: 0;
}

.stations-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.station-item {
  background: #f7fafc;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.station-item:hover {
  background: #edf2f7;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.station-item.selected {
  border-color: #667eea;
  background: #e6f3ff;
}

.station-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.station-name {
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
}

.station-info {
  margin-bottom: 16px;
}

.info-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 14px;
  color: #4a5568;
}

.address {
  font-size: 13px;
  color: #718096;
}

.station-actions {
  display: flex;
  gap: 8px;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e2e8f0;
}

.detail-header h2 {
  font-size: 24px;
  font-weight: 600;
  color: #1a202c;
  margin: 0;
}

.detail-content {
  margin-bottom: 24px;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #e2e8f0;
}

.detail-item label {
  font-weight: 600;
  color: #4a5568;
}

.detail-item span {
  color: #1a202c;
}

.detail-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

@media (max-width: 1200px) {
  .map-container {
    grid-template-columns: 1fr;
    height: auto;
  }
  
  .map-section {
    height: 400px;
  }
  
  .sidebar {
    height: 500px;
  }
}

@media (max-width: 768px) {
  .charging-station-map {
    padding: 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }
  
  .controls-content {
    flex-direction: column;
    gap: 16px;
  }
  
  .filter-section {
    width: 100%;
    justify-content: center;
  }
  
  .map-container {
    gap: 16px;
  }
  
  .detail-grid {
    grid-template-columns: 1fr;
  }
}
</style>
