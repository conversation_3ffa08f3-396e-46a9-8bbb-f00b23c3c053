<template>
  <el-dialog
    v-model="dialogVisible"
    title="车辆详情"
    width="800px"
    @close="$emit('close')"
    class="vehicle-detail-dialog"
  >
    <div v-loading="loading" element-loading-text="加载车辆详情中...">
      <div v-if="vehicle" class="vehicle-detail">
        <!-- 车辆基本信息 -->
        <div class="detail-section">
          <div class="section-header">
            <h3>基本信息</h3>
            <el-tag :type="getStatusType(vehicle.status)" size="large">
              {{ getStatusText(vehicle.status) }}
            </el-tag>
          </div>
          
          <div class="info-grid">
            <div class="info-item">
              <span class="info-label">品牌</span>
              <span class="info-value">{{ vehicle.brand }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">型号</span>
              <span class="info-value">{{ vehicle.model }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">车牌号</span>
              <span class="info-value">{{ vehicle.license_plate || '未设置' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">VIN码</span>
              <span class="info-value">{{ vehicle.vin }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">年份</span>
              <span class="info-value">{{ vehicle.year || '未知' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">颜色</span>
              <span class="info-value">{{ vehicle.color || '未设置' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">车辆类型</span>
              <span class="info-value">{{ getVehicleTypeText(vehicle.vehicle_type) }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">电池容量</span>
              <span class="info-value">{{ vehicle.battery_capacity || '未知' }} kWh</span>
            </div>
          </div>
        </div>

        <!-- 实时状态 -->
        <div class="detail-section">
          <div class="section-header">
            <h3>实时状态</h3>
            <el-button size="small" @click="refreshStatus">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
          
          <div class="status-grid">
            <div class="status-card battery">
              <div class="status-icon">
                <el-icon><Lightning /></el-icon>
              </div>
              <div class="status-content">
                <div class="status-title">电池电量</div>
                <div class="status-value">{{ batteryStatus.level }}%</div>
                <el-progress 
                  :percentage="batteryStatus.level" 
                  :show-text="false"
                  :stroke-width="8"
                  :color="getBatteryColor(batteryStatus.level)"
                />
                <div class="status-detail">
                  健康度: {{ batteryStatus.health }}% | 温度: {{ batteryStatus.temperature }}°C
                </div>
              </div>
            </div>
            
            <div class="status-card location">
              <div class="status-icon">
                <el-icon><Location /></el-icon>
              </div>
              <div class="status-content">
                <div class="status-title">位置信息</div>
                <div class="status-value">{{ locationStatus.address }}</div>
                <div class="status-detail">
                  经度: {{ locationStatus.lng }} | 纬度: {{ locationStatus.lat }}
                </div>
                <div class="status-detail">
                  最后更新: {{ formatTime(locationStatus.lastUpdate) }}
                </div>
              </div>
            </div>
            
            <div class="status-card mileage">
              <div class="status-icon">
                <el-icon><Odometer /></el-icon>
              </div>
              <div class="status-content">
                <div class="status-title">行驶数据</div>
                <div class="status-value">{{ mileageStatus.total.toLocaleString() }} km</div>
                <div class="status-detail">
                  今日: {{ mileageStatus.today }} km | 本月: {{ mileageStatus.thisMonth }} km
                </div>
                <div class="status-detail">
                  平均速度: {{ mileageStatus.avgSpeed }} km/h
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 维护记录 -->
        <div class="detail-section">
          <div class="section-header">
            <h3>维护记录</h3>
            <el-button size="small" type="primary" @click="addMaintenanceRecord">
              <el-icon><Plus /></el-icon>
              添加记录
            </el-button>
          </div>
          
          <div class="maintenance-timeline">
            <div v-for="record in maintenanceRecords" :key="record.id" class="timeline-item">
              <div class="timeline-dot" :class="record.type"></div>
              <div class="timeline-content">
                <div class="timeline-title">{{ record.title }}</div>
                <div class="timeline-description">{{ record.description }}</div>
                <div class="timeline-date">{{ formatDate(record.date) }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="$emit('close')">关闭</el-button>
        <el-button type="primary" @click="editVehicle">编辑车辆</el-button>
        <el-button type="success" @click="controlVehicle">远程控制</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Lightning, Location, Odometer, Refresh, Plus 
} from '@element-plus/icons-vue'
import { useVehicleStore } from '@/stores/vehicle'
import type { Vehicle } from '@/stores/vehicle'

// Props
const props = defineProps<{
  modelValue: boolean
  vehicleId: string
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'close': []
}>()

// Store
const vehicleStore = useVehicleStore()

// 响应式数据
const loading = ref(false)
const vehicle = ref<Vehicle | null>(null)

// 模拟状态数据
const batteryStatus = ref({
  level: 85,
  health: 96,
  temperature: 25
})

const locationStatus = ref({
  address: '北京市朝阳区建国门外大街',
  lng: 116.4317,
  lat: 39.9088,
  lastUpdate: new Date()
})

const mileageStatus = ref({
  total: 45678,
  today: 125,
  thisMonth: 2340,
  avgSpeed: 45
})

const maintenanceRecords = ref([
  {
    id: 1,
    type: 'maintenance',
    title: '定期保养',
    description: '更换空调滤芯，检查轮胎磨损',
    date: '2024-01-15'
  },
  {
    id: 2,
    type: 'repair',
    title: '故障维修',
    description: '修复左前门把手传感器',
    date: '2023-12-08'
  },
  {
    id: 3,
    type: 'upgrade',
    title: '软件升级',
    description: '升级车机系统到最新版本',
    date: '2023-11-20'
  }
])

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 监听器
watch(() => props.vehicleId, async (newId) => {
  if (newId && props.modelValue) {
    await loadVehicleDetail(newId)
  }
})

// 方法
const loadVehicleDetail = async (vehicleId: string) => {
  loading.value = true
  try {
    await vehicleStore.fetchVehicle(vehicleId)
    vehicle.value = vehicleStore.currentVehicle
    // 模拟加载实时状态数据
    await loadRealtimeStatus()
  } catch (error) {
    ElMessage.error('加载车辆详情失败')
  } finally {
    loading.value = false
  }
}

const loadRealtimeStatus = async () => {
  // 模拟API调用延迟
  await new Promise(resolve => setTimeout(resolve, 500))
  
  // 模拟随机状态数据
  batteryStatus.value = {
    level: Math.floor(Math.random() * 40) + 60,
    health: Math.floor(Math.random() * 10) + 90,
    temperature: Math.floor(Math.random() * 20) + 15
  }
  
  locationStatus.value.lastUpdate = new Date()
}

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    'active': 'success',
    'maintenance': 'warning',
    'retired': 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    'active': '在线',
    'maintenance': '维护中',
    'retired': '已退役'
  }
  return texts[status] || status
}

const getVehicleTypeText = (type: string) => {
  const texts: Record<string, string> = {
    'bev': '纯电动',
    'phev': '插电混动',
    'fcev': '燃料电池'
  }
  return texts[type] || type
}

const getBatteryColor = (level: number) => {
  if (level > 80) return '#10b981'
  if (level > 50) return '#f59e0b'
  if (level > 20) return '#ef4444'
  return '#dc2626'
}

const formatTime = (date: Date) => {
  return date.toLocaleString('zh-CN')
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const refreshStatus = async () => {
  await loadRealtimeStatus()
  ElMessage.success('状态刷新成功')
}

const editVehicle = () => {
  // 触发编辑事件
  emit('close')
  // 这里应该打开编辑表单
}

const controlVehicle = () => {
  // 跳转到远程控制页面
  window.open(`/vehicle-network?vehicleId=${props.vehicleId}`, '_blank')
}

const addMaintenanceRecord = () => {
  ElMessage.info('维护记录功能开发中...')
}
</script>

<style scoped>
.vehicle-detail-dialog :deep(.el-dialog) {
  border-radius: 16px;
  overflow: hidden;
}

.vehicle-detail-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 24px;
}

.vehicle-detail-dialog :deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
}

.vehicle-detail {
  padding: 0;
}

/* 详情区域 */
.detail-section {
  margin-bottom: 32px;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid #f3f4f6;
}

.section-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

/* 信息网格 */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.info-value {
  font-size: 14px;
  color: #111827;
  font-weight: 600;
}

/* 状态网格 */
.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.status-card {
  background: #f9fafb;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  display: flex;
  gap: 16px;
}

.status-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  flex-shrink: 0;
}

.status-card.battery .status-icon {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.status-card.location .status-icon {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.status-card.mileage .status-icon {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.status-content {
  flex: 1;
}

.status-title {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 4px;
}

.status-value {
  font-size: 20px;
  font-weight: 700;
  color: #111827;
  margin-bottom: 8px;
}

.status-detail {
  font-size: 12px;
  color: #9ca3af;
  margin-bottom: 4px;
}

/* 维护时间线 */
.maintenance-timeline {
  position: relative;
  padding-left: 24px;
}

.maintenance-timeline::before {
  content: '';
  position: absolute;
  left: 8px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #e5e7eb;
}

.timeline-item {
  position: relative;
  margin-bottom: 24px;
  display: flex;
  gap: 16px;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-dot {
  position: absolute;
  left: -20px;
  top: 4px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
  z-index: 1;
}

.timeline-dot.maintenance {
  background: #10b981;
}

.timeline-dot.repair {
  background: #ef4444;
}

.timeline-dot.upgrade {
  background: #3b82f6;
}

.timeline-content {
  flex: 1;
  background: #f9fafb;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e5e7eb;
}

.timeline-title {
  font-size: 14px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 4px;
}

.timeline-description {
  font-size: 13px;
  color: #6b7280;
  margin-bottom: 8px;
  line-height: 1.4;
}

.timeline-date {
  font-size: 12px;
  color: #9ca3af;
}

/* 对话框底部 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .vehicle-detail-dialog :deep(.el-dialog) {
    width: 95% !important;
    margin: 5vh auto;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .status-grid {
    grid-template-columns: 1fr;
  }

  .section-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .dialog-footer {
    flex-direction: column;
  }
}
</style>
