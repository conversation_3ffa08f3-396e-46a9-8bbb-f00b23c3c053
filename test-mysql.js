/**
 * MySQL 连接测试脚本
 * 用于验证 MySQL 数据库连接配置
 */

const mysql = require('mysql2/promise')
require('dotenv').config()

// MySQL 连接配置
const mysqlConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  charset: 'utf8mb4'
}

async function testConnection() {
  try {
    console.log('🔍 测试 MySQL 连接...')
    console.log('配置信息:')
    console.log(`  主机: ${mysqlConfig.host}`)
    console.log(`  端口: ${mysqlConfig.port}`)
    console.log(`  用户: ${mysqlConfig.user}`)
    console.log(`  密码: ${mysqlConfig.password ? '***' : '(空)'}`)
    
    const connection = await mysql.createConnection(mysqlConfig)
    await connection.execute('SELECT 1 as test')
    console.log('✅ MySQL 连接成功！')
    
    // 检查数据库是否存在
    const dbName = process.env.DB_NAME || 'ev_management'
    const [databases] = await connection.execute(
      'SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?',
      [dbName]
    )
    
    if (databases.length > 0) {
      console.log(`✅ 数据库 '${dbName}' 已存在`)
    } else {
      console.log(`⚠️  数据库 '${dbName}' 不存在，需要创建`)
      console.log(`\n创建数据库的 SQL 命令:`)
      console.log(`CREATE DATABASE ${dbName} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;`)
    }
    
    await connection.end()
    return true
  } catch (error) {
    console.error('❌ MySQL 连接失败:', error.message)
    
    if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('\n🔧 解决方案：')
      console.log('1. 检查 MySQL 用户名和密码是否正确')
      console.log('2. 确保 MySQL 服务正在运行')
      console.log('3. 如果是新安装的 MySQL，可能需要设置 root 密码')
      console.log('\n设置 MySQL root 密码的命令:')
      console.log('mysql -u root')
      console.log('ALTER USER \'root\'@\'localhost\' IDENTIFIED BY \'your_password\';')
      console.log('FLUSH PRIVILEGES;')
    } else if (error.code === 'ECONNREFUSED') {
      console.log('\n🔧 解决方案：')
      console.log('1. 启动 MySQL 服务')
      console.log('2. 检查 MySQL 是否安装')
      console.log('3. 检查端口 3306 是否被占用')
    }
    
    return false
  }
}

async function createDatabase() {
  try {
    const connection = await mysql.createConnection(mysqlConfig)
    const dbName = process.env.DB_NAME || 'ev_management'
    
    await connection.execute(`CREATE DATABASE IF NOT EXISTS ${dbName} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`)
    console.log(`✅ 数据库 '${dbName}' 创建成功！`)
    
    await connection.end()
    return true
  } catch (error) {
    console.error('❌ 创建数据库失败:', error.message)
    return false
  }
}

async function main() {
  console.log('🚀 MySQL 连接测试\n')
  
  const connected = await testConnection()
  
  if (connected) {
    console.log('\n✅ 数据库连接测试通过！')
    console.log('\n📋 下一步：')
    console.log('1. 运行 mysql_init.sql 初始化表结构')
    console.log('2. 运行 migrate-to-mysql.js 完成迁移')
  } else {
    console.log('\n❌ 请先解决数据库连接问题')
  }
}

if (require.main === module) {
  main().catch(console.error)
}

module.exports = { testConnection, createDatabase }