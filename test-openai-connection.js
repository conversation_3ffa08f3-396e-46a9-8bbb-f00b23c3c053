const axios = require('axios');
require('dotenv').config();

async function testOpenAIConnection() {
  console.log('🔑 测试OpenAI API连接...\n');
  
  const apiKey = process.env.OPENAI_API_KEY;
  
  if (!apiKey || apiKey === 'your-openai-api-key-here' || apiKey === 'sk-your-actual-api-key-here') {
    console.log('❌ OpenAI API密钥未配置');
    console.log('请按照以下步骤配置：');
    console.log('1. 访问 https://platform.openai.com/api-keys');
    console.log('2. 创建新的API密钥');
    console.log('3. 在 .env 文件中设置 OPENAI_API_KEY=sk-your-actual-key');
    console.log('4. 重启服务器');
    return;
  }
  
  console.log('✅ API密钥已配置');
  console.log(`🔑 密钥前缀: ${apiKey.substring(0, 7)}...${apiKey.substring(apiKey.length - 4)}`);
  
  try {
    // 测试简单的API调用
    console.log('\n🧪 测试API连接...');
    
    const response = await axios.post('https://api.openai.com/v1/chat/completions', {
      model: 'gpt-3.5-turbo',
      messages: [
        {
          role: 'user',
          content: '请简单回答：你好'
        }
      ],
      max_tokens: 50,
      temperature: 0.7
    }, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });
    
    if (response.data && response.data.choices && response.data.choices[0]) {
      console.log('✅ OpenAI API连接成功！');
      console.log(`🤖 测试回答: ${response.data.choices[0].message.content}`);
      console.log(`💰 使用tokens: ${response.data.usage.total_tokens}`);
      
      // 测试EVAdmin Pro AI助手
      console.log('\n🎯 测试EVAdmin Pro AI助手...');
      
      const aiResponse = await axios.post('http://localhost:3001/api/ai-enhanced/chat', {
        message: '你好，请介绍一下你的功能'
      });
      
      if (aiResponse.data.success) {
        console.log('✅ AI助手连接成功！');
        console.log(`🤖 AI助手回答: ${aiResponse.data.response.substring(0, 100)}...`);
        
        // 检查是否使用了OpenAI
        const usingOpenAI = !aiResponse.data.response.includes('本地知识库');
        console.log(`🔄 当前模式: ${usingOpenAI ? 'OpenAI智能模式' : '本地知识库模式'}`);
      } else {
        console.log('❌ AI助手连接失败');
      }
      
    } else {
      console.log('❌ OpenAI API响应格式异常');
    }
    
  } catch (error) {
    console.log('❌ OpenAI API连接失败');
    
    if (error.response) {
      const status = error.response.status;
      const message = error.response.data?.error?.message || error.response.statusText;
      
      console.log(`状态码: ${status}`);
      console.log(`错误信息: ${message}`);
      
      if (status === 401) {
        console.log('\n🔧 解决方案:');
        console.log('- 检查API密钥是否正确');
        console.log('- 确认API密钥有效且未过期');
        console.log('- 检查账户是否有足够余额');
      } else if (status === 429) {
        console.log('\n🔧 解决方案:');
        console.log('- API调用频率过高，请稍后再试');
        console.log('- 检查账户的速率限制设置');
      } else if (status === 403) {
        console.log('\n🔧 解决方案:');
        console.log('- 检查API密钥权限设置');
        console.log('- 确认账户状态正常');
      }
    } else if (error.code === 'ECONNABORTED') {
      console.log('⏱️ 请求超时，可能是网络问题');
      console.log('🔧 解决方案: 检查网络连接或使用VPN');
    } else {
      console.log(`网络错误: ${error.message}`);
    }
  }
}

testOpenAIConnection();
