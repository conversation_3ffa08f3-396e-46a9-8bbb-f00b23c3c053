import { Router } from 'express'
import { db } from '../config/mysql'
import type { ApiResponse } from '../../shared/types'

const router = Router()

// 获取ADAS系统状态
router.get('/status', async (req, res) => {
  try {
    const { vehicle_id } = req.query
    
    // 模拟ADAS系统状态数据
    const adasStatus = {
      systemStatus: 'active',
      lastUpdate: new Date().toISOString(),
      features: {
        collisionWarning: {
          enabled: true,
          status: 'normal',
          sensitivity: 'medium',
          lastAlert: null
        },
        laneKeeping: {
          enabled: true,
          status: 'active',
          confidence: 95,
          lastCorrection: new Date(Date.now() - 300000).toISOString()
        },
        adaptiveCruise: {
          enabled: false,
          status: 'standby',
          targetSpeed: null,
          followDistance: 'medium'
        },
        blindSpotMonitoring: {
          enabled: true,
          status: 'monitoring',
          leftSide: 'clear',
          rightSide: 'clear'
        },
        automaticBraking: {
          enabled: true,
          status: 'ready',
          threshold: 'normal',
          lastActivation: null
        }
      },
      alerts: [
        {
          id: 1,
          type: 'info',
          message: 'ADAS系统运行正常',
          timestamp: new Date().toISOString(),
          severity: 'low'
        }
      ],
      statistics: {
        totalAlerts: 12,
        preventedCollisions: 2,
        laneCorrections: 45,
        systemUptime: '99.8%'
      }
    }
    
    res.json({
      success: true,
      data: adasStatus
    } as ApiResponse)
  } catch (error) {
    console.error('获取ADAS状态异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 更新ADAS设置
router.put('/settings', async (req, res) => {
  try {
    const { vehicle_id, settings } = req.body
    
    if (!vehicle_id || !settings) {
      return res.status(400).json({
        success: false,
        error: '车辆ID和设置参数为必填字段'
      } as ApiResponse)
    }
    
    // 模拟更新成功
    res.json({
      success: true,
      message: 'ADAS设置更新成功',
      data: {
        vehicle_id,
        settings,
        updated_at: new Date().toISOString()
      }
    } as ApiResponse)
  } catch (error) {
    console.error('更新ADAS设置异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取ADAS历史数据
router.get('/history', async (req, res) => {
  try {
    const { vehicle_id, start_date, end_date, page = 1, limit = 20 } = req.query
    
    // 模拟历史数据
    const history = Array.from({ length: Number(limit) }, (_, i) => ({
      id: i + 1,
      timestamp: new Date(Date.now() - i * 3600000).toISOString(),
      event_type: ['collision_warning', 'lane_correction', 'blind_spot_alert', 'emergency_brake'][i % 4],
      severity: ['low', 'medium', 'high'][i % 3],
      description: `ADAS事件 ${i + 1}`,
      location: {
        latitude: 39.9042 + (Math.random() - 0.5) * 0.1,
        longitude: 116.4074 + (Math.random() - 0.5) * 0.1
      },
      vehicle_speed: Math.floor(Math.random() * 80) + 20,
      weather_condition: 'clear'
    }))
    
    res.json({
      success: true,
      data: history,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: 200,
        totalPages: Math.ceil(200 / Number(limit))
      }
    } as ApiResponse)
  } catch (error) {
    console.error('获取ADAS历史数据异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

export default router