/* 现代化主题样式 - 基于Uiverse.io设计理念 */
/* 新能源汽车智能综合管理系统 - 现代化UI设计系统 */

/* CSS自定义属性 - 颜色系统 */
:root {
  /* 主色调 - 科技蓝紫渐变 */
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --primary-color: #667eea;
  --primary-light: #8b9aff;
  --primary-dark: #4c63d2;
  --primary-shadow: rgba(102, 126, 234, 0.4);
  
  /* 次要色调 - 粉紫渐变 */
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --secondary-color: #f093fb;
  --secondary-light: #ff9eff;
  --secondary-dark: #d16fe8;
  --secondary-shadow: rgba(240, 147, 251, 0.4);
  
  /* 成功色调 - 青蓝渐变 */
  --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --success-color: #4facfe;
  --success-light: #7bc4ff;
  --success-dark: #2b94eb;
  --success-shadow: rgba(79, 172, 254, 0.4);
  
  /* 警告色调 - 橙粉渐变 */
  --warning-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  --warning-color: #fa709a;
  --warning-light: #ff8bb5;
  --warning-dark: #e75f87;
  --warning-shadow: rgba(250, 112, 154, 0.4);
  
  /* 危险色调 - 红橙渐变 */
  --danger-gradient: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  --danger-color: #ff6b6b;
  --danger-light: #ff8e8e;
  --danger-dark: #ff4848;
  --danger-shadow: rgba(255, 107, 107, 0.4);
  
  /* 霓虹色调 */
  --neon-cyan: #00f5ff;
  --neon-pink: #ff6b6b;
  --neon-green: #4ecdc4;
  --neon-purple: #a78bfa;
  --neon-yellow: #fbbf24;
  
  /* 中性色调 */
  --white: #ffffff;
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  --black: #000000;
  
  /* 文字颜色系统 */
  --text-primary: #ffffff;
  --text-secondary: #d1d5db;
  --text-tertiary: #9ca3af;
  --text-muted: #6b7280;
  --text-inverse: #111827;
  --text-on-primary: #ffffff;
  --text-on-secondary: #ffffff;
  --text-on-light: #111827;
  --text-on-dark: #ffffff;
  
  /* 背景色调 */
  --bg-primary: #0a0a0f;
  --bg-secondary: #1a1a2e;
  --bg-tertiary: #16213e;
  --bg-glass: rgba(255, 255, 255, 0.1);
  --bg-glass-hover: rgba(255, 255, 255, 0.15);
  --bg-dark-glass: rgba(0, 0, 0, 0.3);
  
  /* 边框色调 */
  --border-glass: rgba(255, 255, 255, 0.2);
  --border-light: rgba(255, 255, 255, 0.1);
  --border-dark: rgba(0, 0, 0, 0.2);
  
  /* 阴影系统 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  
  /* 现代化阴影 */
  --modern-shadow-sm: 0 4px 16px 0;
  --modern-shadow-md: 0 8px 32px 0;
  --modern-shadow-lg: 0 16px 64px 0;
  --modern-shadow-xl: 0 24px 96px 0;
  
  /* 玻璃态效果 */
  --glass-backdrop: blur(20px);
  --glass-backdrop-sm: blur(10px);
  --glass-backdrop-lg: blur(30px);
  
  /* 边框圆角 */
  --radius-sm: 6px;
  --radius: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 20px;
  --radius-2xl: 24px;
  --radius-full: 9999px;
  
  /* 动画时长 */
  --duration-fast: 0.15s;
  --duration-normal: 0.3s;
  --duration-slow: 0.5s;
  --duration-slower: 0.75s;
  
  /* 动画缓动 */
  --ease-smooth: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --ease-elastic: cubic-bezier(0.175, 0.885, 0.32, 1.275);
  
  /* 字体系统 */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
  
  /* 字体大小 */
  --text-xs: 0.75rem;    /* 12px */
  --text-sm: 0.875rem;   /* 14px */
  --text-base: 1rem;     /* 16px */
  --text-lg: 1.125rem;   /* 18px */
  --text-xl: 1.25rem;    /* 20px */
  --text-2xl: 1.5rem;    /* 24px */
  --text-3xl: 1.875rem;  /* 30px */
  --text-4xl: 2.25rem;   /* 36px */
  --text-5xl: 3rem;      /* 48px */
  --text-6xl: 3.75rem;   /* 60px */
  
  /* 字体粗细 */
  --font-thin: 100;
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;
  --font-black: 900;
  
  /* 间距系统 */
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */
  --space-20: 5rem;     /* 80px */
  --space-24: 6rem;     /* 96px */
  
  /* Z-index 层级 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* 全局基础样式重置 */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: var(--font-family);
  background: var(--bg-primary);
  color: var(--text-primary);
  margin: 0;
  padding: 0;
  min-height: 100vh;
  overflow-x: hidden;
}

/* 现代化滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-gradient);
  border-radius: var(--radius-full);
  transition: background var(--duration-normal) var(--ease-smooth);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #8b9aff 0%, #9d7bc8 100%);
}

/* 选择文本样式 */
::selection {
  background: var(--primary-color);
  color: var(--text-on-primary);
}

::-moz-selection {
  background: var(--primary-color);
  color: var(--text-on-primary);
}

/* 现代化工具类 */
.modern-gradient-primary {
  background: var(--primary-gradient);
}

.modern-gradient-secondary {
  background: var(--secondary-gradient);
}

.modern-gradient-success {
  background: var(--success-gradient);
}

.modern-gradient-warning {
  background: var(--warning-gradient);
}

.modern-gradient-danger {
  background: var(--danger-gradient);
}

/* 玻璃态效果类 */
.modern-glass {
  background: var(--bg-glass);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--border-glass);
}

.modern-glass-hover:hover {
  background: var(--bg-glass-hover);
}

.modern-glass-dark {
  background: var(--bg-dark-glass);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--border-dark);
}

/* 阴影效果类 */
.modern-shadow-primary {
  box-shadow: var(--modern-shadow-md) var(--primary-shadow);
}

.modern-shadow-secondary {
  box-shadow: var(--modern-shadow-md) var(--secondary-shadow);
}

.modern-shadow-success {
  box-shadow: var(--modern-shadow-md) var(--success-shadow);
}

.modern-shadow-warning {
  box-shadow: var(--modern-shadow-md) var(--warning-shadow);
}

.modern-shadow-danger {
  box-shadow: var(--modern-shadow-md) var(--danger-shadow);
}

/* 霓虹效果类 */
.modern-neon-cyan {
  color: var(--neon-cyan);
  text-shadow: 0 0 10px var(--neon-cyan);
  box-shadow: 0 0 20px var(--neon-cyan);
}

.modern-neon-pink {
  color: var(--neon-pink);
  text-shadow: 0 0 10px var(--neon-pink);
  box-shadow: 0 0 20px var(--neon-pink);
}

.modern-neon-green {
  color: var(--neon-green);
  text-shadow: 0 0 10px var(--neon-green);
  box-shadow: 0 0 20px var(--neon-green);
}

/* 动画类 */
.modern-transition {
  transition: all var(--duration-normal) var(--ease-smooth);
}

.modern-transition-fast {
  transition: all var(--duration-fast) var(--ease-smooth);
}

.modern-transition-slow {
  transition: all var(--duration-slow) var(--ease-smooth);
}

/* 悬浮效果类 */
.modern-hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: var(--modern-shadow-lg) var(--primary-shadow);
}

.modern-hover-scale:hover {
  transform: scale(1.05);
}

.modern-hover-glow:hover {
  box-shadow: 
    var(--modern-shadow-md) var(--primary-shadow),
    0 0 40px var(--primary-color);
}

/* 响应式工具类 */
@media (max-width: 768px) {
  .modern-mobile-hidden {
    display: none;
  }
  
  .modern-mobile-full {
    width: 100%;
  }
}

@media (min-width: 769px) {
  .modern-desktop-hidden {
    display: none;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #0a0a0f;
    --bg-secondary: #1a1a2e;
    --bg-tertiary: #16213e;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  :root {
    --border-glass: rgba(255, 255, 255, 0.5);
    --border-light: rgba(255, 255, 255, 0.3);
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* 现代化布局类 */
.modern-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.modern-grid {
  display: grid;
  gap: var(--space-6);
}

.modern-flex {
  display: flex;
  gap: var(--space-4);
}

.modern-flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.modern-flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 现代化文本样式 */
.modern-text-gradient {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.modern-text-glow {
  text-shadow: 0 0 20px var(--primary-color);
}

/* 现代化边框 */
.modern-border-gradient {
  position: relative;
  background: var(--bg-glass);
  border-radius: var(--radius-lg);
}

.modern-border-gradient::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 2px;
  background: var(--primary-gradient);
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  -webkit-mask-composite: xor;
}

/* 加载状态类 */
.modern-loading {
  position: relative;
  overflow: hidden;
}

.modern-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  animation: modernShimmer 2s infinite;
}

@keyframes modernShimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* 脉冲动画 */
.modern-pulse {
  animation: modernPulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes modernPulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 旋转动画 */
.modern-spin {
  animation: modernSpin 1s linear infinite;
}

@keyframes modernSpin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 弹跳动画 */
.modern-bounce {
  animation: modernBounce 1s infinite;
}

@keyframes modernBounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: none;
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}