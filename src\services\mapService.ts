import AMapLoader from '@amap/amap-jsapi-loader';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import { ref, reactive } from 'vue';

// 地图配置
const MAP_CONFIG = {
  amap: {
    key: 'your-amap-key', // 需要替换为实际的高德地图API Key
    version: '2.0',
    plugins: ['AMap.Scale', 'AMap.ToolBar', 'AMap.Geolocation']
  },
  leaflet: {
    defaultCenter: [39.9042, 116.4074], // 北京
    defaultZoom: 10
  }
};

// 地图状态
export const mapStatus = ref<'loading' | 'ready' | 'error'>('loading');

// 充电站数据
export const chargingStations = reactive({
  list: [] as Array<{
    id: string;
    name: string;
    lat: number;
    lng: number;
    status: 'available' | 'occupied' | 'offline';
    totalPorts: number;
    availablePorts: number;
    address: string;
    price: number;
    rating: number;
    distance?: number;
  }>,
  selected: null as any
});

// 车辆位置数据
export const vehicleLocations = reactive({
  list: [] as Array<{
    id: string;
    name: string;
    lat: number;
    lng: number;
    status: 'online' | 'charging' | 'offline';
    battery: number;
    speed: number;
    direction: number;
  }>,
  tracking: null as string | null
});

// 路线规划数据
export const routePlanning = reactive({
  origin: null as { lat: number; lng: number } | null,
  destination: null as { lat: number; lng: number } | null,
  routes: [] as Array<{
    distance: number;
    duration: number;
    path: Array<{ lat: number; lng: number }>;
    instructions: Array<string>;
  }>
});

class MapService {
  private amapInstance: any = null;
  private leafletMap: L.Map | null = null;
  private markers: Map<string, any> = new Map();
  private currentMapType: 'amap' | 'leaflet' = 'amap';

  // 初始化高德地图
  async initAMap(container: string | HTMLElement): Promise<void> {
    try {
      mapStatus.value = 'loading';
      
      const AMap = await AMapLoader.load({
        key: MAP_CONFIG.amap.key,
        version: MAP_CONFIG.amap.version,
        plugins: MAP_CONFIG.amap.plugins
      });

      this.amapInstance = new AMap.Map(container, {
        zoom: 10,
        center: [116.4074, 39.9042],
        mapStyle: 'amap://styles/blue',
        features: ['bg', 'road', 'building', 'point']
      });

      // 添加地图控件
      this.amapInstance.addControl(new AMap.Scale());
      this.amapInstance.addControl(new AMap.ToolBar());

      // 地图点击事件
      this.amapInstance.on('click', (e: any) => {
        console.log('地图点击位置:', e.lnglat.getLng(), e.lnglat.getLat());
      });

      this.currentMapType = 'amap';
      mapStatus.value = 'ready';
      
      // 加载充电站数据
      this.loadChargingStations();
      
    } catch (error) {
      console.error('高德地图初始化失败:', error);
      mapStatus.value = 'error';
      throw error;
    }
  }

  // 初始化Leaflet地图
  initLeafletMap(container: string | HTMLElement): void {
    try {
      mapStatus.value = 'loading';
      
      this.leafletMap = L.map(container).setView(
        MAP_CONFIG.leaflet.defaultCenter as [number, number],
        MAP_CONFIG.leaflet.defaultZoom
      );

      // 添加瓦片图层
      L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
      }).addTo(this.leafletMap);

      // 地图点击事件
      this.leafletMap.on('click', (e) => {
        console.log('地图点击位置:', e.latlng.lng, e.latlng.lat);
      });

      this.currentMapType = 'leaflet';
      mapStatus.value = 'ready';
      
      // 加载充电站数据
      this.loadChargingStations();
      
    } catch (error) {
      console.error('Leaflet地图初始化失败:', error);
      mapStatus.value = 'error';
      throw error;
    }
  }

  // 加载充电站数据
  private loadChargingStations(): void {
    // 模拟充电站数据
    const mockStations = [
      {
        id: 'cs001',
        name: '北京国贸充电站',
        lat: 39.9088,
        lng: 116.4574,
        status: 'available' as const,
        totalPorts: 8,
        availablePorts: 3,
        address: '北京市朝阳区国贸中心',
        price: 1.2,
        rating: 4.5
      },
      {
        id: 'cs002',
        name: '三里屯充电站',
        lat: 39.9347,
        lng: 116.4484,
        status: 'occupied' as const,
        totalPorts: 6,
        availablePorts: 0,
        address: '北京市朝阳区三里屯',
        price: 1.1,
        rating: 4.2
      },
      {
        id: 'cs003',
        name: '王府井充电站',
        lat: 39.9097,
        lng: 116.4074,
        status: 'available' as const,
        totalPorts: 10,
        availablePorts: 7,
        address: '北京市东城区王府井大街',
        price: 1.3,
        rating: 4.8
      },
      {
        id: 'cs004',
        name: '中关村充电站',
        lat: 39.9831,
        lng: 116.3145,
        status: 'offline' as const,
        totalPorts: 4,
        availablePorts: 0,
        address: '北京市海淀区中关村大街',
        price: 1.0,
        rating: 3.9
      }
    ];

    chargingStations.list = mockStations;
    this.addChargingStationMarkers();
  }

  // 添加充电站标记
  private addChargingStationMarkers(): void {
    chargingStations.list.forEach(station => {
      this.addChargingStationMarker(station);
    });
  }

  // 添加单个充电站标记
  addChargingStationMarker(station: any): void {
    if (this.currentMapType === 'amap' && this.amapInstance) {
      const marker = new (window as any).AMap.Marker({
        position: [station.lng, station.lat],
        title: station.name,
        icon: this.getStationIcon(station.status)
      });

      // 添加信息窗口
      const infoWindow = new (window as any).AMap.InfoWindow({
        content: this.createStationInfoContent(station)
      });

      marker.on('click', () => {
        infoWindow.open(this.amapInstance, marker.getPosition());
        chargingStations.selected = station;
      });

      this.amapInstance.add(marker);
      this.markers.set(station.id, marker);
      
    } else if (this.currentMapType === 'leaflet' && this.leafletMap) {
      const marker = L.marker([station.lat, station.lng], {
        icon: this.getLeafletStationIcon(station.status)
      }).addTo(this.leafletMap);

      marker.bindPopup(this.createStationInfoContent(station));
      marker.on('click', () => {
        chargingStations.selected = station;
      });

      this.markers.set(station.id, marker);
    }
  }

  // 获取充电站图标
  private getStationIcon(status: string): string {
    const iconMap = {
      available: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMTAiIGZpbGw9IiM0Q0FGNTASCZ8L2NpcmNsZT4KPHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTggMkw2IDZIMTBMOCAxNEwxMCAxMEg2TDggMloiIGZpbGw9IndoaXRlIi8+Cjwvc3ZnPgo8L3N2Zz4K',
      occupied: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMTAiIGZpbGw9IiNGRjk5MDAiLz4KPHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTggMkw2IDZIMTBMOCAxNEwxMCAxMEg2TDggMloiIGZpbGw9IndoaXRlIi8+Cjwvc3ZnPgo8L3N2Zz4K',
      offline: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMTAiIGZpbGw9IiNGRjQ0NDQiLz4KPHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTggMkw2IDZIMTBMOCAxNEwxMCAxMEg2TDggMloiIGZpbGw9IndoaXRlIi8+Cjwvc3ZnPgo8L3N2Zz4K'
    };
    return iconMap[status as keyof typeof iconMap] || iconMap.available;
  }

  // 获取Leaflet充电站图标
  private getLeafletStationIcon(status: string): L.Icon {
    const colorMap = {
      available: '#4CAF50',
      occupied: '#FF9900',
      offline: '#FF4444'
    };
    
    const color = colorMap[status as keyof typeof colorMap] || colorMap.available;
    
    return L.divIcon({
      html: `<div style="background-color: ${color}; width: 20px; height: 20px; border-radius: 50%; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"></div>`,
      className: 'custom-div-icon',
      iconSize: [20, 20],
      iconAnchor: [10, 10]
    });
  }

  // 创建充电站信息内容
  private createStationInfoContent(station: any): string {
    return `
      <div style="padding: 10px; min-width: 200px;">
        <h3 style="margin: 0 0 8px 0; color: #333;">${station.name}</h3>
        <p style="margin: 4px 0; color: #666;">地址: ${station.address}</p>
        <p style="margin: 4px 0; color: #666;">状态: <span style="color: ${this.getStatusColor(station.status)};">${this.getStatusText(station.status)}</span></p>
        <p style="margin: 4px 0; color: #666;">充电桩: ${station.availablePorts}/${station.totalPorts} 可用</p>
        <p style="margin: 4px 0; color: #666;">价格: ¥${station.price}/kWh</p>
        <p style="margin: 4px 0; color: #666;">评分: ${station.rating}⭐</p>
        <button onclick="mapService.navigateToStation('${station.id}')" style="margin-top: 8px; padding: 4px 8px; background: #409EFF; color: white; border: none; border-radius: 4px; cursor: pointer;">导航</button>
      </div>
    `;
  }

  // 获取状态颜色
  private getStatusColor(status: string): string {
    const colorMap = {
      available: '#4CAF50',
      occupied: '#FF9900',
      offline: '#FF4444'
    };
    return colorMap[status as keyof typeof colorMap] || '#666';
  }

  // 获取状态文本
  private getStatusText(status: string): string {
    const textMap = {
      available: '可用',
      occupied: '占用',
      offline: '离线'
    };
    return textMap[status as keyof typeof textMap] || '未知';
  }

  // 导航到充电站
  navigateToStation(stationId: string): void {
    const station = chargingStations.list.find(s => s.id === stationId);
    if (station) {
      routePlanning.destination = { lat: station.lat, lng: station.lng };
      this.planRoute();
    }
  }

  // 路线规划
  async planRoute(): Promise<void> {
    if (!routePlanning.origin || !routePlanning.destination) {
      console.warn('起点或终点未设置');
      return;
    }

    try {
      // 这里应该调用实际的路线规划API
      // 模拟路线规划结果
      const mockRoute = {
        distance: Math.floor(Math.random() * 20000) + 5000, // 5-25km
        duration: Math.floor(Math.random() * 1800) + 600, // 10-40分钟
        path: [
          routePlanning.origin,
          { lat: (routePlanning.origin.lat + routePlanning.destination.lat) / 2, lng: (routePlanning.origin.lng + routePlanning.destination.lng) / 2 },
          routePlanning.destination
        ],
        instructions: [
          '从起点出发',
          '直行500米',
          '右转进入主干道',
          '直行2公里',
          '到达目的地'
        ]
      };

      routePlanning.routes = [mockRoute];
      this.drawRoute(mockRoute.path);
      
    } catch (error) {
      console.error('路线规划失败:', error);
    }
  }

  // 绘制路线
  private drawRoute(path: Array<{ lat: number; lng: number }>): void {
    if (this.currentMapType === 'amap' && this.amapInstance) {
      const polyline = new (window as any).AMap.Polyline({
        path: path.map(p => [p.lng, p.lat]),
        strokeColor: '#409EFF',
        strokeWeight: 4,
        strokeOpacity: 0.8
      });
      
      this.amapInstance.add(polyline);
      this.amapInstance.setFitView([polyline]);
      
    } else if (this.currentMapType === 'leaflet' && this.leafletMap) {
      const polyline = L.polyline(path.map(p => [p.lat, p.lng]), {
        color: '#409EFF',
        weight: 4,
        opacity: 0.8
      }).addTo(this.leafletMap);
      
      this.leafletMap.fitBounds(polyline.getBounds());
    }
  }

  // 获取当前位置
  async getCurrentLocation(): Promise<{ lat: number; lng: number }> {
    return new Promise((resolve, reject) => {
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            const location = {
              lat: position.coords.latitude,
              lng: position.coords.longitude
            };
            routePlanning.origin = location;
            resolve(location);
          },
          (error) => {
            console.error('获取位置失败:', error);
            // 使用默认位置（北京）
            const defaultLocation = { lat: 39.9042, lng: 116.4074 };
            routePlanning.origin = defaultLocation;
            resolve(defaultLocation);
          }
        );
      } else {
        reject(new Error('浏览器不支持地理定位'));
      }
    });
  }

  // 搜索附近充电站
  searchNearbyStations(lat: number, lng: number, radius: number = 5000): any[] {
    return chargingStations.list.filter(station => {
      const distance = this.calculateDistance(lat, lng, station.lat, station.lng);
      station.distance = distance;
      return distance <= radius;
    }).sort((a, b) => (a.distance || 0) - (b.distance || 0));
  }

  // 计算两点间距离（米）
  private calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
    const R = 6371e3; // 地球半径（米）
    const φ1 = lat1 * Math.PI / 180;
    const φ2 = lat2 * Math.PI / 180;
    const Δφ = (lat2 - lat1) * Math.PI / 180;
    const Δλ = (lng2 - lng1) * Math.PI / 180;

    const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c;
  }

  // 销毁地图
  destroy(): void {
    if (this.amapInstance) {
      this.amapInstance.destroy();
      this.amapInstance = null;
    }
    
    if (this.leafletMap) {
      this.leafletMap.remove();
      this.leafletMap = null;
    }
    
    this.markers.clear();
    mapStatus.value = 'loading';
  }
}

// 创建地图服务实例
export const mapService = new MapService();

// 将mapService暴露到全局，供信息窗口中的按钮使用
(window as any).mapService = mapService;