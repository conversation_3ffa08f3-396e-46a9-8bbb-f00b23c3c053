<template>
  <div class="charging-reports">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <el-icon class="title-icon"><Document /></el-icon>
            报表中心
          </h1>
          <p class="page-description">运营报表、财务统计、数据导出</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="generateReport">
            <el-icon><Plus /></el-icon>
            生成报表
          </el-button>
        </div>
      </div>
    </div>

    <!-- 报表概览 -->
    <div class="reports-overview">
      <div class="overview-cards">
        <div class="overview-card">
          <div class="card-icon monthly">
            <el-icon><Calendar /></el-icon>
          </div>
          <div class="card-content">
            <h3>月度报表</h3>
            <div class="card-value">{{ monthlyReports }}</div>
            <div class="card-change">{{ currentMonthReports }} 本月</div>
          </div>
        </div>

        <div class="overview-card">
          <div class="card-icon weekly">
            <el-icon><Document /></el-icon>
          </div>
          <div class="card-content">
            <h3>周报</h3>
            <div class="card-value">{{ weeklyReports }}</div>
            <div class="card-change">{{ currentWeekReports }} 本周</div>
          </div>
        </div>

        <div class="overview-card">
          <div class="card-icon financial">
            <el-icon><Money /></el-icon>
          </div>
          <div class="card-content">
            <h3>财务报表</h3>
            <div class="card-value">{{ financialReports }}</div>
            <div class="card-change positive">+8% 增长</div>
          </div>
        </div>

        <div class="overview-card">
          <div class="card-icon exports">
            <el-icon><Download /></el-icon>
          </div>
          <div class="card-content">
            <h3>导出次数</h3>
            <div class="card-value">{{ exportCount }}</div>
            <div class="card-change">{{ todayExports }} 今日</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 报表列表 -->
    <div class="reports-table-section">
      <div class="section-header">
        <h2>报表列表</h2>
        <div class="section-actions">
          <el-input
            v-model="searchQuery"
            placeholder="搜索报表"
            style="width: 300px"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-select v-model="typeFilter" placeholder="报表类型" style="width: 120px">
            <el-option label="全部类型" value="" />
            <el-option label="运营报表" value="operational" />
            <el-option label="财务报表" value="financial" />
            <el-option label="设备报表" value="equipment" />
          </el-select>
        </div>
      </div>

      <el-table :data="reportsList" style="width: 100%">
        <el-table-column prop="name" label="报表名称" width="200" />
        <el-table-column prop="type" label="类型" width="120">
          <template #default="scope">
            <el-tag :type="getReportTypeColor(scope.row.type)" size="small">
              {{ getReportTypeText(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="period" label="统计周期" width="120" />
        <el-table-column prop="generatedDate" label="生成日期" width="120" />
        <el-table-column prop="size" label="文件大小" width="100" />
        <el-table-column prop="format" label="格式" width="80" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'ready' ? 'success' : 'warning'" size="small">
              {{ scope.row.status === 'ready' ? '就绪' : '生成中' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button size="small" @click="viewReport(scope.row)">预览</el-button>
            <el-button size="small" type="primary" @click="downloadReport(scope.row)">下载</el-button>
            <el-button size="small" type="danger" @click="deleteReport(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Document,
  Plus,
  Calendar,
  Money,
  Download,
  Search
} from '@element-plus/icons-vue'

// 响应式数据
const searchQuery = ref('')
const typeFilter = ref('')
const monthlyReports = ref(12)
const currentMonthReports = ref(1)
const weeklyReports = ref(4)
const currentWeekReports = ref(1)
const financialReports = ref(8)
const exportCount = ref(156)
const todayExports = ref(12)

// 报表列表数据
const reportsList = ref([
  {
    id: 1,
    name: '2024年1月运营报表',
    type: 'operational',
    period: '月度',
    generatedDate: '2024-01-18',
    size: '2.5MB',
    format: 'PDF',
    status: 'ready'
  },
  {
    id: 2,
    name: '第3周财务统计',
    type: 'financial',
    period: '周度',
    generatedDate: '2024-01-17',
    size: '1.8MB',
    format: 'Excel',
    status: 'ready'
  },
  {
    id: 3,
    name: '设备运行状况报告',
    type: 'equipment',
    period: '日度',
    generatedDate: '2024-01-18',
    size: '3.2MB',
    format: 'PDF',
    status: 'generating'
  }
])

// 方法
const getReportTypeColor = (type: string) => {
  const colors = {
    operational: 'primary',
    financial: 'success',
    equipment: 'warning'
  }
  return colors[type] || ''
}

const getReportTypeText = (type: string) => {
  const texts = {
    operational: '运营报表',
    financial: '财务报表',
    equipment: '设备报表'
  }
  return texts[type] || type
}

const generateReport = () => {
  ElMessage.info('生成报表功能开发中...')
}

const viewReport = (report: any) => {
  ElMessage.info(`预览报表: ${report.name}`)
}

const downloadReport = (report: any) => {
  ElMessage.success(`下载报表: ${report.name}`)
}

const deleteReport = (report: any) => {
  ElMessage.warning(`删除报表: ${report.name}`)
}
</script>

<style scoped>
.charging-reports {
  padding: 24px;
  background: #f8fafc;
  min-height: 100vh;
}

/* 页面头部样式 */
.page-header {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 32px;
  color: white;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 32px;
}

.page-description {
  font-size: 16px;
  opacity: 0.9;
  margin: 0;
}

/* 报表概览样式 */
.reports-overview {
  margin-bottom: 32px;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
}

.overview-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.card-icon.monthly {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.card-icon.weekly {
  background: linear-gradient(135deg, #f093fb, #f5576c);
}

.card-icon.financial {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.card-icon.exports {
  background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.card-content h3 {
  font-size: 14px;
  color: #718096;
  margin: 0 0 8px 0;
  font-weight: 500;
}

.card-value {
  font-size: 24px;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 4px 0;
}

.card-change {
  font-size: 12px;
  color: #718096;
}

.card-change.positive {
  color: #38a169;
}

/* 表格区域样式 */
.reports-table-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.section-header h2 {
  font-size: 20px;
  font-weight: 600;
  color: #1a202c;
  margin: 0;
}

.section-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .overview-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .overview-cards {
    grid-template-columns: 1fr;
  }
  
  .header-content {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }
  
  .section-actions {
    flex-direction: column;
    gap: 12px;
  }
}
</style>
