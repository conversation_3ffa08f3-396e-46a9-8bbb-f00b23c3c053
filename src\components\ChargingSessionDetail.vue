<template>
  <div class="charging-session-detail">
    <div v-if="session" class="session-content">
      <!-- 会话基本信息 -->
      <div class="session-header">
        <div class="session-title">
          <h2>充电会话 #{{ session.id }}</h2>
          <el-tag :type="getStatusType(session.status)" size="large">
            {{ getStatusText(session.status) }}
          </el-tag>
        </div>
        <div class="session-time">
          <span>开始时间: {{ formatDateTime(session.start_time) }}</span>
          <span v-if="session.end_time">结束时间: {{ formatDateTime(session.end_time) }}</span>
        </div>
      </div>

      <!-- 充电信息 -->
      <div class="charging-info">
        <div class="info-grid">
          <div class="info-card">
            <div class="info-icon energy">
              <el-icon><Lightning /></el-icon>
            </div>
            <div class="info-content">
              <div class="info-value">{{ session.energy_delivered || 0 }}</div>
              <div class="info-label">充电量 (kWh)</div>
            </div>
          </div>

          <div class="info-card">
            <div class="info-icon duration">
              <el-icon><Timer /></el-icon>
            </div>
            <div class="info-content">
              <div class="info-value">{{ getDuration() }}</div>
              <div class="info-label">充电时长</div>
            </div>
          </div>

          <div class="info-card">
            <div class="info-icon power">
              <el-icon><Flash /></el-icon>
            </div>
            <div class="info-content">
              <div class="info-value">{{ session.power || 60 }}</div>
              <div class="info-label">充电功率 (kW)</div>
            </div>
          </div>

          <div class="info-card">
            <div class="info-icon cost">
              <el-icon><Money /></el-icon>
            </div>
            <div class="info-content">
              <div class="info-value">¥{{ session.total_cost || 0 }}</div>
              <div class="info-label">总费用</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 详细信息 -->
      <div class="detail-section">
        <h3>详细信息</h3>
        <div class="detail-grid">
          <div class="detail-item">
            <label>充电站:</label>
            <span>{{ session.station_name }}</span>
          </div>
          <div class="detail-item">
            <label>充电桩:</label>
            <span>充电桩 {{ session.port_number || 'N/A' }}</span>
          </div>
          <div class="detail-item">
            <label>用户:</label>
            <span>{{ session.user_name || '用户' + session.user_id }}</span>
          </div>
          <div class="detail-item">
            <label>车辆:</label>
            <span>{{ session.vehicle_info || '未知车辆' }}</span>
          </div>
          <div class="detail-item">
            <label>支付状态:</label>
            <el-tag :type="getPaymentStatusType(session.payment_status)" size="small">
              {{ getPaymentStatusText(session.payment_status) }}
            </el-tag>
          </div>
          <div class="detail-item">
            <label>创建时间:</label>
            <span>{{ formatDateTime(session.created_at || session.start_time) }}</span>
          </div>
        </div>
      </div>

      <!-- 实时数据 (仅进行中的会话) -->
      <div v-if="session.status === 'active'" class="realtime-section">
        <h3>实时数据</h3>
        <div class="realtime-grid">
          <div class="realtime-item">
            <div class="realtime-label">当前功率</div>
            <div class="realtime-value">{{ currentPower }} kW</div>
          </div>
          <div class="realtime-item">
            <div class="realtime-label">电池电量</div>
            <div class="realtime-value">{{ batteryLevel }}%</div>
          </div>
          <div class="realtime-item">
            <div class="realtime-label">预计剩余时间</div>
            <div class="realtime-value">{{ estimatedTimeLeft }}</div>
          </div>
          <div class="realtime-item">
            <div class="realtime-label">当前费用</div>
            <div class="realtime-value">¥{{ currentCost }}</div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-section">
        <el-button v-if="session.status === 'active'" type="warning" @click="$emit('stop')">
          <el-icon><VideoPause /></el-icon>
          停止充电
        </el-button>
        <el-button v-if="session.status === 'completed'" type="success" @click="$emit('download-receipt')">
          <el-icon><Download /></el-icon>
          下载收据
        </el-button>
        <el-button @click="$emit('view-station')">
          <el-icon><Location /></el-icon>
          查看充电站
        </el-button>
      </div>
    </div>

    <div v-else class="empty-state">
      <el-icon class="empty-icon"><Timer /></el-icon>
      <p>请选择一个充电会话查看详情</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, defineEmits } from 'vue'
import {
  Search,
  Lightning,
  Timer,
  Flash,
  Money,
  Location,
  User,
  Connection,
  VideoPause,
  Download
} from '@element-plus/icons-vue'

interface Session {
  id: string
  station_name: string
  user_name?: string
  user_id: string
  status: string
  start_time: string
  end_time?: string
  energy_delivered?: number
  total_cost?: number
  power?: number
  port_number?: string
  vehicle_info?: string
  payment_status?: string
  created_at?: string
}

const props = defineProps<{
  session: Session | null
  stations: any[]
}>()

const emit = defineEmits<{
  stop: []
  'download-receipt': []
  'view-station': []
}>()

// 响应式数据
const searchQuery = ref('')
const statusFilter = ref('')
const selectedStation = ref(null)

// 实时数据 (模拟)
const currentPower = ref(45.2)
const batteryLevel = ref(78)
const estimatedTimeLeft = ref('25分钟')
const currentCost = ref(28.50)

// 计算属性
const filteredStations = computed(() => {
  let filtered = props.stations

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(station =>
      station.name.toLowerCase().includes(query) ||
      station.address.toLowerCase().includes(query)
    )
  }

  if (statusFilter.value) {
    filtered = filtered.filter(station => station.status === statusFilter.value)
  }

  return filtered
})

// 方法
const selectStation = (station: any) => {
  selectedStation.value = station
}

const getDuration = () => {
  if (!props.session?.start_time) return '0分钟'
  
  const start = new Date(props.session.start_time)
  const end = props.session.end_time ? new Date(props.session.end_time) : new Date()
  const duration = Math.floor((end.getTime() - start.getTime()) / (1000 * 60))
  
  if (duration < 60) {
    return `${duration}分钟`
  } else {
    const hours = Math.floor(duration / 60)
    const minutes = duration % 60
    return `${hours}小时${minutes}分钟`
  }
}

const formatDateTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getStatusType = (status: string) => {
  const types = {
    active: 'warning',
    completed: 'success',
    cancelled: 'info',
    failed: 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts = {
    active: '进行中',
    completed: '已完成',
    cancelled: '已取消',
    failed: '失败'
  }
  return texts[status] || '未知'
}

const getPaymentStatusType = (status: string) => {
  const types = {
    paid: 'success',
    pending: 'warning',
    failed: 'danger'
  }
  return types[status] || 'info'
}

const getPaymentStatusText = (status: string) => {
  const texts = {
    paid: '已支付',
    pending: '待支付',
    failed: '支付失败'
  }
  return texts[status] || '未知'
}
</script>

<style scoped>
.charging-session-detail {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.session-content {
  padding: 24px;
}

.session-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e2e8f0;
}

.session-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.session-title h2 {
  font-size: 24px;
  font-weight: 600;
  color: #1a202c;
  margin: 0;
}

.session-time {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 14px;
  color: #718096;
}

.charging-info {
  margin-bottom: 32px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.info-card {
  background: #f7fafc;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
}

.info-card:hover {
  background: #edf2f7;
  transform: translateY(-2px);
}

.info-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
}

.info-icon.energy {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.info-icon.duration {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.info-icon.power {
  background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.info-icon.cost {
  background: linear-gradient(135deg, #fa709a, #fee140);
}

.info-content {
  flex: 1;
}

.info-value {
  font-size: 24px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 4px;
}

.info-label {
  font-size: 14px;
  color: #718096;
}

.detail-section {
  margin-bottom: 32px;
}

.detail-section h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 16px 0;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #e2e8f0;
}

.detail-item label {
  font-weight: 600;
  color: #4a5568;
}

.detail-item span {
  color: #1a202c;
}

.realtime-section {
  margin-bottom: 32px;
}

.realtime-section h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 16px 0;
}

.realtime-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.realtime-item {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-radius: 12px;
  padding: 16px;
  text-align: center;
}

.realtime-label {
  font-size: 14px;
  opacity: 0.9;
  margin-bottom: 8px;
}

.realtime-value {
  font-size: 20px;
  font-weight: 700;
}

.action-section {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #718096;
  padding: 40px;
}

.empty-icon {
  font-size: 48px;
  color: #cbd5e1;
  margin-bottom: 16px;
}

.empty-state p {
  font-size: 16px;
  margin: 0;
}

@media (max-width: 768px) {
  .session-title {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .detail-grid {
    grid-template-columns: 1fr;
  }
  
  .realtime-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .action-section {
    flex-direction: column;
  }
}
</style>
