<template>
  <div class="user-ecosystem-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">
        <el-icon><User /></el-icon>
        用户生态服务
      </h1>
      <p class="page-subtitle">碳积分管理 · 用户画像 · 车队管理 · 生态服务</p>
    </div>

    <!-- 用户概览 -->
    <div class="user-overview">
      <ModernCard class="overview-card" hover-effect>
        <div class="overview-content" v-loading="profileLoading" element-loading-text="加载用户信息中...">
          <div class="user-avatar">
            <el-avatar :size="80" :src="userProfile.avatar">
              <el-icon><User /></el-icon>
            </el-avatar>
            <div class="user-level">
              <el-tag :type="getLevelType(userProfile.level)" size="small">
                {{ userProfile.levelName }}
              </el-tag>
            </div>
          </div>
          <div class="user-info">
            <h2>{{ userProfile.name }}</h2>
            <p class="user-title">{{ userProfile.title }}</p>
            <div class="user-stats">
              <div class="stat-item">
                <span class="stat-value">{{ userProfile.carbonPoints }}</span>
                <span class="stat-label">碳积分</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ userProfile.totalMileage }}</span>
                <span class="stat-label">总里程(km)</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ userProfile.ecoScore }}</span>
                <span class="stat-label">环保评分</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ userProfile.memberDays }}</span>
                <span class="stat-label">会员天数</span>
              </div>
            </div>
          </div>
          <div class="quick-actions">
            <ModernButton variant="primary" @click="viewProfile">
              <el-icon><View /></el-icon>
              查看档案
            </ModernButton>
            <ModernButton variant="success" @click="redeemRewards">
              <el-icon><Present /></el-icon>
              兑换奖励
            </ModernButton>
          </div>
        </div>
      </ModernCard>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧：碳积分管理 -->
      <div class="carbon-section">
        <!-- 碳积分概览 -->
        <ModernCard class="carbon-overview-card" hover-effect>
          <template #header>
            <div class="card-header">
              <span>碳积分管理</span>
              <ModernButton variant="ghost" @click="refreshCarbonData">
                <el-icon><Refresh /></el-icon>
                刷新
              </ModernButton>
            </div>
          </template>
          <div class="carbon-content" v-loading="carbonLoading" element-loading-text="加载碳积分数据中...">
            <!-- 积分统计 -->
            <div class="carbon-stats">
              <div class="carbon-stat-item">
                <div class="stat-icon earned">
                  <el-icon><TrendCharts /></el-icon>
                </div>
                <div class="stat-info">
                  <h3>{{ carbonStats.totalEarned }}</h3>
                  <p>累计获得</p>
                </div>
              </div>
              <div class="carbon-stat-item">
                <div class="stat-icon current">
                  <el-icon><Coin /></el-icon>
                </div>
                <div class="stat-info">
                  <h3>{{ carbonStats.currentBalance }}</h3>
                  <p>当前余额</p>
                </div>
              </div>
              <div class="carbon-stat-item">
                <div class="stat-icon used">
                  <el-icon><ShoppingCart /></el-icon>
                </div>
                <div class="stat-info">
                  <h3>{{ carbonStats.totalUsed }}</h3>
                  <p>已使用</p>
                </div>
              </div>
              <div class="carbon-stat-item">
                <div class="stat-icon monthly">
                  <el-icon><Calendar /></el-icon>
                </div>
                <div class="stat-info">
                  <h3>{{ carbonStats.monthlyEarned }}</h3>
                  <p>本月获得</p>
                </div>
              </div>
            </div>

            <!-- 积分趋势图 -->
            <div class="carbon-chart">
              <h4>积分趋势</h4>
              <div ref="carbonChartRef" class="chart-container"></div>
            </div>

            <!-- 获得积分方式 -->
            <div class="earning-methods">
              <h4>获得积分方式</h4>
              <div class="method-list">
                <div v-for="method in earningMethods" :key="method.id" class="method-item">
                  <div class="method-icon" :class="method.type">
                    <el-icon><component :is="method.icon" /></el-icon>
                  </div>
                  <div class="method-info">
                    <h5>{{ method.title }}</h5>
                    <p>{{ method.description }}</p>
                    <span class="method-points">+{{ method.points }}积分</span>
                  </div>
                  <div class="method-status">
                    <el-tag :type="getMethodStatusType(method.status)" size="small">
                      {{ method.status }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ModernCard>

        <!-- 积分兑换 -->
        <ModernCard class="redemption-card" hover-effect>
          <template #header>
            <div class="card-header">
              <span>积分兑换</span>
              <el-tag type="info" size="small">可用: {{ carbonStats.currentBalance }}积分</el-tag>
            </div>
          </template>
          <div class="redemption-content" v-loading="rewardsLoading" element-loading-text="加载奖励商品中...">
            <div class="reward-grid">
              <div v-for="reward in rewards" :key="reward.id" class="reward-item">
                <div class="reward-image">
                  <img :src="reward.image" :alt="reward.title" />
                </div>
                <div class="reward-info">
                  <h5>{{ reward.title }}</h5>
                  <p class="reward-description">{{ reward.description }}</p>
                  <div class="reward-price">
                    <span class="price">{{ reward.points }}积分</span>
                    <span v-if="reward.originalPrice" class="original-price">原价: ¥{{ reward.originalPrice }}</span>
                  </div>
                </div>
                <div class="reward-actions">
                  <ModernButton 
                    variant="primary" 
                    size="small" 
                    @click="redeemReward(reward)"
                    :disabled="carbonStats.currentBalance < reward.points"
                  >
                    兑换
                  </ModernButton>
                </div>
              </div>
            </div>
          </div>
        </ModernCard>
      </div>

      <!-- 右侧：用户画像和车队管理 -->
      <div class="profile-section">
        <!-- 用户画像 -->
        <ModernCard class="user-profile-card" hover-effect>
          <template #header>
            <div class="card-header">
              <span>用户画像</span>
              <ModernButton variant="ghost" @click="updateProfile">
                <el-icon><Edit /></el-icon>
                编辑
              </ModernButton>
            </div>
          </template>
          <div class="profile-content">
            <!-- 驾驶习惯分析 -->
            <div class="driving-analysis">
              <h4>驾驶习惯分析</h4>
              <div class="analysis-grid">
                <div class="analysis-item">
                  <div class="analysis-label">驾驶风格</div>
                  <div class="analysis-value">
                    <el-tag :type="getDrivingStyleType(drivingProfile.style)">{{ drivingProfile.style }}</el-tag>
                  </div>
                </div>
                <div class="analysis-item">
                  <div class="analysis-label">平均速度</div>
                  <div class="analysis-value">{{ drivingProfile.avgSpeed }}km/h</div>
                </div>
                <div class="analysis-item">
                  <div class="analysis-label">急加速次数</div>
                  <div class="analysis-value">{{ drivingProfile.rapidAcceleration }}/月</div>
                </div>
                <div class="analysis-item">
                  <div class="analysis-label">急刹车次数</div>
                  <div class="analysis-value">{{ drivingProfile.hardBraking }}/月</div>
                </div>
                <div class="analysis-item">
                  <div class="analysis-label">夜间驾驶</div>
                  <div class="analysis-value">{{ drivingProfile.nightDriving }}%</div>
                </div>
                <div class="analysis-item">
                  <div class="analysis-label">高速驾驶</div>
                  <div class="analysis-value">{{ drivingProfile.highwayDriving }}%</div>
                </div>
              </div>
            </div>

            <!-- 环保表现 -->
            <div class="eco-performance">
              <h4>环保表现</h4>
              <div class="eco-chart">
                <div ref="ecoChartRef" class="chart-container"></div>
              </div>
              <div class="eco-metrics">
                <div class="eco-metric">
                  <span class="metric-label">能耗效率</span>
                  <div class="metric-bar">
                    <div class="metric-fill" :style="{ width: ecoProfile.efficiency + '%' }"></div>
                  </div>
                  <span class="metric-value">{{ ecoProfile.efficiency }}%</span>
                </div>
                <div class="eco-metric">
                  <span class="metric-label">碳减排量</span>
                  <div class="metric-bar">
                    <div class="metric-fill" :style="{ width: (ecoProfile.carbonReduction / 100) * 100 + '%' }"></div>
                  </div>
                  <span class="metric-value">{{ ecoProfile.carbonReduction }}kg</span>
                </div>
                <div class="eco-metric">
                  <span class="metric-label">绿色出行</span>
                  <div class="metric-bar">
                    <div class="metric-fill" :style="{ width: ecoProfile.greenTravel + '%' }"></div>
                  </div>
                  <span class="metric-value">{{ ecoProfile.greenTravel }}%</span>
                </div>
              </div>
            </div>

            <!-- 偏好设置 -->
            <div class="preferences">
              <h4>偏好设置</h4>
              <div class="preference-list">
                <div class="preference-item">
                  <span class="preference-label">充电提醒</span>
                  <CosmicToggle v-model="preferences.chargingReminder" />
                </div>
                <div class="preference-item">
                  <span class="preference-label">路况推送</span>
                  <CosmicToggle v-model="preferences.trafficNotification" />
                </div>
                <div class="preference-item">
                  <span class="preference-label">节能模式</span>
                  <CosmicToggle v-model="preferences.ecoMode" />
                </div>
                <div class="preference-item">
                  <span class="preference-label">自动更新</span>
                  <CosmicToggle v-model="preferences.autoUpdate" />
                </div>
              </div>
            </div>
          </div>
        </ModernCard>

        <!-- 车队管理 -->
        <ModernCard class="fleet-management-card" hover-effect>
          <template #header>
            <div class="card-header">
              <span>车队管理</span>
              <ModernButton variant="primary" size="small" @click="addVehicle">
                <el-icon><Plus /></el-icon>
                添加车辆
              </ModernButton>
            </div>
          </template>
          <div class="fleet-content" v-loading="vehiclesLoading" element-loading-text="加载车队信息中...">
            <!-- 车队概览 -->
            <div class="fleet-overview">
              <div class="fleet-stats">
                <div class="fleet-stat">
                  <span class="stat-number">{{ fleetData.totalVehicles }}</span>
                  <span class="stat-label">总车辆数</span>
                </div>
                <div class="fleet-stat">
                  <span class="stat-number">{{ fleetData.activeVehicles }}</span>
                  <span class="stat-label">活跃车辆</span>
                </div>
                <div class="fleet-stat">
                  <span class="stat-number">{{ fleetData.totalMileage }}</span>
                  <span class="stat-label">总里程(km)</span>
                </div>
                <div class="fleet-stat">
                  <span class="stat-number">{{ fleetData.avgEfficiency }}%</span>
                  <span class="stat-label">平均效率</span>
                </div>
              </div>
            </div>

            <!-- 车辆列表 -->
            <div class="vehicle-list">
              <h5>车辆列表</h5>
              <div class="vehicle-grid">
                <div v-for="vehicle in vehicles" :key="vehicle.id" class="vehicle-card">
                  <div class="vehicle-header">
                    <div class="vehicle-info">
                      <h6>{{ vehicle.name }}</h6>
                      <p class="vehicle-model">{{ vehicle.model }}</p>
                    </div>
                    <div class="vehicle-status">
                      <el-tag :type="getVehicleStatusType(vehicle.status)" size="small">
                        {{ vehicle.status }}
                      </el-tag>
                    </div>
                  </div>
                  <div class="vehicle-metrics">
                    <div class="metric-row">
                      <span class="metric-label">电量:</span>
                      <div class="battery-indicator">
                        <div class="battery-bar">
                          <div class="battery-fill" :style="{ width: vehicle.battery + '%' }"></div>
                        </div>
                        <span class="battery-text">{{ vehicle.battery }}%</span>
                      </div>
                    </div>
                    <div class="metric-row">
                      <span class="metric-label">里程:</span>
                      <span class="metric-value">{{ vehicle.mileage }}km</span>
                    </div>
                    <div class="metric-row">
                      <span class="metric-label">位置:</span>
                      <span class="metric-value">{{ vehicle.location }}</span>
                    </div>
                  </div>
                  <div class="vehicle-actions">
                    <ModernButton variant="ghost" size="small" @click="viewVehicleDetail(vehicle)">
                      详情
                    </ModernButton>
                    <ModernButton variant="ghost" size="small" @click="controlVehicle(vehicle)">
                      控制
                    </ModernButton>
                    <ModernButton variant="ghost" size="small" @click="trackVehicle(vehicle)">
                      追踪
                    </ModernButton>
                  </div>
                </div>
              </div>
            </div>

            <!-- 车队分析 -->
            <div class="fleet-analysis">
              <h5>车队分析</h5>
              <div ref="fleetChartRef" class="chart-container"></div>
            </div>
          </div>
        </ModernCard>
      </div>
    </div>

    <!-- 成就系统 -->
    <div class="achievements-section">
      <ModernCard class="achievements-card" hover-effect>
        <template #header>
          <div class="card-header">
            <span>成就系统</span>
            <el-tag type="warning" size="small">{{ achievements.filter(a => a.unlocked).length }}/{{ achievements.length }}已解锁</el-tag>
          </div>
        </template>
        <div class="achievements-content" v-loading="achievementsLoading" element-loading-text="加载成就数据中...">
          <div class="achievement-grid">
            <div v-for="achievement in achievements" :key="achievement.id" class="achievement-item" :class="{ unlocked: achievement.unlocked }">
              <div class="achievement-icon">
                <el-icon><component :is="achievement.icon" /></el-icon>
              </div>
              <div class="achievement-info">
                <h6>{{ achievement.title }}</h6>
                <p class="achievement-description">{{ achievement.description }}</p>
                <div class="achievement-progress">
                  <el-progress 
                    :percentage="achievement.progress" 
                    :show-text="false" 
                    :stroke-width="4"
                    :status="achievement.unlocked ? 'success' : undefined"
                  />
                  <span class="progress-text">{{ achievement.current }}/{{ achievement.target }}</span>
                </div>
              </div>
              <div class="achievement-reward">
                <span class="reward-points">+{{ achievement.reward }}积分</span>
              </div>
            </div>
          </div>
        </div>
      </ModernCard>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import {
  User,
  View,
  Present,
  Refresh,
  TrendCharts,
  Coin,
  ShoppingCart,
  Calendar,
  Edit,
  Plus,
  Trophy,
  Medal,
  Star,
  Lightning
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ModernButton, ModernCard } from '@/components/ui'
import CosmicToggle from '@/components/CosmicToggle.vue'

// 用户档案
const userProfile = ref({
  name: '',
  title: '',
  avatar: '',
  level: 0,
  levelName: '',
  carbonPoints: 0,
  totalMileage: 0,
  ecoScore: 0,
  memberDays: 0
})

// 碳积分统计
const carbonStats = ref({
  totalEarned: 0,
  currentBalance: 0,
  totalUsed: 0,
  monthlyEarned: 0
})

// 获得积分方式
const earningMethods = ref([])

// 奖励商品
const rewards = ref([])

// 驾驶档案
const drivingProfile = ref({
  style: '',
  avgSpeed: 0,
  rapidAcceleration: 0,
  hardBraking: 0,
  nightDriving: 0,
  highwayDriving: 0
})

// 环保档案
const ecoProfile = ref({
  efficiency: 0,
  carbonReduction: 0,
  greenTravel: 0
})

// 偏好设置
const preferences = ref({
  chargingReminder: false,
  trafficNotification: false,
  ecoMode: false,
  autoUpdate: false
})

// 车队数据
const fleetData = ref({
  totalVehicles: 0,
  activeVehicles: 0,
  totalMileage: 0,
  avgEfficiency: 0
})

// 车辆列表
const vehicles = ref([])

// 成就系统
const achievements = ref([])

// Loading状态管理
const loading = ref(false)
const profileLoading = ref(false)
const carbonLoading = ref(false)
const rewardsLoading = ref(false)
const vehiclesLoading = ref(false)
const achievementsLoading = ref(false)

// 图表相关
const carbonChartRef = ref<HTMLElement>()
const ecoChartRef = ref<HTMLElement>()
const fleetChartRef = ref<HTMLElement>()
let carbonChart: echarts.ECharts | null = null
let ecoChart: echarts.ECharts | null = null
let fleetChart: echarts.ECharts | null = null

// API调用函数
const fetchUserProfile = async () => {
  try {
    profileLoading.value = true;
    const response = await fetch('/api/users/profile');
    const data = await response.json();
    if (data.success && data.data) {
      userProfile.value = {
        name: data.data.name || '未知用户',
        title: data.data.title || '新能源车主',
        avatar: data.data.avatar_url || '',
        level: data.data.level || 1,
        levelName: data.data.level_name || '青铜会员',
        carbonPoints: data.data.carbon_points || 0,
        totalMileage: data.data.total_mileage || 0,
        ecoScore: data.data.eco_score || 0,
        memberDays: data.data.member_days || 0
      };
    }
  } catch (error) {
    console.error('获取用户档案失败:', error);
    // 使用默认数据作为后备
    userProfile.value = {
      name: '演示用户',
      title: '新能源车主',
      avatar: '',
      level: 3,
      levelName: '黄金会员',
      carbonPoints: 2580,
      totalMileage: 15420,
      ecoScore: 85,
      memberDays: 365
    };
  } finally {
    profileLoading.value = false;
  }
};

const fetchCarbonStats = async () => {
  try {
    carbonLoading.value = true;
    const response = await fetch('/api/carbon/stats');
    const data = await response.json();
    if (data.success && data.data) {
      carbonStats.value = {
        totalEarned: data.data.total_earned || 0,
        currentBalance: data.data.current_balance || 0,
        totalUsed: data.data.total_used || 0,
        monthlyEarned: data.data.monthly_earned || 0
      };
    }
  } catch (error) {
    console.error('获取碳积分统计失败:', error);
    // 使用默认数据作为后备
    carbonStats.value = {
      totalEarned: 3250,
      currentBalance: 2580,
      totalUsed: 670,
      monthlyEarned: 420
    };
  } finally {
    carbonLoading.value = false;
  }
};

const fetchEarningMethods = async () => {
  try {
    const response = await fetch('/api/carbon/earning-methods');
    const data = await response.json();
    if (data.success && data.data) {
      earningMethods.value = data.data;
    }
  } catch (error) {
    console.error('获取积分获得方式失败:', error);
    // 使用默认数据作为后备
    earningMethods.value = [
      {
        id: 1,
        title: '绿色出行',
        description: '使用新能源车辆出行',
        points: '+10',
        status: '进行中',
        progress: 75
      },
      {
        id: 2,
        title: '节能驾驶',
        description: '保持经济驾驶模式',
        points: '+5',
        status: '可获得',
        progress: 100
      },
      {
        id: 3,
        title: '充电优化',
        description: '在低峰时段充电',
        points: '+8',
        status: '进行中',
        progress: 45
      }
    ];
  }
};

const fetchRewards = async () => {
  try {
    rewardsLoading.value = true;
    const response = await fetch('/api/carbon/rewards');
    const data = await response.json();
    if (data.success && data.data) {
      rewards.value = data.data;
    }
  } catch (error) {
    console.error('获取奖励商品失败:', error);
    // 使用默认数据作为后备
    rewards.value = [
      {
        id: 1,
        name: '充电优惠券',
        description: '充电费用9折优惠',
        points: 500,
        image: '',
        stock: 100,
        category: '充电服务'
      },
      {
        id: 2,
        name: '环保购物袋',
        description: '可重复使用环保袋',
        points: 200,
        image: '',
        stock: 50,
        category: '环保用品'
      },
      {
        id: 3,
        name: '车载空气净化器',
        description: '高效过滤PM2.5',
        points: 1500,
        image: '',
        stock: 20,
        category: '车载用品'
      }
    ];
  } finally {
    rewardsLoading.value = false;
  }
};

const fetchDrivingProfile = async () => {
  try {
    const response = await fetch('/api/users/driving-profile');
    const data = await response.json();
    if (data.success && data.data) {
      drivingProfile.value = {
        totalMileage: data.data.total_mileage || 0,
        avgSpeed: data.data.avg_speed || 0,
        ecoScore: data.data.eco_score || 0,
        safetyScore: data.data.safety_score || 0,
        fuelEfficiency: data.data.fuel_efficiency || 0,
        co2Saved: data.data.co2_saved || 0
      };
    }
  } catch (error) {
    console.error('获取驾驶档案失败:', error);
    // 使用默认数据作为后备
    drivingProfile.value = {
      totalMileage: 15420,
      avgSpeed: 45.2,
      ecoScore: 85,
      safetyScore: 92,
      fuelEfficiency: 4.2,
      co2Saved: 1250
    };
  }
};

const fetchEcoProfile = async () => {
  try {
    const response = await fetch('/api/users/eco-profile');
    const data = await response.json();
    if (data.success && data.data) {
      ecoProfile.value = {
        carbonFootprint: data.data.carbon_footprint || 0,
        energySaved: data.data.energy_saved || 0,
        treesEquivalent: data.data.trees_equivalent || 0,
        ecoRank: data.data.eco_rank || 0,
        monthlyImprovement: data.data.monthly_improvement || 0
      };
    }
  } catch (error) {
    console.error('获取环保档案失败:', error);
    // 使用默认数据作为后备
    ecoProfile.value = {
      carbonFootprint: 2.8,
      energySaved: 1420,
      treesEquivalent: 15,
      ecoRank: 12,
      monthlyImprovement: 8.5
    };
  }
};

const fetchPreferences = async () => {
  try {
    const response = await fetch('/api/users/preferences');
    const data = await response.json();
    if (data.success && data.data) {
      preferences.value = {
        notifications: data.data.notifications !== undefined ? data.data.notifications : true,
        autoCharge: data.data.auto_charge !== undefined ? data.data.auto_charge : false,
        ecoMode: data.data.eco_mode !== undefined ? data.data.eco_mode : true,
        dataSharing: data.data.data_sharing !== undefined ? data.data.data_sharing : false,
        language: data.data.language || 'zh-CN',
        theme: data.data.theme || 'light'
      };
    }
  } catch (error) {
    console.error('获取偏好设置失败:', error);
    // 使用默认数据作为后备
    preferences.value = {
      notifications: true,
      autoCharge: false,
      ecoMode: true,
      dataSharing: false,
      language: 'zh-CN',
      theme: 'light'
    };
  }
};

const fetchFleetData = async () => {
  try {
    const response = await fetch('/api/fleet/stats');
    const data = await response.json();
    if (data.success && data.data) {
      fleetData.value = {
        totalVehicles: data.data.total_vehicles || 0,
        activeVehicles: data.data.active_vehicles || 0,
        totalMileage: data.data.total_mileage || 0,
        avgEfficiency: data.data.avg_efficiency || 0,
        carbonSaved: data.data.carbon_saved || 0,
        costSavings: data.data.cost_savings || 0
      };
    }
  } catch (error) {
    console.error('获取车队数据失败:', error);
    // 使用默认数据作为后备
    fleetData.value = {
      totalVehicles: 5,
      activeVehicles: 4,
      totalMileage: 45680,
      avgEfficiency: 4.5,
      carbonSaved: 3250,
      costSavings: 12500
    };
  }
};

const fetchVehicles = async () => {
  try {
    vehiclesLoading.value = true;
    const response = await fetch('/api/fleet/vehicles');
    const data = await response.json();
    if (data.success && data.data) {
      vehicles.value = data.data;
    }
  } catch (error) {
    console.error('获取车辆列表失败:', error);
    // 使用默认数据作为后备
    vehicles.value = [
      {
        id: 1,
        name: 'Model Y',
        brand: 'Tesla',
        status: '在线',
        battery: 85,
        mileage: 12450,
        efficiency: 4.2,
        lastUpdate: '2024-01-15 14:30'
      },
      {
        id: 2,
        name: 'ID.4',
        brand: '大众',
        status: '充电中',
        battery: 65,
        mileage: 8920,
        efficiency: 4.8,
        lastUpdate: '2024-01-15 13:45'
      },
      {
        id: 3,
        name: 'ES6',
        brand: '蔚来',
        status: '离线',
        battery: 45,
        mileage: 15680,
        efficiency: 4.1,
        lastUpdate: '2024-01-15 10:20'
      }
    ];
  } finally {
    vehiclesLoading.value = false;
  }
};

const fetchAchievements = async () => {
  try {
    achievementsLoading.value = true;
    const response = await fetch('/api/users/achievements');
    const data = await response.json();
    if (data.success && data.data) {
      achievements.value = data.data;
    }
  } catch (error) {
    console.error('获取成就系统失败:', error);
    // 使用默认数据作为后备
    achievements.value = [
      {
        id: 1,
        title: '绿色先锋',
        description: '累计减少碳排放1000kg',
        icon: 'Trophy',
        progress: 85,
        target: 100,
        unlocked: false,
        category: '环保成就'
      },
      {
        id: 2,
        title: '节能达人',
        description: '单次行程效率超过5.0',
        icon: 'Medal',
        progress: 100,
        target: 100,
        unlocked: true,
        category: '驾驶成就'
      },
      {
        id: 3,
        title: '里程王者',
        description: '累计行驶10000公里',
        icon: 'Trophy',
        progress: 75,
        target: 100,
        unlocked: false,
        category: '里程成就'
      }
    ];
  } finally {
    achievementsLoading.value = false;
  }
};

// 方法
const getLevelType = (level: number) => {
  if (level >= 5) return 'warning'
  if (level >= 3) return 'success'
  return 'info'
}

const getMethodStatusType = (status: string) => {
  const types = {
    '进行中': 'warning',
    '可获得': 'success',
    '已完成': 'info'
  }
  return types[status] || 'info'
}

const getDrivingStyleType = (style: string) => {
  const types = {
    '平稳': 'success',
    '激进': 'danger',
    '保守': 'info'
  }
  return types[style] || 'info'
}

const getVehicleStatusType = (status: string) => {
  const types = {
    '在线': 'success',
    '充电中': 'warning',
    '离线': 'info'
  }
  return types[status] || 'info'
}

const viewProfile = async () => {
  try {
    loading.value = true;
    // 获取完整用户档案信息
    const response = await fetch('/api/users/profile/detailed');
    const data = await response.json();
    if (data.success) {
      // 显示用户档案详情对话框
      ElMessageBox.alert(
        `<div style="text-align: left;">
          <h3>用户档案详情</h3>
          <p><strong>姓名:</strong> ${data.data.name}</p>
          <p><strong>邮箱:</strong> ${data.data.email}</p>
          <p><strong>电话:</strong> ${data.data.phone || '未设置'}</p>
          <p><strong>用户类型:</strong> ${data.data.user_type}</p>
          <p><strong>注册时间:</strong> ${new Date(data.data.created_at).toLocaleDateString()}</p>
          <p><strong>最后登录:</strong> ${data.data.last_login ? new Date(data.data.last_login).toLocaleString() : '未知'}</p>
        </div>`,
        '用户档案',
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '关闭'
        }
      );
      ElMessage.success('用户档案加载成功');
    } else {
      ElMessage.error(data.message || '获取用户档案失败');
    }
  } catch (error) {
    console.error('查看用户档案失败:', error);
    ElMessage.error('网络错误，请检查连接后重试');
  } finally {
    loading.value = false;
  }
}

const redeemRewards = async () => {
  try {
    // 获取可兑换奖励列表
    const response = await fetch('/api/carbon-credits/rewards/available');
    const data = await response.json();
    if (data.success && data.data.length > 0) {
      // 显示奖励兑换对话框
      const rewardOptions = data.data.map(reward => 
        `<option value="${reward.id}">${reward.title} (${reward.points}积分)</option>`
      ).join('');
      
      ElMessageBox.prompt(
        `<div style="text-align: left;">
          <p>当前可用积分: <strong>${carbonStats.value.currentBalance}</strong></p>
          <p>选择要兑换的奖励:</p>
          <select id="rewardSelect" style="width: 100%; padding: 8px; margin-top: 10px;">
            ${rewardOptions}
          </select>
        </div>`,
        '兑换奖励',
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确认兑换',
          cancelButtonText: '取消'
        }
      ).then(async ({ value }) => {
        const selectedRewardId = document.getElementById('rewardSelect')?.value;
        if (selectedRewardId) {
          await processRewardRedemption(selectedRewardId);
        }
      });
    } else {
      ElMessage.info('暂无可兑换的奖励');
    }
  } catch (error) {
    console.error('获取奖励列表失败:', error);
    ElMessage.error('获取奖励列表失败');
  }
}

const processRewardRedemption = async (rewardId: string) => {
  try {
    const response = await fetch('/api/carbon-credits/redeem', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ reward_id: rewardId })
    });
    const data = await response.json();
    if (data.success) {
      ElMessage.success('兑换成功！');
      await refreshCarbonData();
    } else {
      ElMessage.error(data.message || '兑换失败');
    }
  } catch (error) {
    console.error('兑换失败:', error);
    ElMessage.error('兑换失败');
  }
}

const refreshCarbonData = async () => {
  try {
    loading.value = true;
    const response = await fetch('/api/carbon-credits/refresh', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    const data = await response.json();
    if (data.success) {
      await Promise.all([
        fetchCarbonStats(),
        fetchEarningMethods(),
        fetchRewards()
      ]);
      ElMessage.success('碳积分数据已刷新');
    } else {
      ElMessage.warning(data.message || '部分数据刷新失败');
    }
  } catch (error) {
    console.error('刷新数据失败:', error);
    ElMessage.error('刷新数据失败，请稍后重试');
  } finally {
    loading.value = false;
  }
}

const redeemReward = (reward: any) => {
  if (carbonStats.value.currentBalance < reward.points) {
    ElMessage.error('积分不足，无法兑换')
    return
  }
  
  ElMessageBox.confirm(
    `确定要用${reward.points}积分兑换${reward.title}吗？`,
    '确认兑换',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    carbonStats.value.currentBalance -= reward.points
    carbonStats.value.totalUsed += reward.points
    ElMessage.success('兑换成功！')
  }).catch(() => {
    ElMessage.info('已取消兑换')
  })
}

const updateProfile = async () => {
  try {
    // 获取当前用户信息
    const response = await fetch('/api/users/profile');
    const data = await response.json();
    if (data.success) {
      const currentUser = data.data;
      
      ElMessageBox.prompt(
        `<div style="text-align: left;">
          <div style="margin-bottom: 15px;">
            <label>姓名:</label>
            <input id="editName" type="text" value="${currentUser.name}" style="width: 100%; padding: 8px; margin-top: 5px; border: 1px solid #ddd; border-radius: 4px;">
          </div>
          <div style="margin-bottom: 15px;">
            <label>电话:</label>
            <input id="editPhone" type="text" value="${currentUser.phone || ''}" style="width: 100%; padding: 8px; margin-top: 5px; border: 1px solid #ddd; border-radius: 4px;">
          </div>
          <div style="margin-bottom: 15px;">
            <label>地址:</label>
            <input id="editAddress" type="text" value="${currentUser.address || ''}" style="width: 100%; padding: 8px; margin-top: 5px; border: 1px solid #ddd; border-radius: 4px;">
          </div>
        </div>`,
        '编辑用户档案',
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '保存',
          cancelButtonText: '取消'
        }
      ).then(async () => {
        const name = (document.getElementById('editName') as HTMLInputElement)?.value;
        const phone = (document.getElementById('editPhone') as HTMLInputElement)?.value;
        const address = (document.getElementById('editAddress') as HTMLInputElement)?.value;
        
        await updateUserProfile({ name, phone, address });
      });
    }
  } catch (error) {
    console.error('获取用户信息失败:', error);
    ElMessage.error('获取用户信息失败');
  }
}

const updateUserProfile = async (profileData: any) => {
  try {
    const response = await fetch('/api/users/profile', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(profileData)
    });
    const data = await response.json();
    if (data.success) {
      ElMessage.success('档案更新成功！');
      await fetchUserProfile();
    } else {
      ElMessage.error(data.message || '档案更新失败');
    }
  } catch (error) {
    console.error('档案更新失败:', error);
    ElMessage.error('档案更新失败');
  }
}

const addVehicle = async () => {
  ElMessageBox.prompt(
    `<div style="text-align: left;">
      <div style="margin-bottom: 15px;">
        <label>车辆品牌:</label>
        <select id="vehicleBrand" style="width: 100%; padding: 8px; margin-top: 5px; border: 1px solid #ddd; border-radius: 4px;">
          <option value="Tesla">Tesla</option>
          <option value="BYD">BYD</option>
          <option value="NIO">NIO</option>
          <option value="XPeng">XPeng</option>
          <option value="Li Auto">Li Auto</option>
        </select>
      </div>
      <div style="margin-bottom: 15px;">
        <label>车辆型号:</label>
        <input id="vehicleModel" type="text" placeholder="请输入车辆型号" style="width: 100%; padding: 8px; margin-top: 5px; border: 1px solid #ddd; border-radius: 4px;">
      </div>
      <div style="margin-bottom: 15px;">
        <label>车牌号:</label>
        <input id="vehiclePlate" type="text" placeholder="请输入车牌号" style="width: 100%; padding: 8px; margin-top: 5px; border: 1px solid #ddd; border-radius: 4px;">
      </div>
      <div style="margin-bottom: 15px;">
        <label>车辆类型:</label>
        <select id="vehicleType" style="width: 100%; padding: 8px; margin-top: 5px; border: 1px solid #ddd; border-radius: 4px;">
          <option value="electric">纯电动</option>
          <option value="hybrid">混合动力</option>
          <option value="fuel">燃油车</option>
        </select>
      </div>
    </div>`,
    '添加车辆',
    {
      dangerouslyUseHTMLString: true,
      confirmButtonText: '添加',
      cancelButtonText: '取消'
    }
  ).then(async () => {
    const brand = (document.getElementById('vehicleBrand') as HTMLSelectElement)?.value;
    const model = (document.getElementById('vehicleModel') as HTMLInputElement)?.value;
    const plate = (document.getElementById('vehiclePlate') as HTMLInputElement)?.value;
    const type = (document.getElementById('vehicleType') as HTMLSelectElement)?.value;
    
    if (!model || !plate) {
      ElMessage.error('请填写完整的车辆信息');
      return;
    }
    
    await createVehicle({ brand, model, license_plate: plate, vehicle_type: type });
  }).catch(() => {
    // 用户取消操作
  });
}

const createVehicle = async (vehicleData: any) => {
  try {
    const response = await fetch('/api/vehicles', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(vehicleData)
    });
    const data = await response.json();
    if (data.success) {
      ElMessage.success('车辆添加成功！');
      await fetchFleetData();
      await fetchVehicles();
    } else {
      ElMessage.error(data.message || '车辆添加失败');
    }
  } catch (error) {
    console.error('车辆添加失败:', error);
    ElMessage.error('车辆添加失败');
  }
}

const viewVehicleDetail = async (vehicle: any) => {
  try {
    const response = await fetch(`/api/vehicles/${vehicle.id}`);
    const data = await response.json();
    if (data.success) {
      const vehicleInfo = data.data;
      ElMessageBox.alert(
        `<div style="text-align: left;">
          <h3>车辆详情</h3>
          <p><strong>品牌:</strong> ${vehicleInfo.brand || vehicle.name}</p>
          <p><strong>型号:</strong> ${vehicleInfo.model || vehicle.model}</p>
          <p><strong>车牌号:</strong> ${vehicleInfo.license_plate || '未设置'}</p>
          <p><strong>车辆类型:</strong> ${vehicleInfo.vehicle_type || '未知'}</p>
          <p><strong>状态:</strong> ${vehicleInfo.status || vehicle.status}</p>
          <p><strong>里程:</strong> ${vehicleInfo.mileage || vehicle.mileage || 0} km</p>
          <p><strong>电池电量:</strong> ${vehicleInfo.battery?.charge_level || vehicle.battery || 0}%</p>
          <p><strong>位置:</strong> ${vehicleInfo.location || vehicle.location || '未知'}</p>
          <p><strong>注册时间:</strong> ${vehicleInfo.created_at ? new Date(vehicleInfo.created_at).toLocaleDateString() : '未知'}</p>
        </div>`,
        '车辆详情',
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '关闭'
        }
      );
    } else {
      ElMessage.error('获取车辆详情失败');
    }
  } catch (error) {
    console.error('获取车辆详情失败:', error);
    ElMessage.error('获取车辆详情失败');
  }
}

const controlVehicle = async (vehicle: any) => {
  try {
    const { value: action } = await ElMessageBox.prompt(
      `<div style="text-align: left;">
        <p>选择要执行的车辆控制操作:</p>
        <select id="actionSelect" style="width: 100%; padding: 8px; margin-top: 10px; border: 1px solid #ddd; border-radius: 4px;">
          <option value="lock">锁车</option>
          <option value="unlock">解锁</option>
          <option value="start">启动</option>
          <option value="stop">熄火</option>
          <option value="horn">鸣笛</option>
          <option value="lights">闪灯</option>
        </select>
      </div>`,
      `控制车辆: ${vehicle.name || vehicle.model}`,
      {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '执行',
        cancelButtonText: '取消',
        inputValidator: () => {
          const select = document.getElementById('actionSelect') as HTMLSelectElement;
          return select?.value || false;
        },
        inputErrorMessage: '请选择控制操作'
      }
    );
    
    const selectedAction = (document.getElementById('actionSelect') as HTMLSelectElement)?.value;
    if (selectedAction) {
      await executeVehicleControl(vehicle.id, selectedAction);
    }
  } catch (error) {
    // 用户取消操作或发生错误
    if (error !== 'cancel') {
      console.error('车辆控制失败:', error);
      ElMessage.error('车辆控制失败');
    }
  }
}

const trackVehicle = async (vehicle: any) => {
  try {
    const response = await fetch(`/api/vehicles/${vehicle.id}/location`);
    const data = await response.json();
    if (data.success) {
      const location = data.data;
      ElMessageBox.alert(
        `<div style="text-align: left;">
          <h3>车辆位置追踪</h3>
          <p><strong>车辆:</strong> ${vehicle.name || vehicle.model}</p>
          <p><strong>车牌号:</strong> ${vehicle.license_plate || '未设置'}</p>
          <p><strong>当前位置:</strong> ${location.address || vehicle.location || '位置信息获取中...'}</p>
          <p><strong>经纬度:</strong> ${location.latitude || 0}, ${location.longitude || 0}</p>
          <p><strong>速度:</strong> ${location.speed || 0} km/h</p>
          <p><strong>更新时间:</strong> ${location.updated_at ? new Date(location.updated_at).toLocaleString() : '未知'}</p>
          <div style="margin-top: 15px; padding: 10px; background: #f0f9ff; border-radius: 4px;">
            <p style="margin: 0; color: #1890ff;">💡 提示: 可在车联网模块查看实时地图位置</p>
          </div>
        </div>`,
        '车辆追踪',
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '关闭'
        }
      );
    } else {
      ElMessage.error('获取车辆位置失败');
    }
  } catch (error) {
    console.error('车辆追踪失败:', error);
    ElMessage.error('车辆追踪失败');
  }
}

const executeVehicleControl = async (vehicleId: string, action: string) => {
  try {
    loading.value = true;
    const response = await fetch(`/api/vehicles/${vehicleId}/control`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ action })
    });
    const data = await response.json();
    if (data.success) {
      ElMessage.success(`车辆${getActionText(action)}指令执行成功`);
      await fetchVehicles(); // 刷新车辆状态
    } else {
      ElMessage.error(data.message || '车辆控制指令执行失败');
    }
  } catch (error) {
    console.error('车辆控制失败:', error);
    ElMessage.error('车辆控制失败，请检查网络连接');
  } finally {
    loading.value = false;
  }
}

const getActionText = (action: string) => {
  const actionMap = {
    'lock': '锁车',
    'unlock': '解锁',
    'start': '启动',
    'stop': '熄火',
    'horn': '鸣笛',
    'lights': '闪灯'
  };
  return actionMap[action] || action;
}

// 初始化图表
const initCarbonChart = () => {
  if (!carbonChartRef.value) return
  
  carbonChart = echarts.init(carbonChartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: {
      type: 'value',
      name: '积分'
    },
    series: [
      {
        name: '获得积分',
        type: 'bar',
        data: [680, 750, 820, 890, 950, 850],
        itemStyle: { color: '#67C23A' }
      },
      {
        name: '使用积分',
        type: 'bar',
        data: [200, 300, 150, 400, 250, 180],
        itemStyle: { color: '#E6A23C' }
      }
    ]
  }
  
  carbonChart.setOption(option)
}

const initEcoChart = () => {
  if (!ecoChartRef.value) return
  
  ecoChart = echarts.init(ecoChartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        name: '环保表现',
        type: 'pie',
        radius: ['40%', '70%'],
        data: [
          { value: 92, name: '能耗效率', itemStyle: { color: '#67C23A' } },
          { value: 85, name: '碳减排', itemStyle: { color: '#409EFF' } },
          { value: 78, name: '绿色出行', itemStyle: { color: '#E6A23C' } }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  
  ecoChart.setOption(option)
}

const initFleetChart = () => {
  if (!fleetChartRef.value) return
  
  fleetChart = echarts.init(fleetChartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['车辆1', '车辆2', '车辆3']
    },
    xAxis: {
      type: 'category',
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    },
    yAxis: {
      type: 'value',
      name: '里程(km)'
    },
    series: [
      {
        name: '车辆1',
        type: 'line',
        data: [45, 52, 38, 65, 48, 72, 55],
        itemStyle: { color: '#409EFF' }
      },
      {
        name: '车辆2',
        type: 'line',
        data: [32, 28, 45, 38, 55, 42, 48],
        itemStyle: { color: '#67C23A' }
      },
      {
        name: '车辆3',
        type: 'line',
        data: [0, 0, 0, 25, 35, 28, 0],
        itemStyle: { color: '#E6A23C' }
      }
    ]
  }
  
  fleetChart.setOption(option)
}

// 初始化数据
const initializeData = async () => {
  await Promise.all([
    fetchUserProfile(),
    fetchCarbonStats(),
    fetchEarningMethods(),
    fetchRewards(),
    fetchDrivingProfile(),
    fetchEcoProfile(),
    fetchPreferences(),
    fetchFleetData(),
    fetchVehicles(),
    fetchAchievements()
  ]);
};

// 生命周期
onMounted(async () => {
  // 初始化数据
  await initializeData();
  
  // 初始化图表
  initCarbonChart();
  initEcoChart();
  initFleetChart();
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    carbonChart?.resize();
    ecoChart?.resize();
    fleetChart?.resize();
  });
  
  // 设置定期数据刷新（每60秒刷新一次）
  const refreshInterval = setInterval(async () => {
    await Promise.all([
      fetchUserProfile(),
      fetchCarbonStats(),
      fetchDrivingProfile(),
      fetchEcoProfile(),
      fetchFleetData()
    ]);
  }, 60000);
  
  // 清理定时器
  onUnmounted(() => {
    clearInterval(refreshInterval);
  });
})

onUnmounted(() => {
  carbonChart?.dispose()
  ecoChart?.dispose()
  fleetChart?.dispose()
  window.removeEventListener('resize', () => {
    carbonChart?.resize()
    ecoChart?.resize()
    fleetChart?.resize()
  })
})
</script>

<style scoped>
.user-ecosystem-container {
  padding: 24px;
  background: #ffffff;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 32px;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.page-subtitle {
  font-size: 16px;
  color: #718096;
  margin: 12px 0 0 0;
}

.user-overview {
  margin-bottom: 30px;
}

.overview-card {
  border-radius: 12px;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.overview-content {
  display: flex;
  align-items: center;
  padding: 30px;
  gap: 30px;
}

.user-avatar {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.user-level {
  margin-top: 10px;
}

.user-info {
  flex: 1;
}

.user-info h2 {
  font-size: 24px;
  font-weight: bold;
  margin: 0 0 5px 0;
  color: #2c3e50;
}

.user-title {
  font-size: 16px;
  color: #7f8c8d;
  margin: 0 0 20px 0;
}

.user-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.stat-item {
  text-align: center;
  padding: 15px;
  background: #f8fafc;
  border-radius: 8px;
}

.stat-value {
  display: block;
  font-size: 20px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
  color: #7f8c8d;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.quick-actions .modern-button {
  min-height: 40px !important;
  padding: 10px 20px !important;
  white-space: nowrap !important;
}

.main-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 30px;
}

.carbon-section,
.profile-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.carbon-overview-card,
.redemption-card,
.user-profile-card,
.fleet-management-card {
  border-radius: 12px;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.carbon-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  margin-bottom: 25px;
}

.carbon-stat-item {
  display: flex;
  align-items: center;
  padding: 15px;
  background: #f8fafc;
  border-radius: 8px;
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 18px;
  color: white;
}

.stat-icon.earned {
  background: #059669;
}

.stat-icon.current {
  background: #3b82f6;
}

.stat-icon.used {
  background: #d97706;
}

.stat-icon.monthly {
  background: #dc2626;
}

.stat-info h3 {
  font-size: 18px;
  font-weight: bold;
  margin: 0;
  color: #2c3e50;
}

.stat-info p {
  font-size: 12px;
  color: #7f8c8d;
  margin: 5px 0 0 0;
}

.carbon-chart h4,
.earning-methods h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
}

.chart-container {
  height: 200px;
  width: 100%;
  margin-bottom: 20px;
}

.method-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.method-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: white;
}

.method-icon {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 16px;
  color: white;
}

.method-icon.eco {
  background: #67C23A;
}

.method-icon.charging {
  background: #409EFF;
}

.method-icon.sharing {
  background: #E6A23C;
}

.method-icon.maintenance {
  background: #F56C6C;
}

.method-info {
  flex: 1;
}

.method-info h5 {
  margin: 0 0 5px 0;
  color: #2c3e50;
  font-size: 14px;
}

.method-info p {
  margin: 0 0 5px 0;
  color: #606266;
  font-size: 12px;
}

.method-points {
  font-size: 12px;
  color: #67C23A;
  font-weight: bold;
}

.reward-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.reward-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 15px;
  background: white;
  transition: all 0.3s ease;
}

.reward-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.reward-image {
  width: 100%;
  height: 80px;
  margin-bottom: 10px;
  border-radius: 6px;
  overflow: hidden;
}

.reward-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.reward-info h5 {
  margin: 0 0 5px 0;
  color: #2c3e50;
  font-size: 14px;
}

.reward-description {
  margin: 0 0 10px 0;
  color: #606266;
  font-size: 12px;
}

.reward-price {
  display: flex;
  flex-direction: column;
  gap: 2px;
  margin-bottom: 10px;
}

.price {
  font-size: 14px;
  font-weight: bold;
  color: #E6A23C;
}

.original-price {
  font-size: 10px;
  color: #909399;
  text-decoration: line-through;
}

.driving-analysis h4,
.eco-performance h4,
.preferences h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
}

.analysis-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  margin-bottom: 20px;
}

.analysis-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px;
  background: #f8fafc;
  border-radius: 6px;
}

.analysis-label {
  font-size: 12px;
  color: #606266;
}

.analysis-value {
  font-size: 12px;
  font-weight: bold;
  color: #2c3e50;
}

.eco-metrics {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.eco-metric {
  display: flex;
  align-items: center;
  gap: 10px;
}

.metric-label {
  min-width: 60px;
  font-size: 12px;
  color: #606266;
}

.metric-bar {
  flex: 1;
  height: 6px;
  background: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
}

.metric-fill {
  height: 100%;
  background: #059669;
  transition: width 0.3s ease;
}

.metric-value {
  min-width: 40px;
  font-size: 12px;
  font-weight: bold;
  color: #2c3e50;
  text-align: right;
}

.preference-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.preference-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: #f8fafc;
  border-radius: 8px;
  min-height: 80px;
}

.preference-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.fleet-overview {
  margin-bottom: 20px;
}

.fleet-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15px;
}

.fleet-stat {
  text-align: center;
  padding: 15px;
  background: #f8fafc;
  border-radius: 8px;
}

.stat-number {
  display: block;
  font-size: 18px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 5px;
}

.vehicle-list h5,
.fleet-analysis h5 {
  margin: 0 0 15px 0;
  color: #2c3e50;
}

.vehicle-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.vehicle-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 15px;
  background: white;
}

.vehicle-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.vehicle-info h6 {
  margin: 0 0 2px 0;
  color: #2c3e50;
  font-size: 14px;
}

.vehicle-model {
  margin: 0;
  color: #7f8c8d;
  font-size: 12px;
}

.vehicle-metrics {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 15px;
}

.metric-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.battery-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  justify-content: flex-end;
}

.battery-bar {
  width: 60px;
  height: 4px;
  background: #f0f0f0;
  border-radius: 2px;
  overflow: hidden;
}

.battery-fill {
  height: 100%;
  background: #059669;
  transition: width 0.3s ease;
}

.battery-text {
  font-size: 10px;
  color: #606266;
}

.vehicle-actions {
  display: flex;
  gap: 5px;
}

.achievements-section {
  margin-top: 20px;
}

.achievements-card {
  border-radius: 12px;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.achievement-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 15px;
}

.achievement-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: white;
  opacity: 0.6;
  transition: all 0.3s ease;
}

.achievement-item.unlocked {
  opacity: 1;
  border-color: #67C23A;
  background: #f0f9ff;
}

.achievement-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 18px;
  color: white;
  background: #909399;
}

.achievement-item.unlocked .achievement-icon {
  background: #059669;
}

.achievement-info {
  flex: 1;
}

.achievement-info h6 {
  margin: 0 0 5px 0;
  color: #2c3e50;
  font-size: 14px;
}

.achievement-description {
  margin: 0 0 8px 0;
  color: #606266;
  font-size: 12px;
}

.achievement-progress {
  display: flex;
  align-items: center;
  gap: 10px;
}

.progress-text {
  font-size: 10px;
  color: #909399;
  min-width: 40px;
}

.achievement-reward {
  text-align: right;
}

.reward-points {
  font-size: 12px;
  color: #E6A23C;
  font-weight: bold;
}

@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 1fr;
  }
  
  .user-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .carbon-stats {
    grid-template-columns: 1fr;
  }
  
  .reward-grid {
    grid-template-columns: 1fr;
  }
  
  .analysis-grid {
    grid-template-columns: 1fr;
  }
  
  .fleet-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .user-ecosystem-container {
    padding: 15px;
  }
  
  .page-title {
    font-size: 24px;
  }
  
  .overview-content {
    flex-direction: column;
    text-align: center;
    padding: 20px;
  }
  
  .user-stats {
    grid-template-columns: 1fr;
  }
  
  .fleet-stats {
    grid-template-columns: 1fr;
  }
  
  .achievement-grid {
    grid-template-columns: 1fr;
  }
}
</style>