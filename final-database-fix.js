const mysql = require('mysql2/promise')

async function finalDatabaseFix() {
  let connection
  
  try {
    console.log('✅ 连接数据库...')
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '123456',
      database: 'ev_management'
    })
    
    console.log('🔧 插入测试设备（包含所有必填字段）...')
    
    const testDevices = [
      {
        device_id: 'EV001',
        name: '电动车001',
        type: 'monitoring',
        device_name: '电动车001',
        device_type: 'electric_vehicle',
        status: 'online',
        location: '北京市朝阳区',
        ip_address: '*************'
      },
      {
        device_id: 'EV002', 
        name: '电动车002',
        type: 'monitoring',
        device_name: '电动车002',
        device_type: 'electric_vehicle',
        status: 'online',
        location: '上海市浦东新区',
        ip_address: '*************'
      },
      {
        device_id: 'EV003',
        name: '电动车003',
        type: 'monitoring',
        device_name: '电动车003',
        device_type: 'electric_vehicle', 
        status: 'offline',
        location: '广州市天河区',
        ip_address: '*************'
      },
      {
        device_id: 'CS001',
        name: '充电站001',
        type: 'control',
        device_name: '充电站001',
        device_type: 'charging_station',
        status: 'online',
        location: '深圳市南山区',
        ip_address: '*************'
      },
      {
        device_id: 'CS002',
        name: '充电站002',
        type: 'control', 
        device_name: '充电站002',
        device_type: 'charging_station',
        status: 'online',
        location: '杭州市西湖区',
        ip_address: '*************'
      }
    ]
    
    for (const device of testDevices) {
      try {
        await connection.execute(`
          INSERT INTO edge_devices 
          (device_id, name, type, device_name, device_type, status, location, ip_address, last_heartbeat) 
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
          ON DUPLICATE KEY UPDATE
          name = VALUES(name),
          type = VALUES(type),
          device_name = VALUES(device_name),
          device_type = VALUES(device_type),
          status = VALUES(status),
          location = VALUES(location),
          ip_address = VALUES(ip_address),
          last_heartbeat = NOW()
        `, [
          device.device_id,
          device.name,
          device.type,
          device.device_name,
          device.device_type,
          device.status,
          device.location,
          device.ip_address
        ])
        console.log(`✅ 设备 ${device.device_id} 创建/更新成功`)
      } catch (error) {
        console.log(`⚠️  设备 ${device.device_id} 创建失败:`, error.message)
      }
    }
    
    console.log('🔧 插入丰富的测试数据...')
    
    const testData = [
      // 电动车001数据
      {
        device_id: 'EV001',
        data_type: 'battery_level',
        value: JSON.stringify({ level: 85, voltage: 400.5, temperature: 25.8, health: 95 }),
        text_value: '85%',
        unit: '%',
        quality: 95.5
      },
      {
        device_id: 'EV001',
        data_type: 'speed',
        value: JSON.stringify({ speed: 60, max_speed: 120, acceleration: 2.5 }),
        text_value: '60km/h',
        unit: 'km/h',
        quality: 98.0
      },
      {
        device_id: 'EV001',
        data_type: 'location',
        value: JSON.stringify({ lat: 39.9042, lng: 116.4074, address: '北京市朝阳区' }),
        text_value: '北京市朝阳区',
        unit: 'GPS',
        quality: 99.2
      },
      {
        device_id: 'EV001',
        data_type: 'temperature',
        value: JSON.stringify({ engine: 45.2, battery: 25.8, ambient: 22.0 }),
        text_value: '25.8°C',
        unit: '°C',
        quality: 96.8
      },
      
      // 电动车002数据
      {
        device_id: 'EV002', 
        data_type: 'charging_power',
        value: JSON.stringify({ power: 50.2, current: 125.5, voltage: 400, efficiency: 92.5 }),
        text_value: '50.2kW',
        unit: 'kW',
        quality: 98.0
      },
      {
        device_id: 'EV002',
        data_type: 'battery_level',
        value: JSON.stringify({ level: 92, voltage: 405.2, temperature: 23.5, health: 98 }),
        text_value: '92%',
        unit: '%',
        quality: 97.8
      },
      {
        device_id: 'EV002',
        data_type: 'charging_status',
        value: JSON.stringify({ status: 'charging', time_remaining: 45, session_id: 'CHG001' }),
        text_value: '充电中',
        unit: 'status',
        quality: 100.0
      },
      
      // 电动车003数据
      {
        device_id: 'EV003',
        data_type: 'battery_level',
        value: JSON.stringify({ level: 45, voltage: 380.2, temperature: 28.5, health: 88 }),
        text_value: '45%',
        unit: '%',
        quality: 92.3
      },
      {
        device_id: 'EV003',
        data_type: 'maintenance_alert',
        value: JSON.stringify({ type: 'battery_check', priority: 'medium', scheduled: '2024-01-15' }),
        text_value: '需要电池检查',
        unit: 'alert',
        quality: 95.0
      },
      
      // 充电站001数据
      {
        device_id: 'CS001',
        data_type: 'station_status',
        value: JSON.stringify({ status: 'available', slots: 8, occupied: 3, queue: 0 }),
        text_value: '可用',
        unit: 'slots',
        quality: 100.0
      },
      {
        device_id: 'CS001',
        data_type: 'power_output',
        value: JSON.stringify({ total_power: 350, max_power: 500, efficiency: 94.2 }),
        text_value: '350kW',
        unit: 'kW',
        quality: 99.2
      },
      {
        device_id: 'CS001',
        data_type: 'utilization',
        value: JSON.stringify({ rate: 62.5, sessions_today: 24, revenue: 1250.50 }),
        text_value: '62.5%',
        unit: '%',
        quality: 98.5
      },
      
      // 充电站002数据
      {
        device_id: 'CS002',
        data_type: 'utilization',
        value: JSON.stringify({ rate: 78.5, active_sessions: 6, total_slots: 10, queue: 2 }),
        text_value: '78.5%',
        unit: '%',
        quality: 96.8
      },
      {
        device_id: 'CS002',
        data_type: 'power_grid',
        value: JSON.stringify({ grid_load: 420, renewable_ratio: 65.2, carbon_offset: 125.8 }),
        text_value: '420kW',
        unit: 'kW',
        quality: 97.5
      },
      {
        device_id: 'CS002',
        data_type: 'environmental',
        value: JSON.stringify({ temperature: 24.5, humidity: 58, air_quality: 85 }),
        text_value: '24.5°C',
        unit: '°C',
        quality: 94.2
      }
    ]
    
    for (const data of testData) {
      try {
        // 使用正确的时间戳格式
        const timestamp = new Date().toISOString().slice(0, 19).replace('T', ' ')
        
        await connection.execute(`
          INSERT INTO edge_device_data 
          (device_id, data_type, value, text_value, unit, quality, timestamp) 
          VALUES (?, ?, ?, ?, ?, ?, ?)
        `, [
          data.device_id,
          data.data_type, 
          data.value,
          data.text_value,
          data.unit,
          data.quality,
          timestamp
        ])
        console.log(`✅ 数据插入成功: ${data.device_id} - ${data.data_type}`)
      } catch (error) {
        console.log(`⚠️  数据插入失败: ${data.device_id} - ${data.data_type}:`, error.message)
      }
    }
    
    // 插入历史数据（用于图表展示）
    console.log('🔧 插入历史数据...')
    
    const now = new Date()
    for (let i = 0; i < 24; i++) {
      const timestamp = new Date(now.getTime() - i * 60 * 60 * 1000) // 每小时一条数据
      const timestampStr = timestamp.toISOString().slice(0, 19).replace('T', ' ')
      
      // 为每个设备生成历史数据
      const historicalData = [
        {
          device_id: 'EV001',
          data_type: 'battery_trend',
          value: JSON.stringify({ level: 85 + Math.random() * 10 - 5 }),
          text_value: `${Math.round(85 + Math.random() * 10 - 5)}%`,
          unit: '%',
          quality: 95 + Math.random() * 5
        },
        {
          device_id: 'EV002',
          data_type: 'charging_trend',
          value: JSON.stringify({ power: 45 + Math.random() * 15 }),
          text_value: `${Math.round(45 + Math.random() * 15)}kW`,
          unit: 'kW',
          quality: 95 + Math.random() * 5
        },
        {
          device_id: 'CS001',
          data_type: 'utilization_trend',
          value: JSON.stringify({ rate: 60 + Math.random() * 20 }),
          text_value: `${Math.round(60 + Math.random() * 20)}%`,
          unit: '%',
          quality: 95 + Math.random() * 5
        }
      ]
      
      for (const data of historicalData) {
        try {
          await connection.execute(`
            INSERT INTO edge_device_data 
            (device_id, data_type, value, text_value, unit, quality, timestamp) 
            VALUES (?, ?, ?, ?, ?, ?, ?)
          `, [
            data.device_id,
            data.data_type,
            data.value,
            data.text_value,
            data.unit,
            data.quality,
            timestampStr
          ])
        } catch (error) {
          // 忽略历史数据插入错误
        }
      }
    }
    
    console.log('✅ 历史数据插入完成')
    
    // 验证最终结果
    console.log('🔍 验证最终结果...')
    
    const [deviceRows] = await connection.execute(`
      SELECT device_id, name, device_name, device_type, status, location 
      FROM edge_devices 
      WHERE device_id IN ('EV001', 'EV002', 'EV003', 'CS001', 'CS002')
      ORDER BY device_id
    `)
    
    console.log('新创建的设备列表:')
    deviceRows.forEach(row => {
      console.log(`  - ${row.device_id}: ${row.name} (${row.device_type}) - ${row.status} @ ${row.location}`)
    })
    
    const [dataRows] = await connection.execute(`
      SELECT device_id, data_type, text_value, unit, quality, timestamp
      FROM edge_device_data 
      WHERE device_id IN ('EV001', 'EV002', 'EV003', 'CS001', 'CS002')
      ORDER BY timestamp DESC 
      LIMIT 15
    `)
    
    console.log('最新数据样本:')
    dataRows.forEach(row => {
      const time = new Date(row.timestamp).toLocaleString('zh-CN')
      console.log(`  - ${row.device_id}: ${row.data_type} = ${row.text_value} ${row.unit || ''} (质量: ${row.quality}) [${time}]`)
    })
    
    // 统计数据
    const [stats] = await connection.execute(`
      SELECT 
        COUNT(DISTINCT device_id) as device_count,
        COUNT(*) as data_count,
        COUNT(DISTINCT data_type) as data_type_count
      FROM edge_device_data 
      WHERE device_id IN ('EV001', 'EV002', 'EV003', 'CS001', 'CS002')
    `)
    
    console.log('📊 数据统计:')
    console.log(`  - 设备数量: ${stats[0].device_count}`)
    console.log(`  - 数据条数: ${stats[0].data_count}`)
    console.log(`  - 数据类型: ${stats[0].data_type_count}`)
    
    console.log('🎉 数据库最终修复完成！现在系统应该有完整的测试数据了')
    
  } catch (error) {
    console.error('❌ 最终修复失败:', error.message)
  } finally {
    if (connection) {
      await connection.end()
      console.log('🔌 数据库连接已关闭')
    }
  }
}

finalDatabaseFix()