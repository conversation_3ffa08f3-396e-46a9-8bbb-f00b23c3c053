import { Router } from 'express'
import { db } from '../config/mysql'
import type { ApiResponse, PaginatedResponse, FleetManagement, Vehicle, User } from '../../shared/types'

const router = Router()

// 获取车队列表
router.get('/', async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      manager_id,
      fleet_type,
      status,
      search
    } = req.query
    const offset = (Number(page) - 1) * Number(limit)
    
    // 构建查询条件
    const conditions = []
    const params = []
    
    if (manager_id) {
      conditions.push('fm.manager_id = ?')
      params.push(manager_id)
    }
    
    if (fleet_type) {
      conditions.push('fm.fleet_type = ?')
      params.push(fleet_type)
    }
    
    if (status) {
      conditions.push('fm.status = ?')
      params.push(status)
    }
    
    if (search) {
      conditions.push('(fm.fleet_name LIKE ? OR fm.description LIKE ?)')
      params.push(`%${search}%`, `%${search}%`)
    }
    
    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : ''
    
    // 获取总数
    const [countResult] = await db.execute(
      `SELECT COUNT(*) as total FROM fleet_management fm ${whereClause}`,
      params
    )
    const count = (countResult as any[])[0].total
    
    // 获取数据
    const [dataResult] = await db.execute(
      `SELECT fm.*, u.username as manager_name, u.email as manager_email, u.phone as manager_phone
       FROM fleet_management fm
       LEFT JOIN users u ON fm.manager_id = u.user_id
       ${whereClause}
       ORDER BY fm.created_at DESC
       LIMIT ? OFFSET ?`,
      [...params, Number(limit), offset]
    )
    const rawData = dataResult as any[]
    
    // 格式化数据
    const data = rawData.map(row => ({
      id: row.id,
      fleet_name: row.fleet_name,
      description: row.description,
      fleet_type: row.fleet_type,
      manager_id: row.manager_id,
      status: row.status,
      created_at: row.created_at,
      updated_at: row.updated_at,
      manager: {
        name: row.manager_name,
        email: row.manager_email,
        phone: row.manager_phone
      }
    }))
    
    const response: PaginatedResponse<FleetManagement> = {
      success: true,
      data,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: count,
        totalPages: Math.ceil(count / Number(limit))
      }
    }
    
    res.json(response)
  } catch (error) {
    console.error('获取车队列表异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取单个车队信息
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params
    
    const [result] = await db.execute(
      `SELECT fm.*, u.user_id as manager_id, u.username as manager_name, u.email as manager_email, u.phone as manager_phone
       FROM fleet_management fm
       LEFT JOIN users u ON fm.manager_id = u.user_id
       WHERE fm.id = ?`,
      [id]
    )
    const rawData = (result as any[])[0]
    
    if (!rawData) {
      return res.status(404).json({
        success: false,
        error: '车队不存在'
      } as ApiResponse)
    }
    
    // 格式化数据
    const data = {
      id: rawData.id,
      fleet_name: rawData.fleet_name,
      description: rawData.description,
      fleet_type: rawData.fleet_type,
      manager_id: rawData.manager_id,
      status: rawData.status,
      created_at: rawData.created_at,
      updated_at: rawData.updated_at,
      manager: {
        id: rawData.manager_id,
        name: rawData.manager_name,
        email: rawData.manager_email,
        phone: rawData.manager_phone
      }
    }
    
    res.json({
      success: true,
      data
    } as ApiResponse<FleetManagement>)
  } catch (error) {
    console.error('获取车队信息异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 创建车队
router.post('/', async (req, res) => {
  try {
    const {
      fleet_name,
      manager_id,
      fleet_type,
      description,
      max_vehicles,
      operating_area,
      contact_info,
      policies
    } = req.body
    
    // 验证必填字段
    if (!fleet_name || !manager_id || !fleet_type) {
      return res.status(400).json({
        success: false,
        error: '车队名称、管理员ID和车队类型为必填字段'
      } as ApiResponse)
    }
    
    // 验证管理员是否存在
    const [managerRows] = await db.execute(
      'SELECT id FROM USERS WHERE id = ?',
      [manager_id]
    )
    
    if (!managerRows || (managerRows as any[]).length === 0) {
      return res.status(400).json({
        success: false,
        error: '管理员不存在'
      } as ApiResponse)
    }
    
    // 检查车队名称是否已存在
    const [existingFleetRows] = await db.execute(
      'SELECT id FROM FLEET_MANAGEMENT WHERE fleet_name = ?',
      [fleet_name]
    )
    
    if (existingFleetRows && (existingFleetRows as any[]).length > 0) {
      return res.status(400).json({
        success: false,
        error: '车队名称已存在'
      } as ApiResponse)
    }
    
    const fleetData = {
      fleet_name,
      manager_id,
      fleet_type,
      description,
      max_vehicles: max_vehicles ? Number(max_vehicles) : null,
      current_vehicles: 0,
      operating_area,
      contact_info,
      policies,
      status: 'active'
    }
    
    const [result] = await db.execute(
      `INSERT INTO FLEET_MANAGEMENT (
        fleet_name, manager_id, fleet_type, description, max_vehicles,
        current_vehicles, operating_area, contact_info, policies, status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        fleetData.fleet_name,
        fleetData.manager_id,
        fleetData.fleet_type,
        fleetData.description,
        fleetData.max_vehicles,
        fleetData.current_vehicles,
        fleetData.operating_area,
        fleetData.contact_info,
        fleetData.policies,
        fleetData.status
      ]
    )
    
    const insertId = (result as any).insertId
    
    // 获取新创建的车队数据
    const [newFleetRows] = await db.execute(
      'SELECT * FROM FLEET_MANAGEMENT WHERE id = ?',
      [insertId]
    )
    
    const data = (newFleetRows as any[])[0]
    
    res.status(201).json({
      success: true,
      data,
      message: '车队创建成功'
    } as ApiResponse<FleetManagement>)
  } catch (error) {
    console.error('创建车队异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 更新车队信息
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params
    const updateData = { ...req.body }
    
    // 移除不允许更新的字段
    delete updateData.id
    delete updateData.current_vehicles
    delete updateData.created_at
    
    // 如果更新管理员，验证新管理员是否存在
    if (updateData.manager_id) {
      const [managerRows] = await db.execute(
        'SELECT id FROM USERS WHERE id = ?',
        [updateData.manager_id]
      )
      
      if (!managerRows || (managerRows as any[]).length === 0) {
        return res.status(400).json({
          success: false,
          error: '新管理员不存在'
        } as ApiResponse)
      }
    }
    
    // 如果更新车队名称，检查是否重复
    if (updateData.fleet_name) {
      const [existingFleetRows] = await db.execute(
        'SELECT id FROM FLEET_MANAGEMENT WHERE fleet_name = ? AND id != ?',
        [updateData.fleet_name, id]
      )
      
      if (existingFleetRows && (existingFleetRows as any[]).length > 0) {
        return res.status(400).json({
          success: false,
          error: '车队名称已存在'
        } as ApiResponse)
      }
    }
    
    updateData.updated_at = new Date().toISOString()
    
    // 构建动态更新语句
    const updateFields = Object.keys(updateData)
    const updateValues = Object.values(updateData)
    
    if (updateFields.length === 0) {
      return res.status(400).json({
        success: false,
        error: '没有要更新的字段'
      } as ApiResponse)
    }
    
    const setClause = updateFields.map(field => `${field} = ?`).join(', ')
    
    await db.execute(
      `UPDATE FLEET_MANAGEMENT SET ${setClause} WHERE id = ?`,
      [...updateValues, id]
    )
    
    // 获取更新后的数据
    const [updatedRows] = await db.execute(
      'SELECT * FROM FLEET_MANAGEMENT WHERE id = ?',
      [id]
    )
    
    const data = (updatedRows as any[])[0]
    
    res.json({
      success: true,
      data,
      message: '车队信息更新成功'
    } as ApiResponse<FleetManagement>)
  } catch (error) {
    console.error('更新车队信息异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 删除车队
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params
    
    // 检查车队是否还有车辆
    const [vehicleRows] = await db.execute(
      'SELECT id FROM VEHICLES WHERE fleet_id = ? LIMIT 1',
      [id]
    )
    
    if (vehicleRows && (vehicleRows as any[]).length > 0) {
      return res.status(400).json({
        success: false,
        error: '车队中还有车辆，无法删除'
      } as ApiResponse)
    }
    
    // 检查车队是否存在
    const [fleetRows] = await db.execute(
      'SELECT id FROM FLEET_MANAGEMENT WHERE id = ?',
      [id]
    )
    
    if (!fleetRows || (fleetRows as any[]).length === 0) {
      return res.status(404).json({
        success: false,
        error: '车队不存在'
      } as ApiResponse)
    }
    
    await db.execute(
      'DELETE FROM FLEET_MANAGEMENT WHERE id = ?',
      [id]
    )
    
    res.json({
      success: true,
      message: '车队删除成功'
    } as ApiResponse)
  } catch (error) {
    console.error('删除车队异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取车队的车辆列表
router.get('/:id/vehicles', async (req, res) => {
  try {
    const { id } = req.params
    const { page = 1, limit = 10, status } = req.query
    const offset = (Number(page) - 1) * Number(limit)
    
    // 验证车队是否存在
    const [fleetRows] = await db.execute(
      'SELECT id, fleet_name FROM FLEET_MANAGEMENT WHERE id = ?',
      [id]
    )
    
    if (!fleetRows || (fleetRows as any[]).length === 0) {
      return res.status(404).json({
        success: false,
        error: '车队不存在'
      } as ApiResponse)
    }
    
    const fleet = (fleetRows as any[])[0]
    
    // 构建查询条件
    let whereClause = 'WHERE v.fleet_id = ?'
    let queryParams: any[] = [id]
    
    if (status) {
      whereClause += ' AND v.status = ?'
      queryParams.push(status)
    }
    
    // 获取总数
    const [countRows] = await db.execute(
      `SELECT COUNT(*) as total FROM VEHICLES v ${whereClause}`,
      queryParams
    )
    
    const total = (countRows as any[])[0].total
    
    // 获取车辆数据
    const [vehicleRows] = await db.execute(
      `SELECT v.*, u.username as user_name, u.email as user_email, u.phone as user_phone
       FROM vehicles v
       LEFT JOIN users u ON v.owner_id = u.user_id
       ${whereClause}
       ORDER BY v.created_at DESC
       LIMIT ? OFFSET ?`,
      [...queryParams, Number(limit), offset]
    )
    
    // 格式化数据
    const vehicles = (vehicleRows as any[]).map(row => ({
      ...row,
      user: row.user_name ? {
        name: row.user_name,
        email: row.user_email,
        phone: row.user_phone
      } : null
    }))
    
    const response = {
      success: true,
      data: {
        fleet: fleet,
        vehicles: vehicles,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total: total,
          totalPages: Math.ceil(total / Number(limit))
        }
      }
    }
    
    res.json(response)
  } catch (error) {
    console.error('获取车队车辆列表异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 添加车辆到车队
router.post('/:id/vehicles', async (req, res) => {
  try {
    const { id } = req.params
    const { vehicle_ids } = req.body
    
    if (!vehicle_ids || !Array.isArray(vehicle_ids) || vehicle_ids.length === 0) {
      return res.status(400).json({
        success: false,
        error: '请提供有效的车辆ID列表'
      } as ApiResponse)
    }
    
    // 验证车队是否存在并获取车队信息
    const [fleetRows] = await db.execute(
      'SELECT id, fleet_name, max_vehicles, current_vehicles FROM FLEET_MANAGEMENT WHERE id = ?',
      [id]
    )
    
    if (!fleetRows || (fleetRows as any[]).length === 0) {
      return res.status(404).json({
        success: false,
        error: '车队不存在'
      } as ApiResponse)
    }
    
    const fleet = (fleetRows as any[])[0]
    
    // 检查车队容量限制
    if (fleet.max_vehicles && (fleet.current_vehicles + vehicle_ids.length) > fleet.max_vehicles) {
      return res.status(400).json({
        success: false,
        error: `车队容量不足，最大容量：${fleet.max_vehicles}，当前：${fleet.current_vehicles}，尝试添加：${vehicle_ids.length}`
      } as ApiResponse)
    }
    
    // 验证车辆是否存在且未分配到其他车队
    const placeholders = vehicle_ids.map(() => '?').join(',')
    const [vehicleRows] = await db.execute(
      `SELECT id, license_plate, fleet_id FROM VEHICLES WHERE id IN (${placeholders})`,
      vehicle_ids
    )
    
    const vehicles = vehicleRows as any[]
    
    if (!vehicles || vehicles.length !== vehicle_ids.length) {
      return res.status(400).json({
        success: false,
        error: '部分车辆不存在'
      } as ApiResponse)
    }
    
    // 检查是否有车辆已分配到其他车队
    const assignedVehicles = vehicles.filter(v => v.fleet_id && v.fleet_id !== id)
    if (assignedVehicles.length > 0) {
      return res.status(400).json({
        success: false,
        error: `以下车辆已分配到其他车队：${assignedVehicles.map(v => v.license_plate).join(', ')}`
      } as ApiResponse)
    }
    
    // 使用事务更新车辆分配和车队数量
    await db.execute('START TRANSACTION')
    
    try {
      // 更新车辆的车队分配
      const updatePlaceholders = vehicle_ids.map(() => '?').join(',')
      await db.execute(
        `UPDATE VEHICLES SET fleet_id = ?, updated_at = ? WHERE id IN (${updatePlaceholders})`,
        [id, new Date().toISOString(), ...vehicle_ids]
      )
      
      // 更新车队的当前车辆数量
      await db.execute(
        'UPDATE FLEET_MANAGEMENT SET current_vehicles = ?, updated_at = ? WHERE id = ?',
        [fleet.current_vehicles + vehicle_ids.length, new Date().toISOString(), id]
      )
      
      await db.execute('COMMIT')
    } catch (error) {
      await db.execute('ROLLBACK')
      console.error('更新车辆车队分配失败:', error)
      return res.status(500).json({
        success: false,
        error: '更新车辆车队分配失败'
      } as ApiResponse)
    }
    
    res.json({
      success: true,
      message: `成功添加 ${vehicle_ids.length} 辆车到车队 ${fleet.fleet_name}`,
      data: {
        fleetId: id,
        addedVehicles: vehicle_ids.length,
        totalVehicles: fleet.current_vehicles + vehicle_ids.length
      }
    } as ApiResponse)
  } catch (error) {
    console.error('添加车辆到车队异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 从车队移除车辆
router.delete('/:id/vehicles', async (req, res) => {
  try {
    const { id } = req.params
    const { vehicle_ids } = req.body
    
    if (!vehicle_ids || !Array.isArray(vehicle_ids) || vehicle_ids.length === 0) {
      return res.status(400).json({
        success: false,
        error: '请提供有效的车辆ID列表'
      } as ApiResponse)
    }
    
    // 验证车队是否存在
    const { data: fleet, error: fleetError } = await supabaseAdmin
      .from(TABLES.FLEET_MANAGEMENT)
      .select('id, fleet_name, current_vehicles')
      .eq('id', id)
      .single()
    
    if (fleetError || !fleet) {
      return res.status(404).json({
        success: false,
        error: '车队不存在'
      } as ApiResponse)
    }
    
    // 验证车辆是否属于该车队
    const { data: vehicles, error: vehicleError } = await supabaseAdmin
      .from(TABLES.VEHICLES)
      .select('id, license_plate, fleet_id')
      .in('id', vehicle_ids)
      .eq('fleet_id', id)
    
    if (vehicleError) {
      console.error('验证车辆失败:', vehicleError)
      return res.status(500).json({
        success: false,
        error: '验证车辆失败'
      } as ApiResponse)
    }
    
    if (!vehicles || vehicles.length !== vehicle_ids.length) {
      return res.status(400).json({
        success: false,
        error: '部分车辆不属于该车队'
      } as ApiResponse)
    }
    
    // 移除车辆的车队分配
    const { error: updateError } = await supabaseAdmin
      .from(TABLES.VEHICLES)
      .update({ fleet_id: null, updated_at: new Date().toISOString() })
      .in('id', vehicle_ids)
    
    if (updateError) {
      console.error('移除车辆车队分配失败:', updateError)
      return res.status(500).json({
        success: false,
        error: '移除车辆车队分配失败'
      } as ApiResponse)
    }
    
    // 更新车队的当前车辆数量
    const { error: fleetUpdateError } = await supabaseAdmin
      .from(TABLES.FLEET_MANAGEMENT)
      .update({ 
        current_vehicles: Math.max(0, fleet.current_vehicles - vehicle_ids.length),
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
    
    if (fleetUpdateError) {
      console.error('更新车队车辆数量失败:', fleetUpdateError)
      return res.status(500).json({
        success: false,
        error: '更新车队车辆数量失败'
      } as ApiResponse)
    }
    
    res.json({
      success: true,
      message: `成功从车队 ${fleet.fleet_name} 移除 ${vehicle_ids.length} 辆车`,
      data: {
        fleetId: id,
        removedVehicles: vehicle_ids.length,
        totalVehicles: Math.max(0, fleet.current_vehicles - vehicle_ids.length)
      }
    } as ApiResponse)
  } catch (error) {
    console.error('从车队移除车辆异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取车队统计信息
router.get('/:id/statistics', async (req, res) => {
  try {
    const { id } = req.params
    const { days = 30 } = req.query
    
    // 验证车队是否存在
    const { data: fleet, error: fleetError } = await supabaseAdmin
      .from(TABLES.FLEET_MANAGEMENT)
      .select('*')
      .eq('id', id)
      .single()
    
    if (fleetError || !fleet) {
      return res.status(404).json({
        success: false,
        error: '车队不存在'
      } as ApiResponse)
    }
    
    // 获取车队车辆
    const { data: vehicles, error: vehicleError } = await supabaseAdmin
      .from(TABLES.VEHICLES)
      .select('id, status')
      .eq('fleet_id', id)
    
    if (vehicleError) {
      console.error('获取车队车辆失败:', vehicleError)
      return res.status(500).json({
        success: false,
        error: '获取车队车辆失败'
      } as ApiResponse)
    }
    
    const fleetVehicles = vehicles || []
    const vehicleIds = fleetVehicles.map(v => v.id)
    
    // 获取指定时间范围内的数据
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - Number(days))
    
    // 获取充电会话统计
    const { data: chargingSessions, error: chargingError } = await supabaseAdmin
      .from(TABLES.CHARGING_SESSIONS)
      .select('id, total_energy_kwh, total_cost, status')
      .in('vehicle_id', vehicleIds)
      .gte('start_time', startDate.toISOString())
    
    // 获取驾驶行为统计
    const { data: drivingBehavior, error: drivingError } = await supabaseAdmin
      .from(TABLES.DRIVING_BEHAVIOR)
      .select('id, distance_km, duration_minutes, driving_score, fuel_consumption')
      .in('vehicle_id', vehicleIds)
      .gte('trip_start_time', startDate.toISOString())
    
    // 获取碳积分统计
    const { data: carbonCredits, error: carbonError } = await supabaseAdmin
      .from(TABLES.CARBON_CREDIT)
      .select('id, amount, transaction_type')
      .in('vehicle_id', vehicleIds)
      .gte('created_at', startDate.toISOString())
    
    const chargingData = chargingSessions || []
    const drivingData = drivingBehavior || []
    const carbonData = carbonCredits || []
    
    // 计算统计数据
    const vehicleStats = {
      total: fleetVehicles.length,
      active: fleetVehicles.filter(v => v.status === 'active').length,
      maintenance: fleetVehicles.filter(v => v.status === 'maintenance').length,
      inactive: fleetVehicles.filter(v => v.status === 'inactive').length
    }
    
    const chargingStats = {
      totalSessions: chargingData.length,
      completedSessions: chargingData.filter(s => s.status === 'completed').length,
      totalEnergy: chargingData.reduce((sum, s) => sum + (s.total_energy_kwh || 0), 0),
      totalCost: chargingData.reduce((sum, s) => sum + (s.total_cost || 0), 0)
    }
    
    const drivingStats = {
      totalTrips: drivingData.length,
      totalDistance: drivingData.reduce((sum, d) => sum + (d.distance_km || 0), 0),
      totalDuration: drivingData.reduce((sum, d) => sum + (d.duration_minutes || 0), 0),
      avgDrivingScore: drivingData.length > 0 ? 
        drivingData.reduce((sum, d) => sum + (d.driving_score || 0), 0) / drivingData.length : 0,
      totalFuelConsumption: drivingData.reduce((sum, d) => sum + (d.fuel_consumption || 0), 0)
    }
    
    const carbonStats = {
      totalTransactions: carbonData.length,
      earnedCredits: carbonData.filter(c => c.transaction_type === 'earned').reduce((sum, c) => sum + c.amount, 0),
      usedCredits: carbonData.filter(c => c.transaction_type === 'used').reduce((sum, c) => sum + c.amount, 0),
      transferredCredits: carbonData.filter(c => c.transaction_type === 'transferred').reduce((sum, c) => sum + c.amount, 0)
    }
    
    const statistics = {
      fleetInfo: {
        id: fleet.id,
        name: fleet.fleet_name,
        type: fleet.fleet_type,
        manager: fleet.manager_id,
        period: `最近${days}天`
      },
      vehicleStats,
      chargingStats,
      drivingStats,
      carbonStats,
      efficiency: {
        avgEnergyPerKm: drivingStats.totalDistance > 0 ? 
          (chargingStats.totalEnergy / drivingStats.totalDistance).toFixed(3) : '0',
        avgCostPerKm: drivingStats.totalDistance > 0 ? 
          (chargingStats.totalCost / drivingStats.totalDistance).toFixed(2) : '0',
        utilizationRate: vehicleStats.total > 0 ? 
          ((vehicleStats.active / vehicleStats.total) * 100).toFixed(1) : '0'
      }
    }
    
    res.json({
      success: true,
      data: statistics
    } as ApiResponse)
  } catch (error) {
    console.error('获取车队统计信息异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取车队统计信息（用户生态服务页面专用）
router.get('/stats', async (req, res) => {
  try {
    const { user_id = 1 } = req.query
    
    // 获取用户的车辆统计
    const [vehicleResult] = await db.execute(
      `SELECT 
        COUNT(*) as total_vehicles,
        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_vehicles,
        SUM(CASE WHEN status = 'maintenance' THEN 1 ELSE 0 END) as maintenance_vehicles,
        SUM(CASE WHEN status = 'inactive' THEN 1 ELSE 0 END) as inactive_vehicles
       FROM vehicles WHERE user_id = ?`,
      [user_id]
    )
    const vehicleStats = (vehicleResult as any[])[0]
    
    // 获取充电统计
    const [chargingResult] = await db.execute(
      `SELECT 
        COUNT(*) as total_sessions,
        COALESCE(SUM(energy_consumed), 0) as total_energy,
        COALESCE(SUM(cost), 0) as total_cost,
        COALESCE(AVG(efficiency), 0) as avg_efficiency
       FROM charging_sessions cs
       JOIN vehicles v ON cs.vehicle_id = v.id
       WHERE v.user_id = ?`,
      [user_id]
    )
    const chargingStats = (chargingResult as any[])[0]
    
    // 获取行驶统计
    const [drivingResult] = await db.execute(
      `SELECT 
        COALESCE(SUM(distance), 0) as total_distance,
        COUNT(*) as total_trips,
        COALESCE(AVG(speed_score + acceleration_score + braking_score + cornering_score) / 4, 0) as avg_score
       FROM driving_behavior db
       JOIN vehicles v ON db.vehicle_id = v.id
       WHERE v.user_id = ?`,
      [user_id]
    )
    const drivingStats = (drivingResult as any[])[0]
    
    const data = {
      vehicleCount: vehicleStats.total_vehicles || 0,
      activeVehicles: vehicleStats.active_vehicles || 0,
      maintenanceVehicles: vehicleStats.maintenance_vehicles || 0,
      inactiveVehicles: vehicleStats.inactive_vehicles || 0,
      totalDistance: Math.round(drivingStats.total_distance || 0),
      totalTrips: drivingStats.total_trips || 0,
      totalEnergy: Math.round(chargingStats.total_energy || 0),
      totalCost: Math.round(chargingStats.total_cost || 0),
      avgEfficiency: Math.round((chargingStats.avg_efficiency || 0) * 100),
      avgDrivingScore: Math.round((drivingStats.avg_score || 0) * 100),
      utilizationRate: vehicleStats.total_vehicles > 0 ? 
        Math.round((vehicleStats.active_vehicles / vehicleStats.total_vehicles) * 100) : 0
    }
    
    res.json({
      success: true,
      data
    } as ApiResponse)
  } catch (error) {
    console.error('获取车队统计信息异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取车队车辆列表（用户生态服务页面专用）
router.get('/vehicles', async (req, res) => {
  try {
    const { user_id = 1 } = req.query
    
    // 获取用户的车辆列表
    const [result] = await db.execute(
      `SELECT 
        v.*,
        COALESCE(cs.last_charge_time, NULL) as last_charge_time,
        COALESCE(cs.battery_level, 0) as battery_level,
        COALESCE(db.last_trip_distance, 0) as last_trip_distance,
        COALESCE(db.last_trip_score, 0) as last_trip_score
       FROM vehicles v
       LEFT JOIN (
         SELECT 
           vehicle_id,
           MAX(end_time) as last_charge_time,
           battery_level
         FROM charging_sessions 
         GROUP BY vehicle_id
       ) cs ON v.id = cs.vehicle_id
       LEFT JOIN (
         SELECT 
           vehicle_id,
           distance as last_trip_distance,
           (speed_score + acceleration_score + braking_score + cornering_score) / 4 as last_trip_score
         FROM driving_behavior 
         WHERE id IN (
           SELECT MAX(id) FROM driving_behavior GROUP BY vehicle_id
         )
       ) db ON v.id = db.vehicle_id
       WHERE v.user_id = ?
       ORDER BY v.created_at DESC`,
      [user_id]
    )
    const vehicles = result as any[]
    
    const data = vehicles.map(vehicle => ({
      id: vehicle.id,
      name: vehicle.model || `车辆 ${vehicle.id}`,
      model: vehicle.model,
      brand: vehicle.brand,
      year: vehicle.year,
      status: vehicle.status,
      batteryLevel: Math.round(vehicle.battery_level || 0),
      lastChargeTime: vehicle.last_charge_time,
      lastTripDistance: Math.round(vehicle.last_trip_distance || 0),
      lastTripScore: Math.round((vehicle.last_trip_score || 0) * 100),
      location: {
        lat: 39.9042 + (Math.random() - 0.5) * 0.1, // 模拟位置
        lng: 116.4074 + (Math.random() - 0.5) * 0.1,
        address: '北京市朝阳区'
      },
      efficiency: Math.round(Math.random() * 20 + 80), // 模拟效率
      mileage: Math.round(Math.random() * 50000 + 10000) // 模拟里程
    }))
    
    res.json({
      success: true,
      data
    } as ApiResponse)
  } catch (error) {
    console.error('获取车队车辆列表异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

export default router