const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

async function testFileUpload() {
  console.log('📁 测试文件上传功能...\n');
  
  try {
    // 检查测试文件是否存在
    if (!fs.existsSync('test-document.txt')) {
      console.log('❌ 测试文件不存在');
      return;
    }
    
    console.log('1. 准备上传文本文件...');
    
    const formData = new FormData();
    formData.append('file', fs.createReadStream('test-document.txt'));
    formData.append('question', '这个文档介绍了什么系统？');
    
    console.log('2. 发送文件分析请求...');
    
    const response = await axios.post('http://localhost:3001/api/ai-enhanced/analyze-file', formData, {
      headers: {
        ...formData.getHeaders()
      },
      timeout: 30000 // 30秒超时
    });
    
    console.log('3. 处理响应...');
    
    if (response.data.success) {
      console.log('✅ 文件分析成功！');
      console.log('文件名:', response.data.fileName);
      console.log('文件类型:', response.data.fileType);
      console.log('分析结果:', response.data.response);
    } else {
      console.log('❌ 文件分析失败:', response.data.message);
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('错误信息:', error.response.data);
    }
  }
}

testFileUpload();
