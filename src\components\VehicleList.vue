<template>
  <div class="vehicle-list" v-loading="loading" element-loading-text="加载车辆数据中...">
    <div class="list-header">
      <div class="list-title">
        <h3>车辆列表</h3>
        <span class="vehicle-count">共 {{ vehicles.length }} 辆车</span>
      </div>
      <div class="view-toggle">
        <el-radio-group v-model="viewMode" @change="handleViewModeChange">
          <el-radio-button label="grid">网格视图</el-radio-button>
          <el-radio-button label="table">表格视图</el-radio-button>
        </el-radio-group>
      </div>
    </div>

    <!-- 网格视图 -->
    <div v-if="viewMode === 'grid'" class="grid-view">
      <div class="vehicle-grid">
        <div 
          v-for="vehicle in vehicles" 
          :key="vehicle.id" 
          class="vehicle-card"
          @click="$emit('view-detail', vehicle.id)"
        >
          <div class="vehicle-header">
            <div class="vehicle-brand">
              <img :src="getBrandLogo(vehicle.brand)" :alt="vehicle.brand" class="brand-logo">
              <span class="brand-name">{{ vehicle.brand }}</span>
            </div>
            <div class="vehicle-status">
              <el-tag :type="getStatusType(vehicle.status)" size="small">
                {{ getStatusText(vehicle.status) }}
              </el-tag>
            </div>
          </div>
          
          <div class="vehicle-info">
            <h4 class="vehicle-name">{{ vehicle.model }}</h4>
            <p class="vehicle-plate">{{ vehicle.license_plate || '未设置车牌' }}</p>
            <p class="vehicle-vin">VIN: {{ vehicle.vin.slice(-8) }}</p>
          </div>
          
          <div class="vehicle-metrics">
            <div class="metric-item">
              <div class="metric-icon battery">
                <el-icon><Lightning /></el-icon>
              </div>
              <div class="metric-content">
                <span class="metric-label">电池电量</span>
                <div class="battery-bar">
                  <div class="battery-fill" :style="{ width: getBatteryLevel(vehicle) + '%' }"></div>
                </div>
                <span class="metric-value">{{ getBatteryLevel(vehicle) }}%</span>
              </div>
            </div>
            
            <div class="metric-item">
              <div class="metric-icon mileage">
                <el-icon><Odometer /></el-icon>
              </div>
              <div class="metric-content">
                <span class="metric-label">总里程</span>
                <span class="metric-value">{{ formatMileage(vehicle) }} km</span>
              </div>
            </div>
          </div>
          
          <div class="vehicle-actions" @click.stop>
            <el-button size="small" @click="$emit('view-detail', vehicle.id)">
              详情
            </el-button>
            <el-button size="small" @click="$emit('edit-vehicle', vehicle.id)">
              编辑
            </el-button>
            <el-dropdown @command="handleAction">
              <el-button size="small">
                更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="{action: 'control', id: vehicle.id}">
                    <el-icon><Setting /></el-icon>远程控制
                  </el-dropdown-item>
                  <el-dropdown-item :command="{action: 'track', id: vehicle.id}">
                    <el-icon><Location /></el-icon>实时追踪
                  </el-dropdown-item>
                  <el-dropdown-item :command="{action: 'maintenance', id: vehicle.id}">
                    <el-icon><Tools /></el-icon>维护记录
                  </el-dropdown-item>
                  <el-dropdown-item :command="{action: 'delete', id: vehicle.id}" divided>
                    <el-icon><Delete /></el-icon>删除车辆
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>
    </div>

    <!-- 表格视图 -->
    <div v-else class="table-view">
      <el-table 
        :data="vehicles" 
        style="width: 100%" 
        @row-click="handleRowClick"
        class="vehicle-table"
      >
        <el-table-column prop="brand" label="品牌" width="100">
          <template #default="{ row }">
            <div class="brand-cell">
              <img :src="getBrandLogo(row.brand)" :alt="row.brand" class="brand-logo-small">
              {{ row.brand }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="model" label="型号" width="120" />
        <el-table-column prop="license_plate" label="车牌号" width="120">
          <template #default="{ row }">
            {{ row.license_plate || '未设置' }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="电池电量" width="120">
          <template #default="{ row }">
            <div class="battery-cell">
              <el-progress 
                :percentage="getBatteryLevel(row)" 
                :show-text="false" 
                :stroke-width="8"
                :color="getBatteryColor(getBatteryLevel(row))"
              />
              <span class="battery-text">{{ getBatteryLevel(row) }}%</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="总里程" width="120">
          <template #default="{ row }">
            {{ formatMileage(row) }} km
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="注册时间" width="120">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="table-actions">
              <el-button size="small" @click.stop="$emit('view-detail', row.id)">
                详情
              </el-button>
              <el-button size="small" @click.stop="$emit('edit-vehicle', row.id)">
                编辑
              </el-button>
              <el-button size="small" @click.stop="$emit('control-vehicle', row.id)">
                控制
              </el-button>
              <el-button 
                size="small" 
                type="danger" 
                @click.stop="$emit('delete-vehicle', row.id)"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 空状态 -->
    <div v-if="vehicles.length === 0 && !loading" class="empty-state">
      <el-empty description="暂无车辆数据">
        <el-button type="primary" @click="$emit('add-vehicle')">添加第一辆车</el-button>
      </el-empty>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { 
  Lightning, Odometer, ArrowDown, Setting, Location, 
  Tools, Delete 
} from '@element-plus/icons-vue'
import type { Vehicle } from '@/stores/vehicle'

// Props
defineProps<{
  vehicles: Vehicle[]
  loading?: boolean
}>()

// Emits
defineEmits<{
  'view-detail': [vehicleId: string]
  'edit-vehicle': [vehicleId: string]
  'delete-vehicle': [vehicleId: string]
  'control-vehicle': [vehicleId: string]
  'add-vehicle': []
}>()

// 响应式数据
const viewMode = ref<'grid' | 'table'>('grid')

// 方法
const getBrandLogo = (brand: string) => {
  const logos: Record<string, string> = {
    'Tesla': '/images/brands/tesla.png',
    'BYD': '/images/brands/byd.png',
    'NIO': '/images/brands/nio.png',
    'XPeng': '/images/brands/xpeng.png',
    'Li Auto': '/images/brands/li.png'
  }
  return logos[brand] || '/images/brands/default.png'
}

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    'active': 'success',
    'maintenance': 'warning',
    'retired': 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    'active': '在线',
    'maintenance': '维护中',
    'retired': '已退役'
  }
  return texts[status] || status
}

const getBatteryLevel = (vehicle: Vehicle) => {
  // 模拟电池电量，实际应该从API获取
  return Math.floor(Math.random() * 40) + 60
}

const getBatteryColor = (level: number) => {
  if (level > 80) return '#10b981'
  if (level > 50) return '#f59e0b'
  if (level > 20) return '#ef4444'
  return '#dc2626'
}

const formatMileage = (vehicle: Vehicle) => {
  // 模拟里程数据，实际应该从API获取
  return (Math.floor(Math.random() * 50000) + 10000).toLocaleString()
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const handleViewModeChange = () => {
  // 视图模式切换逻辑
}

const handleRowClick = (row: Vehicle) => {
  // 表格行点击事件
}

const handleAction = (command: {action: string, id: string}) => {
  const { action, id } = command
  switch (action) {
    case 'control':
      // 远程控制
      break
    case 'track':
      // 实时追踪
      break
    case 'maintenance':
      // 维护记录
      break
    case 'delete':
      // 删除车辆
      break
  }
}
</script>

<style scoped>
.vehicle-list {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  overflow: hidden;
}

/* 列表头部 */
.list-header {
  padding: 24px;
  border-bottom: 1px solid #f3f4f6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.list-title h3 {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 4px 0;
}

.vehicle-count {
  font-size: 14px;
  color: #6b7280;
}

/* 网格视图 */
.grid-view {
  padding: 24px;
}

.vehicle-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
}

.vehicle-card {
  background: white;
  border-radius: 16px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  cursor: pointer;
}

.vehicle-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.vehicle-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.vehicle-brand {
  display: flex;
  align-items: center;
  gap: 8px;
}

.brand-logo {
  width: 24px;
  height: 24px;
  border-radius: 4px;
}

.brand-name {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
}

.vehicle-info {
  margin-bottom: 16px;
}

.vehicle-name {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 8px 0;
}

.vehicle-plate {
  font-size: 16px;
  font-weight: 500;
  color: #374151;
  margin: 0 0 4px 0;
}

.vehicle-vin {
  font-size: 12px;
  color: #9ca3af;
  margin: 0;
}

.vehicle-metrics {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.metric-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
}

.metric-icon.battery {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.metric-icon.mileage {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.metric-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.metric-label {
  font-size: 12px;
  color: #6b7280;
}

.metric-value {
  font-size: 14px;
  font-weight: 600;
  color: #111827;
}

.battery-bar {
  width: 100%;
  height: 6px;
  background: #e5e7eb;
  border-radius: 3px;
  overflow: hidden;
}

.battery-fill {
  height: 100%;
  background: linear-gradient(90deg, #10b981 0%, #059669 100%);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.vehicle-actions {
  display: flex;
  gap: 8px;
}

/* 表格视图 */
.table-view {
  padding: 0;
}

.vehicle-table {
  background: transparent;
}

.brand-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.brand-logo-small {
  width: 20px;
  height: 20px;
  border-radius: 4px;
}

.battery-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.battery-text {
  font-size: 12px;
  font-weight: 500;
  min-width: 35px;
}

.table-actions {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

/* 空状态 */
.empty-state {
  padding: 60px 24px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .list-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .vehicle-grid {
    grid-template-columns: 1fr;
  }

  .grid-view {
    padding: 16px;
  }

  .vehicle-actions {
    flex-wrap: wrap;
  }

  .table-actions {
    flex-direction: column;
    gap: 2px;
  }

  .table-actions .el-button {
    font-size: 12px;
    padding: 4px 8px;
  }
}
</style>
