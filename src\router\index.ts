import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/login',
      name: 'Login',
      component: () => import('../pages/Login.vue'),
      meta: { requiresAuth: false }
    },
    {
      path: '/',
      redirect: '/dashboard'
    },
    {
      path: '/dashboard',
      component: () => import('@/components/Layout-dashboard.vue'),
      meta: { requiresAuth: true },
      children: [
        {
          path: '',
          name: 'Dashboard',
          component: () => import('@/pages/Dashboard-premium.vue')
        },
        {
          path: '/vehicle-network',
          name: 'VehicleNetwork',
          component: () => import('@/pages/VehicleNetwork.vue')
        },
        {
          path: '/vehicle-monitoring',
          name: 'VehicleMonitoring',
          component: () => import('@/pages/VehicleMonitoring.vue')
        },
        {
          path: '/vehicle-management',
          name: 'VehicleManagement',
          component: () => import('@/pages/VehicleManagement.vue')
        },
        {
          path: '/battery-management',
          name: 'BatteryManagement',
          component: () => import('@/pages/BatteryManagement.vue')
        },
        {
          path: '/charging-service',
          name: 'ChargingService',
          component: () => import('@/pages/ChargingService.vue')
        },
        {
          path: '/ai-assistant',
          name: 'AIAssistant',
          component: () => import('@/pages/AIAssistant.vue')
        },
        {
          path: '/adas',
          name: 'ADAS',
          component: () => import('@/pages/ADAS.vue')
        },
        {
          path: '/system-security',
          name: 'SystemSecurity',
          component: () => import('@/pages/SystemSecurity.vue')
        },
        {
          path: '/user-ecosystem',
          name: 'UserEcosystem',
          component: () => import('@/pages/UserEcosystem.vue')
        },
        {
          path: '/tech-support',
          name: 'TechSupport',
          component: () => import('@/pages/TechSupport.vue')
        },
        {
          path: '/about-us',
          name: 'AboutUs',
          component: () => import('@/pages/AboutUs.vue')
        }
      ]
    }
  ]
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  
  // 初始化认证状态
  if (!authStore.isInitialized) {
    await authStore.initAuth()
  }
  
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth !== false)
  
  if (requiresAuth && !authStore.isAuthenticated) {
    next('/login')
  } else if (to.path === '/login' && authStore.isAuthenticated) {
    next('/dashboard')
  } else {
    next()
  }
})

export default router