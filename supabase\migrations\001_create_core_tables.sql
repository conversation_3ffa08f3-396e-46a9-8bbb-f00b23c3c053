-- 新能源汽车智能综合管理系统核心数据表
-- 创建时间: 2024-01-08

-- 1. 用户表
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20),
    name VARCHAR(100) NOT NULL,
    avatar_url TEXT,
    user_type VARCHAR(20) DEFAULT 'individual' CHECK (user_type IN ('individual', 'enterprise', 'fleet')),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. 车辆表
CREATE TABLE vehicles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    vin VA<PERSON>HA<PERSON>(17) UNIQUE NOT NULL,
    license_plate VARCHAR(20),
    brand VARCHAR(50) NOT NULL,
    model VARCHAR(100) NOT NULL,
    year INTEGER,
    color VARCHAR(30),
    vehicle_type VARCHAR(20) DEFAULT 'bev' CHECK (vehicle_type IN ('bev', 'phev', 'fcev')),
    battery_capacity DECIMAL(8,2),
    max_range INTEGER,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'maintenance', 'retired')),
    location JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. 电池表
CREATE TABLE batteries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    vehicle_id UUID REFERENCES vehicles(id) ON DELETE CASCADE,
    battery_id VARCHAR(50) UNIQUE NOT NULL,
    manufacturer VARCHAR(100),
    model VARCHAR(100),
    capacity DECIMAL(8,2) NOT NULL,
    voltage DECIMAL(8,2),
    chemistry VARCHAR(50),
    manufacture_date DATE,
    warranty_end_date DATE,
    current_soh DECIMAL(5,2) DEFAULT 100.00,
    current_soc DECIMAL(5,2),
    temperature DECIMAL(5,2),
    cycle_count INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'normal' CHECK (status IN ('normal', 'warning', 'critical', 'maintenance')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. 电池溯源表
CREATE TABLE battery_trace (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    battery_id UUID REFERENCES batteries(id) ON DELETE CASCADE,
    trace_type VARCHAR(30) NOT NULL CHECK (trace_type IN ('production', 'transport', 'installation', 'maintenance', 'recycling')),
    location VARCHAR(200),
    operator VARCHAR(100),
    description TEXT,
    metadata JSONB,
    blockchain_hash VARCHAR(64),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. 充电站表
CREATE TABLE charging_stations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    station_code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(200) NOT NULL,
    operator VARCHAR(100),
    address TEXT NOT NULL,
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    total_ports INTEGER DEFAULT 1,
    available_ports INTEGER DEFAULT 1,
    power_type VARCHAR(20) CHECK (power_type IN ('ac', 'dc', 'mixed')),
    max_power DECIMAL(8,2),
    pricing JSONB,
    amenities TEXT[],
    operating_hours JSONB,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'maintenance', 'offline')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. 充电会话表
CREATE TABLE charging_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    vehicle_id UUID REFERENCES vehicles(id) ON DELETE CASCADE,
    station_id UUID REFERENCES charging_stations(id) ON DELETE CASCADE,
    session_code VARCHAR(50) UNIQUE NOT NULL,
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE,
    start_soc DECIMAL(5,2),
    end_soc DECIMAL(5,2),
    energy_delivered DECIMAL(8,2),
    peak_power DECIMAL(8,2),
    total_cost DECIMAL(10,2),
    payment_method VARCHAR(30),
    payment_status VARCHAR(20) DEFAULT 'pending' CHECK (payment_status IN ('pending', 'completed', 'failed', 'refunded')),
    session_status VARCHAR(20) DEFAULT 'active' CHECK (session_status IN ('active', 'completed', 'interrupted', 'failed')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. 碳积分表
CREATE TABLE carbon_credit (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    vehicle_id UUID REFERENCES vehicles(id) ON DELETE CASCADE,
    credit_type VARCHAR(30) NOT NULL CHECK (credit_type IN ('driving', 'charging', 'sharing', 'recycling', 'referral')),
    amount DECIMAL(10,2) NOT NULL,
    description TEXT,
    source_data JSONB,
    earned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'used', 'expired')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 8. 驾驶行为表
CREATE TABLE driving_behavior (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    vehicle_id UUID REFERENCES vehicles(id) ON DELETE CASCADE,
    trip_id VARCHAR(50),
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE NOT NULL,
    distance DECIMAL(8,2),
    duration INTEGER,
    avg_speed DECIMAL(5,2),
    max_speed DECIMAL(5,2),
    harsh_acceleration_count INTEGER DEFAULT 0,
    harsh_braking_count INTEGER DEFAULT 0,
    sharp_turn_count INTEGER DEFAULT 0,
    energy_efficiency DECIMAL(5,2),
    eco_score DECIMAL(5,2),
    safety_score DECIMAL(5,2),
    route_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 9. 车队管理表
CREATE TABLE fleet_management (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    fleet_name VARCHAR(200) NOT NULL,
    owner_id UUID REFERENCES users(id) ON DELETE CASCADE,
    fleet_type VARCHAR(30) CHECK (fleet_type IN ('corporate', 'rental', 'sharing', 'logistics')),
    total_vehicles INTEGER DEFAULT 0,
    active_vehicles INTEGER DEFAULT 0,
    description TEXT,
    contact_info JSONB,
    operational_area TEXT[],
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 车队车辆关联表
CREATE TABLE fleet_vehicles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    fleet_id UUID REFERENCES fleet_management(id) ON DELETE CASCADE,
    vehicle_id UUID REFERENCES vehicles(id) ON DELETE CASCADE,
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    UNIQUE(fleet_id, vehicle_id)
);

-- 创建索引
CREATE INDEX idx_vehicles_user_id ON vehicles(user_id);
CREATE INDEX idx_vehicles_vin ON vehicles(vin);
CREATE INDEX idx_batteries_vehicle_id ON batteries(vehicle_id);
CREATE INDEX idx_battery_trace_battery_id ON battery_trace(battery_id);
CREATE INDEX idx_charging_sessions_user_id ON charging_sessions(user_id);
CREATE INDEX idx_charging_sessions_vehicle_id ON charging_sessions(vehicle_id);
CREATE INDEX idx_charging_sessions_station_id ON charging_sessions(station_id);
CREATE INDEX idx_carbon_credit_user_id ON carbon_credit(user_id);
CREATE INDEX idx_driving_behavior_user_id ON driving_behavior(user_id);
CREATE INDEX idx_driving_behavior_vehicle_id ON driving_behavior(vehicle_id);
CREATE INDEX idx_fleet_vehicles_fleet_id ON fleet_vehicles(fleet_id);
CREATE INDEX idx_fleet_vehicles_vehicle_id ON fleet_vehicles(vehicle_id);

-- 启用行级安全策略
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE vehicles ENABLE ROW LEVEL SECURITY;
ALTER TABLE batteries ENABLE ROW LEVEL SECURITY;
ALTER TABLE battery_trace ENABLE ROW LEVEL SECURITY;
ALTER TABLE charging_stations ENABLE ROW LEVEL SECURITY;
ALTER TABLE charging_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE carbon_credit ENABLE ROW LEVEL SECURITY;
ALTER TABLE driving_behavior ENABLE ROW LEVEL SECURITY;
ALTER TABLE fleet_management ENABLE ROW LEVEL SECURITY;
ALTER TABLE fleet_vehicles ENABLE ROW LEVEL SECURITY;

-- 创建基础RLS策略
-- 用户只能访问自己的数据
CREATE POLICY "Users can view own profile" ON users FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON users FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can view own vehicles" ON vehicles FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can manage own vehicles" ON vehicles FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view own charging sessions" ON charging_sessions FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create charging sessions" ON charging_sessions FOR INSERT WITH CHECK (auth.uid() = user_id);

-- 充电站信息对所有认证用户可见
CREATE POLICY "Authenticated users can view charging stations" ON charging_stations FOR SELECT TO authenticated USING (true);

-- 授予权限
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO anon;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO anon;