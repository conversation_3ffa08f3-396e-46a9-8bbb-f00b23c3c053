<template>
  <div class="mobile-navbar" :class="{ 'navbar-hidden': isHidden }">
    <!-- 顶部状态栏 -->
    <div class="status-bar">
      <div class="status-left">
        <span class="time">{{ currentTime }}</span>
      </div>
      <div class="status-right">
        <div class="network-status" :class="networkStatus.type">
          <component :is="networkIcon" class="status-icon" />
        </div>
        <div class="battery-status" :class="{ 'low-battery': batteryLevel < 20 }">
          <Battery class="status-icon" />
          <span class="battery-level">{{ batteryLevel }}%</span>
        </div>
      </div>
    </div>

    <!-- 导航栏 -->
    <div class="navbar-content">
      <div class="navbar-left">
        <button 
          v-if="showBack" 
          class="nav-button back-button"
          @click="handleBack"
        >
          <ArrowLeft class="nav-icon" />
        </button>
        <button 
          v-if="showMenu" 
          class="nav-button menu-button"
          @click="toggleMenu"
        >
          <Menu class="nav-icon" />
        </button>
      </div>
      
      <div class="navbar-center">
        <h1 class="page-title">{{ title }}</h1>
      </div>
      
      <div class="navbar-right">
        <button 
          v-for="action in actions" 
          :key="action.key"
          class="nav-button action-button"
          @click="action.handler"
        >
          <component :is="action.icon" class="nav-icon" />
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  ArrowLeft, 
  Menu
} from '@element-plus/icons-vue'
import { Battery, Wifi, WifiOff, Signal } from 'lucide-vue-next'
import { getNetworkInfo } from '../../utils/mobile'

interface NavAction {
  key: string
  icon: any
  handler: () => void
}

interface Props {
  title?: string
  showBack?: boolean
  showMenu?: boolean
  actions?: NavAction[]
  autoHide?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  title: '新能源汽车管理系统',
  showBack: false,
  showMenu: true,
  actions: () => [],
  autoHide: false
})

const emit = defineEmits<{
  back: []
  menuToggle: []
}>()

const router = useRouter()
const currentTime = ref('')
const batteryLevel = ref(100)
const networkStatus = ref({ type: 'wifi', strength: 'high' })
const isHidden = ref(false)
const lastScrollY = ref(0)

// 更新时间
const updateTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 更新电池状态
const updateBatteryStatus = async () => {
  if ('getBattery' in navigator) {
    try {
      const battery = await (navigator as any).getBattery()
      batteryLevel.value = Math.round(battery.level * 100)
    } catch (error) {
      // 模拟电池电量
      batteryLevel.value = Math.floor(Math.random() * 100) + 1
    }
  }
}

// 更新网络状态
const updateNetworkStatus = () => {
  const info = getNetworkInfo()
  if (!info.online) {
    networkStatus.value = { type: 'offline', strength: 'none' }
  } else {
    const effectiveType = info.effectiveType
    if (effectiveType === 'wifi' || effectiveType === '4g') {
      networkStatus.value = { type: 'wifi', strength: 'high' }
    } else if (effectiveType === '3g') {
      networkStatus.value = { type: 'cellular', strength: 'medium' }
    } else {
      networkStatus.value = { type: 'cellular', strength: 'low' }
    }
  }
}

// 网络图标
const networkIcon = computed(() => {
  const { type, strength } = networkStatus.value
  if (type === 'offline') return WifiOff
  if (type === 'wifi') return Wifi
  
  // 统一使用Signal图标
  return Signal
})

// 处理返回
const handleBack = () => {
  emit('back')
  router.back()
}

// 切换菜单
const toggleMenu = () => {
  emit('menuToggle')
}

// 自动隐藏导航栏
const handleScroll = () => {
  if (!props.autoHide) return
  
  const currentScrollY = window.scrollY
  if (currentScrollY > lastScrollY.value && currentScrollY > 100) {
    isHidden.value = true
  } else {
    isHidden.value = false
  }
  lastScrollY.value = currentScrollY
}

let timeInterval: NodeJS.Timeout
let batteryInterval: NodeJS.Timeout

onMounted(() => {
  updateTime()
  updateBatteryStatus()
  updateNetworkStatus()
  
  // 定时更新
  timeInterval = setInterval(updateTime, 1000)
  batteryInterval = setInterval(updateBatteryStatus, 30000)
  
  // 监听网络状态变化
  window.addEventListener('online', updateNetworkStatus)
  window.addEventListener('offline', updateNetworkStatus)
  
  // 监听滚动事件
  if (props.autoHide) {
    window.addEventListener('scroll', handleScroll, { passive: true })
  }
})

onUnmounted(() => {
  clearInterval(timeInterval)
  clearInterval(batteryInterval)
  window.removeEventListener('online', updateNetworkStatus)
  window.removeEventListener('offline', updateNetworkStatus)
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped>
.mobile-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transition: transform 0.3s ease;
}

.navbar-hidden {
  transform: translateY(-100%);
}

.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 16px;
  font-size: 12px;
  background: rgba(0, 0, 0, 0.1);
  min-height: 20px;
}

.status-left {
  display: flex;
  align-items: center;
}

.status-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.network-status,
.battery-status {
  display: flex;
  align-items: center;
  gap: 2px;
}

.status-icon {
  width: 12px;
  height: 12px;
}

.battery-level {
  font-size: 10px;
}

.low-battery {
  color: #ff4757;
}

.network-status.offline {
  color: #ff4757;
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  min-height: 56px;
}

.navbar-left,
.navbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 60px;
}

.navbar-right {
  justify-content: flex-end;
}

.navbar-center {
  flex: 1;
  text-align: center;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.nav-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.nav-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

.nav-button:active {
  transform: scale(0.95);
}

.nav-icon {
  width: 20px;
  height: 20px;
}

/* 适配安全区域 */
@supports (padding-top: env(safe-area-inset-top)) {
  .mobile-navbar {
    padding-top: env(safe-area-inset-top);
  }
}

/* 暗色模式适配 */
@media (prefers-color-scheme: dark) {
  .mobile-navbar {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  }
}

/* 横屏适配 */
@media screen and (orientation: landscape) and (max-height: 500px) {
  .status-bar {
    display: none;
  }
  
  .navbar-content {
    padding: 8px 16px;
    min-height: 48px;
  }
  
  .page-title {
    font-size: 16px;
  }
  
  .nav-button {
    width: 36px;
    height: 36px;
  }
  
  .nav-icon {
    width: 18px;
    height: 18px;
  }
}
</style>