<template>
  <div class="test-navigation">
    <h1>导航测试页面</h1>
    
    <div class="test-buttons">
      <h2>测试按钮</h2>
      
      <el-button type="primary" @click="testAnalysis">
        测试跳转到健康度分析
      </el-button>
      
      <el-button type="warning" @click="testMaintenance">
        测试跳转到电池维护
      </el-button>
      
      <el-button type="info" @click="testMonitoring">
        测试跳转到实时监控
      </el-button>
    </div>
    
    <div class="test-info">
      <h2>测试信息</h2>
      <p>当前路由: {{ $route.path }}</p>
      <p>点击次数: {{ clickCount }}</p>
      <p>最后点击: {{ lastClick }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const clickCount = ref(0)
const lastClick = ref('')

const testAnalysis = () => {
  clickCount.value++
  lastClick.value = '健康度分析'
  console.log('测试跳转到健康度分析')
  router.push('/battery-management/analysis')
}

const testMaintenance = () => {
  clickCount.value++
  lastClick.value = '电池维护'
  console.log('测试跳转到电池维护')
  router.push('/battery-management/maintenance')
}

const testMonitoring = () => {
  clickCount.value++
  lastClick.value = '实时监控'
  console.log('测试跳转到实时监控')
  router.push('/battery-management/monitoring')
}
</script>

<style scoped>
.test-navigation {
  padding: 20px;
}

.test-buttons {
  margin: 20px 0;
}

.test-buttons .el-button {
  margin: 10px;
}

.test-info {
  margin: 20px 0;
  padding: 20px;
  background: #f5f5f5;
  border-radius: 8px;
}
</style>
