import { Router } from 'express'
import { db, TABLES } from '../config/mysql'
import type { ChargingSession, ChargingStation, Vehicle, User, ApiResponse, PaginatedResponse } from '../../shared/types'

const router = Router()

// 获取充电会话列表
router.get('/', async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      user_id, 
      vehicle_id, 
      station_id, 
      session_status,
      payment_status,
      start_date,
      end_date
    } = req.query
    const offset = (Number(page) - 1) * Number(limit)
    
    // 构建查询条件
    let whereClause = 'WHERE 1=1'
    let queryParams: any[] = []
    
    if (user_id) {
      whereClause += ' AND cs.user_id = ?'
      queryParams.push(user_id)
    }
    
    if (vehicle_id) {
      whereClause += ' AND cs.vehicle_id = ?'
      queryParams.push(vehicle_id)
    }
    
    if (station_id) {
      whereClause += ' AND cs.station_id = ?'
      queryParams.push(station_id)
    }
    
    if (session_status) {
      whereClause += ' AND cs.status = ?'
      queryParams.push(session_status)
    }
    
    if (payment_status) {
      whereClause += ' AND cs.payment_status = ?'
      queryParams.push(payment_status)
    }
    
    if (start_date) {
      whereClause += ' AND cs.start_time >= ?'
      queryParams.push(start_date)
    }
    
    if (end_date) {
      whereClause += ' AND cs.start_time <= ?'
      queryParams.push(end_date)
    }
    
    // 获取总数
    const countResult = await db.query(
      `SELECT COUNT(*) as total FROM charging_sessions cs ${whereClause}`,
      queryParams
    )
    
    const total = countResult[0]?.total || 0
    
    // 获取数据
    const sessionRows = await db.query(
      `SELECT cs.*, 
              u.username as user_name, u.email as user_email,
              v.make as vehicle_brand, v.model as vehicle_model, v.license_plate as vehicle_license_plate,
              st.name as station_name, st.address as station_address
        FROM charging_sessions cs
        LEFT JOIN users u ON cs.user_id = u.user_id
        LEFT JOIN vehicles v ON cs.vehicle_id = v.vehicle_id
        LEFT JOIN charging_stations st ON cs.station_id = st.station_id
       ${whereClause}
       ORDER BY cs.start_time DESC
       LIMIT ? OFFSET ?`,
      [...queryParams, Number(limit), offset]
    )
    
    // 格式化数据
    const data = (sessionRows as any[]).map(row => ({
      ...row,
      users: row.user_name ? {
        name: row.user_name,
        email: row.user_email
      } : null,
      vehicles: row.vehicle_brand ? {
        brand: row.vehicle_brand,
        model: row.vehicle_model,
        license_plate: row.vehicle_license_plate
      } : null,
      charging_stations: row.station_name ? {
        name: row.station_name,
        address: row.station_address
      } : null
    }))
    
    const response: PaginatedResponse<ChargingSession> = {
      success: true,
      data: data,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: total,
        totalPages: Math.ceil(total / Number(limit))
      }
    }
    
    res.json(response)
  } catch (error) {
    console.error('获取充电会话列表异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取最近充电记录
router.get('/recent', async (req, res) => {
  try {
    const { limit = 10, user_id } = req.query
    
    // 构建查询条件
    let whereClause = 'WHERE 1=1'
    const queryParams = []
    
    if (user_id) {
      whereClause += ' AND cs.user_id = ?'
      queryParams.push(user_id)
    }
    
    // 获取数据
    const sessionRows = await db.query(
      `SELECT cs.*, 
              u.username as user_name, u.email as user_email,
              v.make as vehicle_brand, v.model as vehicle_model, v.license_plate as vehicle_license_plate,
              st.name as station_name, st.address as station_address
        FROM charging_sessions cs
        LEFT JOIN users u ON cs.user_id = u.user_id
        LEFT JOIN vehicles v ON cs.vehicle_id = v.vehicle_id
        LEFT JOIN charging_stations st ON cs.station_id = st.station_id
       ${whereClause}
       ORDER BY cs.start_time DESC
       LIMIT ?`,
      [...queryParams, Number(limit)]
    )
    
    const sessions = sessionRows || []
    
    // 格式化数据
    const formattedData = sessions.map(session => ({
      id: session.session_id,
      stationName: session.station_name || '未知充电站',
      startTime: session.start_time,
      endTime: session.end_time,
      duration: session.duration || 0,
      energy: session.energy_delivered_kwh || 0,
      cost: session.total_cost || 0,
      status: session.status,
      vehicleInfo: session.vehicle_brand ? `${session.vehicle_brand} ${session.vehicle_model} (${session.vehicle_license_plate})` : '未知车辆',
      userName: session.user_name || '未知用户'
    }))
    
    res.json({
      success: true,
      data: formattedData
    } as ApiResponse)
  } catch (error) {
    console.error('获取最近充电记录异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取充电功率趋势数据
router.get('/power-trend', async (req, res) => {
  try {
    const { timeRange = '24h' } = req.query
    
    let timeCondition = ''
    switch (timeRange) {
      case '1h':
        timeCondition = "start_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)"
        break
      case '6h':
        timeCondition = "start_time >= DATE_SUB(NOW(), INTERVAL 6 HOUR)"
        break
      case '24h':
      default:
        timeCondition = "start_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)"
        break
      case '7d':
        timeCondition = "start_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)"
        break
    }

    const sessionResult = await db.query(
      `SELECT start_time, end_time, energy_delivered_kwh 
       FROM charging_sessions 
       WHERE status = 'completed' AND ${timeCondition}
       ORDER BY start_time ASC`
    )
    const data = sessionResult || []

    // 按小时分组计算平均功率
    const hourlyData = data?.reduce((acc: any, session: any) => {
      const hour = new Date(session.start_time).getHours()
      const durationHours = session.end_time ? 
        (new Date(session.end_time).getTime() - new Date(session.start_time).getTime()) / (1000 * 60 * 60) : 0
      const power = durationHours > 0 ? (session.energy_delivered_kwh / durationHours) : 0
      
      if (!acc[hour]) {
        acc[hour] = { totalPower: 0, count: 0 }
      }
      acc[hour].totalPower += power
      acc[hour].count += 1
      
      return acc
    }, {}) || {}

    // 转换为图表数据格式
    const chartData = Array.from({ length: 24 }, (_, hour) => {
      const data = hourlyData[hour]
      return {
        time: `${hour.toString().padStart(2, '0')}:00`,
        power: data ? Number((data.totalPower / data.count).toFixed(2)) : 0
      }
    })

    res.json({
      success: true,
      data: chartData
    } as ApiResponse)
  } catch (error) {
    console.error('获取充电功率趋势异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取单个充电会话信息
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params
    
    const sessionRows = await db.query(
      `SELECT cs.*,
              u.username as user_name, u.email as user_email,
              v.make as vehicle_brand, v.model as vehicle_model, v.license_plate as vehicle_license_plate,
              st.name as station_name, st.address as station_address
        FROM charging_sessions cs
        LEFT JOIN users u ON cs.user_id = u.user_id
        LEFT JOIN vehicles v ON cs.vehicle_id = v.vehicle_id
        LEFT JOIN charging_stations st ON cs.station_id = st.station_id
       WHERE cs.session_id = ?`,
      [id]
    )
    
    const sessions = sessionRows || []
    
    if (sessions.length === 0) {
      return res.status(404).json({
        success: false,
        error: '充电会话不存在'
      } as ApiResponse)
    }
    
    const session = sessions[0]
    
    // 格式化数据
    const data = {
      ...session,
      users: session.user_user_id ? {
        id: session.user_user_id,
        name: session.user_name,
        email: session.user_email,
        phone: session.user_phone
      } : null,
      vehicles: session.vehicle_vehicle_id ? {
        id: session.vehicle_vehicle_id,
        brand: session.vehicle_brand,
        model: session.vehicle_model,
        license_plate: session.vehicle_license_plate,
        vin: session.vehicle_vin
      } : null,
      charging_stations: session.station_station_id ? {
        id: session.station_station_id,
        name: session.station_name,
        address: session.station_address,
        operator: session.station_operator
      } : null
    }
    
    res.json({
      success: true,
      data
    } as ApiResponse<ChargingSession>)
  } catch (error) {
    console.error('获取充电会话信息异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 创建充电会话（开始充电）
router.post('/', async (req, res) => {
  try {
    const {
      user_id,
      vehicle_id,
      station_id,
      start_soc,
      payment_method
    } = req.body
    
    // 验证必填字段
    if (!user_id || !vehicle_id || !station_id) {
      return res.status(400).json({
        success: false,
        error: '用户ID、车辆ID和充电站ID为必填字段'
      } as ApiResponse)
    }
    
    // 验证用户、车辆和充电站是否存在
    const userResult = await db.query(
      'SELECT user_id FROM users WHERE user_id = ?',
      [user_id]
    )
    const users = userResult || []
    
    if (users.length === 0) {
      return res.status(400).json({
        success: false,
        error: '用户不存在'
      } as ApiResponse)
    }
    
    const vehicleResult = await db.query(
      'SELECT vehicle_id, owner_id FROM vehicles WHERE vehicle_id = ?',
      [vehicle_id]
    )
    const vehicles = vehicleResult || []
    
    if (vehicles.length === 0) {
      return res.status(400).json({
        success: false,
        error: '车辆不存在'
      } as ApiResponse)
    }
    
    if (vehicles[0].owner_id !== user_id) {
      return res.status(400).json({
        success: false,
        error: '车辆不属于该用户'
      } as ApiResponse)
    }
    
    const stationResult = await db.query(
      'SELECT station_id, available_ports FROM charging_stations WHERE station_id = ?',
      [station_id]
    )
    const stations = stationResult || []
    
    if (stations.length === 0) {
      return res.status(400).json({
        success: false,
        error: '充电站不存在'
      } as ApiResponse)
    }
    
    if (stations[0].available_ports <= 0) {
      return res.status(400).json({
        success: false,
        error: '充电站暂无可用端口'
      } as ApiResponse)
    }
    
    // 检查是否有正在进行的充电会话
    const activeSessionResult = await db.query(
      'SELECT session_id FROM charging_sessions WHERE vehicle_id = ? AND status = "active"',
      [vehicle_id]
    )
    const activeSessions = activeSessionResult || []
    
    if (activeSessions.length > 0) {
      return res.status(400).json({
        success: false,
        error: '该车辆已有正在进行的充电会话'
      } as ApiResponse)
    }
    
    // 生成会话编码
    const session_code = `CS${Date.now()}${Math.random().toString(36).substr(2, 4).toUpperCase()}`
    
    const sessionData = {
      user_id,
      vehicle_id,
      station_id,
      session_code,
      start_time: new Date().toISOString(),
      start_soc,
      payment_method,
      payment_status: 'pending',
      status: 'active'
    }
    
    // 开始事务：创建充电会话并更新充电站可用端口数
    const insertResult = await db.query(
      `INSERT INTO charging_sessions (
        user_id, vehicle_id, station_id, session_code, start_time, 
        start_soc, payment_method, payment_status, status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        user_id, vehicle_id, station_id, session_code, new Date().toISOString(),
        start_soc, payment_method, 'pending', 'active'
      ]
    )
    
    const insertId = insertResult.insertId
    
    // 获取创建的充电会话数据
    const sessionResult = await db.query(
      'SELECT * FROM charging_sessions WHERE session_id = ?',
      [insertId]
    )
    const data = sessionResult[0]
    
    // 更新充电站可用端口数
    await db.query(
      'UPDATE charging_stations SET available_ports = available_ports - 1, updated_at = ? WHERE station_id = ?',
      [new Date().toISOString(), station_id]
    )
    
    res.status(201).json({
      success: true,
      data,
      message: '充电会话创建成功'
    } as ApiResponse<ChargingSession>)
  } catch (error) {
    console.error('创建充电会话异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 结束充电会话
router.patch('/:id/complete', async (req, res) => {
  try {
    const { id } = req.params
    const {
      end_soc,
      energy_delivered_kwh,
      peak_power,
      total_cost
    } = req.body
    
    // 获取当前充电会话信息
    const sessionResult = await db.query(
      `SELECT cs.*, st.available_ports as station_available_ports
       FROM charging_sessions cs
       LEFT JOIN charging_stations st ON cs.station_id = st.station_id
       WHERE cs.session_id = ?`,
      [id]
    )
    const sessions = sessionResult || []
    
    if (sessions.length === 0) {
      return res.status(404).json({
        success: false,
        error: '充电会话不存在'
      } as ApiResponse)
    }
    
    const session = sessions[0]
    
    if (session.status !== 'active') {
      return res.status(400).json({
        success: false,
        error: '充电会话已结束'
      } as ApiResponse)
    }
    
    // 更新充电会话
    await db.query(
      `UPDATE charging_sessions SET 
        end_time = ?, end_soc = ?, energy_delivered_kwh = ?, peak_power = ?, 
        total_cost = ?, status = 'completed', 
        payment_status = ?, updated_at = ?
       WHERE session_id = ?`,
      [
        new Date().toISOString(), end_soc, energy_delivered_kwh, peak_power,
        total_cost, total_cost > 0 ? 'pending' : 'completed', 
        new Date().toISOString(), id
      ]
    )
    
    // 获取更新后的充电会话数据
    const updatedSessionResult = await db.query(
      'SELECT * FROM charging_sessions WHERE session_id = ?',
      [id]
    )
    const data = (updatedSessionResult || [])[0]
    
    // 更新充电站可用端口数
    if (session.station_id) {
      await db.query(
        'UPDATE charging_stations SET available_ports = available_ports + 1, updated_at = ? WHERE station_id = ?',
        [new Date().toISOString(), session.station_id]
      )
    }
    
    res.json({
      success: true,
      data,
      message: '充电会话结束成功'
    } as ApiResponse<ChargingSession>)
  } catch (error) {
    console.error('结束充电会话异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 中断充电会话
router.patch('/:id/interrupt', async (req, res) => {
  try {
    const { id } = req.params
    const { reason } = req.body
    
    // 获取当前充电会话信息
    const sessionResult = await db.query(
      `SELECT cs.*, st.available_ports as station_available_ports
       FROM charging_sessions cs
       LEFT JOIN charging_stations st ON cs.station_id = st.station_id
       WHERE cs.session_id = ?`,
      [id]
    )
    const sessions = sessionResult || []
    
    if (sessions.length === 0) {
      return res.status(404).json({
        success: false,
        error: '充电会话不存在'
      } as ApiResponse)
    }
    
    const session = sessions[0]
    
    if (session.status !== 'active') {
      return res.status(400).json({
        success: false,
        error: '充电会话已结束'
      } as ApiResponse)
    }
    
    // 更新充电会话
    await db.query(
      `UPDATE charging_sessions SET 
        end_time = ?, status = 'interrupted', updated_at = ?
       WHERE session_id = ?`,
      [new Date().toISOString(), new Date().toISOString(), id]
    )
    
    // 获取更新后的充电会话数据
    const updatedSessionResult = await db.query(
      'SELECT * FROM charging_sessions WHERE session_id = ?',
      [id]
    )
    const data = (updatedSessionResult || [])[0]
    
    // 更新充电站可用端口数
    if (session.station_id) {
      await db.query(
        'UPDATE charging_stations SET available_ports = available_ports + 1, updated_at = ? WHERE station_id = ?',
        [new Date().toISOString(), session.station_id]
      )
    }
    
    res.json({
      success: true,
      data,
      message: '充电会话中断成功'
    } as ApiResponse<ChargingSession>)
  } catch (error) {
    console.error('中断充电会话异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 更新支付状态
router.patch('/:id/payment', async (req, res) => {
  try {
    const { id } = req.params
    const { payment_status, payment_method } = req.body
    
    if (!payment_status) {
      return res.status(400).json({
        success: false,
        error: '支付状态为必填字段'
      } as ApiResponse)
    }
    
    // 更新支付状态
    let updateQuery = 'UPDATE charging_sessions SET payment_status = ?, updated_at = ?'
    let queryParams = [payment_status, new Date().toISOString()]
    
    if (payment_method) {
      updateQuery += ', payment_method = ?'
      queryParams.push(payment_method)
    }
    
    updateQuery += ' WHERE session_id = ?'
    queryParams.push(id)
    
    await db.query(updateQuery, queryParams)
    
    // 获取更新后的充电会话数据
    const updatedSessionResult = await db.query(
      'SELECT * FROM charging_sessions WHERE session_id = ?',
      [id]
    )
    const sessions = updatedSessionResult || []
    
    if (sessions.length === 0) {
      return res.status(404).json({
        success: false,
        error: '充电会话不存在'
      } as ApiResponse)
    }
    
    const data = sessions[0]
    
    res.json({
      success: true,
      data,
      message: '支付状态更新成功'
    } as ApiResponse<ChargingSession>)
  } catch (error) {
    console.error('更新支付状态异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取充电会话统计
router.get('/statistics/summary', async (req, res) => {
  try {
    const { start_date, end_date, user_id } = req.query
    
    // 构建查询条件
    let whereConditions = []
    let queryParams = []
    
    if (user_id) {
      whereConditions.push('user_id = ?')
      queryParams.push(user_id)
    }
    
    if (start_date) {
      whereConditions.push('start_time >= ?')
      queryParams.push(start_date)
    }
    
    if (end_date) {
      whereConditions.push('start_time <= ?')
      queryParams.push(end_date)
    }
    
    const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : ''
    
    const sessionResult = await db.query(
      `SELECT * FROM charging_sessions ${whereClause}`,
      queryParams
    )
    const sessions = sessionResult || []
    const totalSessions = sessions.length
    const completedSessions = sessions.filter(s => s.status === 'completed').length
    const totalEnergyDelivered = sessions.reduce((sum, s) => sum + (s.energy_delivered_kwh || 0), 0)
    const totalCost = sessions.reduce((sum, s) => sum + (s.total_cost || 0), 0)
    const avgEnergyPerSession = totalSessions > 0 ? totalEnergyDelivered / totalSessions : 0
    
    const statistics = {
      totalSessions,
      completedSessions,
      interruptedSessions: sessions.filter(s => s.status === 'interrupted').length,
      activeSessions: sessions.filter(s => s.status === 'active').length,
      totalEnergyDelivered: totalEnergyDelivered.toFixed(2),
      totalCost: totalCost.toFixed(2),
      avgEnergyPerSession: avgEnergyPerSession.toFixed(2),
      successRate: totalSessions > 0 ? (completedSessions / totalSessions * 100).toFixed(2) : '0'
    }
    
    res.json({
      success: true,
      data: statistics
    } as ApiResponse)
  } catch (error) {
    console.error('获取充电统计异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取最近充电记录
export default router