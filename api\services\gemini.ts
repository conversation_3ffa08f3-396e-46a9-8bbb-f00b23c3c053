import { GoogleGenerativeAI } from '@google/generative-ai';
import fs from 'fs';
import { db } from '../config/mysql';

export class GeminiService {
  private genAI: GoogleGenerativeAI;
  private model: any;

  constructor() {
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      console.warn('⚠️ Gemini API密钥未配置，将使用本地模式');
      return;
    }
    
    try {
      this.genAI = new GoogleGenerativeAI(apiKey);
      this.model = this.genAI.getGenerativeModel({ model: 'gemini-pro' });
      console.log('✅ Gemini AI服务初始化成功');
    } catch (error) {
      console.error('❌ Gemini AI服务初始化失败:', error);
    }
  }

  // 检查服务是否可用
  isAvailable(): boolean {
    return !!(this.genAI && this.model);
  }

  // 获取AI助手配置
  async getAIConfig() {
    try {
      const result = await db.query(
        'SELECT * FROM ai_assistant_config WHERE is_active = TRUE ORDER BY id DESC LIMIT 1'
      );
      
      const [rows] = result as any;
      
      if (Array.isArray(rows) && rows.length > 0) {
        const config = rows[0];
        return {
          name: config.name,
          fullName: config.full_name,
          description: config.description,
          avatar: config.avatar_url,
          model: 'gemini-pro',
          temperature: config.temperature,
          maxTokens: config.max_tokens,
          capabilities: config.capabilities ? (
            typeof config.capabilities === 'string' ? 
            JSON.parse(config.capabilities) : 
            config.capabilities
          ) : [],
          systemPrompt: config.system_prompt
        };
      }
      
      // 返回默认配置
      return {
        name: '小E',
        fullName: 'EVAdmin Pro AI Assistant (Gemini)',
        description: '我是EVAdmin Pro的专属AI助手，基于Google Gemini提供智能服务',
        avatar: '/images/ai-avatar.svg',
        model: 'gemini-pro',
        temperature: 0.7,
        maxTokens: 2000,
        capabilities: [
          '智能文本对话',
          '文件内容分析',
          '图片识别分析',
          '系统功能指导',
          '数据分析建议',
          '故障诊断帮助'
        ],
        systemPrompt: '你是EVAdmin Pro新能源汽车管理系统的AI助手'
      };
    } catch (error) {
      console.error('获取AI配置失败:', error);
      return {
        name: '小E',
        fullName: 'EVAdmin Pro AI Assistant',
        description: '我是EVAdmin Pro的专属AI助手',
        avatar: '/images/ai-avatar.svg',
        model: 'gemini-pro',
        temperature: 0.7,
        maxTokens: 2000,
        capabilities: [],
        systemPrompt: '你是EVAdmin Pro的AI助手'
      };
    }
  }

  // 搜索知识库
  async searchKnowledge(query: string): Promise<string> {
    try {
      // 先尝试精确匹配
      let result = await db.query(`
        SELECT question, answer, category 
        FROM ai_knowledge_base 
        WHERE is_active = TRUE 
        AND (
          question LIKE CONCAT('%', ?, '%')
          OR answer LIKE CONCAT('%', ?, '%')
          OR keywords LIKE CONCAT('%', ?, '%')
        )
        ORDER BY priority DESC, id DESC
        LIMIT 3
      `, [query, query, query]);
      
      // 如果没有结果，尝试拆分查询词进行搜索
      if ((!result || (Array.isArray(result) && result.length === 0))) {
        const keywords = query.split(/[？?，,。.\s]+/).filter(word => word.length > 1);
        
        if (keywords.length > 0) {
          const keywordConditions = keywords.map(() => 
            '(question LIKE CONCAT(\'%\', ?, \'%\') OR answer LIKE CONCAT(\'%\', ?, \'%\') OR keywords LIKE CONCAT(\'%\', ?, \'%\'))'
          ).join(' OR ');
          
          const keywordParams = keywords.flatMap(keyword => [keyword, keyword, keyword]);
          
          result = await db.query(`
            SELECT question, answer, category 
            FROM ai_knowledge_base 
            WHERE is_active = TRUE 
            AND (${keywordConditions})
            ORDER BY priority DESC, id DESC
            LIMIT 3
          `, keywordParams);
        }
      }

      // 处理查询结果
      let rows;
      if (Array.isArray(result) && result.length >= 2) {
        rows = result[0]; // [rows, fields]格式
      } else if (Array.isArray(result)) {
        rows = result; // 直接是rows数组
      } else {
        rows = [];
      }
      
      if (Array.isArray(rows) && rows.length > 0) {
        return rows.map((row: any) => 
          `【${row.category}】${row.question}: ${row.answer}`
        ).join('\n\n');
      }
      
      return '';
    } catch (error) {
      console.error('搜索知识库失败:', error);
      return '';
    }
  }

  // 文本对话
  async chat(message: string, context: any[] = []): Promise<string> {
    if (!this.isAvailable()) {
      throw new Error('Gemini服务不可用');
    }

    try {
      // 搜索相关知识库内容
      const knowledgeResult = await this.searchKnowledge(message);
      
      // 构建系统提示词
      const config = await this.getAIConfig();
      let systemPrompt = `${config.systemPrompt}

你是EVAdmin Pro新能源汽车管理系统的专属AI助手，名字叫"${config.name}"。

系统功能模块包括：
1. 智能电池管理 - 电池监控、健康度分析、充电优化
2. 智能充电服务 - 充电站管理、预约充电、费用管理
3. 车辆管理 - 车辆档案、状态监控、维护记录
4. ADAS系统 - 高级驾驶辅助功能配置和使用
5. 车联网交互 - 远程控制和诊断
6. 用户生态服务 - 个性化服务和社区功能

请用专业、友好的语气回答用户问题。如果问题涉及系统功能，请提供具体的操作指导。`;

      // 如果有知识库内容，添加到提示词中
      if (knowledgeResult) {
        systemPrompt += `\n\n相关系统知识：\n${knowledgeResult}`;
      }

      // 构建对话历史
      let conversationHistory = '';
      if (context && context.length > 0) {
        conversationHistory = context.map(msg => 
          `${msg.type === 'user' ? '用户' : 'AI助手'}: ${msg.content}`
        ).join('\n');
        conversationHistory += '\n\n';
      }

      // 构建完整的提示词
      const fullPrompt = `${systemPrompt}

${conversationHistory}用户: ${message}

AI助手:`;

      // 调用Gemini API
      const result = await this.model.generateContent(fullPrompt);
      const response = await result.response;
      const text = response.text();

      return text || '抱歉，我暂时无法回答您的问题。';
      
    } catch (error) {
      console.error('Gemini聊天错误:', error);
      throw error;
    }
  }

  // 分析文件
  async analyzeFile(filePath: string, fileName: string, mimeType: string, question: string): Promise<string> {
    if (!this.isAvailable()) {
      throw new Error('Gemini服务不可用');
    }

    try {
      let fileContent = '';
      
      // 根据文件类型读取内容
      if (mimeType === 'application/pdf') {
        return '抱歉，PDF文件分析功能正在开发中，请上传文本文件或图片文件。';
      } else if (mimeType.startsWith('text/') || mimeType === 'application/json') {
        // 文本文件处理
        fileContent = fs.readFileSync(filePath, 'utf-8');
        
        // 构建分析提示词
        const prompt = `请分析以下文件内容：

文件名：${fileName}
文件类型：${mimeType}
用户问题：${question}

文件内容：
${fileContent}

请根据用户问题对文件内容进行专业分析，如果内容与新能源汽车管理系统相关，请结合系统功能进行解读。`;

        const result = await this.model.generateContent(prompt);
        const response = await result.response;
        return response.text() || '文件分析完成，但未能生成分析结果。';
        
      } else if (mimeType.startsWith('image/')) {
        // 图片文件处理
        const imageBuffer = fs.readFileSync(filePath);
        const imageBase64 = imageBuffer.toString('base64');
        
        // 使用Gemini Pro Vision模型
        const visionModel = this.genAI.getGenerativeModel({ model: 'gemini-pro-vision' });
        
        const imagePart = {
          inlineData: {
            data: imageBase64,
            mimeType: mimeType
          }
        };
        
        const prompt = `请分析这张图片：

用户问题：${question}

请详细描述图片内容，如果图片与新能源汽车、充电设备、车辆管理等相关，请结合专业知识进行分析。`;

        const result = await visionModel.generateContent([prompt, imagePart]);
        const response = await result.response;
        return response.text() || '图片分析完成，但未能生成分析结果。';
        
      } else {
        return `抱歉，暂时不支持分析 ${mimeType} 类型的文件。目前支持的文件类型包括：文本文件(.txt)、JSON文件(.json)、图片文件(.jpg, .png, .gif, .webp)。`;
      }
      
    } catch (error) {
      console.error('Gemini文件分析失败:', error);
      throw error;
    }
  }

  // 检查服务状态
  async checkStatus(): Promise<boolean> {
    if (!this.isAvailable()) {
      return false;
    }
    
    try {
      // 发送简单的测试请求
      const result = await this.model.generateContent('测试连接');
      return !!(await result.response);
    } catch (error) {
      console.error('Gemini状态检查失败:', error);
      return false;
    }
  }
}

export const geminiService = new GeminiService();
