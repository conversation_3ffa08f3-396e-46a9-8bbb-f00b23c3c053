const mysql = require('mysql2/promise');
const bcrypt = require('bcrypt');

async function createTestUsers() {
  let connection;
  
  try {
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '123456',
      database: 'ev_management',
      multipleStatements: true
    });

    console.log('✅ 数据库连接成功');

    // 生成密码哈希
    const passwordHash = await bcrypt.hash('123456', 10);
    console.log('🔐 密码哈希生成成功');

    // 创建测试用户
    const testUsers = [
      {
        user_id: 'test_admin_001',
        username: '测试管理员',
        email: '<EMAIL>',
        password_hash: passwordHash,
        phone: '13800138000',
        role: 'admin',
        status: 'active'
      },
      {
        user_id: 'test_user_001',
        username: '测试用户',
        email: '<EMAIL>',
        password_hash: passwordHash,
        phone: '13800138001',
        role: 'user',
        status: 'active'
      }
    ];

    console.log('🔧 创建测试用户...');

    for (const user of testUsers) {
      try {
        await connection.execute(`
          INSERT INTO users (user_id, username, email, password_hash, phone, role, status)
          VALUES (?, ?, ?, ?, ?, ?, ?)
          ON DUPLICATE KEY UPDATE
          username = VALUES(username),
          password_hash = VALUES(password_hash),
          phone = VALUES(phone),
          role = VALUES(role),
          status = VALUES(status)
        `, [
          user.user_id, user.username, user.email, user.password_hash,
          user.phone, user.role, user.status
        ]);
        console.log(`✅ 创建/更新测试用户: ${user.username} (${user.email})`);
      } catch (error) {
        console.log(`⚠️  创建测试用户失败: ${user.username}`, error.message);
      }
    }

    // 验证用户创建
    console.log('🔍 验证用户创建...');
    const [users] = await connection.execute('SELECT user_id, username, email, role, status FROM users WHERE email IN (?, ?)', ['<EMAIL>', '<EMAIL>']);
    
    console.log('创建的测试用户:');
    users.forEach(user => {
      console.log(`  - ${user.username} (${user.email}) - ${user.role} - ${user.status}`);
    });

    console.log('');
    console.log('🎉 测试用户创建完成！');
    console.log('');
    console.log('现在可以使用以下账户登录:');
    console.log('管理员账户: <EMAIL> / 123456');
    console.log('普通用户: <EMAIL> / 123456');

  } catch (error) {
    console.error('❌ 创建测试用户失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 执行创建
createTestUsers();