<template>
  <div class="adas-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">
        <el-icon><Compass /></el-icon>
        高级驾驶辅助系统
      </h1>
      <p class="page-subtitle">环境感知 · 安全预警 · 自动泊车 · 行为分析</p>
    </div>

    <!-- ADAS状态概览 -->
    <div class="adas-overview">
      <ModernCard class="overview-card" hover-effect>
        <div class="overview-content">
          <div class="overview-icon safety">
            <el-icon><Shield /></el-icon>
          </div>
          <div class="overview-info">
            <h3>{{ adasOverview.safetyScore }}</h3>
            <p>安全评分</p>
            <span class="overview-rate">{{ adasOverview.safetyTrend }}% 较上月</span>
          </div>
        </div>
      </ModernCard>

      <ModernCard class="overview-card" hover-effect>
        <div class="overview-content">
          <div class="overview-icon distance">
            <el-icon><Odometer /></el-icon>
          </div>
          <div class="overview-info">
            <h3>{{ adasOverview.totalDistance }}</h3>
            <p>总行驶里程(km)</p>
            <span class="overview-rate">{{ adasOverview.monthlyDistance }}km 本月</span>
          </div>
        </div>
      </ModernCard>

      <ModernCard class="overview-card" hover-effect>
        <div class="overview-content">
          <div class="overview-icon alerts">
            <el-icon><Warning /></el-icon>
          </div>
          <div class="overview-info">
            <h3>{{ adasOverview.alertsCount }}</h3>
            <p>安全预警次数</p>
            <span class="overview-rate">{{ adasOverview.alertsReduction }}% 减少</span>
          </div>
        </div>
      </ModernCard>

      <ModernCard class="overview-card" hover-effect>
        <div class="overview-content">
          <div class="overview-icon efficiency">
            <el-icon><TrendCharts /></el-icon>
          </div>
          <div class="overview-info">
            <h3>{{ adasOverview.fuelEfficiency }}</h3>
            <p>能耗效率(kWh/100km)</p>
            <span class="overview-rate">{{ adasOverview.efficiencyImprovement }}% 优化</span>
          </div>
        </div>
      </ModernCard>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧：实时监控和驾驶分析 -->
      <div class="monitoring-section">
        <!-- 实时驾驶状态 -->
        <ModernCard class="realtime-card" hover-effect>
          <template #header>
            <div class="card-header">
              <span>实时驾驶状态</span>
              <el-tag :type="getStatusType(realtimeStatus.status)" size="small">
                {{ realtimeStatus.status }}
              </el-tag>
            </div>
          </template>
          <div class="realtime-content">
            <div class="status-grid">
              <div class="status-item">
                <div class="status-icon speed">
                  <el-icon><Gauge /></el-icon>
                </div>
                <div class="status-info">
                  <h4>{{ realtimeStatus.speed }}</h4>
                  <p>当前车速(km/h)</p>
                </div>
              </div>
              
              <div class="status-item">
                <div class="status-icon distance-sensor">
                  <el-icon><Radar /></el-icon>
                </div>
                <div class="status-info">
                  <h4>{{ realtimeStatus.frontDistance }}</h4>
                  <p>前车距离(m)</p>
                </div>
              </div>
              
              <div class="status-item">
                <div class="status-icon lane">
                  <el-icon><Guide /></el-icon>
                </div>
                <div class="status-info">
                  <h4>{{ realtimeStatus.lanePosition }}</h4>
                  <p>车道偏移(cm)</p>
                </div>
              </div>
              
              <div class="status-item">
                <div class="status-icon attention">
                  <el-icon><View /></el-icon>
                </div>
                <div class="status-info">
                  <h4>{{ realtimeStatus.attentionLevel }}</h4>
                  <p>注意力水平</p>
                </div>
              </div>
            </div>
            
            <!-- 环境感知 -->
            <div class="environment-perception">
              <h5>环境感知</h5>
              <div class="perception-grid">
                <div class="perception-item">
                  <span class="perception-label">天气条件:</span>
                  <span class="perception-value">{{ environmentData.weather }}</span>
                </div>
                <div class="perception-item">
                  <span class="perception-label">路面状况:</span>
                  <span class="perception-value">{{ environmentData.roadCondition }}</span>
                </div>
                <div class="perception-item">
                  <span class="perception-label">能见度:</span>
                  <span class="perception-value">{{ environmentData.visibility }}m</span>
                </div>
                <div class="perception-item">
                  <span class="perception-label">交通密度:</span>
                  <span class="perception-value">{{ environmentData.trafficDensity }}</span>
                </div>
              </div>
            </div>
          </div>
        </ModernCard>

        <!-- 驾驶行为分析 -->
        <ModernCard class="behavior-analysis-card" hover-effect>
          <template #header>
            <div class="card-header">
              <span>驾驶行为分析</span>
              <el-select v-model="analysisTimeRange" size="small" style="width: 120px">
                <el-option label="今日" value="today" />
                <el-option label="本周" value="week" />
                <el-option label="本月" value="month" />
              </el-select>
            </div>
          </template>
          <div ref="behaviorChartRef" class="chart-container"></div>
        </ModernCard>

        <!-- 安全预警历史 -->
        <ModernCard class="alerts-history-card" hover-effect>
          <template #header>
            <div class="card-header">
              <span>安全预警历史</span>
              <ModernButton variant="ghost" @click="viewAllAlerts">
                查看全部
              </ModernButton>
            </div>
          </template>
          <div class="alerts-list">
            <div v-for="alert in recentAlerts" :key="alert.id" class="alert-item">
              <div class="alert-icon" :class="alert.level">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="alert-info">
                <h5>{{ alert.title }}</h5>
                <p class="alert-time">{{ formatDateTime(alert.timestamp) }}</p>
                <p class="alert-description">{{ alert.description }}</p>
              </div>
              <div class="alert-status">
                <el-tag :type="getAlertLevelType(alert.level)" size="small">
                  {{ getAlertLevelText(alert.level) }}
                </el-tag>
              </div>
            </div>
          </div>
        </ModernCard>
      </div>

      <!-- 右侧：路线优化和功能控制 -->
      <div class="control-section">
        <!-- 路线优化 -->
        <ModernCard class="route-optimization-card" hover-effect>
          <template #header>
            <div class="card-header">
              <span>智能路线优化</span>
              <ModernButton variant="primary" size="small" @click="optimizeRoute">
                重新规划
              </ModernButton>
            </div>
          </template>
          <div class="route-content">
            <div class="route-input">
              <el-input
                v-model="routeForm.destination"
                placeholder="输入目的地"
                clearable
              >
                <template #prefix>
                  <el-icon><Location /></el-icon>
                </template>
              </el-input>
            </div>
            
            <div class="route-options">
              <el-radio-group v-model="routeForm.preference">
                <el-radio label="fastest">最快路线</el-radio>
                <el-radio label="efficient">最省电</el-radio>
                <el-radio label="scenic">风景路线</el-radio>
              </el-radio-group>
            </div>
            
            <div class="route-info">
              <div class="route-detail">
                <span class="route-label">预计时间:</span>
                <span class="route-value">{{ currentRoute.estimatedTime }}</span>
              </div>
              <div class="route-detail">
                <span class="route-label">行驶距离:</span>
                <span class="route-value">{{ currentRoute.distance }}km</span>
              </div>
              <div class="route-detail">
                <span class="route-label">能耗预估:</span>
                <span class="route-value">{{ currentRoute.energyConsumption }}kWh</span>
              </div>
              <div class="route-detail">
                <span class="route-label">充电建议:</span>
                <span class="route-value">{{ currentRoute.chargingAdvice }}</span>
              </div>
            </div>
            
            <!-- 路线地图 -->
            <div class="route-map">
              <MapComponent
                :show-route="true"
                :route-data="currentRoute"
                :charging-stations="nearbyStations"
                style="height: 300px; width: 100%;"
              />
            </div>
          </div>
        </ModernCard>

        <!-- ADAS功能控制 -->
        <ModernCard class="adas-controls-card" hover-effect>
          <template #header>
            <span>ADAS功能控制</span>
          </template>
          <div class="controls-grid">
            <div class="control-item">
              <div class="control-header">
                <span>自适应巡航</span>
                <el-switch v-model="adasControls.adaptiveCruise" />
              </div>
              <p class="control-description">自动调节车速，保持安全跟车距离</p>
            </div>
            
            <div class="control-item">
              <div class="control-header">
                <span>车道保持辅助</span>
                <el-switch v-model="adasControls.laneKeeping" />
              </div>
              <p class="control-description">检测车道偏离并自动修正方向</p>
            </div>
            
            <div class="control-item">
              <div class="control-header">
                <span>前碰撞预警</span>
                <el-switch v-model="adasControls.collisionWarning" />
              </div>
              <p class="control-description">监测前方障碍物并及时预警</p>
            </div>
            
            <div class="control-item">
              <div class="control-header">
                <span>盲点监测</span>
                <el-switch v-model="adasControls.blindSpotMonitoring" />
              </div>
              <p class="control-description">监测侧后方盲区车辆</p>
            </div>
            
            <div class="control-item">
              <div class="control-header">
                <span>自动泊车</span>
                <el-switch v-model="adasControls.autoPark" />
              </div>
              <p class="control-description">自动识别停车位并完成泊车</p>
            </div>
            
            <div class="control-item">
              <div class="control-header">
                <span>疲劳驾驶监测</span>
                <el-switch v-model="adasControls.fatigueDetection" />
              </div>
              <p class="control-description">监测驾驶员疲劳状态</p>
            </div>
          </div>
        </ModernCard>

        <!-- 安全评分详情 -->
        <ModernCard class="safety-score-card" hover-effect>
          <template #header>
            <span>安全评分详情</span>
          </template>
          <div class="score-content">
            <div class="score-circle">
              <div class="score-value">{{ adasOverview.safetyScore }}</div>
              <div class="score-label">综合评分</div>
            </div>
            
            <div class="score-breakdown">
              <div class="score-item">
                <span class="score-category">速度控制</span>
                <div class="score-bar">
                  <div class="score-progress" :style="{ width: safetyScores.speedControl + '%' }"></div>
                </div>
                <span class="score-number">{{ safetyScores.speedControl }}</span>
              </div>
              
              <div class="score-item">
                <span class="score-category">跟车距离</span>
                <div class="score-bar">
                  <div class="score-progress" :style="{ width: safetyScores.followingDistance + '%' }"></div>
                </div>
                <span class="score-number">{{ safetyScores.followingDistance }}</span>
              </div>
              
              <div class="score-item">
                <span class="score-category">车道保持</span>
                <div class="score-bar">
                  <div class="score-progress" :style="{ width: safetyScores.laneKeeping + '%' }"></div>
                </div>
                <span class="score-number">{{ safetyScores.laneKeeping }}</span>
              </div>
              
              <div class="score-item">
                <span class="score-category">平稳驾驶</span>
                <div class="score-bar">
                  <div class="score-progress" :style="{ width: safetyScores.smoothDriving + '%' }"></div>
                </div>
                <span class="score-number">{{ safetyScores.smoothDriving }}</span>
              </div>
            </div>
          </div>
        </ModernCard>
      </div>
    </div>

    <!-- 浮动操作按钮 -->
    <div class="floating-actions">
      <ModernButton variant="primary" circle @click="startAutoPark">
        <el-icon><Position /></el-icon>
      </ModernButton>
      <ModernButton variant="warning" circle @click="emergencyStop">
        <el-icon><CircleClose /></el-icon>
      </ModernButton>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import {
  Compass,
  Odometer,
  Warning,
  TrendCharts,
  Guide,
  View,
  Location,
  Position,
  CircleClose
} from '@element-plus/icons-vue'
import { Shield, Gauge, Radar } from 'lucide-vue-next'
import { ElMessage } from 'element-plus'
import MapComponent from '@/components/MapComponent.vue'
import { ModernButton, ModernCard } from '@/components/ui'
import { useAdasStore } from '@/stores/adas'

// 使用ADAS store
const adasStore = useAdasStore()

// ADAS概览数据 - 使用computed从store获取真实数据
const adasOverview = computed(() => ({
  safetyScore: adasStore.drivingScore,
  safetyTrend: 5.2,
  totalDistance: adasStore.behaviorMetrics.distance * 100, // 转换为总里程
  monthlyDistance: adasStore.behaviorMetrics.distance,
  alertsCount: adasStore.warnings.length,
  alertsReduction: 15.3,
  fuelEfficiency: 16.8,
  efficiencyImprovement: 8.5
}))

// 实时状态数据
const realtimeStatus = ref({
  status: '正常行驶',
  speed: 65,
  frontDistance: 45,
  lanePosition: 5,
  attentionLevel: '良好'
})

// 环境数据
const environmentData = ref({
  weather: '晴朗',
  roadCondition: '良好',
  visibility: 500,
  trafficDensity: '中等'
})

// 路线相关
const routeForm = ref({
  destination: '',
  preference: 'fastest'
})

const currentRoute = ref({
  estimatedTime: '45分钟',
  distance: 32.5,
  energyConsumption: 5.2,
  chargingAdvice: '无需充电',
  startPoint: { lat: 39.9042, lng: 116.4074 },
  endPoint: { lat: 39.9142, lng: 116.4274 },
  waypoints: [
    { lat: 39.9092, lng: 116.4174, type: 'charging' }
  ]
})

// 附近充电站数据
const nearbyStations = ref([
  {
    id: 'station-1',
    name: '国贸充电站',
    lat: 39.9092,
    lng: 116.4174,
    status: 'available',
    totalPiles: 8,
    availablePiles: 3,
    distance: 2.5
  },
  {
    id: 'station-2', 
    name: '三里屯充电站',
    lat: 39.9342,
    lng: 116.4474,
    status: 'busy',
    totalPiles: 6,
    availablePiles: 1,
    distance: 5.2
  }
])

// ADAS控制
const adasControls = ref({
  adaptiveCruise: true,
  laneKeeping: true,
  collisionWarning: true,
  blindSpotMonitoring: false,
  autoPark: false,
  fatigueDetection: true
})

// 安全评分详情 - 使用computed从store获取真实数据
const safetyScores = computed(() => ({
  speedControl: adasStore.behaviorScores.safety,
  followingDistance: Math.max(80, adasStore.behaviorScores.safety - 5),
  laneKeeping: adasStore.behaviorScores.comfort,
  smoothDriving: adasStore.behaviorScores.economy
}))

// 预警历史
const recentAlerts = ref([
  {
    id: 'A001',
    title: '前车距离过近',
    description: '检测到前车距离小于安全距离，建议减速',
    timestamp: new Date(Date.now() - 30 * 60 * 1000),
    level: 'warning'
  },
  {
    id: 'A002',
    title: '车道偏离预警',
    description: '车辆偏离当前车道，已自动修正',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
    level: 'info'
  },
  {
    id: 'A003',
    title: '疲劳驾驶提醒',
    description: '检测到驾驶员疲劳状态，建议休息',
    timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
    level: 'danger'
  }
])

// 分析时间范围
const analysisTimeRange = ref('today')

// 图表相关
const behaviorChartRef = ref<HTMLElement>()
let behaviorChart: echarts.ECharts | null = null

// 方法
const getStatusType = (status: string) => {
  const types = {
    '正常行驶': 'success',
    '注意驾驶': 'warning',
    '危险状态': 'danger'
  }
  return types[status] || 'info'
}

const getAlertLevelType = (level: string) => {
  const types = {
    info: 'info',
    warning: 'warning',
    danger: 'danger'
  }
  return types[level] || 'info'
}

const getAlertLevelText = (level: string) => {
  const texts = {
    info: '提示',
    warning: '警告',
    danger: '危险'
  }
  return texts[level] || '未知'
}

const optimizeRoute = () => {
  ElMessage.success('正在重新规划最优路线...')
}

const viewAllAlerts = () => {
  ElMessage.info('跳转到预警历史页面')
}

const startAutoPark = () => {
  ElMessage.success('启动自动泊车功能')
}

const emergencyStop = () => {
  ElMessage.warning('紧急制动已激活')
}

const formatDateTime = (date: Date) => {
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 初始化驾驶行为分析图表
const initBehaviorChart = () => {
  if (!behaviorChartRef.value) return
  
  behaviorChart = echarts.init(behaviorChartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['急加速', '急减速', '急转弯', '超速']
    },
    xAxis: {
      type: 'category',
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    },
    yAxis: {
      type: 'value',
      name: '次数'
    },
    series: [
      {
        name: '急加速',
        type: 'line',
        data: [2, 1, 3, 0, 2, 1, 0],
        itemStyle: { color: '#E6A23C' }
      },
      {
        name: '急减速',
        type: 'line',
        data: [1, 2, 1, 1, 0, 2, 1],
        itemStyle: { color: '#F56C6C' }
      },
      {
        name: '急转弯',
        type: 'line',
        data: [0, 1, 0, 2, 1, 0, 1],
        itemStyle: { color: '#909399' }
      },
      {
        name: '超速',
        type: 'line',
        data: [0, 0, 1, 0, 0, 0, 0],
        itemStyle: { color: '#F56C6C' }
      }
    ]
  }
  
  behaviorChart.setOption(option)
}

// 生命周期
onMounted(async () => {
  initBehaviorChart()
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    behaviorChart?.resize()
  })
  
  // 获取真实数据
  try {
    await adasStore.fetchBehaviorData()
    await adasStore.fetchDetectedObjects()
    await adasStore.fetchWarnings()
  } catch (error) {
    console.error('Failed to fetch ADAS data:', error)
  }
  
  // 模拟实时数据更新
  const updateInterval = setInterval(() => {
    // 更新实时状态数据
    realtimeStatus.value.speed = Math.floor(Math.random() * 20) + 60
    realtimeStatus.value.frontDistance = Math.floor(Math.random() * 30) + 30
    realtimeStatus.value.lanePosition = Math.floor(Math.random() * 20) - 10
    
    // 定期刷新真实数据
    if (Math.random() < 0.3) {
      adasStore.fetchDetectedObjects()
      adasStore.fetchWarnings()
    }
  }, 3000)
  
  // 清理定时器
  onUnmounted(() => {
    clearInterval(updateInterval)
  })
})

onUnmounted(() => {
  behaviorChart?.dispose()
  window.removeEventListener('resize', () => {
    behaviorChart?.resize()
  })
})
</script>

<style scoped>
.adas-container {
  padding: 24px;
  background: #ffffff;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 32px;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.page-subtitle {
  font-size: 16px;
  color: #718096;
  margin: 12px 0 0 0;
}

.adas-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.overview-card {
  border-radius: 12px;
  background: #ffffff;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border-color: #cbd5e1;
}

.overview-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.overview-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 20px;
  color: white;
}

.overview-icon.safety {
  background: #3b82f6;
}

.overview-icon.distance {
  background: #059669;
}

.overview-icon.alerts {
  background: #dc2626;
}

.overview-icon.efficiency {
  background: #7c3aed;
}

.overview-info h3 {
  font-size: 24px;
  font-weight: bold;
  margin: 0;
  color: #1e293b;
}

.overview-info p {
  font-size: 14px;
  color: #64748b;
  margin: 5px 0;
}

.overview-rate {
  font-size: 12px;
  font-weight: bold;
  color: #3b82f6;
}

.main-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.monitoring-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.control-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.realtime-card,
.behavior-analysis-card,
.alerts-history-card,
.route-optimization-card,
.adas-controls-card,
.safety-score-card {
  border-radius: 12px;
  background: #ffffff;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.realtime-content {
  padding: 10px 0;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 20px;
}

.status-item {
  display: flex;
  align-items: center;
  padding: 15px;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
  border: 1px solid #e2e8f0;
}

.status-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 18px;
  color: white;
}

.status-icon.speed {
  background: #3b82f6;
}

.status-icon.distance-sensor {
  background: #059669;
}

.status-icon.lane {
  background: #dc2626;
}

.status-icon.attention {
  background: #7c3aed;
}

.status-info h4 {
  font-size: 20px;
  font-weight: bold;
  margin: 0;
  color: #1e293b;
}

.status-info p {
  font-size: 12px;
  color: #64748b;
  margin: 5px 0 0 0;
}

.environment-perception h5 {
  margin: 0 0 15px 0;
  color: #1e293b;
  font-size: 16px;
}

.perception-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.perception-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.perception-label {
  font-size: 12px;
  color: #606266;
}

.perception-value {
  font-size: 12px;
  font-weight: bold;
  color: #1e293b;
}

.chart-container {
  height: 250px;
  width: 100%;
}

.alerts-list {
  max-height: 300px;
  overflow-y: auto;
}

.alert-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.alert-icon {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 16px;
  color: white;
}

.alert-icon.info {
  background: #409EFF;
}

.alert-icon.warning {
  background: #E6A23C;
}

.alert-icon.danger {
  background: #F56C6C;
}

.alert-info {
  flex: 1;
}

.alert-info h5 {
  margin: 0 0 5px 0;
  color: #1e293b;
  font-size: 14px;
}

.alert-time {
  margin: 0 0 5px 0;
  color: #64748b;
  font-size: 12px;
}

.alert-description {
  margin: 0;
  color: #606266;
  font-size: 12px;
}

.route-content {
  padding: 10px 0;
}

.route-input {
  margin-bottom: 15px;
}

.route-options {
  margin-bottom: 20px;
}

.route-info {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  margin-bottom: 20px;
}

.route-detail {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8fafc;
  border-radius: 6px;
}

.route-label {
  font-size: 12px;
  color: #606266;
}

.route-value {
  font-size: 12px;
  font-weight: bold;
  color: #1e293b;
}

.route-map {
  height: 150px;
  border-radius: 8px;
  overflow: hidden;
}

.mock-route-map {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  position: relative;
}

.route-path {
  position: absolute;
  top: 50%;
  left: 10%;
  right: 10%;
  height: 3px;
  background: linear-gradient(90deg, #409EFF 0%, #67C23A 100%);
  border-radius: 2px;
  transform: translateY(-50%);
}

.route-markers {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  display: flex;
  justify-content: space-between;
  padding: 0 10%;
}

.marker {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 10px;
  color: white;
  font-weight: bold;
}

.marker.start {
  background: #409EFF;
}

.marker.charging {
  background: #E6A23C;
}

.marker.end {
  background: #67C23A;
}

.controls-grid {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.control-item {
  padding: 15px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.control-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.control-header span {
  font-weight: bold;
  color: #1e293b;
}

.control-description {
  margin: 0;
  font-size: 12px;
  color: #64748b;
}

.score-content {
  display: flex;
  align-items: center;
  gap: 30px;
  padding: 20px 0;
}

.score-circle {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: #3b82f6;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.score-value {
  font-size: 24px;
  font-weight: bold;
}

.score-label {
  font-size: 12px;
}

.score-breakdown {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.score-item {
  display: flex;
  align-items: center;
  gap: 15px;
}

.score-category {
  width: 80px;
  font-size: 12px;
  color: #606266;
}

.score-bar {
  flex: 1;
  height: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
}

.score-progress {
  height: 100%;
  background: #3b82f6;
  transition: width 0.3s ease;
}

.score-number {
  width: 30px;
  text-align: right;
  font-size: 12px;
  font-weight: bold;
  color: #1e293b;
}

.floating-actions {
  position: fixed;
  right: 30px;
  bottom: 30px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.floating-actions .modern-button {
  width: 50px;
  height: 50px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 1fr;
  }
  
  .adas-overview {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
  
  .status-grid {
    grid-template-columns: 1fr;
  }
  
  .perception-grid {
    grid-template-columns: 1fr;
  }
  
  .route-info {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .adas-container {
    padding: 15px;
  }
  
  .adas-overview {
    grid-template-columns: 1fr;
  }
  
  .page-title {
    font-size: 24px;
  }
  
  .score-content {
    flex-direction: column;
    text-align: center;
  }
}
</style>