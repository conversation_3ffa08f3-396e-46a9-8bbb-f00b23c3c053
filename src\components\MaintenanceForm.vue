<template>
  <el-dialog
    v-model="dialogVisible"
    :title="mode === 'add' ? '添加维护记录' : '编辑维护记录'"
    width="600px"
    @close="$emit('close')"
    class="maintenance-form-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="loading"
      element-loading-text="处理中..."
    >
      <!-- 基本信息 -->
      <div class="form-section">
        <h4 class="section-title">基本信息</h4>
        
        <el-form-item label="电池ID" prop="battery_id">
          <el-select
            v-model="formData.battery_id"
            placeholder="请选择电池"
            style="width: 100%"
            filterable
          >
            <el-option
              v-for="battery in availableBatteries"
              :key="battery.id"
              :label="`${battery.id} (${battery.manufacturer} ${battery.model})`"
              :value="battery.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="维护类型" prop="type">
          <el-select v-model="formData.type" placeholder="请选择维护类型" style="width: 100%">
            <el-option label="定期检查" value="inspection" />
            <el-option label="部件更换" value="replacement" />
            <el-option label="系统校准" value="calibration" />
            <el-option label="故障维修" value="repair" />
            <el-option label="预防性维护" value="preventive" />
            <el-option label="紧急维修" value="emergency" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="维护标题" prop="title">
          <el-input v-model="formData.title" placeholder="请输入维护标题" />
        </el-form-item>
        
        <el-form-item label="维护描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="4"
            placeholder="请详细描述维护内容、发现的问题、采取的措施等"
          />
        </el-form-item>
      </div>

      <!-- 执行信息 -->
      <div class="form-section">
        <h4 class="section-title">执行信息</h4>
        
        <el-form-item label="维护日期" prop="date">
          <el-date-picker
            v-model="formData.date"
            type="datetime"
            placeholder="请选择维护日期时间"
            style="width: 100%"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        
        <el-form-item label="技师姓名" prop="technician">
          <el-input v-model="formData.technician" placeholder="请输入技师姓名" />
        </el-form-item>
        
        <el-form-item label="维护费用" prop="cost">
          <el-input-number
            v-model="formData.cost"
            :min="0"
            :max="100000"
            :precision="2"
            :step="10"
            style="width: 100%"
            placeholder="请输入维护费用"
          />
        </el-form-item>
        
        <el-form-item label="维护状态" prop="status">
          <el-select v-model="formData.status" placeholder="请选择状态" style="width: 100%">
            <el-option label="已完成" value="completed" />
            <el-option label="进行中" value="pending" />
            <el-option label="已取消" value="cancelled" />
            <el-option label="计划中" value="scheduled" />
          </el-select>
        </el-form-item>
      </div>

      <!-- 附加信息 -->
      <div class="form-section">
        <h4 class="section-title">附加信息</h4>
        
        <el-form-item label="更换部件">
          <el-input v-model="formData.replaced_parts" placeholder="如有更换部件，请列出" />
        </el-form-item>
        
        <el-form-item label="下次维护">
          <el-date-picker
            v-model="formData.next_maintenance"
            type="date"
            placeholder="建议下次维护日期"
            style="width: 100%"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        
        <el-form-item label="备注">
          <el-input
            v-model="formData.notes"
            type="textarea"
            :rows="3"
            placeholder="其他备注信息"
          />
        </el-form-item>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="$emit('close')">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          {{ mode === 'add' ? '添加' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { useBatteryStore } from '@/stores/battery'

// Props
const props = defineProps<{
  modelValue: boolean
  recordId?: string
  mode: 'add' | 'edit'
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'close': []
  'success': []
}>()

// Store
const batteryStore = useBatteryStore()

// 响应式数据
const formRef = ref<FormInstance>()
const loading = ref(false)
const availableBatteries = ref<any[]>([])

const formData = ref({
  battery_id: '',
  type: '',
  title: '',
  description: '',
  date: '',
  technician: '',
  cost: 0,
  status: 'completed',
  replaced_parts: '',
  next_maintenance: '',
  notes: ''
})

// 表单验证规则
const formRules: FormRules = {
  battery_id: [
    { required: true, message: '请选择电池', trigger: 'change' }
  ],
  type: [
    { required: true, message: '请选择维护类型', trigger: 'change' }
  ],
  title: [
    { required: true, message: '请输入维护标题', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入维护描述', trigger: 'blur' }
  ],
  date: [
    { required: true, message: '请选择维护日期', trigger: 'change' }
  ],
  technician: [
    { required: true, message: '请输入技师姓名', trigger: 'blur' }
  ],
  cost: [
    { required: true, message: '请输入维护费用', trigger: 'blur' },
    { type: 'number', min: 0, message: '费用不能为负数', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择维护状态', trigger: 'change' }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 监听器
watch(() => props.modelValue, async (visible) => {
  if (visible) {
    await loadAvailableBatteries()
    if (props.mode === 'edit' && props.recordId) {
      await loadRecordData(props.recordId)
    } else {
      resetForm()
    }
  }
})

// 方法
const loadAvailableBatteries = async () => {
  try {
    await batteryStore.fetchBatteries()
    availableBatteries.value = batteryStore.batteries
  } catch (error) {
    console.error('加载电池列表失败:', error)
  }
}

const loadRecordData = async (recordId: string) => {
  loading.value = true
  try {
    // 这里应该从API加载记录数据
    // const record = await batteryStore.fetchMaintenanceRecord(recordId)
    // 暂时使用模拟数据
    ElMessage.info('编辑功能开发中')
  } catch (error) {
    ElMessage.error('加载维护记录失败')
  } finally {
    loading.value = false
  }
}

const resetForm = () => {
  formData.value = {
    battery_id: '',
    type: '',
    title: '',
    description: '',
    date: '',
    technician: '',
    cost: 0,
    status: 'completed',
    replaced_parts: '',
    next_maintenance: '',
    notes: ''
  }
  formRef.value?.clearValidate()
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    loading.value = true
    
    // 这里应该调用API保存数据
    // if (props.mode === 'add') {
    //   await batteryStore.createMaintenanceRecord(formData.value)
    // } else if (props.recordId) {
    //   await batteryStore.updateMaintenanceRecord(props.recordId, formData.value)
    // }
    
    // 暂时模拟成功
    setTimeout(() => {
      loading.value = false
      emit('success')
    }, 1000)
    
  } catch (error) {
    if (error !== false) { // 不是表单验证错误
      ElMessage.error(props.mode === 'add' ? '添加维护记录失败' : '更新维护记录失败')
    }
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  loadAvailableBatteries()
})
</script>

<style scoped>
.maintenance-form-dialog :deep(.el-dialog__body) {
  padding: 24px;
  max-height: 70vh;
  overflow-y: auto;
}

.form-section {
  margin-bottom: 32px;
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 表单样式优化 */
.maintenance-form-dialog :deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
}

.maintenance-form-dialog :deep(.el-input__wrapper) {
  border-radius: 8px;
}

.maintenance-form-dialog :deep(.el-select .el-input__wrapper) {
  border-radius: 8px;
}

.maintenance-form-dialog :deep(.el-date-editor.el-input) {
  border-radius: 8px;
}

.maintenance-form-dialog :deep(.el-input-number .el-input__wrapper) {
  border-radius: 8px;
}

.maintenance-form-dialog :deep(.el-textarea__inner) {
  border-radius: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .maintenance-form-dialog :deep(.el-dialog) {
    width: 95% !important;
    margin: 5vh auto;
  }
  
  .maintenance-form-dialog :deep(.el-form-item__label) {
    width: 80px !important;
  }
}
</style>
