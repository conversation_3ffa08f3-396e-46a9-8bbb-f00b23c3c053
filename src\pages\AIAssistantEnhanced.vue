<template>
  <div class="ai-assistant-enhanced">
    <!-- 页面头部 -->
    <div class="ai-header">
      <div class="ai-header-content">
        <div class="ai-avatar-container">
          <div class="ai-avatar">
            <img 
              v-if="aiInfo?.avatar" 
              :src="aiInfo.avatar" 
              :alt="aiInfo.name"
              @error="useDefaultAvatar"
            />
            <el-icon v-else :size="40" color="#ffffff">
              <ChatDotRound />
            </el-icon>
          </div>
          <div class="ai-status-indicator" :class="{ online: isOnline }"></div>
        </div>
        <div class="ai-info">
          <h2>{{ aiInfo?.name || '小E' }}</h2>
          <p>{{ aiInfo?.description || '我是您的专属AI助手，可以回答关于新能源汽车管理系统的任何问题' }}</p>
          <div class="ai-capabilities">
            <el-tag 
              v-for="capability in (aiInfo?.capabilities || [])" 
              :key="capability"
              size="small"
              type="info"
              class="capability-tag"
            >
              {{ capability }}
            </el-tag>
          </div>
        </div>
        <div class="ai-status">
          <el-tag :type="isOnline ? 'success' : 'warning'" size="small">
            {{ isOnline ? '在线' : '离线' }}
          </el-tag>
          <div class="ai-model-info">
            <span>{{ aiInfo?.model || 'GPT-4' }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 对话区域 -->
    <div class="chat-container" ref="chatContainer">
      <div class="chat-messages">
        <!-- 欢迎消息 -->
        <div v-if="messages.length === 0" class="welcome-message">
          <div class="welcome-card">
            <div class="welcome-avatar">
              <img 
                v-if="aiInfo?.avatar" 
                :src="aiInfo.avatar" 
                :alt="aiInfo.name"
                @error="useDefaultAvatar"
              />
              <el-icon v-else :size="60" color="#4f46e5">
                <ChatDotRound />
              </el-icon>
            </div>
            <h3>欢迎使用{{ aiInfo?.name || '智能AI助手' }}</h3>
            <p>我可以帮您处理以下类型的问题：</p>
            <div class="feature-grid">
              <div class="feature-item">
                <el-icon><ChatDotRound /></el-icon>
                <span>文本对话</span>
              </div>
              <div class="feature-item">
                <el-icon><Document /></el-icon>
                <span>文件分析</span>
              </div>
              <div class="feature-item">
                <el-icon><Picture /></el-icon>
                <span>图片识别</span>
              </div>
              <div class="feature-item">
                <el-icon><VideoCamera /></el-icon>
                <span>视频分析</span>
              </div>
            </div>
            <div class="quick-questions">
              <el-button 
                v-for="question in quickQuestions" 
                :key="question"
                type="primary" 
                plain 
                size="small"
                @click="sendQuickQuestion(question)"
                class="quick-btn"
              >
                {{ question }}
              </el-button>
            </div>
          </div>
        </div>

        <!-- 消息列表 -->
        <div 
          v-for="(message, index) in messages" 
          :key="index" 
          class="message-item"
          :class="message.type"
        >
          <div class="message-avatar">
            <el-avatar 
              v-if="message.type === 'user'"
              :size="36"
              :src="userAvatar"
              icon="User"
            />
            <div v-else class="ai-message-avatar">
              <img 
                v-if="aiInfo?.avatar" 
                :src="aiInfo.avatar" 
                :alt="aiInfo.name"
                @error="useDefaultAvatar"
              />
              <el-icon v-else :size="36" color="#4f46e5">
                <ChatDotRound />
              </el-icon>
            </div>
          </div>
          <div class="message-content">
            <div class="message-bubble">
              <!-- 文件信息显示 -->
              <div v-if="message.fileInfo" class="file-info">
                <el-icon><Document /></el-icon>
                <span>{{ message.fileInfo.name }}</span>
                <el-tag size="small">{{ formatFileSize(message.fileInfo.size) }}</el-tag>
              </div>
              
              <!-- 正在输入指示器 -->
              <div v-if="message.type === 'ai' && message.isTyping" class="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
                <span class="typing-text">{{ aiInfo?.name || '小E' }}正在思考...</span>
              </div>
              
              <!-- 消息内容 -->
              <div v-else class="message-text" v-html="formatMessage(message.content)"></div>
            </div>
            <div class="message-time">
              {{ formatTime(message.timestamp) }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="chat-input-container">
      <div class="input-wrapper">
        <!-- 文件上传区域 -->
        <div class="file-upload-area" v-if="showFileUpload">
          <el-upload
            ref="uploadRef"
            :auto-upload="false"
            :show-file-list="false"
            :on-change="handleFileSelect"
            accept=".txt,.json,.csv,.pdf,.jpg,.jpeg,.png,.gif,.webp,.mp4,.avi,.mov"
            drag
            class="upload-dragger"
          >
            <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <div class="el-upload__tip">
              支持文本、图片、视频文件，最大50MB
            </div>
          </el-upload>
        </div>

        <!-- 输入框 -->
        <div class="input-row">
          <el-input
            v-model="inputMessage"
            type="textarea"
            :rows="1"
            :autosize="{ minRows: 1, maxRows: 4 }"
            placeholder="请输入您的问题，或上传文件进行分析..."
            @keydown.enter.prevent="handleEnterKey"
            :disabled="isLoading"
            class="message-input"
          />
          <div class="input-actions">
            <el-button
              :icon="Paperclip"
              @click="toggleFileUpload"
              :type="showFileUpload ? 'primary' : 'default'"
              circle
              class="file-btn"
            />
            <el-button
              type="primary"
              :icon="Promotion"
              @click="sendMessage"
              :loading="isLoading"
              :disabled="!inputMessage.trim() && !selectedFile"
              class="send-btn"
            >
              发送
            </el-button>
          </div>
        </div>

        <!-- 选中的文件显示 -->
        <div v-if="selectedFile" class="selected-file">
          <div class="file-preview">
            <el-icon><Document /></el-icon>
            <span>{{ selectedFile.name }}</span>
            <el-tag size="small">{{ formatFileSize(selectedFile.size) }}</el-tag>
            <el-button 
              :icon="Close" 
              size="small" 
              text 
              @click="clearSelectedFile"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  ChatDotRound,
  Promotion,
  Paperclip,
  Document,
  Picture,
  VideoCamera,
  UploadFilled,
  Close
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { useAIAssistantStore, type Message } from '@/stores/ai-assistant'

const authStore = useAuthStore()
const aiStore = useAIAssistantStore()

// 响应式数据
const chatContainer = ref<HTMLElement>()
const uploadRef = ref()
const inputMessage = ref('')
const showFileUpload = ref(false)
const selectedFile = ref<File | null>(null)

// 计算属性
const userAvatar = computed(() => authStore.user?.avatar || '')
const messages = computed(() => aiStore.messages)
const isLoading = computed(() => aiStore.isLoading)
const isOnline = computed(() => aiStore.isOnline)
const aiInfo = computed(() => aiStore.aiInfo)

// 快速问题
const quickQuestions = [
  '系统功能介绍',
  '如何查看电池状态？',
  '充电站在哪里？',
  '如何设置充电计划？',
  '车辆保养提醒',
  '碳积分如何获得？'
]

// 方法
const sendQuickQuestion = (question: string) => {
  inputMessage.value = question
  sendMessage()
}

const handleEnterKey = (event: KeyboardEvent) => {
  if (!event.shiftKey) {
    sendMessage()
  }
}

const sendMessage = async () => {
  if (selectedFile.value) {
    // 发送文件分析请求
    await sendFileAnalysis()
  } else if (inputMessage.value.trim()) {
    // 发送文本消息
    await sendTextMessage()
  }
}

const sendTextMessage = async () => {
  if (!inputMessage.value.trim() || isLoading.value) return

  const message = inputMessage.value.trim()
  inputMessage.value = ''

  try {
    await aiStore.sendMessage(message, messages.value)
    await scrollToBottom()
  } catch (error) {
    ElMessage.error('发送消息失败')
  }
}

const sendFileAnalysis = async () => {
  if (!selectedFile.value) return

  const question = inputMessage.value.trim() || '请分析这个文件的内容'
  inputMessage.value = ''

  try {
    await aiStore.analyzeFile(selectedFile.value, question)
    clearSelectedFile()
    await scrollToBottom()
  } catch (error) {
    ElMessage.error('文件分析失败')
  }
}

const toggleFileUpload = () => {
  showFileUpload.value = !showFileUpload.value
}

const handleFileSelect = (file: any) => {
  selectedFile.value = file.raw
  showFileUpload.value = false
  ElMessage.success(`已选择文件：${file.name}`)
}

const clearSelectedFile = () => {
  selectedFile.value = null
}

const useDefaultAvatar = (event: Event) => {
  // 当头像加载失败时使用默认图标
  const target = event.target as HTMLImageElement
  target.style.display = 'none'
}

const scrollToBottom = async () => {
  await nextTick()
  if (chatContainer.value) {
    chatContainer.value.scrollTop = chatContainer.value.scrollHeight
  }
}

const formatMessage = (content: string) => {
  return content
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/\n/g, '<br>')
    .replace(/`(.*?)`/g, '<code>$1</code>')
    .replace(/###\s*(.*?)$/gm, '<h3>$1</h3>')
    .replace(/##\s*(.*?)$/gm, '<h2>$1</h2>')
    .replace(/•\s*(.*?)$/gm, '<li>$1</li>')
}

const formatTime = (timestamp: Date) => {
  return timestamp.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 初始化
onMounted(async () => {
  try {
    await aiStore.fetchAIInfo()
    await aiStore.checkStatus()
  } catch (error) {
    console.error('初始化AI助手失败:', error)
  }
})
</script>

<style scoped>
.ai-assistant-enhanced {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.ai-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.ai-header-content {
  display: flex;
  align-items: center;
  gap: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.ai-avatar-container {
  position: relative;
}

.ai-avatar {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
  overflow: hidden;
}

.ai-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.ai-status-indicator {
  position: absolute;
  bottom: 4px;
  right: 4px;
  width: 16px;
  height: 16px;
  background: #ef4444;
  border: 3px solid white;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.ai-status-indicator.online {
  background: #10b981;
}

.ai-info {
  flex: 1;
}

.ai-info h2 {
  margin: 0 0 8px 0;
  color: #1a202c;
  font-size: 28px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.ai-info p {
  margin: 0 0 12px 0;
  color: #64748b;
  font-size: 16px;
  line-height: 1.5;
}

.ai-capabilities {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.capability-tag {
  border-radius: 12px !important;
  font-size: 12px !important;
}

.ai-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

.ai-model-info {
  font-size: 12px;
  color: #64748b;
  background: rgba(255, 255, 255, 0.8);
  padding: 4px 8px;
  border-radius: 8px;
}

.chat-container {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.chat-messages {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.welcome-message {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.welcome-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 48px;
  border-radius: 24px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.welcome-avatar {
  width: 100px;
  height: 100px;
  margin: 0 auto 24px;
  border-radius: 50%;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
}

.welcome-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.welcome-card h3 {
  margin: 0 0 16px 0;
  color: #1e293b;
  font-size: 24px;
  font-weight: 700;
}

.welcome-card p {
  color: #64748b;
  margin-bottom: 32px;
  font-size: 16px;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-bottom: 32px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 12px;
  color: #4f46e5;
  font-weight: 500;
}

.quick-questions {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: center;
}

.quick-btn {
  border-radius: 20px !important;
  font-size: 13px !important;
  padding: 8px 16px !important;
}

.message-item {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
}

.message-item.user {
  flex-direction: row-reverse;
}

.message-avatar {
  flex-shrink: 0;
}

.ai-message-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.ai-message-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.message-content {
  flex: 1;
  max-width: 75%;
}

.message-item.user .message-content {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.message-bubble {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 16px 20px;
  border-radius: 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  word-wrap: break-word;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.message-item.user .message-bubble {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  border: none;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 12px;
  margin-bottom: 12px;
  font-size: 14px;
  color: #4f46e5;
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.typing-indicator span:not(.typing-text) {
  width: 8px;
  height: 8px;
  background: #94a3b8;
  border-radius: 50%;
  animation: typing 1.4s infinite;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

.typing-text {
  color: #64748b;
  font-size: 14px;
  font-style: italic;
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.4;
  }
  30% {
    transform: translateY(-8px);
    opacity: 1;
  }
}

.message-time {
  font-size: 12px;
  color: #94a3b8;
  margin-top: 6px;
}

.chat-input-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding: 24px;
}

.input-wrapper {
  max-width: 1200px;
  margin: 0 auto;
}

.file-upload-area {
  margin-bottom: 16px;
}

.upload-dragger {
  border: 2px dashed #d1d5db !important;
  border-radius: 16px !important;
  background: rgba(255, 255, 255, 0.8) !important;
}

.input-row {
  display: flex;
  gap: 12px;
  align-items: flex-end;
}

.message-input {
  flex: 1;
}

.input-actions {
  display: flex;
  gap: 8px;
}

.file-btn {
  width: 40px !important;
  height: 40px !important;
}

.send-btn {
  border-radius: 12px !important;
  padding: 12px 24px !important;
  height: auto !important;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%) !important;
  border: none !important;
  box-shadow: 0 4px 16px rgba(79, 70, 229, 0.3) !important;
  transition: all 0.3s ease !important;
}

.send-btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4) !important;
}

.selected-file {
  margin-top: 12px;
}

.file-preview {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 12px;
  font-size: 14px;
  color: #4f46e5;
}

:deep(.message-input .el-textarea__inner) {
  border-radius: 16px !important;
  border: 2px solid rgba(255, 255, 255, 0.3) !important;
  background: rgba(255, 255, 255, 0.9) !important;
  padding: 16px 20px !important;
  font-size: 15px !important;
  line-height: 1.6 !important;
  resize: none !important;
  transition: all 0.3s ease !important;
}

:deep(.message-input .el-textarea__inner:focus) {
  border-color: #4f46e5 !important;
  box-shadow: 0 0 0 4px rgba(79, 70, 229, 0.1) !important;
  background: rgba(255, 255, 255, 1) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ai-header {
    padding: 20px 16px;
  }
  
  .ai-header-content {
    gap: 16px;
  }
  
  .ai-avatar {
    width: 60px;
    height: 60px;
  }
  
  .ai-info h2 {
    font-size: 22px;
  }
  
  .chat-container {
    padding: 16px;
  }
  
  .message-content {
    max-width: 85%;
  }
  
  .welcome-card {
    padding: 32px 24px;
    margin: 0 16px;
  }
  
  .feature-grid {
    grid-template-columns: 1fr;
  }
  
  .chat-input-container {
    padding: 16px;
  }
  
  .input-row {
    flex-direction: column;
    gap: 12px;
  }
  
  .input-actions {
    width: 100%;
    justify-content: space-between;
  }
  
  .send-btn {
    flex: 1;
  }
}
</style>
