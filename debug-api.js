/**
 * 调试API参数问题
 */

const mysql = require('mysql2/promise')
require('dotenv').config()

const mysqlConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME || 'ev_management',
  charset: 'utf8mb4'
}

async function debugAPI() {
  let connection;
  try {
    connection = await mysql.createConnection(mysqlConfig)
    console.log('✅ 连接数据库成功')
    
    // 测试简单查询
    console.log('\n🔍 测试简单查询...')
    const [simpleResult] = await connection.execute('SELECT COUNT(*) as count FROM users')
    console.log('简单查询结果:', simpleResult[0])
    
    // 测试带参数的查询
    console.log('\n🔍 测试带参数的查询...')
    const limit = 5
    const offset = 0
    
    console.log('参数类型检查:')
    console.log('limit:', limit, typeof limit)
    console.log('offset:', offset, typeof offset)
    
    // 测试用户查询 - 使用execute
    try {
      const [userResult] = await connection.execute(
        'SELECT * FROM users ORDER BY created_at DESC LIMIT ? OFFSET ?',
        [limit, offset]
      )
      console.log('✅ 用户查询(execute)成功，返回', userResult.length, '条记录')
    } catch (error) {
      console.error('❌ 用户查询(execute)失败:', error.message)
    }

    // 测试用户查询 - 使用query
    try {
      const [userResult2] = await connection.query(
        'SELECT * FROM users ORDER BY created_at DESC LIMIT ? OFFSET ?',
        [limit, offset]
      )
      console.log('✅ 用户查询(query)成功，返回', userResult2.length, '条记录')
    } catch (error) {
      console.error('❌ 用户查询(query)失败:', error.message)
    }

    // 测试用户查询 - 不使用参数
    try {
      const [userResult3] = await connection.execute(
        `SELECT * FROM users ORDER BY created_at DESC LIMIT ${limit} OFFSET ${offset}`
      )
      console.log('✅ 用户查询(无参数)成功，返回', userResult3.length, '条记录')
    } catch (error) {
      console.error('❌ 用户查询(无参数)失败:', error.message)
    }
    
    // 测试车辆查询
    try {
      const [vehicleResult] = await connection.execute(
        `SELECT v.*, u.name as user_name, u.email as user_email
         FROM vehicles v
         LEFT JOIN users u ON v.owner_id = u.user_id
         ORDER BY v.created_at DESC
         LIMIT ? OFFSET ?`,
        [limit, offset]
      )
      console.log('✅ 车辆查询成功，返回', vehicleResult.length, '条记录')
    } catch (error) {
      console.error('❌ 车辆查询失败:', error.message)
    }
    
    // 测试电池查询
    try {
      const [batteryResult] = await connection.execute(
        `SELECT b.*, v.make as brand, v.model, v.license_plate, u.name as owner_name
         FROM batteries b
         LEFT JOIN vehicles v ON b.vehicle_id = v.vehicle_id
         LEFT JOIN users u ON v.owner_id = u.user_id
         ORDER BY b.created_at DESC
         LIMIT ? OFFSET ?`,
        [limit, offset]
      )
      console.log('✅ 电池查询成功，返回', batteryResult.length, '条记录')
    } catch (error) {
      console.error('❌ 电池查询失败:', error.message)
    }
    
    // 测试充电站查询
    try {
      const [stationResult] = await connection.execute(
        'SELECT * FROM charging_stations ORDER BY created_at DESC LIMIT ? OFFSET ?',
        [limit, offset]
      )
      console.log('✅ 充电站查询成功，返回', stationResult.length, '条记录')
    } catch (error) {
      console.error('❌ 充电站查询失败:', error.message)
    }
    
  } catch (error) {
    console.error('❌ 错误:', error.message)
  } finally {
    if (connection) {
      await connection.end()
    }
  }
}

debugAPI()
