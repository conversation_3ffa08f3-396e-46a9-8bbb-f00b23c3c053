-- 新能源汽车智能综合管理系统数据库表结构
-- 创建时间: 2024-12-28

-- 1. 用户表
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20),
    name VARCHAR(100) NOT NULL,
    avatar_url TEXT,
    user_type VARCHAR(20) DEFAULT 'individual' CHECK (user_type IN ('individual', 'enterprise', 'fleet_manager', 'admin')),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. 车辆表
CREATE TABLE IF NOT EXISTS vehicles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    vin VARCHA<PERSON>(17) UNIQUE NOT NULL,
    brand VARCHAR(50) NOT NULL,
    model VARCHAR(100) NOT NULL,
    year INTEGER NOT NULL,
    color VARCHAR(30),
    license_plate VARCHAR(20),
    vehicle_type VARCHAR(30) DEFAULT 'passenger' CHECK (vehicle_type IN ('passenger', 'commercial', 'bus', 'truck')),
    battery_capacity DECIMAL(8,2), -- kWh
    max_range INTEGER, -- km
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'maintenance', 'retired')),
    location_lat DECIMAL(10,8),
    location_lng DECIMAL(11,8),
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. 电池表
CREATE TABLE IF NOT EXISTS batteries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    vehicle_id UUID REFERENCES vehicles(id) ON DELETE CASCADE,
    battery_id VARCHAR(50) UNIQUE NOT NULL,
    manufacturer VARCHAR(100) NOT NULL,
    model VARCHAR(100) NOT NULL,
    capacity DECIMAL(8,2) NOT NULL, -- kWh
    voltage DECIMAL(8,2), -- V
    chemistry VARCHAR(50), -- 电池化学类型
    manufacture_date DATE,
    warranty_end_date DATE,
    current_soc DECIMAL(5,2), -- 当前电量百分比
    current_soh DECIMAL(5,2), -- 当前健康度百分比
    temperature DECIMAL(5,2), -- 当前温度
    cycle_count INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'normal' CHECK (status IN ('normal', 'warning', 'critical', 'maintenance')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. 电池溯源表
CREATE TABLE IF NOT EXISTS battery_trace (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    battery_id UUID REFERENCES batteries(id) ON DELETE CASCADE,
    trace_type VARCHAR(30) NOT NULL CHECK (trace_type IN ('production', 'transport', 'installation', 'maintenance', 'recycling')),
    location VARCHAR(200),
    operator VARCHAR(100),
    description TEXT,
    blockchain_hash VARCHAR(64), -- 区块链哈希值
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB
);

-- 5. 充电站表
CREATE TABLE IF NOT EXISTS charging_stations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(200) NOT NULL,
    address TEXT NOT NULL,
    latitude DECIMAL(10,8) NOT NULL,
    longitude DECIMAL(11,8) NOT NULL,
    operator VARCHAR(100),
    station_type VARCHAR(30) CHECK (station_type IN ('public', 'private', 'semi_public')),
    total_ports INTEGER DEFAULT 0,
    available_ports INTEGER DEFAULT 0,
    power_types TEXT[], -- 支持的充电功率类型
    pricing JSONB, -- 价格信息
    amenities TEXT[], -- 设施信息
    operating_hours JSONB,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'maintenance', 'offline')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. 充电会话表
CREATE TABLE IF NOT EXISTS charging_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    vehicle_id UUID REFERENCES vehicles(id) ON DELETE CASCADE,
    station_id UUID REFERENCES charging_stations(id) ON DELETE CASCADE,
    session_id VARCHAR(100) UNIQUE NOT NULL,
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE,
    start_soc DECIMAL(5,2), -- 开始充电时电量
    end_soc DECIMAL(5,2), -- 结束充电时电量
    energy_delivered DECIMAL(8,2), -- kWh
    cost DECIMAL(10,2), -- 费用
    payment_method VARCHAR(50),
    payment_status VARCHAR(20) DEFAULT 'pending' CHECK (payment_status IN ('pending', 'completed', 'failed', 'refunded')),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'completed', 'interrupted', 'failed')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. 碳积分表
CREATE TABLE IF NOT EXISTS carbon_credits (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    vehicle_id UUID REFERENCES vehicles(id) ON DELETE CASCADE,
    credit_type VARCHAR(30) CHECK (credit_type IN ('driving', 'charging', 'sharing', 'recycling')),
    amount DECIMAL(10,2) NOT NULL,
    description TEXT,
    calculation_data JSONB,
    blockchain_hash VARCHAR(64),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'redeemed', 'expired')),
    earned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE
);

-- 8. 驾驶行为表
CREATE TABLE IF NOT EXISTS driving_behavior (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    vehicle_id UUID REFERENCES vehicles(id) ON DELETE CASCADE,
    trip_id VARCHAR(100) NOT NULL,
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE NOT NULL,
    distance DECIMAL(8,2), -- km
    duration INTEGER, -- 秒
    avg_speed DECIMAL(5,2), -- km/h
    max_speed DECIMAL(5,2), -- km/h
    energy_consumption DECIMAL(8,2), -- kWh
    efficiency_score DECIMAL(5,2), -- 效率评分
    safety_score DECIMAL(5,2), -- 安全评分
    eco_score DECIMAL(5,2), -- 环保评分
    harsh_acceleration_count INTEGER DEFAULT 0,
    harsh_braking_count INTEGER DEFAULT 0,
    speeding_duration INTEGER DEFAULT 0, -- 超速时长(秒)
    route_data JSONB, -- 路线数据
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 9. 车队管理表
CREATE TABLE IF NOT EXISTS fleet_management (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    fleet_name VARCHAR(200) NOT NULL,
    manager_id UUID REFERENCES users(id) ON DELETE CASCADE,
    company_name VARCHAR(200),
    fleet_type VARCHAR(30) CHECK (fleet_type IN ('logistics', 'taxi', 'rental', 'corporate')),
    total_vehicles INTEGER DEFAULT 0,
    active_vehicles INTEGER DEFAULT 0,
    description TEXT,
    contact_info JSONB,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 10. 车队车辆关联表
CREATE TABLE IF NOT EXISTS fleet_vehicles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    fleet_id UUID REFERENCES fleet_management(id) ON DELETE CASCADE,
    vehicle_id UUID REFERENCES vehicles(id) ON DELETE CASCADE,
    assigned_driver_id UUID REFERENCES users(id) ON DELETE SET NULL,
    assignment_date DATE DEFAULT CURRENT_DATE,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'maintenance')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(fleet_id, vehicle_id)
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_vehicles_user_id ON vehicles(user_id);
CREATE INDEX IF NOT EXISTS idx_vehicles_vin ON vehicles(vin);
CREATE INDEX IF NOT EXISTS idx_batteries_vehicle_id ON batteries(vehicle_id);
CREATE INDEX IF NOT EXISTS idx_battery_trace_battery_id ON battery_trace(battery_id);
CREATE INDEX IF NOT EXISTS idx_charging_sessions_user_id ON charging_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_charging_sessions_vehicle_id ON charging_sessions(vehicle_id);
CREATE INDEX IF NOT EXISTS idx_charging_sessions_station_id ON charging_sessions(station_id);
CREATE INDEX IF NOT EXISTS idx_carbon_credits_user_id ON carbon_credits(user_id);
CREATE INDEX IF NOT EXISTS idx_driving_behavior_user_id ON driving_behavior(user_id);
CREATE INDEX IF NOT EXISTS idx_driving_behavior_vehicle_id ON driving_behavior(vehicle_id);
CREATE INDEX IF NOT EXISTS idx_fleet_vehicles_fleet_id ON fleet_vehicles(fleet_id);
CREATE INDEX IF NOT EXISTS idx_fleet_vehicles_vehicle_id ON fleet_vehicles(vehicle_id);

-- 启用行级安全策略 (RLS)
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE vehicles ENABLE ROW LEVEL SECURITY;
ALTER TABLE batteries ENABLE ROW LEVEL SECURITY;
ALTER TABLE battery_trace ENABLE ROW LEVEL SECURITY;
ALTER TABLE charging_stations ENABLE ROW LEVEL SECURITY;
ALTER TABLE charging_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE carbon_credits ENABLE ROW LEVEL SECURITY;
ALTER TABLE driving_behavior ENABLE ROW LEVEL SECURITY;
ALTER TABLE fleet_management ENABLE ROW LEVEL SECURITY;
ALTER TABLE fleet_vehicles ENABLE ROW LEVEL SECURITY;

-- 创建RLS策略
-- 用户只能访问自己的数据
CREATE POLICY "Users can view own profile" ON users FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON users FOR UPDATE USING (auth.uid() = id);

-- 车辆访问策略
CREATE POLICY "Users can view own vehicles" ON vehicles FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can manage own vehicles" ON vehicles FOR ALL USING (auth.uid() = user_id);

-- 电池访问策略
CREATE POLICY "Users can view own vehicle batteries" ON batteries FOR SELECT USING (
    EXISTS (SELECT 1 FROM vehicles WHERE vehicles.id = batteries.vehicle_id AND vehicles.user_id = auth.uid())
);

-- 充电会话访问策略
CREATE POLICY "Users can view own charging sessions" ON charging_sessions FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can manage own charging sessions" ON charging_sessions FOR ALL USING (auth.uid() = user_id);

-- 碳积分访问策略
CREATE POLICY "Users can view own carbon credits" ON carbon_credits FOR SELECT USING (auth.uid() = user_id);

-- 驾驶行为访问策略
CREATE POLICY "Users can view own driving behavior" ON driving_behavior FOR SELECT USING (auth.uid() = user_id);

-- 充电站公开访问策略
CREATE POLICY "Charging stations are publicly viewable" ON charging_stations FOR SELECT TO authenticated USING (true);

-- 车队管理策略
CREATE POLICY "Fleet managers can manage their fleets" ON fleet_management FOR ALL USING (auth.uid() = manager_id);
CREATE POLICY "Fleet vehicles access" ON fleet_vehicles FOR SELECT USING (
    EXISTS (SELECT 1 FROM fleet_management WHERE fleet_management.id = fleet_vehicles.fleet_id AND fleet_management.manager_id = auth.uid())
);