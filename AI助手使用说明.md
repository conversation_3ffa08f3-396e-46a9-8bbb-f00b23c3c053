# EVAdmin Pro AI助手 - 使用说明

## 🤖 AI助手介绍

EVAdmin Pro AI助手（小E）是专为新能源汽车管理系统设计的智能助手，提供以下核心功能：

### ✨ 主要功能

1. **智能对话** - 回答系统使用问题，提供操作指导
2. **文件分析** - 分析上传的文本文件、JSON文件等
3. **图片识别** - 识别和分析图片内容（需要OpenAI API）
4. **系统咨询** - 提供系统功能介绍和使用建议
5. **故障诊断** - 帮助解决系统使用中的问题

### 🔧 技术架构

- **前端**: Vue 3 + TypeScript + Element Plus
- **后端**: Node.js + Express + TypeScript  
- **AI服务**: OpenAI GPT-4o（可降级到本地知识库）
- **数据库**: MySQL 8.0
- **文件处理**: Multer + 多格式支持

## 📋 使用指南

### 1. 文本对话

直接在输入框中输入问题，AI助手会基于系统知识库回答：

**示例问题：**
- "系统有哪些功能？"
- "如何查看电池状态？"
- "充电站在哪里？"
- "ADAS功能怎么使用？"

### 2. 文件上传分析

点击📎按钮上传文件进行分析：

**支持的文件类型：**
- 文本文件 (.txt)
- JSON文件 (.json)
- CSV文件 (.csv)
- 图片文件 (.jpg, .png, .gif, .webp)

**使用步骤：**
1. 点击输入框旁的📎按钮
2. 拖拽文件到上传区域或点击选择文件
3. 输入分析问题（可选）
4. 点击发送按钮

### 3. 快速问题

在欢迎页面点击预设的快速问题按钮，快速获取常见问题的答案。

## ⚙️ 配置说明

### 环境变量配置

在 `.env` 文件中配置以下参数：

```env
# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=gpt-4o
OPENAI_MAX_TOKENS=2000
OPENAI_TEMPERATURE=0.7
```

### 数据库表结构

系统使用以下数据库表：

1. **ai_chat_logs** - 聊天记录
2. **ai_knowledge_base** - 知识库
3. **ai_assistant_config** - AI助手配置

## 🚀 部署说明

### 1. 安装依赖

```bash
npm install openai multer axios
```

### 2. 数据库初始化

```bash
node check-ai-tables.js
node build-knowledge-base.js
```

### 3. 启动服务

```bash
# 启动后端服务
npm run server:dev

# 启动前端服务
npm run client:dev
```

### 4. 访问系统

- 前端地址：http://localhost:5175
- AI助手页面：http://localhost:5175/ai-assistant
- API地址：http://localhost:3001/api/ai-enhanced

## 🔍 API接口

### 聊天接口

```
POST /api/ai-enhanced/chat
Content-Type: application/json

{
  "message": "用户问题",
  "context": [] // 可选的对话历史
}
```

### 文件分析接口

```
POST /api/ai-enhanced/analyze-file
Content-Type: multipart/form-data

file: 上传的文件
question: 分析问题（可选）
```

### 状态检查接口

```
GET /api/ai-enhanced/status
```

### AI信息接口

```
GET /api/ai-enhanced/info
```

## 🛠️ 故障排除

### 常见问题

1. **OpenAI连接失败**
   - 检查API密钥是否正确
   - 检查网络连接
   - 系统会自动降级到本地知识库

2. **文件上传失败**
   - 检查文件大小（限制10MB）
   - 确认文件类型支持
   - 检查uploads目录权限

3. **前端页面无法加载**
   - 检查前端服务器是否启动
   - 确认端口5175未被占用
   - 检查浏览器控制台错误

### 日志查看

- 后端日志：查看nodemon输出
- 前端日志：浏览器开发者工具
- 数据库日志：ai_chat_logs表

## 📈 性能优化

1. **知识库优化**
   - 定期更新知识库内容
   - 优化关键词匹配
   - 调整优先级设置

2. **文件处理优化**
   - 限制文件大小
   - 异步处理大文件
   - 及时清理临时文件

3. **缓存策略**
   - AI配置缓存
   - 知识库查询缓存
   - 静态资源缓存

## 🔐 安全考虑

1. **文件上传安全**
   - 文件类型验证
   - 文件大小限制
   - 临时文件清理

2. **API安全**
   - 用户身份验证
   - 请求频率限制
   - 输入内容过滤

3. **数据安全**
   - 敏感信息脱敏
   - 聊天记录加密
   - 定期数据清理

## 📞 技术支持

如遇到问题，请联系技术支持团队，并提供：
- 错误截图
- 操作步骤
- 浏览器和系统信息
- 相关日志信息

---

**版本**: 2.0.0-local  
**更新时间**: 2025-08-20  
**维护团队**: EVAdmin Pro开发团队
