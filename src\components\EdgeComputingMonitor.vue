<template>
  <div class="edge-computing-monitor">
    <!-- 服务状态概览 -->
    <div class="service-overview">
      <el-card class="status-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <el-icon><Cpu /></el-icon>
            <span>边缘计算服务</span>
            <el-tag :type="serviceStatus.isRunning ? 'success' : 'danger'" size="small">
              {{ serviceStatus.isRunning ? '运行中' : '已停止' }}
            </el-tag>
          </div>
        </template>
        
        <div class="status-grid">
          <div class="status-item">
            <div class="status-value">{{ serviceStatus.deviceCount }}</div>
            <div class="status-label">边缘设备</div>
          </div>
          <div class="status-item">
            <div class="status-value">{{ serviceStatus.activeTaskCount }}</div>
            <div class="status-label">活跃任务</div>
          </div>
          <div class="status-item">
            <div class="status-value">{{ serviceStatus.dataBufferSize }}</div>
            <div class="status-label">缓冲数据</div>
          </div>
          <div class="status-item">
            <div class="status-value">{{ formatTime(serviceStatus.lastSyncTime) }}</div>
            <div class="status-label">最后同步</div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 设备管理 -->
    <div class="devices-section">
      <el-card class="devices-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <el-icon><Monitor /></el-icon>
            <span>边缘设备管理</span>
            <ModernButton size="small" variant="ghost" @click="refreshDevices">
              <template #icon>
                <el-icon><Refresh /></el-icon>
              </template>
              刷新
            </ModernButton>
          </div>
        </template>
        
        <div class="devices-grid">
          <div 
            v-for="device in devices" 
            :key="device.id" 
            class="device-card"
            :class="device.status"
            @click="selectDevice(device)"
          >
            <div class="device-header">
              <el-icon class="device-icon">
                <Monitor v-if="device.type === 'sensor'" />
                <Setting v-else-if="device.type === 'controller'" />
                <Connection v-else-if="device.type === 'gateway'" />
                <VideoCamera v-else-if="device.type === 'camera'" />
              </el-icon>
              <div class="device-info">
                <div class="device-name">{{ device.name }}</div>
                <div class="device-type">{{ getDeviceTypeLabel(device.type) }}</div>
              </div>
              <el-tag :type="getStatusType(device.status)" size="small">
                {{ getStatusLabel(device.status) }}
              </el-tag>
            </div>
            
            <div class="device-details">
              <div class="device-location">
                <el-icon><Location /></el-icon>
                {{ device.location.address }}
              </div>
              <div class="device-heartbeat">
                <el-icon><Timer /></el-icon>
                {{ formatTime(device.lastHeartbeat) }}
              </div>
            </div>
            
            <div class="device-capabilities">
              <el-tag 
                v-for="capability in device.capabilities" 
                :key="capability" 
                size="small" 
                class="capability-tag"
              >
                {{ capability }}
              </el-tag>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 任务管理 -->
    <div class="tasks-section">
      <el-card class="tasks-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <el-icon><List /></el-icon>
            <span>任务管理</span>
            <div class="header-actions">
              <ModernButton size="small" variant="primary" @click="showCreateTaskDialog = true">
                <template #icon>
                  <el-icon><Plus /></el-icon>
                </template>
                创建任务
              </ModernButton>
              <ModernButton size="small" variant="ghost" @click="refreshTasks">
                <template #icon>
                  <el-icon><Refresh /></el-icon>
                </template>
                刷新
              </ModernButton>
            </div>
          </div>
        </template>
        
        <el-table :data="tasks" stripe style="width: 100%">
          <el-table-column prop="name" label="任务名称" width="200" />
          <el-table-column prop="type" label="类型" width="150">
            <template #default="{ row }" v-if="row">
              {{ getTaskTypeLabel(row.type) }}
            </template>
          </el-table-column>
          <el-table-column prop="priority" label="优先级" width="100">
            <template #default="{ row }" v-if="row">
              <el-tag :type="getPriorityType(row.priority)" size="small">
                {{ getPriorityLabel(row.priority) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }" v-if="row">
              <el-tag :type="getTaskStatusType(row.status)" size="small">
                {{ getTaskStatusLabel(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="deviceId" label="设备ID" width="150" />
          <el-table-column prop="createdAt" label="创建时间" width="180">
            <template #default="{ row }" v-if="row">
              {{ formatDateTime(row.createdAt) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }" v-if="row">
              <ModernButton size="small" variant="ghost" @click="viewTaskDetails(row)">详情</ModernButton>
              <ModernButton size="small" variant="primary" @click="editTask(row)">编辑</ModernButton>
              <ModernButton size="small" variant="danger" @click="deleteTask(row.id)">删除</ModernButton>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 数据统计 -->
    <div class="data-section">
      <el-card class="data-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <el-icon><DataAnalysis /></el-icon>
            <span>数据处理统计</span>
          </div>
        </template>
        
        <div class="data-stats">
          <div class="stat-item">
            <div class="stat-value">{{ dataStats.totalProcessed }}</div>
            <div class="stat-label">已处理数据</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ dataStats.processingRate }}/s</div>
            <div class="stat-label">处理速率</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ dataStats.errorRate }}%</div>
            <div class="stat-label">错误率</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ dataStats.avgLatency }}ms</div>
            <div class="stat-label">平均延迟</div>
          </div>
        </div>
        
        <div class="data-chart">
          <ChartComponent
            title="数据处理趋势"
            type="line"
            :data="dataProcessingTrend"
            height="300px"
            :auto-refresh="true"
            :refresh-interval="10000"
          />
        </div>
      </el-card>
    </div>

    <!-- 创建任务对话框 -->
    <el-dialog 
      v-model="showCreateTaskDialog" 
      title="创建边缘计算任务" 
      width="600px"
    >
      <el-form :model="newTask" label-width="100px">
        <el-form-item label="任务名称">
          <ModernInput v-model="newTask.name" placeholder="请输入任务名称" variant="glass" />
        </el-form-item>
        <el-form-item label="任务类型">
          <el-select v-model="newTask.type" placeholder="请选择任务类型">
            <el-option label="数据处理" value="data_processing" />
            <el-option label="AI推理" value="ai_inference" />
            <el-option label="实时分析" value="real_time_analysis" />
            <el-option label="告警检测" value="alert_detection" />
          </el-select>
        </el-form-item>
        <el-form-item label="优先级">
          <el-select v-model="newTask.priority" placeholder="请选择优先级">
            <el-option label="低" value="low" />
            <el-option label="中" value="medium" />
            <el-option label="高" value="high" />
            <el-option label="紧急" value="critical" />
          </el-select>
        </el-form-item>
        <el-form-item label="目标设备">
          <el-select v-model="newTask.deviceId" placeholder="请选择目标设备">
            <el-option 
              v-for="device in devices" 
              :key="device.id" 
              :label="device.name" 
              :value="device.id" 
            />
          </el-select>
        </el-form-item>
        <el-form-item label="配置参数">
          <ModernInput 
            v-model="newTask.configJson" 
            type="textarea" 
            :rows="4" 
            placeholder="请输入JSON格式的配置参数"
            variant="glass"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <ModernButton variant="ghost" @click="showCreateTaskDialog = false">取消</ModernButton>
        <ModernButton variant="primary" @click="createTask">创建</ModernButton>
      </template>
    </el-dialog>

    <!-- 任务详情对话框 -->
    <el-dialog 
      v-model="showTaskDetailsDialog" 
      title="任务详情" 
      width="800px"
    >
      <div v-if="selectedTask" class="task-details">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="任务ID">{{ selectedTask.id }}</el-descriptions-item>
          <el-descriptions-item label="任务名称">{{ selectedTask.name }}</el-descriptions-item>
          <el-descriptions-item label="类型">{{ getTaskTypeLabel(selectedTask.type) }}</el-descriptions-item>
          <el-descriptions-item label="优先级">
            <el-tag :type="getPriorityType(selectedTask.priority)">
              {{ getPriorityLabel(selectedTask.priority) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getTaskStatusType(selectedTask.status)">
              {{ getTaskStatusLabel(selectedTask.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="设备ID">{{ selectedTask.deviceId }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDateTime(selectedTask.createdAt) }}</el-descriptions-item>
          <el-descriptions-item label="完成时间">
            {{ selectedTask.completedAt ? formatDateTime(selectedTask.completedAt) : '未完成' }}
          </el-descriptions-item>
        </el-descriptions>
        
        <div v-if="selectedTask.config" class="task-config">
          <h4>配置参数</h4>
          <pre>{{ JSON.stringify(selectedTask.config, null, 2) }}</pre>
        </div>
        
        <div v-if="selectedTask.result" class="task-result">
          <h4>执行结果</h4>
          <pre>{{ JSON.stringify(selectedTask.result, null, 2) }}</pre>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { ModernButton, ModernCard, ModernInput } from '@/components/ui'
import {
  Cpu,
  Monitor,
  Refresh,
  Setting,
  VideoCamera,
  Location,
  Timer,
  List,
  Plus,
  DataAnalysis
} from '@element-plus/icons-vue'
import { Wifi as Connection } from 'lucide-vue-next'
import { edgeComputingService, type EdgeDevice, type EdgeTask } from '@/services/edgeComputingService'
import ChartComponent from '@/components/ChartComponent.vue'

// 响应式数据
const devices = ref<EdgeDevice[]>([])
const tasks = ref<EdgeTask[]>([])
const serviceStatus = ref({
  isRunning: false,
  deviceCount: 0,
  activeTaskCount: 0,
  dataBufferSize: 0,
  lastSyncTime: new Date()
})

const dataStats = ref({
  totalProcessed: 0,
  processingRate: 0,
  errorRate: 0,
  avgLatency: 0
})

const dataProcessingTrend = ref([
  { name: '00:00', value: 120 },
  { name: '04:00', value: 80 },
  { name: '08:00', value: 150 },
  { name: '12:00', value: 280 },
  { name: '16:00', value: 320 },
  { name: '20:00', value: 380 }
])

// 对话框状态
const showCreateTaskDialog = ref(false)
const showTaskDetailsDialog = ref(false)
const selectedTask = ref<EdgeTask | null>(null)

// 新任务表单
const newTask = reactive({
  name: '',
  type: 'data_processing',
  priority: 'medium',
  deviceId: '',
  configJson: '{}'
})

// 定时器
let refreshTimer: NodeJS.Timeout | null = null

// 刷新数据
const refreshData = () => {
  devices.value = edgeComputingService.getDevices()
  tasks.value = edgeComputingService.getTasks()
  serviceStatus.value = edgeComputingService.getServiceStatus()
  
  // 更新数据统计
  updateDataStats()
}

// 更新数据统计
const updateDataStats = () => {
  const dataBuffer = edgeComputingService.getDataBuffer()
  const processedData = dataBuffer.filter(data => data.processed)
  
  dataStats.value = {
    totalProcessed: processedData.length,
    processingRate: Math.floor(Math.random() * 50) + 10,
    errorRate: Number((Math.random() * 2).toFixed(1)),
    avgLatency: Math.floor(Math.random() * 100) + 50
  }
}

// 刷新设备
const refreshDevices = async () => {
  try {
    const response = await fetch('/api/edge-devices/refresh', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    const data = await response.json();
    if (data.success) {
      devices.value = edgeComputingService.getDevices();
      ElMessage.success('设备列表已刷新');
    } else {
      ElMessage.warning(data.message || '设备刷新部分失败');
    }
  } catch (error) {
    console.error('刷新设备失败:', error);
    ElMessage.error('刷新设备失败，请检查网络连接');
  }
}

// 刷新任务
const refreshTasks = async () => {
  try {
    const response = await fetch('/api/edge-tasks/refresh', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    const data = await response.json();
    if (data.success) {
      tasks.value = edgeComputingService.getTasks();
      ElMessage.success('任务列表已刷新');
    } else {
      ElMessage.warning(data.message || '任务刷新部分失败');
    }
  } catch (error) {
    console.error('刷新任务失败:', error);
    ElMessage.error('刷新任务失败，请检查网络连接');
  }
}

// 选择设备
const selectDevice = async (device: EdgeDevice) => {
  try {
    const response = await fetch(`/api/edge-devices/${device.id}/details`);
    const data = await response.json();
    if (data.success) {
      const deviceInfo = data.data;
      ElMessageBox.alert(
        `<div style="text-align: left;">
          <h3>设备详情</h3>
          <p><strong>设备名称:</strong> ${deviceInfo.name}</p>
          <p><strong>设备类型:</strong> ${getDeviceTypeLabel(deviceInfo.type)}</p>
          <p><strong>状态:</strong> <span style="color: ${deviceInfo.status === 'online' ? '#67C23A' : '#F56C6C'}">${getStatusLabel(deviceInfo.status)}</span></p>
          <p><strong>位置:</strong> ${deviceInfo.location.address}</p>
          <p><strong>最后心跳:</strong> ${formatDateTime(new Date(deviceInfo.lastHeartbeat))}</p>
          <p><strong>功能:</strong> ${deviceInfo.capabilities.join(', ')}</p>
          <p><strong>运行时间:</strong> ${deviceInfo.uptime || '未知'}</p>
          <p><strong>CPU使用率:</strong> ${deviceInfo.cpuUsage || 0}%</p>
          <p><strong>内存使用率:</strong> ${deviceInfo.memoryUsage || 0}%</p>
        </div>`,
        '设备详情',
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '关闭'
        }
      );
    } else {
      ElMessage.error('获取设备详情失败');
    }
  } catch (error) {
    console.error('获取设备详情失败:', error);
    ElMessage.error('获取设备详情失败');
  }
}

// 创建任务
const createTask = async () => {
  try {
    let config = {}
    if (newTask.configJson.trim()) {
      try {
        config = JSON.parse(newTask.configJson)
      } catch (parseError) {
        ElMessage.error('配置JSON格式错误')
        return
      }
    }
    
    const response = await fetch('/api/edge-tasks', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        name: newTask.name,
        type: newTask.type,
        priority: newTask.priority,
        deviceId: newTask.deviceId,
        config
      })
    });
    
    const data = await response.json();
    if (data.success) {
      ElMessage.success('任务创建成功')
      showCreateTaskDialog.value = false
      
      // 重置表单
      Object.assign(newTask, {
        name: '',
        type: 'data_processing',
        priority: 'medium',
        deviceId: '',
        configJson: '{}'
      })
      
      await refreshTasks()
    } else {
      ElMessage.error(data.message || '任务创建失败')
    }
    
  } catch (error) {
    console.error('任务创建失败:', error)
    ElMessage.error('任务创建失败，请检查网络连接')
  }
}

// 执行任务
const executeTask = async (task: EdgeTask) => {
  try {
    await edgeComputingService.executeTask(task)
    ElMessage.success('任务执行成功')
    refreshTasks()
  } catch (error) {
    ElMessage.error('任务执行失败: ' + (error instanceof Error ? error.message : '未知错误'))
  }
}

// 查看任务详情
const viewTaskDetails = async (task: EdgeTask) => {
  try {
    const response = await fetch(`/api/edge-tasks/${task.id}/details`);
    const data = await response.json();
    if (data.success) {
      const taskInfo = data.data;
      ElMessageBox.alert(
        `<div style="text-align: left;">
          <h3>任务详情</h3>
          <p><strong>任务名称:</strong> ${taskInfo.name}</p>
          <p><strong>任务类型:</strong> ${getTaskTypeLabel(taskInfo.type)}</p>
          <p><strong>优先级:</strong> <span style="color: ${getPriorityColor(taskInfo.priority)}">${getPriorityLabel(taskInfo.priority)}</span></p>
          <p><strong>状态:</strong> <span style="color: ${getTaskStatusColor(taskInfo.status)}">${getTaskStatusLabel(taskInfo.status)}</span></p>
          <p><strong>设备ID:</strong> ${taskInfo.deviceId}</p>
          <p><strong>创建时间:</strong> ${formatDateTime(new Date(taskInfo.createdAt))}</p>
          <p><strong>完成时间:</strong> ${taskInfo.completedAt ? formatDateTime(new Date(taskInfo.completedAt)) : '未完成'}</p>
          <p><strong>执行进度:</strong> ${taskInfo.progress || 0}%</p>
          <p><strong>错误信息:</strong> ${taskInfo.error || '无'}</p>
          <div style="margin-top: 15px;">
            <h4>任务配置:</h4>
            <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px; font-size: 12px; max-height: 200px; overflow-y: auto;">${JSON.stringify(taskInfo.config, null, 2)}</pre>
          </div>
        </div>`,
        '任务详情',
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '关闭'
        }
      );
    } else {
      ElMessage.error('获取任务详情失败');
    }
  } catch (error) {
    console.error('获取任务详情失败:', error);
    ElMessage.error('获取任务详情失败');
  }
}

// 编辑任务
const editTask = async (task: EdgeTask) => {
  if (!task) return;
  try {
    const { value: taskData } = await ElMessageBox.prompt(
      `<div style="text-align: left;">
        <div style="margin-bottom: 15px;">
          <label>任务名称:</label>
          <input id="editTaskName" type="text" value="${task.name}" style="width: 100%; padding: 8px; margin-top: 5px; border: 1px solid #ddd; border-radius: 4px;">
        </div>
        <div style="margin-bottom: 15px;">
          <label>优先级:</label>
          <select id="editTaskPriority" style="width: 100%; padding: 8px; margin-top: 5px; border: 1px solid #ddd; border-radius: 4px;">
            <option value="low" ${task.priority === 'low' ? 'selected' : ''}>低</option>
            <option value="medium" ${task.priority === 'medium' ? 'selected' : ''}>中</option>
            <option value="high" ${task.priority === 'high' ? 'selected' : ''}>高</option>
            <option value="critical" ${task.priority === 'critical' ? 'selected' : ''}>紧急</option>
          </select>
        </div>
      </div>`,
      '编辑任务',
      {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '保存',
        cancelButtonText: '取消'
      }
    );
    
    const name = (document.getElementById('editTaskName') as HTMLInputElement)?.value;
    const priority = (document.getElementById('editTaskPriority') as HTMLSelectElement)?.value;
    
    if (name && priority) {
      await updateTask(task.id, { name, priority });
    }
  } catch (error) {
    // 用户取消操作或发生错误
    if (error !== 'cancel') {
      console.error('编辑任务失败:', error);
      ElMessage.error('编辑任务失败');
    }
  }
}

// 更新任务
const updateTask = async (taskId: string, updateData: any) => {
  try {
    const response = await fetch(`/api/edge-tasks/${taskId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(updateData)
    });
    const data = await response.json();
    if (data.success) {
      ElMessage.success('任务更新成功');
      await refreshTasks();
    } else {
      ElMessage.error(data.message || '任务更新失败');
    }
  } catch (error) {
    console.error('更新任务失败:', error);
    ElMessage.error('更新任务失败');
  }
}

// 删除任务
const deleteTask = async (taskId: string) => {
  if (!taskId) return;
  try {
    await ElMessageBox.confirm(
      '确定要删除这个任务吗？此操作不可撤销。',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    const response = await fetch(`/api/edge-tasks/${taskId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    const data = await response.json();
    if (data.success) {
      ElMessage.success('任务删除成功');
      await refreshTasks();
    } else {
      ElMessage.error(data.message || '任务删除失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除任务失败:', error);
      ElMessage.error('删除任务失败');
    }
  }
}

// 格式化时间
const formatTime = (time: Date) => {
  const now = new Date()
  const diff = now.getTime() - time.getTime()
  const minutes = Math.floor(diff / (1000 * 60))
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  
  const hours = Math.floor(minutes / 60)
  if (hours < 24) return `${hours}小时前`
  
  const days = Math.floor(hours / 24)
  return `${days}天前`
}

// 格式化日期时间
const formatDateTime = (date: Date) => {
  return date.toLocaleString('zh-CN')
}

// 获取设备类型标签
const getDeviceTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    sensor: '传感器',
    controller: '控制器',
    gateway: '网关',
    camera: '摄像头'
  }
  return labels[type] || type
}

// 获取状态类型
const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    online: 'success',
    offline: 'danger',
    error: 'warning'
  }
  return types[status] || 'info'
}

// 获取状态标签
const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    online: '在线',
    offline: '离线',
    error: '错误'
  }
  return labels[status] || status
}

// 获取任务类型标签
const getTaskTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    data_processing: '数据处理',
    ai_inference: 'AI推理',
    real_time_analysis: '实时分析',
    alert_detection: '告警检测'
  }
  return labels[type] || type
}

// 获取优先级类型
const getPriorityType = (priority: string) => {
  const types: Record<string, string> = {
    low: 'info',
    medium: 'warning',
    high: 'danger',
    critical: 'danger'
  }
  return types[priority] || 'info'
}

// 获取优先级标签
const getPriorityLabel = (priority: string) => {
  const labels: Record<string, string> = {
    low: '低',
    medium: '中',
    high: '高',
    critical: '紧急'
  }
  return labels[priority] || priority
}

// 获取任务状态类型
const getTaskStatusType = (status: string) => {
  const types: Record<string, string> = {
    pending: 'info',
    running: 'warning',
    completed: 'success',
    failed: 'danger'
  }
  return types[status] || 'info'
}

// 获取任务状态标签
const getTaskStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    pending: '待执行',
    running: '执行中',
    completed: '已完成',
    failed: '失败'
  }
  return labels[status] || status
}

// 获取优先级颜色
const getPriorityColor = (priority: string) => {
  const colors: Record<string, string> = {
    low: '#909399',
    medium: '#E6A23C',
    high: '#F56C6C',
    critical: '#F56C6C'
  }
  return colors[priority] || '#909399'
}

// 获取任务状态颜色
const getTaskStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    pending: '#909399',
    running: '#E6A23C',
    completed: '#67C23A',
    failed: '#F56C6C'
  }
  return colors[status] || '#909399'
}

// 生命周期
onMounted(() => {
  refreshData()
  
  // 启动定时刷新
  refreshTimer = setInterval(refreshData, 5000) // 每5秒刷新一次
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})
</script>

<style scoped>
.edge-computing-monitor {
  padding: 20px;
  background: #f5f7fa;
}

.service-overview {
  margin-bottom: 20px;
}

.status-card {
  border-radius: 12px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: bold;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
}

.status-item {
  text-align: center;
  padding: 15px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 8px;
}

.status-value {
  font-size: 24px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 5px;
}

.status-label {
  font-size: 14px;
  color: #7f8c8d;
}

.devices-section {
  margin-bottom: 20px;
}

.devices-card {
  border-radius: 12px;
}

.header-actions {
  display: flex;
  gap: 10px;
  margin-left: auto;
}

.devices-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 15px;
}

.device-card {
  padding: 15px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.device-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.device-card.online {
  border-color: #67c23a;
}

.device-card.offline {
  border-color: #f56c6c;
}

.device-card.error {
  border-color: #e6a23c;
}

.device-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.device-icon {
  font-size: 24px;
  color: #409eff;
}

.device-info {
  flex: 1;
}

.device-name {
  font-weight: bold;
  color: #2c3e50;
}

.device-type {
  font-size: 12px;
  color: #7f8c8d;
}

.device-details {
  margin-bottom: 10px;
}

.device-location,
.device-heartbeat {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
  color: #7f8c8d;
  margin-bottom: 5px;
}

.device-capabilities {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.capability-tag {
  font-size: 10px;
}

.tasks-section {
  margin-bottom: 20px;
}

.tasks-card {
  border-radius: 12px;
}

.data-section {
  margin-bottom: 20px;
}

.data-card {
  border-radius: 12px;
}

.data-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.stat-item {
  text-align: center;
  padding: 15px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 8px;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
  color: #7f8c8d;
}

.data-chart {
  margin-top: 20px;
}

.task-details {
  padding: 20px;
}

.task-config,
.task-result {
  margin-top: 20px;
}

.task-config h4,
.task-result h4 {
  margin-bottom: 10px;
  color: #2c3e50;
}

.task-config pre,
.task-result pre {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  font-size: 12px;
  overflow-x: auto;
}

@media (max-width: 768px) {
  .edge-computing-monitor {
    padding: 15px;
  }
  
  .status-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .devices-grid {
    grid-template-columns: 1fr;
  }
  
  .data-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .edge-computing-monitor {
    padding: 10px;
  }
  
  .status-grid,
  .data-stats {
    grid-template-columns: 1fr;
  }
}
</style>