import { Router } from 'express'
import { db, TABLES } from '../config/mysql'
import type { ChargingStation, ApiResponse, PaginatedResponse } from '../config/mysql'

const router = Router()

// 获取充电站列表
router.get('/', async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      operator, 
      power_type, 
      status,
      latitude,
      longitude,
      radius = 10 // 默认搜索半径10公里
    } = req.query
    const offset = (Number(page) - 1) * Number(limit)
    
    // 构建查询条件
    const conditions = []
    const params = []
    
    if (operator) {
      conditions.push('operator LIKE ?')
      params.push(`%${operator}%`)
    }
    
    if (power_type) {
      conditions.push('power_type = ?')
      params.push(power_type)
    }
    
    if (status) {
      conditions.push('status = ?')
      params.push(status)
    }
    
    // 如果提供了经纬度，按距离筛选
    if (latitude && longitude) {
      const lat = Number(latitude)
      const lng = Number(longitude)
      const radiusInDegrees = Number(radius) / 111 // 大约1度 = 111公里
      
      conditions.push('latitude BETWEEN ? AND ?')
      conditions.push('longitude BETWEEN ? AND ?')
      params.push(lat - radiusInDegrees, lat + radiusInDegrees)
      params.push(lng - radiusInDegrees, lng + radiusInDegrees)
    }
    
    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : ''
    
    // 获取总数
    const [countResult] = await db.execute(
      `SELECT COUNT(*) as total FROM charging_stations ${whereClause}`,
      params
    )
    const count = (countResult as any[])[0].total
    
    // 获取数据
    const [dataResult] = await db.execute(
      `SELECT * FROM charging_stations ${whereClause} ORDER BY created_at DESC LIMIT ? OFFSET ?`,
      [...params, Number(limit), offset]
    )
    const data = dataResult as any[]
    
    // 如果提供了精确坐标，计算实际距离并排序
    let processedData = data
    if (latitude && longitude && data.length > 0) {
      const lat1 = Number(latitude)
      const lng1 = Number(longitude)
      
      processedData = data
        .map(station => {
          if (station.latitude && station.longitude) {
            const distance = calculateDistance(lat1, lng1, station.latitude, station.longitude)
            return { ...station, distance }
          }
          return { ...station, distance: null }
        })
        .filter(station => station.distance === null || station.distance <= Number(radius))
        .sort((a, b) => (a.distance || 0) - (b.distance || 0))
    }
    
    const response: PaginatedResponse<ChargingStation> = {
      success: true,
      data: processedData,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: count,
        totalPages: Math.ceil(count / Number(limit))
      }
    }
    
    res.json(response)
  } catch (error) {
    console.error('获取充电站列表异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 计算两点间距离（单位：公里）
function calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
  const R = 6371 // 地球半径（公里）
  const dLat = (lat2 - lat1) * Math.PI / 180
  const dLng = (lng2 - lng1) * Math.PI / 180
  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLng / 2) * Math.sin(dLng / 2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
  return R * c
}



// 创建充电站
router.post('/', async (req, res) => {
  try {
    const {
      station_code,
      name,
      operator,
      address,
      latitude,
      longitude,
      total_ports = 1,
      available_ports,
      power_type,
      max_power,
      pricing,
      amenities,
      operating_hours
    } = req.body
    
    // 验证必填字段
    if (!station_code || !name || !address) {
      return res.status(400).json({
        success: false,
        error: '充电站编码、名称和地址为必填字段'
      } as ApiResponse)
    }
    
    // 检查充电站编码是否已存在
    const [existingResult] = await db.execute(
      'SELECT id FROM charging_stations WHERE station_code = ?',
      [station_code]
    )
    
    if ((existingResult as any[]).length > 0) {
      return res.status(400).json({
        success: false,
        error: '充电站编码已存在'
      } as ApiResponse)
    }
    
    const finalAvailablePorts = available_ports !== undefined ? available_ports : total_ports
    
    const [result] = await db.execute(
      `INSERT INTO charging_stations (
        station_code, name, operator, address, latitude, longitude,
        total_ports, available_ports, power_type, max_power, pricing,
        amenities, operating_hours, status, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', NOW(), NOW())`,
      [
        station_code, name, operator, address, latitude, longitude,
        total_ports, finalAvailablePorts, power_type, max_power,
        JSON.stringify(pricing), JSON.stringify(amenities), JSON.stringify(operating_hours)
      ]
    )
    
    const insertId = (result as any).insertId
    
    // 获取创建的充电站数据
    const [stationResult] = await db.execute(
      'SELECT * FROM charging_stations WHERE station_id = ?',
      [insertId]
    )
    const stationData = (stationResult as any[])[0]
    
    res.status(201).json({
      success: true,
      data: stationData,
      message: '充电站创建成功'
    } as ApiResponse<ChargingStation>)
  } catch (error) {
    console.error('创建充电站异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 更新充电站信息
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params
    const {
      name,
      operator,
      address,
      latitude,
      longitude,
      total_ports,
      available_ports,
      power_type,
      max_power,
      pricing,
      amenities,
      operating_hours,
      status
    } = req.body
    
    // 构建更新字段和参数
    const updateFields = []
    const params = []
    
    if (name !== undefined) {
      updateFields.push('name = ?')
      params.push(name)
    }
    if (operator !== undefined) {
      updateFields.push('operator = ?')
      params.push(operator)
    }
    if (address !== undefined) {
      updateFields.push('address = ?')
      params.push(address)
    }
    if (latitude !== undefined) {
      updateFields.push('latitude = ?')
      params.push(latitude)
    }
    if (longitude !== undefined) {
      updateFields.push('longitude = ?')
      params.push(longitude)
    }
    if (total_ports !== undefined) {
      updateFields.push('total_ports = ?')
      params.push(total_ports)
    }
    if (available_ports !== undefined) {
      updateFields.push('available_ports = ?')
      params.push(available_ports)
    }
    if (power_type !== undefined) {
      updateFields.push('power_type = ?')
      params.push(power_type)
    }
    if (max_power !== undefined) {
      updateFields.push('max_power = ?')
      params.push(max_power)
    }
    if (pricing !== undefined) {
      updateFields.push('pricing = ?')
      params.push(JSON.stringify(pricing))
    }
    if (amenities !== undefined) {
      updateFields.push('amenities = ?')
      params.push(JSON.stringify(amenities))
    }
    if (operating_hours !== undefined) {
      updateFields.push('operating_hours = ?')
      params.push(JSON.stringify(operating_hours))
    }
    if (status !== undefined) {
      updateFields.push('status = ?')
      params.push(status)
    }
    
    updateFields.push('updated_at = NOW()')
    params.push(id)
    
    if (updateFields.length === 1) { // 只有updated_at字段
      return res.status(400).json({
        success: false,
        error: '没有提供要更新的字段'
      } as ApiResponse)
    }
    
    await db.execute(
      `UPDATE charging_stations SET ${updateFields.join(', ')} WHERE station_id = ?`,
      params
    )
    
    // 获取更新后的数据
    const [result] = await db.execute(
      'SELECT * FROM charging_stations WHERE station_id = ?',
      [id]
    )
    const data = (result as any[])[0]
    
    if (!data) {
      return res.status(404).json({
        success: false,
        error: '充电站不存在'
      } as ApiResponse)
    }
    
    res.json({
      success: true,
      data,
      message: '充电站信息更新成功'
    } as ApiResponse<ChargingStation>)
  } catch (error) {
    console.error('更新充电站异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 删除充电站
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params
    
    const [result] = await db.execute(
      'DELETE FROM charging_stations WHERE station_id = ?',
      [id]
    )
    
    if ((result as any).affectedRows === 0) {
      return res.status(404).json({
        success: false,
        error: '充电站不存在'
      } as ApiResponse)
    }
    
    res.json({
      success: true,
      message: '充电站删除成功'
    } as ApiResponse)
  } catch (error) {
    console.error('删除充电站异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 更新充电站可用端口数
router.patch('/:id/ports', async (req, res) => {
  try {
    const { id } = req.params
    const { available_ports } = req.body
    
    if (available_ports === undefined) {
      return res.status(400).json({
        success: false,
        error: '可用端口数为必填字段'
      } as ApiResponse)
    }
    
    await db.execute(
      'UPDATE charging_stations SET available_ports = ?, updated_at = NOW() WHERE station_id = ?',
      [available_ports, id]
    )
    
    // 获取更新后的数据
    const [result] = await db.execute(
      'SELECT * FROM charging_stations WHERE station_id = ?',
      [id]
    )
    const data = (result as any[])[0]
    
    if (!data) {
      return res.status(404).json({
        success: false,
        error: '充电站不存在'
      } as ApiResponse)
    }
    
    res.json({
      success: true,
      data,
      message: '充电站端口数更新成功'
    } as ApiResponse<ChargingStation>)
  } catch (error) {
    console.error('更新充电站端口数异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取充电站的充电记录统计
router.get('/:id/statistics', async (req, res) => {
  try {
    const { id } = req.params
    const { start_date, end_date } = req.query
    
    // 构建查询条件
    const conditions = ['station_id = ?']
    const params = [id]
    
    if (start_date) {
      conditions.push('start_time >= ?')
      params.push(start_date)
    }
    
    if (end_date) {
      conditions.push('start_time <= ?')
      params.push(end_date)
    }
    
    const whereClause = `WHERE ${conditions.join(' AND ')}`
    
    const [result] = await db.execute(
      `SELECT * FROM charging_sessions ${whereClause}`,
      params
    )
    const sessions = result as any[]
    
    // 计算统计数据
    const totalSessions = sessions.length
    const completedSessions = sessions.filter(s => s.status === 'completed').length
    const totalEnergyDelivered = sessions.reduce((sum, s) => sum + (s.energy_delivered_kwh || 0), 0)
    const totalRevenue = sessions.reduce((sum, s) => sum + (s.total_cost || 0), 0)
    const avgSessionDuration = sessions.length > 0 
      ? sessions.reduce((sum, s) => {
          if (s.start_time && s.end_time) {
            const duration = new Date(s.end_time).getTime() - new Date(s.start_time).getTime()
            return sum + duration
          }
          return sum
        }, 0) / sessions.length / (1000 * 60) // 转换为分钟
      : 0
    
    const statistics = {
      totalSessions,
      completedSessions,
      successRate: totalSessions > 0 ? (completedSessions / totalSessions * 100).toFixed(2) : '0',
      totalEnergyDelivered: totalEnergyDelivered.toFixed(2),
      totalRevenue: totalRevenue.toFixed(2),
      avgSessionDuration: Math.round(avgSessionDuration)
    }
    
    res.json({
      success: true,
      data: statistics
    } as ApiResponse)
  } catch (error) {
    console.error('获取充电站统计异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取充电站统计数据
router.get('/stats', async (req, res) => {
  try {
    // 获取充电站总数和活跃充电站数
    const [result] = await db.execute(
      'SELECT station_id, status, available_ports, total_ports FROM charging_stations'
    )
    const stations = result as any[]

    const totalStations = stations.length
    const activeStations = stations.filter(s => s.status === 'active').length
    
    // 计算总利用率
    const totalPorts = stations.reduce((sum, station) => sum + (station.total_ports || 0), 0)
    const availablePorts = stations.reduce((sum, station) => sum + (station.available_ports || 0), 0)
    const utilization = totalPorts > 0 ? Number(((totalPorts - availablePorts) / totalPorts * 100).toFixed(1)) : 0

    res.json({
      success: true,
      data: {
        stations: totalStations,
        activeStations,
        utilization,
        totalPorts,
        availablePorts,
        busyPorts: totalPorts - availablePorts
      }
    } as ApiResponse)
  } catch (error) {
    console.error('获取充电站统计异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取附近充电站
router.get('/nearby', async (req, res) => {
  try {
    const { 
      latitude, 
      longitude, 
      radius = 10 // 默认10公里
    } = req.query
    
    if (!latitude || !longitude) {
      return res.status(400).json({
        success: false,
        error: '缺少经纬度参数'
      } as ApiResponse)
    }
    
    const lat = Number(latitude)
    const lng = Number(longitude)
    const radiusInDegrees = Number(radius) / 111 // 大约1度 = 111公里
    
    const [result] = await db.execute(
      `SELECT * FROM charging_stations 
       WHERE latitude >= ? AND latitude <= ? 
       AND longitude >= ? AND longitude <= ? 
       AND status = 'active'`,
      [lat - radiusInDegrees, lat + radiusInDegrees, lng - radiusInDegrees, lng + radiusInDegrees]
    )
    const stations = result as any[]
    
    // 计算距离并排序
    const stationsWithDistance = stations.map(station => {
      const distance = calculateDistance(lat, lng, station.latitude, station.longitude)
      return {
        ...station,
        distance: Number(distance.toFixed(2)),
        // 模拟充电桩端口数据
        ports: Array.from({ length: station.total_ports }, (_, i) => ({
          id: `${station.station_id}-port-${i + 1}`,
          number: i + 1,
          status: i < station.available_ports ? 'available' : 'busy'
        }))
      }
    }).filter(station => station.distance <= Number(radius))
      .sort((a, b) => a.distance - b.distance)
    
    res.json({
      success: true,
      data: stationsWithDistance
    } as ApiResponse)
  } catch (error) {
    console.error('获取附近充电站异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取充电站概览数据
router.get('/overview', async (req, res) => {
  try {
    // 获取充电站统计
    const [stationsResult] = await db.execute(
      'SELECT station_id, status, total_ports, available_ports FROM charging_stations'
    )
    const stations = stationsResult as any[]
    
    // 获取今日充电会话数据
    const today = new Date().toISOString().split('T')[0]
    const [sessionsResult] = await db.execute(
      `SELECT session_id, energy_delivered_kwh, total_cost, status 
       FROM charging_sessions 
       WHERE start_time >= ?`,
      [today]
    )
    const sessions = sessionsResult as any[]
    
    // 计算统计数据
    const totalStations = stations.length || 0
    const availableStations = stations.filter(s => s.status === 'active' && s.available_ports > 0).length || 0
    const chargingSessions = sessions ? sessions.filter(s => s.status === 'active').length : 0
    const totalPorts = stations.reduce((sum, s) => sum + s.total_ports, 0) || 0
    const availablePorts = stations.reduce((sum, s) => sum + s.available_ports, 0) || 0
    const utilizationRate = totalPorts > 0 ? Number(((totalPorts - availablePorts) / totalPorts * 100).toFixed(1)) : 0
    
    const todayRevenue = sessions?.reduce((sum, s) => sum + (s.total_cost || 0), 0) || 0
    const energyDelivered = sessions?.reduce((sum, s) => sum + (s.energy_delivered_kwh || 0), 0) || 0
    const avgPower = sessions?.length > 0 ? Number((energyDelivered / sessions.length).toFixed(1)) : 0
    
    const overview = {
      totalStations,
      availableStations,
      chargingSessions,
      utilizationRate,
      todayRevenue: Number(todayRevenue.toFixed(2)),
      revenueGrowth: 12.3, // 模拟增长率
      energyDelivered: Number(energyDelivered.toFixed(1)),
      avgPower
    }
    
    res.json({
      success: true,
      data: overview
    } as ApiResponse)
  } catch (error) {
    console.error('获取充电站概览异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 搜索充电站
router.get('/search', async (req, res) => {
  try {
    const { location, radius = 5, status, fastCharge } = req.query
    const filters = { status, fastCharge: fastCharge === 'true' }
    
    // 这里可以集成地理编码服务来将地址转换为经纬度
    // 暂时返回模拟数据
    const mockStations = [
      {
        id: 'station-1',
        name: '万达广场充电站',
        address: '北京市朝阳区建国路93号万达广场',
        latitude: 39.9042,
        longitude: 116.4074,
        distance: 1.2,
        status: 'available',
        total_ports: 8,
        available_ports: 3,
        power_type: 'DC',
        max_power: 120
      },
      {
        id: 'station-2', 
        name: '国贸中心充电站',
        address: '北京市朝阳区建国门外大街1号',
        latitude: 39.9088,
        longitude: 116.4317,
        distance: 2.1,
        status: 'available',
        total_ports: 12,
        available_ports: 7,
        power_type: 'DC',
        max_power: 180
      }
    ]
    
    res.json({
      success: true,
      data: mockStations
    } as ApiResponse)
  } catch (error) {
    console.error('搜索充电站异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 快速充电站推荐
router.get('/quick-charge', async (req, res) => {
  try {
    const { latitude, longitude, powerRequirement = 'fast', maxDistance = 3, urgency = 'high' } = req.query
    
    if (!latitude || !longitude) {
      return res.status(400).json({
        success: false,
        error: '缺少位置信息'
      } as ApiResponse)
    }
    
    // 获取高功率充电站
    const [result] = await db.execute(
      `SELECT * FROM charging_stations 
       WHERE status = 'active' 
       AND power_type = 'DC' 
       AND max_power >= 100 
       AND available_ports > 0 
       ORDER BY max_power DESC`
    )
    const stations = result as any[]
    
    if (stations.length === 0) {
      return res.json({
        success: false,
        error: '暂无可用的快速充电站'
      } as ApiResponse)
    }
    
    // 计算距离并筛选
    const userLat = Number(latitude)
    const userLng = Number(longitude)
    const maxDist = Number(maxDistance)
    
    const nearbyStations = stations
      .map(station => ({
        ...station,
        distance: calculateDistance(userLat, userLng, station.latitude, station.longitude)
      }))
      .filter(station => station.distance <= maxDist)
      .sort((a, b) => {
        // 根据紧急程度排序：距离、可用端口数、功率
        if (urgency === 'high') {
          return a.distance - b.distance
        }
        return (b.available_ports - a.available_ports) || (b.max_power - a.max_power)
      })
    
    if (nearbyStations.length === 0) {
      return res.json({
        success: false,
        error: `${maxDist}公里范围内暂无可用的快速充电站`
      } as ApiResponse)
    }
    
    // 选择最佳充电站
    const recommendedStation = nearbyStations[0]
    const estimatedTime = Math.round(recommendedStation.distance * 3) // 假设每公里3分钟
    
    res.json({
      success: true,
      data: {
        station: recommendedStation,
        estimatedTime: `${estimatedTime}分钟`,
        distance: `${recommendedStation.distance.toFixed(1)}km`,
        message: `为您推荐${recommendedStation.name}，预计${estimatedTime}分钟到达`
      }
    } as ApiResponse)
  } catch (error) {
    console.error('快速充电推荐异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 导航到充电站
router.get('/navigate', async (req, res) => {
  try {
    const { stationId, latitude, longitude, name, address } = req.query
    const destination = { latitude: Number(latitude), longitude: Number(longitude), name, address }
    
    if (!stationId || !destination) {
      return res.status(400).json({
        success: false,
        error: '缺少必要参数'
      } as ApiResponse)
    }
    
    // 这里可以集成实际的导航服务
    // 暂时返回成功响应
    res.json({
      success: true,
      data: {
        navigationUrl: `https://maps.google.com/maps?daddr=${destination.latitude},${destination.longitude}`,
        estimatedTime: '12分钟',
        distance: '3.2km'
      },
      message: '导航已启动'
    } as ApiResponse)
  } catch (error) {
    console.error('导航异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取充电站使用率分布数据
router.get('/utilization-distribution', async (req, res) => {
  try {
    const [result] = await db.execute(
      'SELECT station_id, name, available_ports, total_ports, status FROM charging_stations WHERE status = "active"'
    )
    const stations = result as any[]

    // 计算每个充电站的使用率
    const utilizationData = stations.map(station => {
      const utilization = station.total_ports > 0 ? 
        Number(((station.total_ports - station.available_ports) / station.total_ports * 100).toFixed(1)) : 0
      
      return {
        name: station.name || `充电站${station.station_id}`,
        value: utilization,
        max: 100
      }
    })

    res.json({
      success: true,
      data: utilizationData
    } as ApiResponse)
  } catch (error) {
    console.error('获取充电站使用率分布异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

// 获取单个充电站信息 - 必须放在最后，避免与其他路由冲突
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params
    
    const [stationResult] = await db.execute(
      'SELECT * FROM charging_stations WHERE station_id = ?',
      [id]
    )
    const stations = stationResult as any[]
    
    if (stations.length === 0) {
      return res.status(404).json({
        success: false,
        error: '充电站不存在'
      } as ApiResponse)
    }
    
    const station = stations[0]
    
    // 获取相关的充电会话数据
    const [sessionsResult] = await db.execute(
      `SELECT cs.session_id, cs.session_code, cs.start_time, cs.end_time, cs.energy_delivered_kwh, cs.status,
              v.make, v.model, v.license_plate
       FROM charging_sessions cs
       LEFT JOIN vehicles v ON cs.vehicle_id = v.vehicle_id
       WHERE cs.station_id = ?`,
      [id]
    )
    const sessions = sessionsResult as any[]
    
    const data = {
      ...station,
      charging_sessions: sessions
    }
    
    res.json({
      success: true,
      data
    } as ApiResponse<ChargingStation>)
  } catch (error) {
    console.error('获取充电站信息异常:', error)
    res.status(500).json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse)
  }
})

export default router