<template>
  <div class="modern-animations">
    <!-- 波纹效果容器 -->
    <div 
      v-if="showRipple" 
      class="ripple-container"
      :style="rippleContainerStyle"
    >
      <div 
        class="ripple-effect"
        :style="rippleStyle"
        @animationend="onRippleEnd"
      />
    </div>
    
    <!-- 粒子效果容器 -->
    <div 
      v-if="showParticles" 
      class="particles-container"
      :style="particlesContainerStyle"
    >
      <div 
        v-for="particle in particles" 
        :key="particle.id"
        class="particle"
        :style="getParticleStyle(particle)"
        @animationend="onParticleEnd(particle.id)"
      />
    </div>
    
    <!-- 发光效果容器 -->
    <div 
      v-if="showGlow" 
      class="glow-container"
      :style="glowContainerStyle"
    >
      <div 
        class="glow-effect"
        :style="glowStyle"
        @animationend="onGlowEnd"
      />
    </div>
    
    <!-- 震动效果容器 -->
    <div 
      v-if="showShake" 
      class="shake-container"
      :style="shakeContainerStyle"
    >
      <div 
        class="shake-effect"
        @animationend="onShakeEnd"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue'

// Props 定义
interface Props {
  // 波纹效果
  rippleColor?: string
  rippleDuration?: number
  rippleSize?: number
  
  // 粒子效果
  particleCount?: number
  particleColor?: string
  particleSize?: number
  particleDuration?: number
  
  // 发光效果
  glowColor?: string
  glowIntensity?: number
  glowDuration?: number
  
  // 震动效果
  shakeIntensity?: number
  shakeDuration?: number
}

const props = withDefaults(defineProps<Props>(), {
  rippleColor: '#667eea',
  rippleDuration: 600,
  rippleSize: 100,
  particleCount: 12,
  particleColor: '#667eea',
  particleSize: 4,
  particleDuration: 800,
  glowColor: '#667eea',
  glowIntensity: 0.8,
  glowDuration: 500,
  shakeIntensity: 5,
  shakeDuration: 300
})

// Emits 定义
interface Emits {
  rippleComplete: []
  particlesComplete: []
  glowComplete: []
  shakeComplete: []
}

const emit = defineEmits<Emits>()

// 响应式数据
const showRipple = ref(false)
const showParticles = ref(false)
const showGlow = ref(false)
const showShake = ref(false)

const ripplePosition = ref({ x: 0, y: 0 })
const particlesPosition = ref({ x: 0, y: 0 })
const glowPosition = ref({ x: 0, y: 0 })
const shakePosition = ref({ x: 0, y: 0 })

const particles = ref<Array<{
  id: number
  angle: number
  distance: number
  delay: number
}>>([]))

let particleIdCounter = 0

// 计算属性
const rippleContainerStyle = computed(() => ({
  position: 'fixed',
  top: '0',
  left: '0',
  width: '100vw',
  height: '100vh',
  pointerEvents: 'none',
  zIndex: '9999'
}))

const rippleStyle = computed(() => ({
  position: 'absolute',
  left: `${ripplePosition.value.x - props.rippleSize / 2}px`,
  top: `${ripplePosition.value.y - props.rippleSize / 2}px`,
  width: `${props.rippleSize}px`,
  height: `${props.rippleSize}px`,
  borderRadius: '50%',
  background: `radial-gradient(circle, ${props.rippleColor}40 0%, ${props.rippleColor}20 50%, transparent 100%)`,
  animation: `rippleAnimation ${props.rippleDuration}ms ease-out`,
  transform: 'scale(0)',
  opacity: '0'
}))

const particlesContainerStyle = computed(() => ({
  position: 'fixed',
  top: '0',
  left: '0',
  width: '100vw',
  height: '100vh',
  pointerEvents: 'none',
  zIndex: '9998'
}))

const glowContainerStyle = computed(() => ({
  position: 'fixed',
  top: '0',
  left: '0',
  width: '100vw',
  height: '100vh',
  pointerEvents: 'none',
  zIndex: '9997'
}))

const glowStyle = computed(() => ({
  position: 'absolute',
  left: `${glowPosition.value.x - 50}px`,
  top: `${glowPosition.value.y - 50}px`,
  width: '100px',
  height: '100px',
  borderRadius: '50%',
  background: `radial-gradient(circle, ${props.glowColor}${Math.round(props.glowIntensity * 255).toString(16)} 0%, transparent 70%)`,
  animation: `glowAnimation ${props.glowDuration}ms ease-out`,
  transform: 'scale(0)',
  opacity: '0'
}))

const shakeContainerStyle = computed(() => ({
  position: 'fixed',
  top: '0',
  left: '0',
  width: '100vw',
  height: '100vh',
  pointerEvents: 'none',
  zIndex: '9996'
}))

// 方法
const getParticleStyle = (particle: any) => ({
  position: 'absolute',
  left: `${particlesPosition.value.x}px`,
  top: `${particlesPosition.value.y}px`,
  width: `${props.particleSize}px`,
  height: `${props.particleSize}px`,
  borderRadius: '50%',
  background: props.particleColor,
  animation: `particleAnimation ${props.particleDuration}ms ease-out ${particle.delay}ms`,
  transform: `rotate(${particle.angle}deg) translateX(${particle.distance}px)`,
  opacity: '0'
})

// 触发波纹效果
const triggerRipple = (x: number, y: number) => {
  ripplePosition.value = { x, y }
  showRipple.value = true
}

// 触发粒子效果
const triggerParticles = (x: number, y: number) => {
  particlesPosition.value = { x, y }
  particles.value = []
  
  for (let i = 0; i < props.particleCount; i++) {
    particles.value.push({
      id: particleIdCounter++,
      angle: (360 / props.particleCount) * i,
      distance: 50 + Math.random() * 30,
      delay: Math.random() * 100
    })
  }
  
  showParticles.value = true
}

// 触发发光效果
const triggerGlow = (x: number, y: number) => {
  glowPosition.value = { x, y }
  showGlow.value = true
}

// 触发震动效果
const triggerShake = (element?: HTMLElement) => {
  if (element) {
    const rect = element.getBoundingClientRect()
    shakePosition.value = {
      x: rect.left + rect.width / 2,
      y: rect.top + rect.height / 2
    }
  }
  showShake.value = true
}

// 组合效果
const triggerClickEffect = (event: MouseEvent) => {
  const { clientX, clientY } = event
  triggerRipple(clientX, clientY)
  
  nextTick(() => {
    setTimeout(() => {
      triggerParticles(clientX, clientY)
    }, 100)
  })
}

const triggerHoverEffect = (event: MouseEvent) => {
  const { clientX, clientY } = event
  triggerGlow(clientX, clientY)
}

const triggerErrorEffect = (element?: HTMLElement) => {
  triggerShake(element)
}

const triggerSuccessEffect = (event: MouseEvent) => {
  const { clientX, clientY } = event
  triggerRipple(clientX, clientY)
  triggerGlow(clientX, clientY)
}

// 事件处理
const onRippleEnd = () => {
  showRipple.value = false
  emit('rippleComplete')
}

const onParticleEnd = (particleId: number) => {
  particles.value = particles.value.filter(p => p.id !== particleId)
  if (particles.value.length === 0) {
    showParticles.value = false
    emit('particlesComplete')
  }
}

const onGlowEnd = () => {
  showGlow.value = false
  emit('glowComplete')
}

const onShakeEnd = () => {
  showShake.value = false
  emit('shakeComplete')
}

// 暴露方法
defineExpose({
  triggerRipple,
  triggerParticles,
  triggerGlow,
  triggerShake,
  triggerClickEffect,
  triggerHoverEffect,
  triggerErrorEffect,
  triggerSuccessEffect
})
</script>

<style scoped>
.modern-animations {
  position: relative;
}

/* 波纹动画 */
@keyframes rippleAnimation {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  50% {
    transform: scale(0.5);
    opacity: 0.8;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

/* 粒子动画 */
@keyframes particleAnimation {
  0% {
    transform: rotate(var(--angle)) translateX(0) scale(1);
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    transform: rotate(var(--angle)) translateX(var(--distance)) scale(0);
    opacity: 0;
  }
}

/* 发光动画 */
@keyframes glowAnimation {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.5);
    opacity: 1;
  }
  100% {
    transform: scale(3);
    opacity: 0;
  }
}

/* 震动动画 */
.shake-effect {
  animation: shakeAnimation var(--shake-duration, 300ms) ease-in-out;
}

@keyframes shakeAnimation {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(calc(var(--shake-intensity, 5) * -1px));
  }
  20%, 40%, 60%, 80% {
    transform: translateX(calc(var(--shake-intensity, 5) * 1px));
  }
}

/* 脉冲效果 */
.pulse-effect {
  animation: pulseAnimation 1s ease-in-out infinite;
}

@keyframes pulseAnimation {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 呼吸效果 */
.breathe-effect {
  animation: breatheAnimation 3s ease-in-out infinite;
}

@keyframes breatheAnimation {
  0%, 100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.02);
    opacity: 1;
  }
}

/* 浮动效果 */
.float-effect {
  animation: floatAnimation 4s ease-in-out infinite;
}

@keyframes floatAnimation {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* 旋转发光效果 */
.rotate-glow-effect {
  animation: rotateGlowAnimation 2s linear infinite;
}

@keyframes rotateGlowAnimation {
  0% {
    transform: rotate(0deg);
    filter: hue-rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
    filter: hue-rotate(360deg);
  }
}

/* 弹性缩放效果 */
.elastic-scale-effect {
  animation: elasticScaleAnimation 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes elasticScaleAnimation {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

/* 滑入效果 */
.slide-in-effect {
  animation: slideInAnimation 0.5s ease-out;
}

@keyframes slideInAnimation {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 淡入效果 */
.fade-in-effect {
  animation: fadeInAnimation 0.5s ease-out;
}

@keyframes fadeInAnimation {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 翻转效果 */
.flip-effect {
  animation: flipAnimation 0.6s ease-in-out;
}

@keyframes flipAnimation {
  0% {
    transform: rotateY(0);
  }
  50% {
    transform: rotateY(90deg);
  }
  100% {
    transform: rotateY(0);
  }
}

/* 响应式适配 */
@media (max-width: 768px) {
  .ripple-effect {
    width: 60px !important;
    height: 60px !important;
  }
  
  .particle {
    width: 3px !important;
    height: 3px !important;
  }
  
  .glow-effect {
    width: 60px !important;
    height: 60px !important;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .modern-animations *,
  .modern-animations *::before,
  .modern-animations *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
</style>