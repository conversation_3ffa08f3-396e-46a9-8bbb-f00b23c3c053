<template>
  <div class="min-h-screen bg-gray-100 dark:bg-gray-900 flex">
    <!-- 左侧菜单栏 -->
    <aside class="w-64 bg-white dark:bg-gray-800 shadow-lg flex flex-col">
      <!-- Logo区域 -->
      <div class="h-16 flex items-center px-6 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center">
          <div class="w-8 h-8 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center">
            <span class="text-white font-bold text-sm">EV</span>
          </div>
          <span class="ml-3 text-lg font-semibold text-gray-900 dark:text-white">
            EVAdmin Pro
          </span>
        </div>
      </div>

      <!-- 菜单导航 -->
      <nav class="flex-1 px-4 py-6 space-y-2">
        <router-link 
          to="/dashboard" 
          class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200"
          :class="$route.path === '/dashboard' ? 'bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400' : 'text-gray-700 hover:bg-gray-50 dark:text-gray-300 dark:hover:bg-gray-700'"
        >
          <el-icon class="w-5 h-5 mr-3">
            <Monitor />
          </el-icon>
          首页
        </router-link>

        <div class="pt-4">
          <h3 class="px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-3">
            数据管理
          </h3>
          <router-link 
            to="/vehicle-network" 
            class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200"
            :class="$route.path === '/vehicle-network' ? 'bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400' : 'text-gray-700 hover:bg-gray-50 dark:text-gray-300 dark:hover:bg-gray-700'"
          >
            <el-icon class="w-5 h-5 mr-3">
              <DataBoard />
            </el-icon>
            数据查询
          </router-link>

          <router-link 
            to="/battery-management" 
            class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200"
            :class="$route.path === '/battery-management' ? 'bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400' : 'text-gray-700 hover:bg-gray-50 dark:text-gray-300 dark:hover:bg-gray-700'"
          >
            <el-icon class="w-5 h-5 mr-3">
              <Document />
            </el-icon>
            新增凭证
          </router-link>

          <router-link 
            to="/charging-service" 
            class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200"
            :class="$route.path === '/charging-service' ? 'bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400' : 'text-gray-700 hover:bg-gray-50 dark:text-gray-300 dark:hover:bg-gray-700'"
          >
            <el-icon class="w-5 h-5 mr-3">
              <ChatDotRound />
            </el-icon>
            余额查询
          </router-link>
        </div>

        <div class="pt-4">
          <h3 class="px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-3">
            列表管理
          </h3>
          <router-link 
            to="/ai-assistant" 
            class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200"
            :class="$route.path === '/ai-assistant' ? 'bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400' : 'text-gray-700 hover:bg-gray-50 dark:text-gray-300 dark:hover:bg-gray-700'"
          >
            <el-icon class="w-5 h-5 mr-3">
              <Files />
            </el-icon>
            财务报表
          </router-link>

          <router-link 
            to="/adas" 
            class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200"
            :class="$route.path === '/adas' ? 'bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400' : 'text-gray-700 hover:bg-gray-50 dark:text-gray-300 dark:hover:bg-gray-700'"
          >
            <el-icon class="w-5 h-5 mr-3">
              <TrendCharts />
            </el-icon>
            数据统计
          </router-link>
        </div>

        <div class="pt-4">
          <h3 class="px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-3">
            条件管理
          </h3>
          <router-link 
            to="/system-security" 
            class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200"
            :class="$route.path === '/system-security' ? 'bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400' : 'text-gray-700 hover:bg-gray-50 dark:text-gray-300 dark:hover:bg-gray-700'"
          >
            <el-icon class="w-5 h-5 mr-3">
              <Setting />
            </el-icon>
            条件管理
          </router-link>

          <div class="flex items-center px-4 py-3 text-sm font-medium text-gray-700 dark:text-gray-300">
            <el-icon class="w-5 h-5 mr-3">
              <Histogram />
            </el-icon>
            多级菜单
          </div>

          <div class="flex items-center px-4 py-3 text-sm font-medium text-gray-700 dark:text-gray-300">
            <el-icon class="w-5 h-5 mr-3">
              <User />
            </el-icon>
            组织管理
          </div>

          <div class="flex items-center px-4 py-3 text-sm font-medium text-gray-700 dark:text-gray-300">
            <el-icon class="w-5 h-5 mr-3">
              <Notification />
            </el-icon>
            自定义指令
          </div>

          <div class="flex items-center px-4 py-3 text-sm font-medium text-gray-700 dark:text-gray-300">
            <el-icon class="w-5 h-5 mr-3">
              <Connection />
            </el-icon>
            外部链接
          </div>

          <div class="flex items-center px-4 py-3 text-sm font-medium text-gray-700 dark:text-gray-300">
            <el-icon class="w-5 h-5 mr-3">
              <Tools />
            </el-icon>
            权限测试
          </div>

          <div class="flex items-center px-4 py-3 text-sm font-medium text-gray-700 dark:text-gray-300">
            <el-icon class="w-5 h-5 mr-3">
              <MapLocation />
            </el-icon>
            高德地图
          </div>

          <div class="flex items-center px-4 py-3 text-sm font-medium text-gray-700 dark:text-gray-300">
            <el-icon class="w-5 h-5 mr-3">
              <Flag />
            </el-icon>
            国际化
          </div>

          <div class="flex items-center px-4 py-3 text-sm font-medium text-gray-700 dark:text-gray-300">
            <el-icon class="w-5 h-5 mr-3">
              <InfoFilled />
            </el-icon>
            关于项目
          </div>
        </div>
      </nav>

      <!-- 底部用户信息 -->
      <div class="p-4 border-t border-gray-200 dark:border-gray-700">
        <div class="flex items-center">
          <div class="w-8 h-8 bg-gradient-to-r from-green-500 to-blue-600 rounded-full flex items-center justify-center">
            <span class="text-white text-sm font-medium">超</span>
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-900 dark:text-white">超级管理员</p>
            <p class="text-xs text-gray-500 dark:text-gray-400"><EMAIL></p>
          </div>
        </div>
      </div>
    </aside>

    <!-- 右侧主内容区域 -->
    <div class="flex-1 flex flex-col">
      <!-- 顶部导航栏 -->
      <header class="h-16 bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 flex items-center justify-between px-6">
        <div class="flex items-center">
          <nav class="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
            <span>首页</span>
          </nav>
        </div>
        
        <div class="flex items-center space-x-4">
          <el-button size="small" class="text-gray-600 dark:text-gray-300">
            <el-icon class="mr-1">
              <Search />
            </el-icon>
          </el-button>
          <el-button size="small" class="text-gray-600 dark:text-gray-300">
            <el-icon class="mr-1">
              <FullScreen />
            </el-icon>
          </el-button>
          <ThemeToggle />
          <el-button size="small" class="text-gray-600 dark:text-gray-300">
            <el-icon class="mr-1">
              <Bell />
            </el-icon>
          </el-button>
          <el-button size="small" class="text-gray-600 dark:text-gray-300">
            <el-icon class="mr-1">
              <FullScreen />
            </el-icon>
          </el-button>
          <el-button size="small" class="text-gray-600 dark:text-gray-300">
            <el-icon class="mr-1">
              <Setting />
            </el-icon>
          </el-button>
          <el-button size="small" class="text-gray-600 dark:text-gray-300">
            <el-icon class="mr-1">
              <Message />
            </el-icon>
          </el-button>
          <div class="flex items-center space-x-2">
            <div class="w-6 h-6 bg-gradient-to-r from-green-500 to-blue-600 rounded-full flex items-center justify-center">
              <span class="text-white text-xs font-medium">超</span>
            </div>
            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">超级管理员</span>
            <el-icon class="text-gray-400">
              <ArrowDown />
            </el-icon>
          </div>
        </div>
      </header>

      <!-- 主要内容区域 -->
      <main class="flex-1 p-6 bg-gray-50 dark:bg-gray-900 overflow-auto">
        <router-view />
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import ThemeToggle from './ThemeToggle.vue'
import { useRoute } from 'vue-router'
import { 
  Monitor, DataBoard, Document, ChatDotRound, Files, TrendCharts, 
  Setting, Histogram, User, Notification, Connection, Tools, 
  MapLocation, Flag, InfoFilled, Search, FullScreen, Bell, 
  Message, ArrowDown 
} from '@element-plus/icons-vue'

const route = useRoute()

const getPageTitle = () => {
  const titles: Record<string, string> = {
    '/dashboard': '仪表盘',
    '/vehicle-network': '数据查询',
    '/battery-management': '新增凭证',
    '/charging-service': '余额查询',
    '/ai-assistant': '财务报表',
    '/adas': '数据统计',
    '/system-security': '条件管理'
  }
  return titles[route.path] || '首页'
}
</script>

<style scoped>
/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 暗色模式滚动条 */
.dark ::-webkit-scrollbar-track {
  background: #374151;
}

.dark ::-webkit-scrollbar-thumb {
  background: #6b7280;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}
</style>