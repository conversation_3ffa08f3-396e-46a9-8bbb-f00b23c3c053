const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

async function demoAIFeatures() {
  console.log('🎯 EVAdmin Pro AI助手功能演示\n');
  
  const baseURL = 'http://localhost:3001/api/ai-enhanced';
  
  try {
    // 1. 获取AI助手信息
    console.log('1️⃣ 获取AI助手信息...');
    const infoResponse = await axios.get(`${baseURL}/info`);
    const aiInfo = infoResponse.data.data;
    
    console.log(`✅ AI助手: ${aiInfo.name} (${aiInfo.fullName})`);
    console.log(`📝 描述: ${aiInfo.description}`);
    console.log(`🔧 模型: ${aiInfo.model}`);
    console.log(`⚡ 功能: ${aiInfo.capabilities.join(', ')}\n`);
    
    // 2. 检查服务状态
    console.log('2️⃣ 检查服务状态...');
    const statusResponse = await axios.get(`${baseURL}/status`);
    console.log(`✅ 状态: ${statusResponse.data.status}`);
    console.log(`📡 OpenAI连接: ${statusResponse.data.openaiConnected ? '已连接' : '未连接'}`);
    console.log(`🔄 模式: ${statusResponse.data.mode || 'standard'}\n`);
    
    // 3. 演示智能对话
    console.log('3️⃣ 演示智能对话功能...');
    const questions = [
      '系统有哪些核心功能？',
      '电池健康度如何计算？',
      '如何查找充电站？',
      '车辆保养提醒怎么设置？',
      'ADAS功能如何开启？'
    ];
    
    for (let i = 0; i < questions.length; i++) {
      const question = questions[i];
      console.log(`\n💬 问题 ${i + 1}: ${question}`);
      
      try {
        const chatResponse = await axios.post(`${baseURL}/chat`, {
          message: question,
          context: []
        });
        
        if (chatResponse.data.success) {
          const answer = chatResponse.data.response;
          console.log(`🤖 回答: ${answer.substring(0, 150)}${answer.length > 150 ? '...' : ''}`);
          console.log(`⏱️ 响应时间: ${new Date(chatResponse.data.timestamp).toLocaleTimeString()}`);
        } else {
          console.log(`❌ 回答失败: ${chatResponse.data.message}`);
        }
      } catch (error) {
        console.log(`❌ 请求失败: ${error.message}`);
      }
      
      // 添加延迟避免请求过快
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // 4. 演示文件分析功能
    console.log('\n\n4️⃣ 演示文件分析功能...');
    
    if (fs.existsSync('test-document.txt')) {
      console.log('📄 分析测试文档...');
      
      const formData = new FormData();
      formData.append('file', fs.createReadStream('test-document.txt'));
      formData.append('question', '这个文档介绍了什么系统？请总结主要功能模块。');
      
      try {
        const fileResponse = await axios.post(`${baseURL}/analyze-file`, formData, {
          headers: {
            ...formData.getHeaders()
          },
          timeout: 15000
        });
        
        if (fileResponse.data.success) {
          console.log('✅ 文件分析成功');
          console.log(`📁 文件: ${fileResponse.data.fileName} (${fileResponse.data.fileType})`);
          console.log(`🔍 分析结果: ${fileResponse.data.response.substring(0, 200)}...`);
        } else {
          console.log(`❌ 文件分析失败: ${fileResponse.data.message}`);
        }
      } catch (error) {
        console.log(`❌ 文件分析请求失败: ${error.message}`);
      }
    } else {
      console.log('⚠️ 测试文档不存在，跳过文件分析演示');
    }
    
    // 5. 演示对话上下文
    console.log('\n\n5️⃣ 演示对话上下文功能...');
    
    const contextMessages = [
      { type: 'user', content: '我想了解电池管理功能' },
      { type: 'ai', content: '电池管理功能包括实时监控、健康度分析、充电优化等。' }
    ];
    
    try {
      const contextResponse = await axios.post(`${baseURL}/chat`, {
        message: '刚才提到的健康度分析具体是怎么工作的？',
        context: contextMessages
      });
      
      if (contextResponse.data.success) {
        console.log('✅ 上下文对话成功');
        console.log(`🤖 回答: ${contextResponse.data.response.substring(0, 150)}...`);
      } else {
        console.log(`❌ 上下文对话失败: ${contextResponse.data.message}`);
      }
    } catch (error) {
      console.log(`❌ 上下文对话请求失败: ${error.message}`);
    }
    
    console.log('\n🎉 AI助手功能演示完成！');
    console.log('\n📖 使用说明：');
    console.log('- 访问 http://localhost:5175/ai-assistant 体验完整功能');
    console.log('- 当前运行在本地模式，配置OpenAI API密钥可获得更智能的回答');
    console.log('- 支持文件上传分析，最大文件大小10MB');
    console.log('- 所有对话和文件分析都会记录到数据库中');
    
  } catch (error) {
    console.error('❌ 演示失败:', error.message);
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('错误信息:', error.response.data);
    }
  }
}

demoAIFeatures();
