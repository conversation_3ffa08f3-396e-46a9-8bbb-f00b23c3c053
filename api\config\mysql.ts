import mysql from 'mysql2/promise'
import { config } from 'dotenv'

// 加载环境变量
config()

// MySQL数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'ev_management',
  charset: 'utf8mb4',
  timezone: '+08:00'
}

// 检查MySQL是否可用
let mysqlAvailable = false;
let pool: mysql.Pool | null = null;

// 初始化MySQL连接
async function initializeMySQL() {
  try {
    pool = mysql.createPool({
      ...dbConfig,
      waitForConnections: true,
      connectionLimit: 10,
      queueLimit: 0
    });
    
    // 测试连接
    await pool.execute('SELECT 1');
    mysqlAvailable = true;
    console.log('✅ MySQL 连接成功');
  } catch (error) {
    console.warn('⚠️  MySQL 连接失败，将使用模拟数据:', error.message);
    mysqlAvailable = false;
    pool = null;
  }
}

// 立即初始化
initializeMySQL();

export { pool }

// 数据库表名常量
export const TABLES = {
  USERS: 'users',
  VEHICLES: 'vehicles',
  BATTERIES: 'batteries',
  BATTERY_TRACE: 'battery_trace',
  CHARGING_STATIONS: 'charging_stations',
  CHARGING_SESSIONS: 'charging_sessions',
  CARBON_CREDIT: 'carbon_credit',
  DRIVING_BEHAVIOR: 'driving_behavior',
  FLEET_MANAGEMENT: 'fleet_management',
  FLEET_VEHICLES: 'fleet_vehicles',
  EDGE_DEVICES: 'edge_devices',
  EDGE_DEVICE_DATA: 'edge_device_data',
  EDGE_TASKS: 'edge_tasks'
} as const

// 数据库操作工具类
export class DatabaseService {
  private static instance: DatabaseService
  
  private constructor() {}
  
  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService()
    }
    return DatabaseService.instance
  }
  
  // 执行查询
  async query(sql: string, params?: any[]): Promise<any> {
    try {
      if (!pool || !mysqlAvailable) {
        console.warn('MySQL 不可用，返回模拟数据');
        return [];
      }
      const [rows] = await pool.query(sql, params)
      return rows
    } catch (error) {
      console.error('数据库查询错误:', error)
      // 对于参数错误，直接抛出异常而不是返回空数组
      if (error.code === 'ER_WRONG_ARGUMENTS') {
        throw error;
      }
      // 对于连接问题，尝试重新连接
      if (error.code === 'PROTOCOL_CONNECTION_LOST') {
        console.log('尝试重新初始化MySQL连接...');
        await initializeMySQL();
      }
      return [];
    }
  }

  // 执行查询（兼容mysql2的execute方法）
  async execute(sql: string, params?: any[]): Promise<any> {
    try {
      if (!pool || !mysqlAvailable) {
        console.warn('MySQL 不可用，返回模拟数据');
        return [[], []];
      }
      return await pool.execute(sql, params)
    } catch (error) {
      console.error('数据库执行错误:', error)
      // 对于参数错误，直接抛出异常
      if (error.code === 'ER_WRONG_ARGUMENTS') {
        throw error;
      }
      // 对于连接问题，尝试重新连接
      if (error.code === 'PROTOCOL_CONNECTION_LOST') {
        console.log('尝试重新初始化MySQL连接...');
        await initializeMySQL();
      }
      return [[], []];
    }
  }
  
  // 执行事务
  async transaction(callback: (connection: any) => Promise<any>): Promise<any> {
    if (!pool || !mysqlAvailable) {
      console.warn('MySQL 不可用，跳过事务操作');
      return null;
    }
    
    const connection = await pool.getConnection()
    try {
      await connection.beginTransaction()
      const result = await callback(connection)
      await connection.commit()
      return result
    } catch (error) {
      await connection.rollback()
      mysqlAvailable = false;
      throw error
    } finally {
      connection.release()
    }
  }
  
  // 分页查询
  async paginate(sql: string, params: any[], page: number = 1, limit: number = 10) {
    if (!pool || !mysqlAvailable) {
      console.warn('MySQL 不可用，返回模拟分页数据');
      return {
        data: [],
        pagination: {
          page,
          limit,
          total: 0,
          totalPages: 0
        }
      };
    }
    
    try {
      const offset = (page - 1) * limit
      
      // 获取总数
      const countSql = `SELECT COUNT(*) as total FROM (${sql}) as count_table`
      const [countResult] = await this.query(countSql, params) as any[]
      const total = countResult.total
      
      // 获取分页数据
      const dataSql = `${sql} LIMIT ? OFFSET ?`
      const data = await this.query(dataSql, [...params, limit, offset])
      
      return {
        data,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      }
    } catch (error) {
      console.error('分页查询错误:', error);
      mysqlAvailable = false;
      return {
        data: [],
        pagination: {
          page,
          limit,
          total: 0,
          totalPages: 0
        }
      };
    }
  }
  
  // 插入数据
  async insert(table: string, data: Record<string, any>): Promise<any> {
    if (!pool || !mysqlAvailable) {
      console.warn('MySQL 不可用，模拟插入操作');
      return { insertId: Math.floor(Math.random() * 1000), affectedRows: 1 };
    }
    
    try {
      const keys = Object.keys(data)
      const values = Object.values(data)
      const placeholders = keys.map(() => '?').join(', ')
      
      const sql = `INSERT INTO ${table} (${keys.join(', ')}) VALUES (${placeholders})`
      const result = await this.query(sql, values)
      return result
    } catch (error) {
      console.error('插入数据错误:', error);
      mysqlAvailable = false;
      return { insertId: Math.floor(Math.random() * 1000), affectedRows: 1 };
    }
  }
  
  // 更新数据
  async update(table: string, data: Record<string, any>, where: Record<string, any>): Promise<any> {
    if (!pool || !mysqlAvailable) {
      console.warn('MySQL 不可用，模拟更新操作');
      return { affectedRows: 1 };
    }
    
    try {
      const setClause = Object.keys(data).map(key => `${key} = ?`).join(', ')
      const whereClause = Object.keys(where).map(key => `${key} = ?`).join(' AND ')
      
      const sql = `UPDATE ${table} SET ${setClause} WHERE ${whereClause}`
      const params = [...Object.values(data), ...Object.values(where)]
      
      return await this.query(sql, params)
    } catch (error) {
      console.error('更新数据错误:', error);
      mysqlAvailable = false;
      return { affectedRows: 1 };
    }
  }
  
  // 删除数据
  async delete(table: string, where: Record<string, any>): Promise<any> {
    if (!pool || !mysqlAvailable) {
      console.warn('MySQL 不可用，模拟删除操作');
      return { affectedRows: 1 };
    }
    
    try {
      const whereClause = Object.keys(where).map(key => `${key} = ?`).join(' AND ')
      const sql = `DELETE FROM ${table} WHERE ${whereClause}`
      
      return await this.query(sql, Object.values(where))
    } catch (error) {
      console.error('删除数据错误:', error);
      mysqlAvailable = false;
      return { affectedRows: 1 };
    }
  }
  
  // 查找单条记录
  async findOne(table: string, where: Record<string, any>, select: string = '*'): Promise<any> {
    if (!pool || !mysqlAvailable) {
      console.warn('MySQL 不可用，返回模拟数据');
      return null;
    }
    
    try {
      const whereClause = Object.keys(where).map(key => `${key} = ?`).join(' AND ')
      const sql = `SELECT ${select} FROM ${table} WHERE ${whereClause} LIMIT 1`
      
      const result = await this.query(sql, Object.values(where))
      return result[0] || null
    } catch (error) {
      console.error('查找单条记录错误:', error);
      mysqlAvailable = false;
      return null;
    }
  }
  
  // 查找多条记录
  async findMany(table: string, where: Record<string, any> = {}, select: string = '*', orderBy: string = ''): Promise<any[]> {
    if (!pool || !mysqlAvailable) {
      console.warn('MySQL 不可用，返回模拟数据');
      return [];
    }
    
    try {
      let sql = `SELECT ${select} FROM ${table}`
      const params: any[] = []
      
      if (Object.keys(where).length > 0) {
        const whereClause = Object.keys(where).map(key => `${key} = ?`).join(' AND ')
        sql += ` WHERE ${whereClause}`
        params.push(...Object.values(where))
      }
      
      if (orderBy) {
        sql += ` ORDER BY ${orderBy}`
      }
      
      return await this.query(sql, params)
    } catch (error) {
      console.error('查找多条记录错误:', error);
      mysqlAvailable = false;
      return [];
    }
  }
}

// 导出数据库服务实例
export const db = DatabaseService.getInstance()

// 类型定义（保持与Supabase相同的接口）
export interface User {
  id: string
  email: string
  phone?: string
  name: string
  avatar_url?: string
  user_type: 'individual' | 'enterprise' | 'fleet'
  status: 'active' | 'inactive' | 'suspended'
  created_at: string
  updated_at: string
}

export interface Vehicle {
  id: string
  user_id: string
  vin: string
  license_plate?: string
  brand: string
  model: string
  year?: number
  color?: string
  vehicle_type: 'bev' | 'phev' | 'fcev'
  battery_capacity?: number
  max_range?: number
  status: 'active' | 'maintenance' | 'retired'
  location?: any
  created_at: string
  updated_at: string
}

export interface Battery {
  id: string
  vehicle_id: string
  battery_id: string
  manufacturer?: string
  model?: string
  capacity: number
  voltage?: number
  chemistry?: string
  manufacture_date?: string
  warranty_end_date?: string
  current_soh: number
  current_soc?: number
  temperature?: number
  cycle_count: number
  status: 'normal' | 'warning' | 'critical' | 'maintenance'
  created_at: string
  updated_at: string
}

export interface ChargingStation {
  id: string
  station_code: string
  name: string
  operator?: string
  address: string
  latitude?: number
  longitude?: number
  total_ports: number
  available_ports: number
  power_type?: 'ac' | 'dc' | 'mixed'
  max_power?: number
  pricing?: any
  amenities?: string[]
  operating_hours?: any
  status: 'active' | 'maintenance' | 'offline'
  created_at: string
  updated_at: string
}

export interface ChargingSession {
  id: string
  user_id: string
  vehicle_id: string
  station_id: string
  session_code: string
  start_time: string
  end_time?: string
  start_soc?: number
  end_soc?: number
  energy_delivered?: number
  peak_power?: number
  total_cost?: number
  payment_method?: string
  payment_status: 'pending' | 'completed' | 'failed' | 'refunded'
  session_status: 'active' | 'completed' | 'interrupted' | 'failed'
  created_at: string
  updated_at: string
}

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T = any> {
  success: boolean
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
  error?: string
}

// 测试数据库连接
export async function testConnection(): Promise<boolean> {
  try {
    if (!pool) {
      console.error('MySQL连接池未初始化')
      return false
    }
    await pool.execute('SELECT 1')
    mysqlAvailable = true;
    console.log('MySQL数据库连接成功')
    return true
  } catch (error) {
    console.error('MySQL数据库连接失败:', error)
    mysqlAvailable = false;
    return false
  }
}

export default db