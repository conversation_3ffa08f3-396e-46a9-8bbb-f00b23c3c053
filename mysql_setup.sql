-- =============================================
-- 新能源汽车智能综合管理系统 - MySQL完整设置脚本
-- =============================================

-- 1. 创建数据库
CREATE DATABASE IF NOT EXISTS ev_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE ev_management;

-- 2. 用户表
CREATE TABLE IF NOT EXISTS users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id VARCHAR(50) UNIQUE NOT NULL,
    username VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    role ENUM('admin', 'user', 'operator') DEFAULT 'user',
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    avatar_url TEXT,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_status (status)
);

-- 3. 车辆表
CREATE TABLE IF NOT EXISTS vehicles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    vehicle_id VARCHAR(50) UNIQUE NOT NULL,
    owner_id VARCHAR(50) NOT NULL,
    make VARCHAR(100) NOT NULL,
    model VARCHAR(100) NOT NULL,
    year INT NOT NULL,
    vin VARCHAR(17) UNIQUE NOT NULL,
    license_plate VARCHAR(20) UNIQUE NOT NULL,
    color VARCHAR(50),
    battery_capacity DECIMAL(8,2),
    range_km INT,
    status ENUM('active', 'maintenance', 'charging', 'inactive') DEFAULT 'active',
    current_location JSON,
    last_service_date DATE,
    next_service_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_vehicle_id (vehicle_id),
    INDEX idx_owner_id (owner_id),
    INDEX idx_vin (vin),
    INDEX idx_license_plate (license_plate),
    INDEX idx_status (status),
    FOREIGN KEY (owner_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- 4. 电池表
CREATE TABLE IF NOT EXISTS batteries (
    id INT PRIMARY KEY AUTO_INCREMENT,
    battery_id VARCHAR(50) UNIQUE NOT NULL,
    vehicle_id VARCHAR(50) NOT NULL,
    manufacturer VARCHAR(100) NOT NULL,
    model VARCHAR(100) NOT NULL,
    capacity_kwh DECIMAL(8,2) NOT NULL,
    current_charge DECIMAL(5,2) DEFAULT 0,
    health_percentage DECIMAL(5,2) DEFAULT 100,
    cycle_count INT DEFAULT 0,
    temperature DECIMAL(5,2),
    voltage DECIMAL(8,2),
    status ENUM('normal', 'warning', 'critical', 'maintenance') DEFAULT 'normal',
    last_charged TIMESTAMP NULL,
    warranty_expiry DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_battery_id (battery_id),
    INDEX idx_vehicle_id (vehicle_id),
    INDEX idx_status (status),
    INDEX idx_health (health_percentage),
    FOREIGN KEY (vehicle_id) REFERENCES vehicles(vehicle_id) ON DELETE CASCADE
);

-- 5. 电池溯源记录表
CREATE TABLE IF NOT EXISTS battery_trace (
    id INT PRIMARY KEY AUTO_INCREMENT,
    battery_id VARCHAR(50) NOT NULL,
    event_type ENUM('manufacture', 'install', 'maintenance', 'replace', 'recycle') NOT NULL,
    event_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    location VARCHAR(255),
    operator VARCHAR(100),
    description TEXT,
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_battery_id (battery_id),
    INDEX idx_event_type (event_type),
    INDEX idx_event_date (event_date),
    FOREIGN KEY (battery_id) REFERENCES batteries(battery_id) ON DELETE CASCADE
);

-- 6. 充电站表
CREATE TABLE IF NOT EXISTS charging_stations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    station_id VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    address TEXT NOT NULL,
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    total_ports INT NOT NULL DEFAULT 1,
    available_ports INT NOT NULL DEFAULT 1,
    power_output_kw DECIMAL(8,2) NOT NULL,
    pricing_per_kwh DECIMAL(8,4) NOT NULL,
    operator VARCHAR(100),
    status ENUM('active', 'maintenance', 'offline') DEFAULT 'active',
    amenities JSON,
    operating_hours JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_station_id (station_id),
    INDEX idx_location (latitude, longitude),
    INDEX idx_status (status),
    INDEX idx_available_ports (available_ports)
);

-- 7. 充电会话表
CREATE TABLE IF NOT EXISTS charging_sessions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    session_id VARCHAR(50) UNIQUE NOT NULL,
    vehicle_id VARCHAR(50) NOT NULL,
    station_id VARCHAR(50) NOT NULL,
    user_id VARCHAR(50) NOT NULL,
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP NULL,
    energy_delivered_kwh DECIMAL(8,2) DEFAULT 0,
    cost DECIMAL(10,2) DEFAULT 0,
    payment_method VARCHAR(50),
    status ENUM('active', 'completed', 'interrupted', 'failed') DEFAULT 'active',
    start_battery_level DECIMAL(5,2),
    end_battery_level DECIMAL(5,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_session_id (session_id),
    INDEX idx_vehicle_id (vehicle_id),
    INDEX idx_station_id (station_id),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_start_time (start_time),
    FOREIGN KEY (vehicle_id) REFERENCES vehicles(vehicle_id) ON DELETE CASCADE,
    FOREIGN KEY (station_id) REFERENCES charging_stations(station_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- 8. 碳积分表
CREATE TABLE IF NOT EXISTS carbon_credit (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id VARCHAR(50) NOT NULL,
    vehicle_id VARCHAR(50) NOT NULL,
    credits_earned DECIMAL(10,2) NOT NULL,
    activity_type ENUM('charging', 'driving', 'referral', 'bonus') NOT NULL,
    activity_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    co2_saved_kg DECIMAL(8,2),
    distance_km DECIMAL(8,2),
    description TEXT,
    status ENUM('pending', 'approved', 'redeemed') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_vehicle_id (vehicle_id),
    INDEX idx_activity_type (activity_type),
    INDEX idx_status (status),
    INDEX idx_activity_date (activity_date),
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (vehicle_id) REFERENCES vehicles(vehicle_id) ON DELETE CASCADE
);

-- 9. 驾驶行为表
CREATE TABLE IF NOT EXISTS driving_behavior (
    id INT PRIMARY KEY AUTO_INCREMENT,
    vehicle_id VARCHAR(50) NOT NULL,
    user_id VARCHAR(50) NOT NULL,
    trip_id VARCHAR(50) UNIQUE NOT NULL,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP NOT NULL,
    distance_km DECIMAL(8,2) NOT NULL,
    avg_speed_kmh DECIMAL(5,2),
    max_speed_kmh DECIMAL(5,2),
    energy_consumption_kwh DECIMAL(8,2),
    efficiency_km_per_kwh DECIMAL(5,2),
    harsh_braking_count INT DEFAULT 0,
    rapid_acceleration_count INT DEFAULT 0,
    speeding_duration_minutes INT DEFAULT 0,
    eco_score DECIMAL(5,2),
    route_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_vehicle_id (vehicle_id),
    INDEX idx_user_id (user_id),
    INDEX idx_trip_id (trip_id),
    INDEX idx_start_time (start_time),
    INDEX idx_eco_score (eco_score),
    FOREIGN KEY (vehicle_id) REFERENCES vehicles(vehicle_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- 10. 车队管理表
CREATE TABLE IF NOT EXISTS fleet_management (
    id INT PRIMARY KEY AUTO_INCREMENT,
    fleet_id VARCHAR(50) UNIQUE NOT NULL,
    fleet_name VARCHAR(255) NOT NULL,
    company_name VARCHAR(255) NOT NULL,
    manager_id VARCHAR(50) NOT NULL,
    total_vehicles INT DEFAULT 0,
    active_vehicles INT DEFAULT 0,
    fleet_type ENUM('corporate', 'rental', 'delivery', 'taxi') NOT NULL,
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    contact_email VARCHAR(255),
    contact_phone VARCHAR(20),
    address TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_fleet_id (fleet_id),
    INDEX idx_manager_id (manager_id),
    INDEX idx_fleet_type (fleet_type),
    INDEX idx_status (status),
    FOREIGN KEY (manager_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- 11. 车队车辆关联表
CREATE TABLE IF NOT EXISTS fleet_vehicles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    fleet_id VARCHAR(50) NOT NULL,
    vehicle_id VARCHAR(50) NOT NULL,
    assigned_driver_id VARCHAR(50),
    assignment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('assigned', 'available', 'maintenance', 'retired') DEFAULT 'assigned',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_fleet_vehicle (fleet_id, vehicle_id),
    INDEX idx_fleet_id (fleet_id),
    INDEX idx_vehicle_id (vehicle_id),
    INDEX idx_driver_id (assigned_driver_id),
    INDEX idx_status (status),
    FOREIGN KEY (fleet_id) REFERENCES fleet_management(fleet_id) ON DELETE CASCADE,
    FOREIGN KEY (vehicle_id) REFERENCES vehicles(vehicle_id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_driver_id) REFERENCES users(user_id) ON DELETE SET NULL
);

-- 12. 边缘设备表
CREATE TABLE IF NOT EXISTS edge_devices (
    id INT PRIMARY KEY AUTO_INCREMENT,
    device_id VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    type ENUM('monitoring', 'tracking', 'data_collection', 'control') NOT NULL,
    status ENUM('online', 'offline', 'maintenance', 'error') DEFAULT 'offline',
    location VARCHAR(255),
    ip_address VARCHAR(45),
    firmware_version VARCHAR(50),
    last_heartbeat TIMESTAMP NULL,
    configuration JSON,
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_device_id (device_id),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_location (location),
    INDEX idx_last_heartbeat (last_heartbeat)
);

-- 13. 边缘设备数据表
CREATE TABLE IF NOT EXISTS edge_device_data (
    id INT PRIMARY KEY AUTO_INCREMENT,
    device_id VARCHAR(50) NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_type VARCHAR(100) NOT NULL,
    value JSON NOT NULL,
    quality_score DECIMAL(3,2) DEFAULT 1.00,
    processed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_device_id (device_id),
    INDEX idx_timestamp (timestamp),
    INDEX idx_data_type (data_type),
    INDEX idx_processed (processed),
    FOREIGN KEY (device_id) REFERENCES edge_devices(device_id) ON DELETE CASCADE
);

-- 14. 边缘任务表
CREATE TABLE IF NOT EXISTS edge_tasks (
    id INT PRIMARY KEY AUTO_INCREMENT,
    task_id VARCHAR(50) UNIQUE NOT NULL,
    device_id VARCHAR(50) NOT NULL,
    task_type VARCHAR(100) NOT NULL,
    priority ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
    status ENUM('pending', 'running', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
    parameters JSON,
    result JSON,
    scheduled_at TIMESTAMP NULL,
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    error_message TEXT,
    retry_count INT DEFAULT 0,
    max_retries INT DEFAULT 3,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_task_id (task_id),
    INDEX idx_device_id (device_id),
    INDEX idx_status (status),
    INDEX idx_priority (priority),
    INDEX idx_scheduled_at (scheduled_at),
    FOREIGN KEY (device_id) REFERENCES edge_devices(device_id) ON DELETE CASCADE
);

-- =============================================
-- 创建视图
-- =============================================

-- 车辆详情视图
CREATE OR REPLACE VIEW vehicle_details AS
SELECT 
    v.*,
    u.username as owner_name,
    u.email as owner_email,
    b.battery_id,
    b.current_charge,
    b.health_percentage,
    b.status as battery_status
FROM vehicles v
LEFT JOIN users u ON v.owner_id = u.user_id
LEFT JOIN batteries b ON v.vehicle_id = b.vehicle_id;

-- 充电会话统计视图
CREATE OR REPLACE VIEW charging_session_stats AS
SELECT 
    cs.*,
    v.make,
    v.model,
    v.license_plate,
    st.name as station_name,
    st.address as station_address,
    u.username,
    TIMESTAMPDIFF(MINUTE, cs.start_time, cs.end_time) as duration_minutes
FROM charging_sessions cs
LEFT JOIN vehicles v ON cs.vehicle_id = v.vehicle_id
LEFT JOIN charging_stations st ON cs.station_id = st.station_id
LEFT JOIN users u ON cs.user_id = u.user_id;

-- 边缘设备状态视图
CREATE OR REPLACE VIEW edge_device_status AS
SELECT 
    ed.*,
    COUNT(edd.id) as data_points_today,
    MAX(edd.timestamp) as last_data_time,
    COUNT(et.id) as pending_tasks
FROM edge_devices ed
LEFT JOIN edge_device_data edd ON ed.device_id = edd.device_id 
    AND DATE(edd.timestamp) = CURDATE()
LEFT JOIN edge_tasks et ON ed.device_id = et.device_id 
    AND et.status = 'pending'
GROUP BY ed.id;

-- =============================================
-- 插入示例数据
-- =============================================

-- 插入用户数据
INSERT INTO users (user_id, username, email, password_hash, phone, role) VALUES
('USR001', '张三', '<EMAIL>', '$2b$10$hash1', '13800138001', 'user'),
('USR002', '李四', '<EMAIL>', '$2b$10$hash2', '13800138002', 'user'),
('USR003', '王五', '<EMAIL>', '$2b$10$hash3', '13800138003', 'operator'),
('USR004', '管理员', '<EMAIL>', '$2b$10$hash4', '13800138004', 'admin');

-- 插入车辆数据
INSERT INTO vehicles (vehicle_id, owner_id, make, model, year, vin, license_plate, color, battery_capacity, range_km) VALUES
('VEH001', 'USR001', '特斯拉', 'Model 3', 2023, '1HGBH41JXMN109186', '京A12345', '白色', 75.0, 500),
('VEH002', 'USR002', '比亚迪', '汉EV', 2023, '2HGBH41JXMN109187', '京B67890', '黑色', 85.4, 550),
('VEH003', 'USR003', '蔚来', 'ES6', 2023, '3HGBH41JXMN109188', '京C11111', '蓝色', 100.0, 610);

-- 插入电池数据
INSERT INTO batteries (battery_id, vehicle_id, manufacturer, model, capacity_kwh, current_charge, health_percentage) VALUES
('BAT001', 'VEH001', '松下', 'NCR18650B', 75.0, 85.5, 98.2),
('BAT002', 'VEH002', '比亚迪', 'Blade Battery', 85.4, 72.3, 99.1),
('BAT003', 'VEH003', '宁德时代', 'NCM811', 100.0, 90.8, 97.5);

-- 插入充电站数据
INSERT INTO charging_stations (station_id, name, address, latitude, longitude, total_ports, available_ports, power_output_kw, pricing_per_kwh) VALUES
('CS001', '北京朝阳充电站', '北京市朝阳区建国路88号', 39.9042, 116.4074, 8, 6, 120.0, 1.2),
('CS002', '上海浦东充电站', '上海市浦东新区世纪大道1000号', 31.2304, 121.4737, 12, 10, 150.0, 1.1),
('CS003', '深圳南山充电站', '深圳市南山区科技园南区', 22.5431, 113.9344, 6, 4, 100.0, 1.3);

-- 插入边缘设备数据
INSERT INTO edge_devices (device_id, name, type, status, location, ip_address) VALUES
('EDGE001', '充电站监控设备A', 'monitoring', 'online', '北京市朝阳区', '*************'),
('EDGE002', '车辆追踪设备B', 'tracking', 'online', '上海市浦东新区', '*************'),
('EDGE003', '数据采集设备C', 'data_collection', 'offline', '深圳市南山区', '*************');

-- 插入边缘设备数据
INSERT INTO edge_device_data (device_id, data_type, value) VALUES
('EDGE001', 'sensor', '{"temperature": 25.5, "humidity": 60, "power": 85}'),
('EDGE002', 'location', '{"lat": 39.9042, "lng": 116.4074, "speed": 45}'),
('EDGE003', 'status', '{"cpu": 45, "memory": 70, "disk": 30}');

-- =============================================
-- 完成提示
-- =============================================
SELECT '数据库初始化完成！' as message;
SELECT COUNT(*) as total_tables FROM information_schema.tables WHERE table_schema = 'ev_management';
SELECT table_name FROM information_schema.tables WHERE table_schema = 'ev_management' ORDER BY table_name;