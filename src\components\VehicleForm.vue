<template>
  <el-dialog
    v-model="dialogVisible"
    :title="mode === 'add' ? '添加车辆' : '编辑车辆'"
    width="600px"
    @close="$emit('close')"
    class="vehicle-form-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="loading"
      element-loading-text="处理中..."
    >
      <!-- 基本信息 -->
      <div class="form-section">
        <h4 class="section-title">基本信息</h4>
        
        <el-form-item label="车辆品牌" prop="brand">
          <el-select v-model="formData.brand" placeholder="请选择品牌" style="width: 100%">
            <el-option label="特斯拉" value="Tesla" />
            <el-option label="比亚迪" value="BYD" />
            <el-option label="蔚来" value="NIO" />
            <el-option label="小鹏" value="XPeng" />
            <el-option label="理想" value="Li Auto" />
            <el-option label="其他" value="Other" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="车辆型号" prop="model">
          <el-input v-model="formData.model" placeholder="请输入车辆型号" />
        </el-form-item>
        
        <el-form-item label="VIN码" prop="vin">
          <el-input 
            v-model="formData.vin" 
            placeholder="请输入17位VIN码"
            maxlength="17"
            show-word-limit
          />
        </el-form-item>
        
        <el-form-item label="车牌号" prop="license_plate">
          <el-input v-model="formData.license_plate" placeholder="请输入车牌号" />
        </el-form-item>
      </div>

      <!-- 详细信息 -->
      <div class="form-section">
        <h4 class="section-title">详细信息</h4>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="生产年份" prop="year">
              <el-date-picker
                v-model="formData.year"
                type="year"
                placeholder="选择年份"
                style="width: 100%"
                value-format="YYYY"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="车身颜色" prop="color">
              <el-input v-model="formData.color" placeholder="请输入颜色" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="车辆类型" prop="vehicle_type">
          <el-radio-group v-model="formData.vehicle_type">
            <el-radio label="bev">纯电动</el-radio>
            <el-radio label="phev">插电混动</el-radio>
            <el-radio label="fcev">燃料电池</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="电池容量" prop="battery_capacity">
              <el-input-number
                v-model="formData.battery_capacity"
                :min="10"
                :max="200"
                :step="0.1"
                placeholder="kWh"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="续航里程" prop="max_range">
              <el-input-number
                v-model="formData.max_range"
                :min="100"
                :max="1000"
                :step="10"
                placeholder="km"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="车辆状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio label="active">在线</el-radio>
            <el-radio label="maintenance">维护中</el-radio>
            <el-radio label="retired">已退役</el-radio>
          </el-radio-group>
        </el-form-item>
      </div>

      <!-- 所有者信息 -->
      <div class="form-section" v-if="mode === 'add'">
        <h4 class="section-title">所有者信息</h4>
        
        <el-form-item label="所有者" prop="user_id">
          <el-select 
            v-model="formData.user_id" 
            placeholder="请选择车辆所有者"
            style="width: 100%"
            filterable
            remote
            :remote-method="searchUsers"
            :loading="userSearchLoading"
          >
            <el-option
              v-for="user in userOptions"
              :key="user.id"
              :label="`${user.username} (${user.email})`"
              :value="user.id"
            />
          </el-select>
        </el-form-item>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="$emit('close')">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          {{ mode === 'add' ? '添加车辆' : '保存修改' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, reactive } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { useVehicleStore } from '@/stores/vehicle'
import type { Vehicle } from '@/stores/vehicle'

// Props
const props = defineProps<{
  modelValue: boolean
  vehicleId?: string
  mode: 'add' | 'edit'
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'success': []
  'close': []
}>()

// Store
const vehicleStore = useVehicleStore()

// 响应式数据
const loading = ref(false)
const userSearchLoading = ref(false)
const formRef = ref<FormInstance>()
const userOptions = ref<any[]>([])

// 表单数据
const formData = reactive({
  brand: '',
  model: '',
  vin: '',
  license_plate: '',
  year: '',
  color: '',
  vehicle_type: 'bev' as 'bev' | 'phev' | 'fcev',
  battery_capacity: null as number | null,
  max_range: null as number | null,
  status: 'active' as 'active' | 'maintenance' | 'retired',
  user_id: ''
})

// 表单验证规则
const formRules: FormRules = {
  brand: [
    { required: true, message: '请选择车辆品牌', trigger: 'change' }
  ],
  model: [
    { required: true, message: '请输入车辆型号', trigger: 'blur' }
  ],
  vin: [
    { required: true, message: '请输入VIN码', trigger: 'blur' },
    { min: 17, max: 17, message: 'VIN码必须为17位', trigger: 'blur' },
    { pattern: /^[A-HJ-NPR-Z0-9]{17}$/, message: 'VIN码格式不正确', trigger: 'blur' }
  ],
  license_plate: [
    { pattern: /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]$/, message: '车牌号格式不正确', trigger: 'blur' }
  ],
  user_id: [
    { required: true, message: '请选择车辆所有者', trigger: 'change' }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 监听器
watch(() => props.vehicleId, async (newId) => {
  if (newId && props.mode === 'edit' && props.modelValue) {
    await loadVehicleData(newId)
  }
})

watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    if (props.mode === 'add') {
      resetForm()
    }
    loadUsers()
  }
})

// 方法
const loadVehicleData = async (vehicleId: string) => {
  loading.value = true
  try {
    await vehicleStore.fetchVehicle(vehicleId)
    const vehicle = vehicleStore.currentVehicle
    if (vehicle) {
      Object.assign(formData, {
        brand: vehicle.brand,
        model: vehicle.model,
        vin: vehicle.vin,
        license_plate: vehicle.license_plate || '',
        year: vehicle.year?.toString() || '',
        color: vehicle.color || '',
        vehicle_type: vehicle.vehicle_type,
        battery_capacity: vehicle.battery_capacity,
        max_range: vehicle.max_range,
        status: vehicle.status,
        user_id: vehicle.user_id
      })
    }
  } catch (error) {
    ElMessage.error('加载车辆数据失败')
  } finally {
    loading.value = false
  }
}

const loadUsers = async () => {
  try {
    // 模拟用户数据，实际应该调用用户API
    userOptions.value = [
      { id: 'user-001', username: '张三', email: '<EMAIL>' },
      { id: 'user-002', username: '李四', email: '<EMAIL>' },
      { id: 'user-003', username: '王五', email: '<EMAIL>' }
    ]
  } catch (error) {
    console.error('加载用户列表失败:', error)
  }
}

const searchUsers = async (query: string) => {
  if (!query) return
  userSearchLoading.value = true
  try {
    // 模拟搜索用户，实际应该调用搜索API
    await new Promise(resolve => setTimeout(resolve, 300))
    userOptions.value = userOptions.value.filter(user => 
      user.username.includes(query) || user.email.includes(query)
    )
  } catch (error) {
    console.error('搜索用户失败:', error)
  } finally {
    userSearchLoading.value = false
  }
}

const resetForm = () => {
  Object.assign(formData, {
    brand: '',
    model: '',
    vin: '',
    license_plate: '',
    year: '',
    color: '',
    vehicle_type: 'bev',
    battery_capacity: null,
    max_range: null,
    status: 'active',
    user_id: ''
  })
  formRef.value?.clearValidate()
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    loading.value = true
    
    const submitData = {
      ...formData,
      year: formData.year ? parseInt(formData.year) : undefined
    }
    
    if (props.mode === 'add') {
      await vehicleStore.createVehicle(submitData)
    } else if (props.vehicleId) {
      await vehicleStore.updateVehicle(props.vehicleId, submitData)
    }
    
    emit('success')
  } catch (error) {
    if (error !== false) { // 不是表单验证错误
      ElMessage.error(props.mode === 'add' ? '添加车辆失败' : '更新车辆失败')
    }
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.vehicle-form-dialog :deep(.el-dialog) {
  border-radius: 16px;
  overflow: hidden;
}

.vehicle-form-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 24px;
}

.vehicle-form-dialog :deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
}

.vehicle-form-dialog :deep(.el-dialog__body) {
  padding: 24px;
}

/* 表单区域 */
.form-section {
  margin-bottom: 32px;
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 20px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #f3f4f6;
}

/* 表单样式优化 */
.vehicle-form-dialog :deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
}

.vehicle-form-dialog :deep(.el-input__wrapper) {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.vehicle-form-dialog :deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #667eea;
}

.vehicle-form-dialog :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.vehicle-form-dialog :deep(.el-select) {
  width: 100%;
}

.vehicle-form-dialog :deep(.el-select .el-input__wrapper) {
  border-radius: 8px;
}

.vehicle-form-dialog :deep(.el-radio-group) {
  display: flex;
  gap: 16px;
}

.vehicle-form-dialog :deep(.el-radio) {
  margin-right: 0;
}

.vehicle-form-dialog :deep(.el-input-number) {
  width: 100%;
}

/* 对话框底部 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .vehicle-form-dialog :deep(.el-dialog) {
    width: 95% !important;
    margin: 5vh auto;
  }

  .vehicle-form-dialog :deep(.el-form-item) {
    margin-bottom: 20px;
  }

  .vehicle-form-dialog :deep(.el-radio-group) {
    flex-direction: column;
    gap: 8px;
  }

  .dialog-footer {
    flex-direction: column;
  }
}
</style>
