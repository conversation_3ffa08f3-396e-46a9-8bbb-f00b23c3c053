const mysql = require('mysql2/promise')

async function fixValueField() {
  let connection
  
  try {
    console.log('✅ 连接数据库...')
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '123456',
      database: 'ev_management'
    })
    
    console.log('🔧 修复 value 字段约束...')
    
    // 修改 value 字段允许为空
    await connection.execute(`
      ALTER TABLE edge_device_data 
      MODIFY COLUMN value JSON NULL
    `)
    console.log('✅ value 字段修改成功')
    
    // 为现有的空值设置默认值
    await connection.execute(`
      UPDATE edge_device_data 
      SET value = JSON_OBJECT('default', 0) 
      WHERE value IS NULL
    `)
    console.log('✅ 空值已更新为默认值')
    
    // 插入一些测试数据
    console.log('🔧 插入测试数据...')
    
    const testData = [
      {
        device_id: 'EV001',
        data_type: 'battery_level',
        value: JSON.stringify({ level: 85, voltage: 400.5 }),
        text_value: '85%',
        unit: '%',
        quality: 95.5
      },
      {
        device_id: 'EV002', 
        data_type: 'charging_power',
        value: JSON.stringify({ power: 50.2, current: 125.5 }),
        text_value: '50.2kW',
        unit: 'kW',
        quality: 98.0
      },
      {
        device_id: 'EV003',
        data_type: 'temperature',
        value: JSON.stringify({ temp: 25.8, status: 'normal' }),
        text_value: '25.8°C',
        unit: '°C', 
        quality: 92.3
      },
      {
        device_id: 'CS001',
        data_type: 'station_status',
        value: JSON.stringify({ status: 'available', slots: 8 }),
        text_value: '可用',
        unit: 'slots',
        quality: 100.0
      },
      {
        device_id: 'CS002',
        data_type: 'utilization',
        value: JSON.stringify({ rate: 78.5, active_sessions: 6 }),
        text_value: '78.5%',
        unit: '%',
        quality: 96.8
      }
    ]
    
    for (const data of testData) {
      await connection.execute(`
        INSERT INTO edge_device_data 
        (device_id, data_type, value, text_value, unit, quality, timestamp) 
        VALUES (?, ?, ?, ?, ?, ?, NOW())
      `, [
        data.device_id,
        data.data_type, 
        data.value,
        data.text_value,
        data.unit,
        data.quality
      ])
    }
    
    console.log('✅ 测试数据插入成功')
    
    // 验证数据
    const [rows] = await connection.execute(`
      SELECT device_id, data_type, text_value, unit, quality 
      FROM edge_device_data 
      ORDER BY created_at DESC 
      LIMIT 10
    `)
    
    console.log('🔍 最新数据:')
    rows.forEach(row => {
      console.log(`  - ${row.device_id}: ${row.data_type} = ${row.text_value} ${row.unit || ''} (质量: ${row.quality})`)
    })
    
    console.log('🎉 数据库修复完成！')
    
  } catch (error) {
    console.error('❌ 修复失败:', error.message)
  } finally {
    if (connection) {
      await connection.end()
      console.log('🔌 数据库连接已关闭')
    }
  }
}

fixValueField()