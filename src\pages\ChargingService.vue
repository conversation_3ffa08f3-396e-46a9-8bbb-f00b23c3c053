<template>
  <div class="charging-service-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">
        <el-icon><Position /></el-icon>
        智能充电服务平台
      </h1>
      <p class="page-subtitle">充电站导航 · 预约调度 · 费用结算 · 历史管理</p>
    </div>

    <!-- 充电服务控制面板 -->
    <ModernCard variant="glassmorphism" :hover-effect="true" class="charging-control-panel">
      <template #header>
        <span>充电服务控制</span>
      </template>
      <div class="charging-control-grid">
        <div class="control-group">
          <span class="control-label">智能调度</span>
          <ModernToggle 
            v-model="chargingControls.smartScheduling" 
            size="default"
            @change="handleChargingControlChange('smartScheduling', $event)"
          />
        </div>
        <div class="control-group">
          <span class="control-label">快速充电</span>
          <ModernToggle 
            v-model="chargingControls.fastCharging" 
            size="default"
            variant="warning"
            @change="handleChargingControlChange('fastCharging', $event)"
          />
        </div>
        <div class="control-group">
          <span class="control-label">预约服务</span>
          <ModernToggle 
            v-model="chargingControls.reservationService" 
            size="default"
            variant="success"
            @change="handleChargingControlChange('reservationService', $event)"
          />
        </div>
        <div class="control-group">
          <span class="control-label">绿色充电</span>
          <ModernToggle 
            v-model="chargingControls.greenCharging" 
            size="default"
            variant="primary"
            @change="handleChargingControlChange('greenCharging', $event)"
          />
        </div>
      </div>
    </ModernCard>

    <!-- 充电服务概览 -->
    <div class="service-overview">
      <ModernCard variant="glassmorphism" :hover-effect="true" class="overview-card">
        <div class="overview-content">
          <div class="overview-icon stations">
            <el-icon><MapLocation /></el-icon>
          </div>
          <div class="overview-info">
            <h3>{{ serviceOverview.totalStations }}</h3>
            <p>充电站总数</p>
            <span class="overview-rate">{{ serviceOverview.availableStations }} 可用</span>
          </div>
        </div>
        </ModernCard>

      <ModernCard variant="glassmorphism" :hover-effect="true" class="overview-card">
        <div class="overview-content">
          <div class="overview-icon charging">
            <el-icon><Lightning /></el-icon>
          </div>
          <div class="overview-info">
            <h3>{{ serviceOverview.chargingSessions }}</h3>
            <p>进行中充电</p>
            <span class="overview-rate">{{ serviceOverview.utilizationRate }}% 利用率</span>
          </div>
        </div>
        </ModernCard>

      <ModernCard variant="glassmorphism" :hover-effect="true" class="overview-card">
        <div class="overview-content">
          <div class="overview-icon revenue">
            <el-icon><Money /></el-icon>
          </div>
          <div class="overview-info">
            <h3>¥{{ serviceOverview.todayRevenue }}</h3>
            <p>今日收入</p>
            <span class="overview-rate">+{{ serviceOverview.revenueGrowth }}%</span>
          </div>
        </div>
        </ModernCard>

      <ModernCard variant="glassmorphism" :hover-effect="true" class="overview-card">
        <div class="overview-content">
          <div class="overview-icon energy">
            <el-icon><Sunny /></el-icon>
          </div>
          <div class="overview-info">
            <h3>{{ serviceOverview.energyDelivered }}</h3>
            <p>今日充电量(kWh)</p>
            <span class="overview-rate">{{ serviceOverview.avgPower }}kW 平均功率</span>
          </div>
        </div>
        </ModernCard>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧：地图和充电站列表 -->
      <div class="map-section">
        <!-- 地图区域 -->
        <ModernCard variant="glassmorphism" :hover-effect="true" class="map-card">
          <template #header>
            <div class="card-header">
              <span>充电站地图</span>
              <div class="map-controls">
                <ModernInput
                  v-model="searchLocation"
                  placeholder="搜索地址或充电站"
                  size="small"
                  style="width: 200px; margin-right: 10px"
                  variant="glass"
                  clearable
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </ModernInput>
                <ModernButton variant="primary" size="small" @click="locateMe">
                  <el-icon><Aim /></el-icon>
                  定位
                </ModernButton>
              </div>
            </div>
          </template>
          <div class="map-container">
            <!-- 真实地图组件 -->
            <MapComponent
              :charging-stations="nearbyStations"
              :selected-station="selectedStation"
              @station-selected="selectStation"
              @navigate-to-station="navigateToStation"
              style="height: 400px; width: 100%;"
            />
          </div>
        </ModernCard>

        <!-- 充电站列表 -->
        <ModernCard variant="glassmorphism" :hover-effect="true" class="stations-list-card">
          <template #header>
            <div class="card-header">
              <span>附近充电站</span>
              <el-select v-model="stationFilter" size="small" style="width: 120px">
                <el-option label="全部" value="all" />
                <el-option label="可用" value="available" />
                <el-option label="使用中" value="busy" />
                <el-option label="维护中" value="maintenance" />
              </el-select>
            </div>
          </template>
          <div class="stations-list">
            <div
              v-for="station in filteredStations"
              :key="station.id"
              class="station-item"
              :class="{ selected: selectedStation?.id === station.id }"
              @click="selectStation(station)"
            >
              <div class="station-info">
                <h4>{{ station.name }}</h4>
                <p class="station-address">{{ station.address }}</p>
                <div class="station-details">
                  <span class="distance">{{ station.distance }}km</span>
                  <el-tag :type="getStationStatusType(station.status)" size="small">
                    {{ getStationStatusText(station.status) }}
                  </el-tag>
                </div>
              </div>
              <div class="station-actions">
                <div class="station-stats">
                  <span>{{ station.availablePorts }}/{{ station.totalPorts }}</span>
                  <small>可用/总数</small>
                </div>
                <ModernButton variant="ghost" size="small" @click.stop="navigateToStation(station)">
                  导航
                </ModernButton>
              </div>
            </div>
          </div>
        </ModernCard>
      </div>

      <!-- 右侧：充电详情和操作 -->
      <div class="details-section">
        <!-- 选中充电站详情 -->
        <ModernCard v-if="selectedStation" variant="glassmorphism" :hover-effect="true" class="station-details-card">
          <template #header>
            <div class="card-header">
              <span>{{ selectedStation.name }}</span>
              <ModernButton variant="primary" size="small" @click="makeReservation">
                预约充电
              </ModernButton>
              <ModernButton variant="warning" size="small" @click="startChargingWithPayment(selectedStation, selectedStation.ports[0])">
                立即充电
              </ModernButton>
            </div>
          </template>
          <div class="station-detail-info">
            <div class="detail-grid">
              <div class="detail-item">
                <label>地址:</label>
                <span>{{ selectedStation.address }}</span>
              </div>
              <div class="detail-item">
                <label>运营商:</label>
                <span>{{ selectedStation.operator }}</span>
              </div>
              <div class="detail-item">
                <label>充电桩数量:</label>
                <span>{{ selectedStation.totalPorts }}个</span>
              </div>
              <div class="detail-item">
                <label>可用数量:</label>
                <span>{{ selectedStation.availablePorts }}个</span>
              </div>
              <div class="detail-item">
                <label>充电功率:</label>
                <span>{{ selectedStation.power }}kW</span>
              </div>
              <div class="detail-item">
                <label>充电价格:</label>
                <span>¥{{ selectedStation.price }}/kWh</span>
              </div>
            </div>
            
            <!-- 充电桩状态 -->
            <div class="ports-status">
              <h5>充电桩状态</h5>
              <div class="ports-grid">
                <div
                  v-for="port in selectedStation.ports"
                  :key="port.id"
                  class="port-item"
                  :class="port.status"
                >
                  <span class="port-number">{{ port.number }}</span>
                  <span class="port-status">{{ getPortStatusText(port.status) }}</span>
                </div>
              </div>
            </div>
          </div>
        </ModernCard>

        <!-- 充电记录 -->
        <ModernCard variant="glassmorphism" :hover-effect="true" class="charging-records-card">
          <template #header>
            <div class="card-header">
              <span>充电记录</span>
              <ModernButton variant="ghost" @click="viewAllRecords">
                查看全部
              </ModernButton>
            </div>
          </template>
          <div class="records-list">
            <div v-for="record in recentRecords" :key="record.id" class="record-item">
              <div class="record-info">
                <h5>{{ record.stationName }}</h5>
                <p class="record-time">{{ formatDateTime(record.startTime) }}</p>
                <div class="record-details">
                  <span>{{ record.energy }}kWh</span>
                  <span>{{ record.duration }}分钟</span>
                  <span class="record-cost">¥{{ record.cost }}</span>
                </div>
              </div>
              <div class="record-status">
                <el-tag :type="getRecordStatusType(record.status)" size="small">
                  {{ getRecordStatusText(record.status) }}
                </el-tag>
              </div>
            </div>
          </div>
        </ModernCard>

        <!-- 费用统计 -->
        <ModernCard variant="glassmorphism" :hover-effect="true" class="cost-stats-card">
          <template #header>
            <span>费用统计</span>
          </template>
          <div ref="costChartRef" class="chart-container"></div>
        </ModernCard>
      </div>
    </div>

    <!-- 预约对话框 -->
    <el-dialog v-model="reservationDialogVisible" title="预约充电" width="500px">
      <el-form :model="reservationForm" label-width="100px">
        <el-form-item label="充电站">
          <ModernInput :value="selectedStation?.name || ''" disabled variant="glass" />
        </el-form-item>
        <el-form-item label="充电桩">
          <el-select v-model="reservationForm.portId" placeholder="选择充电桩">
            <el-option
              v-for="port in availablePorts"
              :key="port.id"
              :label="`充电桩 ${port.number}`"
              :value="port.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="预约时间">
          <el-date-picker
            v-model="reservationForm.reservationTime"
            type="datetime"
            placeholder="选择预约时间"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="预计时长">
          <el-input-number v-model="reservationForm.duration" :min="30" :max="480" />分钟
        </el-form-item>
        <el-form-item label="车辆">
          <el-select v-model="reservationForm.vehicleId" placeholder="选择车辆">
            <el-option label="BYD001" value="BYD001" />
            <el-option label="TESLA002" value="TESLA002" />
            <el-option label="NIO003" value="NIO003" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <ModernButton variant="ghost" @click="reservationDialogVisible = false">取消</ModernButton>
        <ModernButton variant="primary" @click="confirmReservation">确认预约</ModernButton>
      </template>
    </el-dialog>

    <!-- 浮动操作按钮 -->
    <div class="floating-actions">
      <ModernButton variant="primary" circle @click="showNearbyStations">
        <el-icon><Location /></el-icon>
      </ModernButton>
      <ModernButton variant="success" circle @click="quickCharge">
        <el-icon><Lightning /></el-icon>
      </ModernButton>
      <ModernButton variant="warning" circle @click="openBillManagement" style="margin-top: 10px;">
        <el-icon><Money /></el-icon>
      </ModernButton>
    </div>

    <!-- 支付对话框 -->
    <PaymentDialog
      v-model="paymentDialog"
      :order-info="paymentOrderInfo"
      @payment-success="handlePaymentSuccess"
      @payment-failed="handlePaymentFailed"
    />

    <!-- 账单管理对话框 -->
    <el-dialog
      v-model="showBillManagement"
      title="账单管理"
      width="90%"
      :before-close="() => showBillManagement = false"
    >
      <BillManagement />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import {
  Position,
  MapLocation,
  Lightning,
  Money,
  Sunny,
  Search,
  Aim,
  Location,
  CreditCard,
  Wallet
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import MapComponent from '@/components/MapComponent.vue'
import PaymentDialog from '@/components/PaymentDialog.vue'
import BillManagement from '@/components/BillManagement.vue'
import { ModernButton, ModernCard, ModernInput } from '@/components/ui'
import ModernToggle from '@/components/ModernToggle.vue'

// 服务概览数据
const serviceOverview = ref({
  totalStations: 89,
  availableStations: 67,
  chargingSessions: 234,
  utilizationRate: 78.5,
  todayRevenue: 45680,
  revenueGrowth: 12.3,
  energyDelivered: 3456,
  avgPower: 120
})

// 搜索和筛选
const searchLocation = ref('')
const stationFilter = ref('all')
const selectedStation = ref(null)

// 充电站数据
const nearbyStations = ref([])

// 充电记录
const recentRecords = ref([])

// 预约相关
const reservationDialogVisible = ref(false)
const reservationForm = ref({
  portId: '',
  reservationTime: null,
  duration: 120,
  vehicleId: ''
})

// 支付相关
const paymentDialog = ref(false)
const paymentOrderInfo = ref({
  title: '',
  description: '',
  amount: 0,
  originalAmount: 0,
  discount: 0
})

// 账单管理
const showBillManagement = ref(false)

// 充电控制设置
const chargingControls = ref({
  smartScheduling: true,
  fastCharging: false,
  reservationService: true,
  greenCharging: true
})

// 图表相关
const costChartRef = ref<HTMLElement>()
let costChart: echarts.ECharts | null = null

// API调用函数
const fetchNearbyStations = async () => {
  try {
    // 使用默认位置（北京市中心）
    const latitude = 39.9042
    const longitude = 116.4074
    const response = await fetch(`/api/charging-stations/nearby?latitude=${latitude}&longitude=${longitude}&radius=10`)
    const data = await response.json()
    if (data.success) {
      nearbyStations.value = data.data
      // 如果有数据，默认选择第一个充电站
      if (data.data.length > 0) {
        selectedStation.value = data.data[0]
      }
    } else {
      ElMessage.error(data.error || '获取附近充电站失败')
    }
  } catch (error) {
    console.error('获取附近充电站失败:', error)
    ElMessage.error('获取附近充电站失败')
  }
}

const fetchRecentRecords = async () => {
  try {
    const response = await fetch('/api/charging-sessions/recent?limit=10')
    const data = await response.json()
    if (data.success) {
      recentRecords.value = data.data
    } else {
      ElMessage.error(data.error || '获取充电记录失败')
    }
  } catch (error) {
    console.error('获取充电记录失败:', error)
    ElMessage.error('获取充电记录失败')
  }
}

const fetchServiceOverview = async () => {
  try {
    const response = await fetch('/api/charging-stations/overview')
    const data = await response.json()
    if (data.success) {
      serviceOverview.value = data.data
    } else {
      ElMessage.error(data.error || '获取服务概览失败')
    }
  } catch (error) {
    console.error('获取服务概览失败:', error)
    ElMessage.error('获取服务概览失败')
  }
}

// 计算属性
const filteredStations = computed(() => {
  let filtered = nearbyStations.value
  
  if (stationFilter.value !== 'all') {
    filtered = filtered.filter(station => station.status === stationFilter.value)
  }
  
  return filtered.sort((a, b) => a.distance - b.distance)
})

const availablePorts = computed(() => {
  if (!selectedStation.value) return []
  return selectedStation.value.ports.filter(port => port.status === 'available')
})

// 方法
const getStationStatusType = (status: string) => {
  const types = {
    available: 'success',
    busy: 'warning',
    maintenance: 'danger'
  }
  return types[status] || 'info'
}

const getStationStatusText = (status: string) => {
  const texts = {
    available: '可用',
    busy: '使用中',
    maintenance: '维护中'
  }
  return texts[status] || '未知'
}

const getPortStatusText = (status: string) => {
  const texts = {
    available: '可用',
    busy: '使用中',
    maintenance: '维护中'
  }
  return texts[status] || '未知'
}

const getRecordStatusType = (status: string) => {
  const types = {
    completed: 'success',
    charging: 'warning',
    failed: 'danger'
  }
  return types[status] || 'info'
}

const getRecordStatusText = (status: string) => {
  const texts = {
    completed: '已完成',
    charging: '充电中',
    failed: '失败'
  }
  return texts[status] || '未知'
}

const selectStation = (station: any) => {
  selectedStation.value = station
}

const navigateToStation = async (station: any) => {
  try {
    const response = await fetch(`/api/charging-stations/navigate?stationId=${station.id}&latitude=${station.latitude}&longitude=${station.longitude}&name=${encodeURIComponent(station.name)}&address=${encodeURIComponent(station.address)}`);
    const data = await response.json();
    
    if (data.success) {
      ElMessage.success(`正在导航到${station.name}`);
      // 这里可以集成实际的导航功能
      window.open(data.data.navigationUrl, '_blank');
    } else {
      ElMessage.error(data.error || '导航失败');
    }
  } catch (error) {
    console.error('导航失败:', error);
    ElMessage.error('导航失败');
  }
}

const locateMe = async () => {
  try {
    ElMessage.info('正在获取您的位置...');
    
    if (!navigator.geolocation) {
      ElMessage.error('您的浏览器不支持地理定位');
      return;
    }
    
    navigator.geolocation.getCurrentPosition(
      async (position) => {
        const { latitude, longitude } = position.coords;
        ElMessage.success('定位成功');
        
        // 调用API获取附近充电站
        const response = await fetch(`/api/charging-stations/nearby?latitude=${latitude}&longitude=${longitude}&radius=5`);
        const data = await response.json();
        
        if (data.success) {
          nearbyStations.value = data.data;
          if (data.data.length > 0) {
            selectedStation.value = data.data[0];
          }
          ElMessage.success(`找到 ${data.data.length} 个附近充电站`);
        } else {
          ElMessage.error(data.error || '获取附近充电站失败');
        }
      },
      (error) => {
        console.error('定位失败:', error);
        ElMessage.error('定位失败，请检查位置权限设置');
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000
      }
    );
  } catch (error) {
    console.error('定位异常:', error);
    ElMessage.error('定位服务异常');
  }
}

const makeReservation = () => {
  if (!selectedStation.value) {
    ElMessage.warning('请先选择充电站');
    return;
  }
  if (availablePorts.value.length === 0) {
    ElMessage.warning('该充电站暂无可用充电桩');
    return;
  }
  reservationDialogVisible.value = true
}

const confirmReservation = async () => {
  try {
    const response = await fetch('/api/charging-sessions/reserve', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        stationId: selectedStation.value.id,
        portId: reservationForm.value.portId,
        reservationTime: reservationForm.value.reservationTime,
        duration: reservationForm.value.duration,
        vehicleId: reservationForm.value.vehicleId
      })
    });
    const data = await response.json();
    
    if (data.success) {
      ElMessage.success('预约成功！');
      reservationDialogVisible.value = false;
      // 重置表单
      reservationForm.value = {
        portId: '',
        reservationTime: null,
        duration: 120,
        vehicleId: ''
      };
      // 刷新数据
      await fetchRecentRecords();
    } else {
      ElMessage.error(data.message || '预约失败');
    }
  } catch (error) {
    console.error('预约失败:', error);
    ElMessage.error('预约失败');
  }
}

const viewAllRecords = async () => {
  try {
    const response = await fetch('/api/charging-sessions/all');
    const data = await response.json();
    
    if (data.success) {
      // 这里可以打开一个新的对话框显示所有记录
      ElMessage.success('正在加载所有充电记录...');
      // 或者跳转到专门的记录页面
      // router.push('/charging-records');
    } else {
      ElMessage.error(data.message || '获取充电记录失败');
    }
  } catch (error) {
    console.error('获取充电记录失败:', error);
    ElMessage.error('获取充电记录失败');
  }
}

// 处理充电控制变化
const handleChargingControlChange = (controlName: string, value: boolean) => {
  console.log(`充电${controlName} 状态变更为:`, value)
  // 这里可以添加具体的控制逻辑
  switch (controlName) {
    case 'smartScheduling':
      // 处理智能调度开关
      if (value) {
        ElMessage.success('智能调度已开启')
      } else {
        ElMessage.info('智能调度已关闭')
      }
      break
    case 'fastCharging':
      // 处理快速充电开关
      if (value) {
        ElMessage.success('快速充电已开启')
      } else {
        ElMessage.info('快速充电已关闭')
      }
      break
    case 'reservationService':
      // 处理预约服务开关
      if (value) {
        ElMessage.success('预约服务已开启')
      } else {
        ElMessage.info('预约服务已关闭')
      }
      break
    case 'greenCharging':
      // 处理绿色充电开关
      if (value) {
        ElMessage.success('绿色充电已开启')
      } else {
        ElMessage.info('绿色充电已关闭')
      }
      break
  }
}

const showNearbyStations = async () => {
  try {
    ElMessage.info('正在搜索附近充电站...');
    
    const params = new URLSearchParams({
      location: searchLocation.value || '当前位置',
      radius: '5',
      status: 'available',
      fastCharge: 'true'
    });
    
    const response = await fetch(`/api/charging-stations/search?${params}`);
    const data = await response.json();
    
    if (data.success) {
      nearbyStations.value = data.data;
      ElMessage.success(`找到 ${data.data.length} 个附近充电站`);
    } else {
      ElMessage.error(data.error || '搜索充电站失败');
    }
  } catch (error) {
    console.error('搜索充电站失败:', error);
    ElMessage.error('搜索充电站失败');
  }
}

const quickCharge = async () => {
  try {
    ElMessage.info('正在为您寻找最近的快充站点...');
    
    // 使用默认位置查找快速充电站
    const latitude = 39.9042
    const longitude = 116.4074
    const response = await fetch(`/api/charging-stations/quick-charge?latitude=${latitude}&longitude=${longitude}&powerRequirement=fast&maxDistance=3&urgency=high`);
    const data = await response.json();
    
    if (data.success && data.data.length > 0) {
      const quickStation = data.data[0]; // 选择最近的快充站
      selectedStation.value = quickStation;
      ElMessage.success(`找到快充站：${quickStation.name}，距离 ${quickStation.distance}km`);
      
      // 自动开始预约流程
      if (quickStation.availablePorts > 0) {
        makeReservation();
      } else {
        ElMessage.warning('快充站暂无可用充电桩，请选择其他站点');
      }
    } else {
      ElMessage.warning(data.error || '附近暂无可用的快充站点');
    }
  } catch (error) {
    console.error('快充搜索失败:', error);
    ElMessage.error('快充搜索失败');
  }
}

// 开始充电并支付
const startChargingWithPayment = async (station: any, pile: any) => {
  if (!station) {
    ElMessage.warning('请先选择充电站');
    return;
  }
  
  try {
    ElMessage.info('正在启动充电...');
    
    // 创建充电会话
    const sessionResponse = await fetch('/api/charging-sessions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        station_id: station.id,
        port_id: pile?.id || station.ports?.[0]?.id || 1,
        vehicle_id: 1, // 假设用户车辆ID
        user_id: 1, // 假设用户ID
        estimated_duration: 60 // 默认60分钟
      })
    });
    
    const sessionData = await sessionResponse.json();
    
    if (sessionData.success) {
      // 计算充电费用
      const basePrice = 1.2 // 基础电价
      const estimatedKwh = 50 // 预估充电量
      const originalAmount = basePrice * estimatedKwh
      const discount = originalAmount * 0.1 // 10% 折扣
      const finalAmount = originalAmount - discount

      paymentOrderInfo.value = {
        title: '充电费用',
        description: `${station.name} - ${pile?.name || '充电桩'}`,
        amount: finalAmount,
        originalAmount: originalAmount,
        discount: discount,
        sessionId: sessionData.data.id
      }

      paymentDialog.value = true
    } else {
      ElMessage.error(sessionData.error || '启动充电失败');
    }
  } catch (error) {
    console.error('启动充电失败:', error);
    ElMessage.error('充电服务暂时不可用');
  }
}

// 支付成功处理
const handlePaymentSuccess = async (paymentData: any) => {
  try {
    // 更新充电会话状态
    const sessionId = paymentOrderInfo.value?.sessionId || paymentData.sessionId;
    const response = await fetch(`/api/charging-sessions/${sessionId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        status: 'charging',
        payment_status: 'paid',
        payment_id: paymentData.paymentId || paymentData.id
      })
    });
    
    const data = await response.json();
    
    if (data.success) {
      ElMessage.success('支付成功，充电已开始');
      paymentDialog.value = false;
      
      // 刷新数据
      await fetchRecentRecords();
    } else {
      ElMessage.error(data.error || '支付处理失败');
    }
  } catch (error) {
    console.error('支付处理失败:', error);
    ElMessage.error('支付处理异常');
  }
}

// 支付失败处理
const handlePaymentFailed = async (error: string) => {
  try {
    // 如果有会话ID，更新会话状态为失败
    const sessionId = paymentOrderInfo.value?.sessionId;
    if (sessionId) {
      const response = await fetch(`/api/charging-sessions/${sessionId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          status: 'failed',
          payment_status: 'failed',
          error_message: error
        })
      });
      
      const data = await response.json();
      if (!data.success) {
        console.error('更新会话状态失败:', data.error);
      }
    }
    
    ElMessage.error(`支付失败: ${error}`);
    paymentDialog.value = false;
  } catch (err) {
    console.error('支付失败处理异常:', err);
    ElMessage.error(`支付失败: ${error}`);
  }
}

// 显示账单管理
const openBillManagement = () => {
  showBillManagement.value = true
}

const formatDateTime = (date: Date) => {
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 初始化费用统计图表
const initCostChart = () => {
  if (!costChartRef.value) return
  
  costChart = echarts.init(costChartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: {
      type: 'value',
      name: '费用(元)'
    },
    series: [
      {
        name: '充电费用',
        type: 'bar',
        data: [1200, 1450, 1100, 1680, 1890, 2100],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#409EFF' },
            { offset: 1, color: '#66B1FF' }
          ])
        }
      }
    ]
  }
  
  costChart.setOption(option)
}

// 初始化数据
const initializeData = async () => {
  await Promise.all([
    fetchServiceOverview(),
    fetchNearbyStations(),
    fetchRecentRecords()
  ])
  
  // 默认选择第一个充电站
  if (nearbyStations.value.length > 0) {
    selectedStation.value = nearbyStations.value[0]
  }
}

// 生命周期
onMounted(async () => {
  // 初始化数据
  await initializeData()
  
  initCostChart()
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    costChart?.resize()
  })
})

onUnmounted(() => {
  costChart?.dispose()
  window.removeEventListener('resize', () => {
    costChart?.resize()
  })
})
</script>

<style scoped>
.charging-service-container {
  padding: 24px;
  background: #ffffff;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 32px;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.page-subtitle {
  font-size: 16px;
  color: #718096;
  margin: 12px 0 0 0;
}

.service-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-5);
  margin-bottom: var(--space-8);
  position: relative;
  z-index: 1;
}

.overview-card {
  border-radius: 12px;
  background: #ffffff;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  transition: all var(--duration-normal) var(--ease-smooth);
  position: relative;
  overflow: hidden;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border-color: #cbd5e1;
}

.overview-content {
  display: flex;
  align-items: center;
  padding: var(--space-4);
  position: relative;
  z-index: 1;
}

.overview-icon {
  width: 50px;
  height: 50px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--space-4);
  font-size: var(--text-xl);
  color: white;
  transition: all var(--duration-normal) var(--ease-smooth);
  box-shadow: var(--shadow-sm);
}

.overview-card:hover .overview-icon {
  transform: scale(1.05);
  box-shadow: var(--shadow-md);
}

.overview-icon.stations {
  background: var(--primary-color);
}

.overview-icon.charging {
  background: var(--accent-color);
}

.overview-icon.revenue {
  background: var(--secondary-color);
}

.overview-icon.energy {
  background: var(--accent-color);
}

.overview-info h3 {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  margin: 0;
  color: var(--text-primary);
}

.overview-info p {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin: var(--space-1) 0;
}

.overview-rate {
  font-size: var(--text-xs);
  font-weight: var(--font-bold);
  color: var(--accent-color);
}

.main-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-5);
  position: relative;
  z-index: 1;
}

.map-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-5);
}

.details-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-5);
}

.map-card,
.stations-list-card,
.station-details-card,
.charging-records-card,
.cost-stats-card {
  border-radius: var(--radius-xl);
  background: var(--bg-card);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-sm);
  transition: all var(--duration-normal) var(--ease-smooth);
  position: relative;
  overflow: hidden;
}

.map-card:hover,
.stations-list-card:hover,
.station-details-card:hover,
.charging-records-card:hover,
.cost-stats-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--border-medium);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: var(--font-bold);
  color: var(--text-primary);
}

.map-controls {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.map-controls .modern-button {
  background: var(--bg-card);
  border: 1px solid var(--border-light);
  color: var(--text-primary);
  transition: all var(--duration-normal) var(--ease-smooth);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius);
  font-weight: var(--font-medium);
}

.map-controls .modern-button:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
  background: var(--bg-secondary);
}

.map-container {
  height: 300px;
  position: relative;
  border-radius: var(--radius-lg);
  overflow: hidden;
  border: 1px solid var(--border-glass);
  box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.1);
}

/* 地图容器样式 */
.map-container {
  height: 400px;
  border-radius: var(--radius-lg);
  overflow: hidden;
  background: var(--bg-secondary);
  border: 1px solid var(--border-light);
}

.stations-list {
  max-height: 400px;
  overflow-y: auto;
}

.stations-list::-webkit-scrollbar {
  width: 4px;
}

.stations-list::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: var(--radius);
}

.stations-list::-webkit-scrollbar-thumb {
  background: var(--border-medium);
  border-radius: var(--radius);
}

.stations-list::-webkit-scrollbar-thumb:hover {
  background: var(--primary-color);
}

.station-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-4);
  border-bottom: 1px solid var(--border-glass);
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-smooth);
  position: relative;
  background: transparent;
}

.station-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 0;
  height: 100%;
  background: var(--primary-gradient);
  transition: width var(--duration-normal) var(--ease-smooth);
  z-index: -1;
}

.station-item:hover,
.station-item.selected {
  background: var(--bg-secondary);
  border-left: 4px solid var(--primary-color);
  transform: translateX(4px);
  box-shadow: var(--shadow-sm);
}

.station-item:hover::before,
.station-item.selected::before {
  width: 4px;
}

.station-info h4 {
  margin: 0 0 var(--space-1) 0;
  color: var(--text-primary);
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
}

.station-address {
  margin: 0 0 var(--space-2) 0;
  color: var(--text-secondary);
  font-size: var(--text-xs);
}

.station-details {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.distance {
  font-size: var(--text-xs);
  color: var(--primary-color);
  font-weight: var(--font-bold);
}

.station-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-2);
}

.station-stats {
  text-align: center;
}

.station-stats span {
  font-weight: var(--font-bold);
  color: var(--text-primary);
}

.station-stats small {
  display: block;
  color: var(--text-secondary);
  font-size: var(--text-xs);
}

.station-detail-info {
  padding: var(--space-3) 0;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-4);
  margin-bottom: var(--space-5);
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-2) 0;
  border-bottom: 1px solid var(--border-light);
  transition: all var(--duration-fast) var(--ease-smooth);
}

.detail-item:hover {
  padding-left: var(--space-2);
  background: var(--bg-secondary);
}

.detail-item label {
  font-weight: var(--font-bold);
  color: var(--text-secondary);
}

.detail-item span {
  color: var(--text-primary);
}

.ports-status h5 {
  margin: 0 0 var(--space-3) 0;
  color: var(--text-primary);
  font-weight: var(--font-semibold);
}

.ports-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: var(--space-3);
}

.port-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--space-3);
  border-radius: var(--radius);
  border: 2px solid;
  transition: all var(--duration-normal) var(--ease-smooth);
  background: var(--bg-secondary);
}

.port-item:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-md);
}

.port-item.available {
  border-color: #67c23a;
  background: var(--bg-secondary);
}

.port-item.busy {
  border-color: #e6a23c;
  background: var(--bg-secondary);
}

.port-item.maintenance {
  border-color: #f56c6c;
  background: var(--bg-secondary);
}

.port-number {
  font-weight: var(--font-bold);
  font-size: var(--text-sm);
  margin-bottom: var(--space-1);
  color: var(--text-primary);
}

.port-status {
  font-size: var(--text-xs);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: var(--font-medium);
}

.records-list {
  max-height: 300px;
  overflow-y: auto;
}

.records-list::-webkit-scrollbar {
  width: 6px;
}

.records-list::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: var(--radius);
}

.records-list::-webkit-scrollbar-thumb {
  background: var(--border-medium);
  border-radius: var(--radius);
}

.records-list::-webkit-scrollbar-thumb:hover {
  background: var(--primary-color);
}

.record-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-3);
  border-bottom: 1px solid var(--border-glass);
  transition: all var(--duration-fast) var(--ease-smooth);
  position: relative;
}

.record-item:hover {
  padding-left: var(--space-2);
  background: var(--bg-secondary);
}

.record-info h5 {
  margin: 0 0 var(--space-1) 0;
  color: var(--text-primary);
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
}

.record-time {
  margin: 0 0 var(--space-2) 0;
  color: var(--text-secondary);
  font-size: var(--text-xs);
}

.record-details {
  display: flex;
  gap: var(--space-4);
  font-size: var(--text-xs);
  color: var(--text-secondary);
}

.record-cost {
  font-weight: var(--font-bold);
  color: var(--primary-color);
}

.chart-container {
  height: 200px;
  width: 100%;
  background: var(--bg-secondary);
  border-radius: var(--radius);
  border: 1px solid var(--border-light);
}

.floating-actions {
  position: fixed;
  right: var(--space-8);
  bottom: var(--space-8);
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  z-index: 1000;
}

.floating-actions .modern-button {
  width: 50px;
  height: 50px;
  box-shadow: var(--shadow-lg);
  background: var(--primary-color);
  border: 1px solid var(--border-light);
  color: white;
}

.floating-actions .modern-button:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-xl);
  background: var(--primary-color-hover);
}

  .charging-control-panel {
    margin-bottom: var(--space-6);
    border-radius: 12px;
    background: #ffffff;
    border: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    transition: all var(--duration-normal) var(--ease-smooth);
  }

  .charging-control-panel:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border-color: #cbd5e1;
  }

  .charging-control-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-4);
    padding: var(--space-4);
  }

  .control-group {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-3);
    border-radius: var(--radius-lg);
    background: var(--bg-secondary);
    border: 1px solid var(--border-light);
    transition: all var(--duration-fast) var(--ease-smooth);
  }

  .control-group:hover {
    transform: translateY(-1px);
    background: var(--bg-card);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-sm);
  }

  .control-label {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    font-weight: var(--font-medium);
    transition: color var(--duration-fast) var(--ease-smooth);
  }

  .control-group:hover .control-label {
    color: var(--text-primary);
  }

@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 1fr;
    gap: var(--space-5);
  }

  .map-section {
    order: 1;
  }

  .details-section {
    order: 2;
  }
  
  .service-overview {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}

@media (max-width: 768px) {
  .charging-service-container {
    padding: var(--space-3);
  }
  
  .service-overview {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .overview-card {
    padding: var(--space-4);
  }

  .main-content {
    gap: var(--space-4);
  }

  .card {
    padding: var(--space-4);
  }
  
  .detail-grid {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }
  
  .page-title {
    font-size: var(--text-2xl);
  }

  .page-subtitle {
    font-size: var(--text-sm);
  }
  
  .map-controls {
    flex-direction: column;
    gap: var(--space-3);
  }
  
  .ports-grid {
    grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
    gap: var(--space-2);
  }

  .port-item {
    padding: var(--space-2);
  }

  .floating-actions {
    bottom: var(--space-5);
    right: var(--space-5);
  }

  .floating-actions .modern-button {
    width: 48px;
    height: 48px;
  }
}
</style>