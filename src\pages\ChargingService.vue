<template>
  <div class="charging-service">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <el-icon class="title-icon"><Lightning /></el-icon>
            充电服务管理
          </h1>
          <p class="page-description">智能充电服务平台 - 充电站管理、会话监控、数据分析</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="quickCharge">
            <el-icon><Lightning /></el-icon>
            快速充电
          </el-button>
          <el-button @click="findNearbyStations">
            <el-icon><Location /></el-icon>
            附近充电站
          </el-button>
          <el-button @click="refreshData">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="stats-overview">
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon charging">
            <el-icon><Lightning /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ totalStations }}</div>
            <div class="stat-label">充电站总数</div>
            <div class="stat-change positive">+{{ newStationsThisMonth }} 本月新增</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon available">
            <el-icon><CircleCheck /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ availableStations }}</div>
            <div class="stat-label">可用充电站</div>
            <div class="stat-change">{{ availabilityRate }}% 可用率</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon sessions">
            <el-icon><Timer /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ activeSessions }}</div>
            <div class="stat-label">进行中会话</div>
            <div class="stat-change">{{ totalSessionsToday }} 今日总数</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon revenue">
            <el-icon><Money /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">¥{{ todayRevenue }}</div>
            <div class="stat-label">今日收入</div>
            <div class="stat-change positive">+{{ revenueGrowth }}% 较昨日</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速导航 -->
    <div class="quick-navigation">
      <div class="nav-grid">
        <div class="nav-card" @click="navigateTo('/charging-service/stations')">
          <div class="nav-icon stations">
            <el-icon><OfficeBuilding /></el-icon>
          </div>
          <div class="nav-content">
            <h3>充电站管理</h3>
            <p>管理充电站信息、状态监控</p>
            <div class="nav-stats">
              <span>{{ totalStations }} 个充电站</span>
              <span>{{ availableStations }} 个可用</span>
            </div>
          </div>
          <div class="nav-arrow">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>

        <div class="nav-card" @click="navigateTo('/charging-service/sessions')">
          <div class="nav-icon sessions">
            <el-icon><Timer /></el-icon>
          </div>
          <div class="nav-content">
            <h3>充电会话</h3>
            <p>充电会话管理、实时监控</p>
            <div class="nav-stats">
              <span>{{ activeSessions }} 个进行中</span>
              <span>{{ totalSessionsToday }} 今日总数</span>
            </div>
          </div>
          <div class="nav-arrow">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>

        <div class="nav-card" @click="navigateTo('/charging-service/map')">
          <div class="nav-icon map">
            <el-icon><Location /></el-icon>
          </div>
          <div class="nav-content">
            <h3>充电站地图</h3>
            <p>地图导航、位置查找</p>
            <div class="nav-stats">
              <span>实时位置</span>
              <span>智能导航</span>
            </div>
          </div>
          <div class="nav-arrow">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>

        <div class="nav-card" @click="navigateTo('/charging-service/analytics')">
          <div class="nav-icon analytics">
            <el-icon><TrendCharts /></el-icon>
          </div>
          <div class="nav-content">
            <h3>数据分析</h3>
            <p>充电统计、收入分析</p>
            <div class="nav-stats">
              <span>¥{{ todayRevenue }} 今日收入</span>
              <span>+{{ revenueGrowth }}% 增长</span>
            </div>
          </div>
          <div class="nav-arrow">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>

        <div class="nav-card" @click="navigateTo('/charging-service/pricing')">
          <div class="nav-icon pricing">
            <el-icon><Money /></el-icon>
          </div>
          <div class="nav-content">
            <h3>价格管理</h3>
            <p>充电价格设置、优惠策略</p>
            <div class="nav-stats">
              <span>¥{{ currentPrice }}/kWh</span>
              <span>{{ activePromotions }} 个优惠</span>
            </div>
          </div>
          <div class="nav-arrow">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>

        <div class="nav-card" @click="navigateTo('/charging-service/maintenance')">
          <div class="nav-icon maintenance">
            <el-icon><Tools /></el-icon>
          </div>
          <div class="nav-content">
            <h3>设备维护</h3>
            <p>设备检修、故障处理</p>
            <div class="nav-stats">
              <span>{{ maintenanceCount }} 待维护</span>
              <span>{{ lastMaintenanceDate }} 上次检修</span>
            </div>
          </div>
          <div class="nav-arrow">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>

        <div class="nav-card" @click="navigateTo('/charging-service/reports')">
          <div class="nav-icon reports">
            <el-icon><Document /></el-icon>
          </div>
          <div class="nav-content">
            <h3>报表中心</h3>
            <p>运营报表、财务统计</p>
            <div class="nav-stats">
              <span>{{ monthlyReports }} 月度报表</span>
              <span>{{ weeklyReports }} 周报</span>
            </div>
          </div>
          <div class="nav-arrow">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 充电站列表 -->
    <div class="stations-section">
      <div class="section-header">
        <h2>充电站列表</h2>
        <div class="section-actions">
          <el-input
            v-model="searchQuery"
            placeholder="搜索充电站"
            style="width: 300px"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 120px">
            <el-option label="全部状态" value="" />
            <el-option label="可用" value="available" />
            <el-option label="使用中" value="busy" />
            <el-option label="维护中" value="maintenance" />
            <el-option label="离线" value="offline" />
          </el-select>
        </div>
      </div>

      <div class="stations-grid">
        <div
          v-for="station in filteredStations"
          :key="station.id"
          class="station-card"
          @click="selectStation(station)"
        >
          <div class="station-header">
            <div class="station-name">{{ station.name }}</div>
            <div class="station-status">
              <el-tag :type="getStationStatusType(station.status)" size="small">
                {{ getStationStatusText(station.status) }}
              </el-tag>
            </div>
          </div>
          <div class="station-info">
            <div class="info-item">
              <el-icon><Location /></el-icon>
              <span>{{ station.address }}</span>
            </div>
            <div class="info-item">
              <el-icon><User /></el-icon>
              <span>{{ station.operator }}</span>
            </div>
            <div class="info-item">
              <el-icon><Connection /></el-icon>
              <span>{{ station.available_ports }}/{{ station.total_ports }} 可用</span>
            </div>
          </div>
          <div class="station-actions">
            <el-button size="small" @click.stop="viewStationDetail(station.id)">
              详情
            </el-button>
            <el-button size="small" type="primary" @click.stop="makeReservation()">
              预约
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 最近充电记录 -->
    <div class="recent-sessions">
      <div class="section-header">
        <h2>最近充电记录</h2>
        <div class="section-actions">
          <el-button @click="navigateTo('/charging-service/sessions')">
            查看全部
          </el-button>
        </div>
      </div>

      <div class="sessions-grid">
        <div
          v-for="session in recentSessions"
          :key="session.id"
          class="session-card"
        >
          <div class="session-header">
            <div class="session-station">{{ session.station_name }}</div>
            <div class="session-status">
              <el-tag :type="getSessionStatusType(session.status)" size="small">
                {{ getSessionStatusText(session.status) }}
              </el-tag>
            </div>
          </div>
          <div class="session-info">
            <div class="info-row">
              <span class="info-label">开始时间:</span>
              <span>{{ formatDateTime(session.start_time) }}</span>
            </div>
            <div class="info-row">
              <span class="info-label">充电量:</span>
              <span>{{ session.energy_delivered || 0 }} kWh</span>
            </div>
            <div class="info-row">
              <span class="info-label">费用:</span>
              <span class="session-cost">¥{{ session.total_cost || 0 }}</span>
            </div>
          </div>
          <div class="session-actions">
            <el-button size="small" @click="viewSessionDetail(session.id)">
              详情
            </el-button>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>


<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useChargingStore } from '@/stores/charging'
import {
  Lightning,
  OfficeBuilding,
  Timer,
  Location,
  TrendCharts,
  ArrowRight,
  CircleCheck,
  Money,
  Refresh,
  Search,
  User,
  Connection,
  Tools,
  Document
} from '@element-plus/icons-vue'

const router = useRouter()
const chargingStore = useChargingStore()

// 响应式数据
const searchQuery = ref('')
const statusFilter = ref('')
const selectedStation = ref(null)

// 统计数据
const totalStations = computed(() => chargingStore.stations.length)
const availableStations = computed(() =>
  chargingStore.stations.filter(s => s.status === 'available' && s.available_ports > 0).length
)
const activeSessions = computed(() =>
  chargingStore.sessions.filter(s => s.status === 'charging').length
)
const totalSessionsToday = computed(() => {
  const today = new Date().toDateString()
  return chargingStore.sessions.filter(s =>
    new Date(s.start_time).toDateString() === today
  ).length
})
const todayRevenue = computed(() => chargingStore.todayRevenue)
const revenueGrowth = computed(() => 15.2) // 示例增长率
const availabilityRate = computed(() =>
  totalStations.value > 0 ? Math.round((availableStations.value / totalStations.value) * 100) : 0
)
const newStationsThisMonth = computed(() => 3) // 示例数据

// 新增数据属性
const currentPrice = computed(() => 1.2) // 当前电价
const activePromotions = computed(() => 2) // 活跃优惠数量
const maintenanceCount = computed(() => 5) // 待维护设备数量
const lastMaintenanceDate = computed(() => '2024-01-15') // 上次维护日期
const monthlyReports = computed(() => 12) // 月度报表数量
const weeklyReports = computed(() => 4) // 周报数量

// 最近充电会话
const recentSessions = computed(() =>
  chargingStore.sessions.slice(0, 6)
)

// 筛选后的充电站
const filteredStations = computed(() => {
  let filtered = chargingStore.stations

  if (searchQuery.value) {
    filtered = filtered.filter(station =>
      station.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      station.address.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  }

  if (statusFilter.value) {
    filtered = filtered.filter(station => station.status === statusFilter.value)
  }

  return filtered.slice(0, 8) // 只显示前8个
})

// 方法
const navigateTo = (path: string) => {
  router.push(path)
}

const refreshData = async () => {
  try {
    await chargingStore.fetchStations()
    await chargingStore.fetchSessions()
    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败')
  }
}

const quickCharge = () => {
  navigateTo('/charging-service/sessions')
}

const findNearbyStations = () => {
  navigateTo('/charging-service/map')
}

const selectStation = (station: any) => {
  selectedStation.value = station
}

const viewStationDetail = (stationId: string) => {
  navigateTo(`/charging-service/stations/${stationId}`)
}

const makeReservation = () => {
  // 这里可以打开预约对话框或跳转到预约页面
  ElMessage.info('跳转到预约页面...')
  navigateTo('/charging-service/sessions')
}

const viewSessionDetail = (sessionId: string) => {
  navigateTo(`/charging-service/sessions/${sessionId}`)
}

// 状态格式化方法
const getStationStatusType = (status: string) => {
  const types = {
    available: 'success',
    busy: 'warning',
    maintenance: 'warning',
    offline: 'danger'
  }
  return types[status] || 'info'
}

const getStationStatusText = (status: string) => {
  const texts = {
    available: '可用',
    busy: '使用中',
    maintenance: '维护中',
    offline: '离线'
  }
  return texts[status] || '未知'
}

const getSessionStatusType = (status: string) => {
  const types = {
    charging: 'warning',
    completed: 'success',
    failed: 'danger',
    interrupted: 'info'
  }
  return types[status] || 'info'
}

const getSessionStatusText = (status: string) => {
  const texts = {
    charging: '充电中',
    completed: '已完成',
    failed: '失败',
    interrupted: '已中断'
  }
  return texts[status] || '未知'
}

const formatDateTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 生命周期
onMounted(async () => {
  await refreshData()
})
</script>

<style scoped>
.charging-service {
  padding: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.page-header {
  margin-bottom: 32px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 36px;
  color: #667eea;
}

.page-description {
  font-size: 16px;
  color: #718096;
  margin: 8px 0 0 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.stats-overview {
  margin-bottom: 32px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.charging {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.stat-icon.available {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.stat-icon.sessions {
  background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.stat-icon.revenue {
  background: linear-gradient(135deg, #fa709a, #fee140);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #718096;
  margin-bottom: 4px;
}

.stat-change {
  font-size: 12px;
  color: #718096;
}

.stat-change.positive {
  color: #48bb78;
}

.quick-navigation {
  margin-bottom: 32px;
}

.nav-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
}

@media (max-width: 1200px) {
  .nav-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .nav-grid {
    grid-template-columns: 1fr;
  }
}

.nav-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.nav-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.nav-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.nav-icon.stations {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.nav-icon.sessions {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.nav-icon.map {
  background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.nav-icon.analytics {
  background: linear-gradient(135deg, #fa709a, #fee140);
}

.nav-icon.pricing {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.nav-icon.maintenance {
  background: linear-gradient(135deg, #f093fb, #f5576c);
}

.nav-icon.reports {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.nav-content {
  flex: 1;
}

.nav-content h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 8px 0;
}

.nav-content p {
  font-size: 14px;
  color: #718096;
  margin: 0 0 12px 0;
}

.nav-stats {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #4a5568;
}

.nav-arrow {
  font-size: 20px;
  color: #cbd5e1;
  transition: all 0.3s ease;
}

.nav-card:hover .nav-arrow {
  color: #667eea;
  transform: translateX(4px);
}

/* 充电站列表样式 */
.stations-section {
  margin-bottom: 32px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.section-header h2 {
  font-size: 24px;
  font-weight: 600;
  color: #1a202c;
  margin: 0;
}

.section-actions {
  display: flex;
  gap: 16px;
  align-items: center;
}

.stations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;
}

.station-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.station-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.station-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.station-name {
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
}

.station-info {
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 14px;
  color: #4a5568;
}

.info-label {
  font-weight: 500;
  color: #718096;
  min-width: 80px;
}

.station-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

/* 充电记录样式 */
.recent-sessions {
  margin-bottom: 32px;
}

.sessions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;
}

.session-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.session-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.session-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.session-station {
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
}

.session-info {
  margin-bottom: 20px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.info-label {
  color: #718096;
  font-weight: 500;
}

.session-cost {
  color: #667eea;
  font-weight: 600;
}

.session-actions {
  display: flex;
  justify-content: flex-end;
}

@media (max-width: 768px) {
  .charging-service {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .stats-grid,
  .nav-grid,
  .stations-grid,
  .sessions-grid {
    grid-template-columns: 1fr;
  }

  .section-header {
    flex-direction: column;
    gap: 16px;
  }

  .section-actions {
    width: 100%;
    justify-content: center;
  }
}
</style>
