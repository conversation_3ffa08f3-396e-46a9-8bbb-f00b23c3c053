const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

async function importSampleData() {
  let connection;
  
  try {
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '123456',
      database: 'ev_management',
      multipleStatements: true
    });
    
    console.log('✅ 数据库连接成功');
    
    // 读取SQL文件
    const sqlFilePath = path.join(__dirname, 'sample_data.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
    
    console.log('📖 读取SQL文件成功');
    
    // 分割SQL语句（按分号分割，但忽略注释中的分号）
    const statements = sqlContent
      .split('\n')
      .filter(line => !line.trim().startsWith('--') && line.trim() !== '')
      .join('\n')
      .split(';')
      .filter(statement => statement.trim() !== '');
    
    console.log(`📝 准备执行 ${statements.length} 条SQL语句`);
    
    // 逐条执行SQL语句
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i].trim();
      if (statement) {
        try {
          await connection.execute(statement);
          if (i % 10 === 0) {
            console.log(`⏳ 已执行 ${i + 1}/${statements.length} 条语句`);
          }
        } catch (error) {
          console.warn(`⚠️  语句执行警告 (${i + 1}): ${error.message}`);
          // 继续执行其他语句
        }
      }
    }
    
    console.log('✅ 所有SQL语句执行完成');
    
    // 验证数据导入结果
    const [results] = await connection.execute(`
      SELECT 
        '用户数据' as table_name, COUNT(*) as count FROM users
      UNION ALL
      SELECT '车辆数据', COUNT(*) FROM vehicles
      UNION ALL
      SELECT '电池数据', COUNT(*) FROM batteries
      UNION ALL
      SELECT '充电站数据', COUNT(*) FROM charging_stations
      UNION ALL
      SELECT '充电会话', COUNT(*) FROM charging_sessions
      UNION ALL
      SELECT '碳积分记录', COUNT(*) FROM carbon_credit
      UNION ALL
      SELECT '驾驶行为', COUNT(*) FROM driving_behavior
      UNION ALL
      SELECT '车队管理', COUNT(*) FROM fleet_management
      UNION ALL
      SELECT '边缘设备', COUNT(*) FROM edge_devices
      UNION ALL
      SELECT '设备数据', COUNT(*) FROM edge_device_data
      UNION ALL
      SELECT '边缘任务', COUNT(*) FROM edge_tasks
    `);
    
    console.log('\n📊 数据导入统计:');
    console.log('==================');
    results.forEach(row => {
      console.log(`${row.table_name}: ${row.count} 条记录`);
    });
    
    console.log('\n🎉 示例数据导入完成！');
    console.log('💡 现在可以访问各个模块查看真实数据了');
    
  } catch (error) {
    console.error('❌ 数据导入失败:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 执行导入
importSampleData();