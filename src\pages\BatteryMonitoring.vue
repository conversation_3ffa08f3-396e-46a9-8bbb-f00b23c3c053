<template>
  <div class="battery-monitoring">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <el-icon class="title-icon"><Monitor /></el-icon>
            电池实时监控
          </h1>
          <p class="page-description">实时监控电池状态，及时发现异常情况</p>
        </div>
        <div class="header-actions">
          <div class="monitoring-status">
            <span class="status-label">监控状态:</span>
            <el-tag :type="monitoringActive ? 'success' : 'danger'" size="small">
              {{ monitoringActive ? '运行中' : '已停止' }}
            </el-tag>
          </div>
          <el-button
            :type="monitoringActive ? 'danger' : 'success'"
            @click="toggleMonitoring"
          >
            <el-icon><component :is="monitoringActive ? 'VideoPause' : 'VideoPlay'" /></el-icon>
            {{ monitoringActive ? '停止监控' : '开始监控' }}
          </el-button>
          <el-button @click="refreshData">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 导航按钮区域 -->
    <div class="navigation-tabs">
      <div class="nav-buttons">
        <!-- 测试用原生按钮 -->
        <button
          @click="testNavigateToAnalysis"
          style="padding: 10px 20px; margin: 5px; background: #409eff; color: white; border: none; border-radius: 4px; cursor: pointer;"
        >
          🔍 健康度分析 (测试)
        </button>

        <button
          @click="testNavigateToMaintenance"
          style="padding: 10px 20px; margin: 5px; background: #e6a23c; color: white; border: none; border-radius: 4px; cursor: pointer;"
        >
          🔧 电池维护 (测试)
        </button>

        <!-- 原来的Element Plus按钮 -->
        <router-link to="/battery-management/analysis" custom v-slot="{ navigate }">
          <el-button type="primary" plain @click="(e) => {
            console.log('Element Plus按钮：点击健康度分析按钮', e);
            console.log('navigate函数:', navigate);
            try {
              navigate(e);
              console.log('navigate调用成功');
            } catch (error) {
              console.error('navigate调用失败:', error);
            }
          }">
            <el-icon><TrendCharts /></el-icon>
            健康度分析
          </el-button>
        </router-link>
        <router-link to="/battery-management/maintenance" custom v-slot="{ navigate }">
          <el-button type="warning" plain @click="(e) => { console.log('点击电池维护按钮', e); navigate(e); }">
            <el-icon><Tools /></el-icon>
            电池维护
          </el-button>
        </router-link>
        <el-button type="info" plain disabled>
          <el-icon><Monitor /></el-icon>
          实时监控
        </el-button>
      </div>
    </div>

    <!-- 实时状态概览 -->
    <div class="realtime-overview">
      <div class="overview-grid">
        <div class="realtime-card">
          <div class="card-header">
            <h3>在线电池</h3>
            <div class="status-indicator online"></div>
          </div>
          <div class="card-content">
            <div class="metric-value">{{ onlineBatteries }}</div>
            <div class="metric-total">/ {{ totalBatteries }}</div>
          </div>
        </div>

        <div class="realtime-card">
          <div class="card-header">
            <h3>异常警报</h3>
            <div class="status-indicator" :class="alertCount > 0 ? 'alert' : 'normal'"></div>
          </div>
          <div class="card-content">
            <div class="metric-value alert">{{ alertCount }}</div>
            <div class="metric-label">个异常</div>
          </div>
        </div>

        <div class="realtime-card">
          <div class="card-header">
            <h3>平均温度</h3>
            <div class="status-indicator" :class="getTemperatureStatus(avgTemperature)"></div>
          </div>
          <div class="card-content">
            <div class="metric-value">{{ avgTemperature }}</div>
            <div class="metric-label">°C</div>
          </div>
        </div>

        <div class="realtime-card">
          <div class="card-header">
            <h3>数据更新</h3>
            <div class="status-indicator updating"></div>
          </div>
          <div class="card-content">
            <div class="metric-value small">{{ lastUpdateTime }}</div>
            <div class="metric-label">最后更新</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 实时监控表格 -->
    <div class="monitoring-table">
      <div class="table-header">
        <h3>实时电池状态</h3>
        <div class="table-controls">
          <el-input
            v-model="searchQuery"
            placeholder="搜索电池ID"
            size="small"
            style="width: 200px"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-select v-model="statusFilter" placeholder="状态筛选" size="small" style="width: 120px">
            <el-option label="全部" value="" />
            <el-option label="正常" value="normal" />
            <el-option label="预警" value="warning" />
            <el-option label="异常" value="critical" />
          </el-select>
        </div>
      </div>

      <el-table 
        :data="filteredBatteries" 
        style="width: 100%" 
        v-loading="loading"
        element-loading-text="监控中..."
        :row-class-name="getRowClassName"
      >
        <el-table-column prop="id" label="电池ID" width="120" />
        <el-table-column label="状态" width="80">
          <template #default="{ row }">
            <div class="status-indicator-small" :class="getStatusClass(row)"></div>
          </template>
        </el-table-column>
        <el-table-column label="健康度" width="120">
          <template #default="{ row }">
            <div class="health-display">
              <el-progress
                :percentage="row.soh"
                :color="getHealthColor(row.soh)"
                :stroke-width="6"
                :show-text="false"
              />
              <span class="health-text">{{ row.soh }}%</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="电量" width="120">
          <template #default="{ row }">
            <div class="charge-display">
              <el-progress
                :percentage="row.soc"
                color="#3b82f6"
                :stroke-width="6"
                :show-text="false"
              />
              <span class="charge-text">{{ row.soc }}%</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="temperature" label="温度(°C)" width="100">
          <template #default="{ row }">
            <span class="temperature-value" :class="getTemperatureClass(row.temperature)">
              {{ row.temperature }}°C
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="voltage" label="电压(V)" width="100">
          <template #default="{ row }">
            <span class="voltage-value">{{ row.voltage }}V</span>
          </template>
        </el-table-column>
        <el-table-column label="车辆信息" width="150">
          <template #default="{ row }">
            <div v-if="row.vehicle" class="vehicle-info">
              <div class="vehicle-name">{{ row.vehicle.brand }} {{ row.vehicle.model }}</div>
              <div class="vehicle-vin">{{ row.vehicle.vin?.slice(-8) }}</div>
            </div>
            <span v-else class="no-vehicle">未绑定</span>
          </template>
        </el-table-column>
        <el-table-column label="最后更新" width="120">
          <template #default="{ row }">
            <span class="update-time">{{ getRelativeTime(row.updated_at) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <div class="table-actions">
              <el-button size="small" @click="viewDetail(row.id)">
                详情
              </el-button>
              <el-button size="small" @click="viewHistory(row.id)">
                历史
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 警报面板 -->
    <div v-if="alerts.length > 0" class="alerts-panel">
      <div class="panel-header">
        <h3>实时警报</h3>
        <el-button size="small" @click="clearAllAlerts">清除全部</el-button>
      </div>
      <div class="alerts-list">
        <div 
          v-for="alert in alerts" 
          :key="alert.id"
          class="alert-item"
          :class="alert.level"
        >
          <div class="alert-icon">
            <el-icon><component :is="getAlertIcon(alert.level)" /></el-icon>
          </div>
          <div class="alert-content">
            <div class="alert-title">{{ alert.title }}</div>
            <div class="alert-message">{{ alert.message }}</div>
            <div class="alert-time">{{ formatTime(alert.timestamp) }}</div>
          </div>
          <div class="alert-actions">
            <el-button size="small" @click="handleAlert(alert)">
              处理
            </el-button>
            <el-button size="small" @click="dismissAlert(alert.id)">
              忽略
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import {
  Monitor, VideoPlay, VideoPause, Refresh, Search,
  Warning, CircleClose, InfoFilled, TrendCharts, Tools
} from '@element-plus/icons-vue'
import { useBatteryStore } from '@/stores/battery'

// Router
const router = useRouter()

// Store
const batteryStore = useBatteryStore()

// 响应式数据
const loading = ref(false)
const monitoringActive = ref(true)
const searchQuery = ref('')
const statusFilter = ref('')
const lastUpdateTime = ref('')
const monitoringInterval = ref<NodeJS.Timeout | null>(null)

// 模拟警报数据
const alerts = ref([
  {
    id: '1',
    level: 'critical',
    title: '电池温度过高',
    message: '电池 battery-003 温度达到 38°C，超过安全阈值',
    timestamp: new Date(),
    battery_id: 'battery-003'
  },
  {
    id: '2',
    level: 'warning',
    title: '电池健康度下降',
    message: '电池 battery-002 健康度降至 88%，建议检查',
    timestamp: new Date(Date.now() - 5 * 60 * 1000),
    battery_id: 'battery-002'
  }
])

// 计算属性
const totalBatteries = computed(() => batteryStore.batteries.length)
const onlineBatteries = computed(() => batteryStore.batteries.length) // 假设所有电池都在线
const alertCount = computed(() => alerts.value.length)
const avgTemperature = computed(() => {
  const batteries = batteryStore.batteries
  if (batteries.length === 0) return 25
  const total = batteries.reduce((sum, battery) => sum + battery.temperature, 0)
  return Math.round(total / batteries.length)
})

const filteredBatteries = computed(() => {
  let result = batteryStore.batteries
  
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(battery => 
      battery.id.toLowerCase().includes(query)
    )
  }
  
  if (statusFilter.value) {
    result = result.filter(battery => {
      if (statusFilter.value === 'normal') return battery.health_status === 'good'
      if (statusFilter.value === 'warning') return battery.health_status === 'warning'
      if (statusFilter.value === 'critical') return battery.health_status === 'critical'
      return true
    })
  }
  
  return result
})

// 方法
const testNavigateToAnalysis = () => {
  console.log('测试按钮：跳转到健康度分析')
  console.log('当前路由:', router.currentRoute.value.path)
  try {
    // 先测试简化版页面
    router.push('/battery-management/analysis-simple')
    console.log('router.push 调用成功 (简化版)')
  } catch (error) {
    console.error('router.push 失败:', error)
    // 备用方案：使用window.location
    window.location.href = '/battery-management/analysis-simple'
  }
}

const testNavigateToMaintenance = () => {
  console.log('测试按钮：跳转到电池维护')
  router.push('/battery-management/maintenance')
}

const toggleMonitoring = () => {
  monitoringActive.value = !monitoringActive.value
  if (monitoringActive.value) {
    startMonitoring()
    ElMessage.success('监控已启动')
  } else {
    stopMonitoring()
    ElMessage.info('监控已停止')
  }
}

const startMonitoring = () => {
  if (monitoringInterval.value) return
  
  monitoringInterval.value = setInterval(() => {
    updateLastUpdateTime()
    // 这里可以添加实时数据更新逻辑
  }, 5000) // 每5秒更新一次
}

const stopMonitoring = () => {
  if (monitoringInterval.value) {
    clearInterval(monitoringInterval.value)
    monitoringInterval.value = null
  }
}

const updateLastUpdateTime = () => {
  lastUpdateTime.value = new Date().toLocaleTimeString('zh-CN')
}

const refreshData = async () => {
  try {
    await batteryStore.fetchBatteries()
    updateLastUpdateTime()
    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败')
  }
}

const getStatusClass = (battery: any) => {
  if (battery.health_status === 'critical') return 'critical'
  if (battery.health_status === 'warning') return 'warning'
  return 'normal'
}

const getRowClassName = ({ row }: { row: any }) => {
  if (row.health_status === 'critical') return 'critical-row'
  if (row.health_status === 'warning') return 'warning-row'
  return ''
}

const getHealthColor = (health: number) => {
  if (health >= 80) return '#67C23A'
  if (health >= 60) return '#E6A23C'
  return '#F56C6C'
}

const getTemperatureClass = (temperature: number) => {
  if (temperature > 35) return 'temp-high'
  if (temperature > 30) return 'temp-medium'
  return 'temp-normal'
}

const getTemperatureStatus = (temperature: number) => {
  if (temperature > 35) return 'alert'
  if (temperature > 30) return 'warning'
  return 'normal'
}

const getRelativeTime = (dateString: string) => {
  const now = new Date()
  const date = new Date(dateString)
  const diff = now.getTime() - date.getTime()
  const minutes = Math.floor(diff / 60000)
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  const hours = Math.floor(minutes / 60)
  if (hours < 24) return `${hours}小时前`
  const days = Math.floor(hours / 24)
  return `${days}天前`
}

const viewDetail = (batteryId: string) => {
  ElMessage.info(`查看电池 ${batteryId} 详情`)
}

const viewHistory = (batteryId: string) => {
  ElMessage.info(`查看电池 ${batteryId} 历史数据`)
}

const getAlertIcon = (level: string) => {
  const icons: Record<string, any> = {
    'critical': CircleClose,
    'warning': Warning,
    'info': InfoFilled
  }
  return icons[level] || InfoFilled
}

const handleAlert = (alert: any) => {
  ElMessage.info(`处理警报: ${alert.title}`)
}

const dismissAlert = (alertId: string) => {
  const index = alerts.value.findIndex(a => a.id === alertId)
  if (index !== -1) {
    alerts.value.splice(index, 1)
    ElMessage.success('警报已忽略')
  }
}

const clearAllAlerts = () => {
  alerts.value = []
  ElMessage.success('所有警报已清除')
}

const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN')
}

// 生命周期
onMounted(() => {
  batteryStore.initializeMockData()
  batteryStore.fetchBatteries()
  updateLastUpdateTime()
  startMonitoring()
})

onUnmounted(() => {
  stopMonitoring()
})
</script>

<style scoped>
.battery-monitoring {
  padding: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  position: relative;
}

.battery-monitoring::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 50%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
  pointer-events: none;
}

/* 页面头部 */
.page-header {
  position: relative;
  z-index: 1;
  padding: 32px 24px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  color: white;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 36px;
  color: #fbbf24;
}

.page-description {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.monitoring-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

/* 实时概览 */
.realtime-overview {
  position: relative;
  z-index: 1;
  padding: 0 24px 32px;
  max-width: 1200px;
  margin: 0 auto;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.realtime-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.realtime-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.card-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-indicator.online {
  background: #10b981;
}

.status-indicator.normal {
  background: #6b7280;
}

.status-indicator.warning {
  background: #f59e0b;
}

.status-indicator.alert {
  background: #ef4444;
}

.status-indicator.updating {
  background: #3b82f6;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.card-content {
  display: flex;
  align-items: baseline;
  gap: 8px;
}

.metric-value {
  font-size: 32px;
  font-weight: 700;
  color: #111827;
}

.metric-value.small {
  font-size: 16px;
}

.metric-value.alert {
  color: #ef4444;
}

.metric-total {
  font-size: 18px;
  color: #6b7280;
}

.metric-label {
  font-size: 14px;
  color: #6b7280;
}

/* 监控表格 */
.monitoring-table {
  position: relative;
  z-index: 1;
  padding: 0 24px 32px;
  max-width: 1200px;
  margin: 0 auto;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: white;
  margin: 0;
}

.table-controls {
  display: flex;
  gap: 12px;
}

/* 表格样式 */
.monitoring-table :deep(.el-table) {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  overflow: hidden;
}

.monitoring-table :deep(.critical-row) {
  background: rgba(239, 68, 68, 0.1) !important;
}

.monitoring-table :deep(.warning-row) {
  background: rgba(245, 158, 11, 0.1) !important;
}

.status-indicator-small {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin: 0 auto;
  animation: pulse 2s infinite;
}

.status-indicator-small.normal {
  background: #10b981;
}

.status-indicator-small.warning {
  background: #f59e0b;
}

.status-indicator-small.critical {
  background: #ef4444;
}

.health-display, .charge-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.health-text, .charge-text {
  font-size: 12px;
  font-weight: 500;
  min-width: 35px;
}

.temperature-value {
  font-weight: 500;
}

.temperature-value.temp-normal {
  color: #10b981;
}

.temperature-value.temp-medium {
  color: #f59e0b;
}

.temperature-value.temp-high {
  color: #ef4444;
}

.voltage-value {
  font-weight: 500;
  color: #6b7280;
}

.vehicle-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.vehicle-name {
  font-size: 13px;
  font-weight: 500;
  color: #111827;
}

.vehicle-vin {
  font-size: 12px;
  color: #6b7280;
}

.no-vehicle {
  font-size: 12px;
  color: #9ca3af;
  font-style: italic;
}

.update-time {
  font-size: 12px;
  color: #6b7280;
}

.table-actions {
  display: flex;
  gap: 4px;
}

/* 警报面板 */
.alerts-panel {
  position: relative;
  z-index: 1;
  padding: 0 24px 32px;
  max-width: 1200px;
  margin: 0 auto;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.panel-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: white;
  margin: 0;
}

.alerts-list {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  overflow: hidden;
}

.alert-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 20px;
  border-bottom: 1px solid #f3f4f6;
  transition: all 0.3s ease;
}

.alert-item:last-child {
  border-bottom: none;
}

.alert-item:hover {
  background: rgba(0, 0, 0, 0.02);
}

.alert-item.critical {
  border-left: 4px solid #ef4444;
}

.alert-item.warning {
  border-left: 4px solid #f59e0b;
}

.alert-item.info {
  border-left: 4px solid #3b82f6;
}

.alert-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
}

.alert-item.critical .alert-icon {
  background: #ef4444;
}

.alert-item.warning .alert-icon {
  background: #f59e0b;
}

.alert-item.info .alert-icon {
  background: #3b82f6;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 4px;
}

.alert-message {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 4px;
}

.alert-time {
  font-size: 12px;
  color: #9ca3af;
}

.alert-actions {
  display: flex;
  gap: 8px;
}

/* 导航按钮样式 */
.navigation-tabs {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.nav-buttons {
  display: flex;
  gap: 16px;
  align-items: center;
}

.nav-buttons .el-button {
  height: 40px;
  padding: 0 20px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.nav-buttons .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.nav-buttons .el-button .el-icon {
  margin-right: 8px;
}

/* 当前页面按钮样式 */
.nav-buttons .el-button[disabled] {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  opacity: 1;
  cursor: default;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .header-actions {
    flex-direction: column;
    gap: 8px;
    width: 100%;
  }

  .overview-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }

  .table-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .table-controls {
    width: 100%;
    justify-content: space-between;
  }

  .alert-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .alert-actions {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>
