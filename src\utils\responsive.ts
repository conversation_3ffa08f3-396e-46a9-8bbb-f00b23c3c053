// 响应式设计工具类

// 断点定义
export const breakpoints = {
  xs: 0,      // 超小屏幕
  sm: 576,    // 小屏幕
  md: 768,    // 中等屏幕
  lg: 992,    // 大屏幕
  xl: 1200,   // 超大屏幕
  xxl: 1400   // 超超大屏幕
} as const

export type Breakpoint = keyof typeof breakpoints

// 媒体查询字符串
export const mediaQueries = {
  xs: `(max-width: ${breakpoints.sm - 1}px)`,
  sm: `(min-width: ${breakpoints.sm}px) and (max-width: ${breakpoints.md - 1}px)`,
  md: `(min-width: ${breakpoints.md}px) and (max-width: ${breakpoints.lg - 1}px)`,
  lg: `(min-width: ${breakpoints.lg}px) and (max-width: ${breakpoints.xl - 1}px)`,
  xl: `(min-width: ${breakpoints.xl}px) and (max-width: ${breakpoints.xxl - 1}px)`,
  xxl: `(min-width: ${breakpoints.xxl}px)`,
  
  // 向上断点（从指定断点开始）
  smUp: `(min-width: ${breakpoints.sm}px)`,
  mdUp: `(min-width: ${breakpoints.md}px)`,
  lgUp: `(min-width: ${breakpoints.lg}px)`,
  xlUp: `(min-width: ${breakpoints.xl}px)`,
  xxlUp: `(min-width: ${breakpoints.xxl}px)`,
  
  // 向下断点（到指定断点为止）
  xsDown: `(max-width: ${breakpoints.sm - 1}px)`,
  smDown: `(max-width: ${breakpoints.md - 1}px)`,
  mdDown: `(max-width: ${breakpoints.lg - 1}px)`,
  lgDown: `(max-width: ${breakpoints.xl - 1}px)`,
  xlDown: `(max-width: ${breakpoints.xxl - 1}px)`
} as const

// 响应式值类型
export interface ResponsiveValue<T> {
  xs?: T
  sm?: T
  md?: T
  lg?: T
  xl?: T
  xxl?: T
}

// 获取当前断点
export function getCurrentBreakpoint(): Breakpoint {
  const width = window.innerWidth
  
  if (width >= breakpoints.xxl) return 'xxl'
  if (width >= breakpoints.xl) return 'xl'
  if (width >= breakpoints.lg) return 'lg'
  if (width >= breakpoints.md) return 'md'
  if (width >= breakpoints.sm) return 'sm'
  return 'xs'
}

// 检查是否匹配指定断点
export function matchesBreakpoint(breakpoint: Breakpoint): boolean {
  const width = window.innerWidth
  return width >= breakpoints[breakpoint]
}

// 检查是否为移动设备
export function isMobile(): boolean {
  return window.innerWidth < breakpoints.md
}

// 检查是否为平板设备
export function isTablet(): boolean {
  const width = window.innerWidth
  return width >= breakpoints.md && width < breakpoints.lg
}

// 检查是否为桌面设备
export function isDesktop(): boolean {
  return window.innerWidth >= breakpoints.lg
}

// 根据断点获取响应式值
export function getResponsiveValue<T>(value: ResponsiveValue<T> | T, fallback?: T): T | undefined {
  if (typeof value !== 'object' || value === null) {
    return value as T
  }
  
  const currentBreakpoint = getCurrentBreakpoint()
  const responsiveValue = value as ResponsiveValue<T>
  
  // 按优先级查找值
  const breakpointOrder: Breakpoint[] = ['xxl', 'xl', 'lg', 'md', 'sm', 'xs']
  const currentIndex = breakpointOrder.indexOf(currentBreakpoint)
  
  // 从当前断点开始向下查找
  for (let i = currentIndex; i < breakpointOrder.length; i++) {
    const bp = breakpointOrder[i]
    if (responsiveValue[bp] !== undefined) {
      return responsiveValue[bp]
    }
  }
  
  // 如果没找到，向上查找
  for (let i = currentIndex - 1; i >= 0; i--) {
    const bp = breakpointOrder[i]
    if (responsiveValue[bp] !== undefined) {
      return responsiveValue[bp]
    }
  }
  
  return fallback
}

// 创建媒体查询监听器
export function createMediaQueryListener(
  query: string,
  callback: (matches: boolean) => void
): () => void {
  const mediaQuery = window.matchMedia(query)
  
  // 立即执行一次
  callback(mediaQuery.matches)
  
  // 添加监听器
  const listener = (e: MediaQueryListEvent) => callback(e.matches)
  mediaQuery.addEventListener('change', listener)
  
  // 返回清理函数
  return () => mediaQuery.removeEventListener('change', listener)
}

// 响应式类名生成器
export function generateResponsiveClasses(
  baseClass: string,
  values: ResponsiveValue<string | boolean>
): string {
  const classes: string[] = []
  
  Object.entries(values).forEach(([breakpoint, value]) => {
    if (value) {
      const suffix = typeof value === 'string' ? value : ''
      const className = breakpoint === 'xs' 
        ? `${baseClass}${suffix}`
        : `${breakpoint}:${baseClass}${suffix}`
      classes.push(className)
    }
  })
  
  return classes.join(' ')
}

// 响应式样式生成器
export function generateResponsiveStyles(
  property: string,
  values: ResponsiveValue<string | number>
): string {
  const styles: string[] = []
  
  Object.entries(values).forEach(([breakpoint, value]) => {
    if (value !== undefined) {
      const cssValue = typeof value === 'number' ? `${value}px` : value
      
      if (breakpoint === 'xs') {
        styles.push(`${property}: ${cssValue};`)
      } else {
        const query = mediaQueries[`${breakpoint}Up` as keyof typeof mediaQueries]
        styles.push(`@media ${query} { ${property}: ${cssValue}; }`)
      }
    }
  })
  
  return styles.join(' ')
}

// 容器查询支持
export function supportsContainerQueries(): boolean {
  return 'container' in document.documentElement.style
}

// 视口单位转换
export function vwToPx(vw: number): number {
  return (vw * window.innerWidth) / 100
}

export function vhToPx(vh: number): number {
  return (vh * window.innerHeight) / 100
}

export function pxToVw(px: number): number {
  return (px * 100) / window.innerWidth
}

export function pxToVh(px: number): number {
  return (px * 100) / window.innerHeight
}

// 响应式字体大小计算
export function getResponsiveFontSize(
  minSize: number,
  maxSize: number,
  minWidth: number = breakpoints.sm,
  maxWidth: number = breakpoints.xl
): string {
  const slope = (maxSize - minSize) / (maxWidth - minWidth)
  const yAxisIntersection = -minWidth * slope + minSize
  
  return `clamp(${minSize}px, ${yAxisIntersection}px + ${slope * 100}vw, ${maxSize}px)`
}

// 响应式间距计算
export function getResponsiveSpacing(
  minSpacing: number,
  maxSpacing: number,
  minWidth: number = breakpoints.sm,
  maxWidth: number = breakpoints.xl
): string {
  return getResponsiveFontSize(minSpacing, maxSpacing, minWidth, maxWidth)
}

// 设备方向检测
export function getOrientation(): 'portrait' | 'landscape' {
  return window.innerHeight > window.innerWidth ? 'portrait' : 'landscape'
}

// 触摸设备检测
export function isTouchDevice(): boolean {
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0
}

// 高分辨率屏幕检测
export function isHighDPI(): boolean {
  return window.devicePixelRatio > 1
}

// 暗色模式检测
export function prefersDarkMode(): boolean {
  return window.matchMedia('(prefers-color-scheme: dark)').matches
}

// 减少动画偏好检测
export function prefersReducedMotion(): boolean {
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches
}

// 高对比度偏好检测
export function prefersHighContrast(): boolean {
  return window.matchMedia('(prefers-contrast: high)').matches
}

// 响应式图片源生成
export function generateResponsiveImageSources(
  basePath: string,
  sizes: ResponsiveValue<{ width: number; height?: number }>
): Array<{ srcset: string; media: string; sizes: string }> {
  const sources: Array<{ srcset: string; media: string; sizes: string }> = []
  
  Object.entries(sizes).forEach(([breakpoint, size]) => {
    if (size) {
      const { width, height } = size
      const srcset = height 
        ? `${basePath}?w=${width}&h=${height} 1x, ${basePath}?w=${width * 2}&h=${height * 2} 2x`
        : `${basePath}?w=${width} 1x, ${basePath}?w=${width * 2} 2x`
      
      const media = breakpoint === 'xs' 
        ? mediaQueries.xsDown
        : mediaQueries[`${breakpoint}Up` as keyof typeof mediaQueries]
      
      sources.push({
        srcset,
        media,
        sizes: `${width}px`
      })
    }
  })
  
  return sources.reverse() // 大屏优先
}

// 响应式网格系统
export interface GridConfig {
  columns: ResponsiveValue<number>
  gap: ResponsiveValue<string | number>
  minItemWidth?: ResponsiveValue<string | number>
}

export function generateGridStyles(config: GridConfig): string {
  const { columns, gap, minItemWidth } = config
  const styles: string[] = []
  
  // 基础网格样式
  styles.push('display: grid;')
  
  // 响应式列数
  if (columns) {
    Object.entries(columns).forEach(([breakpoint, cols]) => {
      if (cols !== undefined) {
        const gridTemplate = minItemWidth
          ? `repeat(auto-fit, minmax(${getResponsiveValue(minItemWidth)}, 1fr))`
          : `repeat(${cols}, 1fr)`
        
        if (breakpoint === 'xs') {
          styles.push(`grid-template-columns: ${gridTemplate};`)
        } else {
          const query = mediaQueries[`${breakpoint}Up` as keyof typeof mediaQueries]
          styles.push(`@media ${query} { grid-template-columns: ${gridTemplate}; }`)
        }
      }
    })
  }
  
  // 响应式间距
  if (gap) {
    Object.entries(gap).forEach(([breakpoint, gapValue]) => {
      if (gapValue !== undefined) {
        const cssGap = typeof gapValue === 'number' ? `${gapValue}px` : gapValue
        
        if (breakpoint === 'xs') {
          styles.push(`gap: ${cssGap};`)
        } else {
          const query = mediaQueries[`${breakpoint}Up` as keyof typeof mediaQueries]
          styles.push(`@media ${query} { gap: ${cssGap}; }`)
        }
      }
    })
  }
  
  return styles.join(' ')
}

// 导出默认配置
export const defaultResponsiveConfig = {
  containerMaxWidths: {
    sm: 540,
    md: 720,
    lg: 960,
    xl: 1140,
    xxl: 1320
  },
  
  gridColumns: {
    xs: 1,
    sm: 2,
    md: 3,
    lg: 4,
    xl: 6,
    xxl: 8
  },
  
  spacing: {
    xs: 8,
    sm: 12,
    md: 16,
    lg: 24,
    xl: 32,
    xxl: 40
  },
  
  fontSize: {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
    xxl: 24
  }
}

// 类型导出
export type MediaQuery = keyof typeof mediaQueries
export type GridConfigType = GridConfig