const mysql = require('mysql2/promise');
const bcrypt = require('bcrypt');

async function fixAllPasswords() {
  let connection;
  
  try {
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '123456',
      database: 'ev_management'
    });
    
    console.log('🔌 连接到MySQL数据库...');
    
    // 查询所有用户
    const [rows] = await connection.execute(
      'SELECT id, email, password_hash FROM users'
    );
    
    console.log(`📊 找到 ${rows.length} 个用户`);
    
    // 测试密码（根据之前的设置）
    const testPassword = '123456';
    const newHash = await bcrypt.hash(testPassword, 10);
    
    console.log('\n🔧 修复所有用户密码...');
    
    for (const user of rows) {
      console.log(`\n👤 处理用户: ${user.email}`);
      
      // 检查当前密码是否有效
      let isValid = false;
      try {
        isValid = await bcrypt.compare(testPassword, user.password_hash);
      } catch (error) {
        console.log('  ⚠️ 密码哈希格式错误');
      }
      
      if (!isValid) {
        // 更新密码
        await connection.execute(
          'UPDATE users SET password_hash = ? WHERE id = ?',
          [newHash, user.id]
        );
        console.log('  ✅ 密码已更新');
      } else {
        console.log('  ✓ 密码已正确');
      }
    }
    
    console.log('\n🎉 所有用户密码修复完成！');
    console.log('📝 所有用户的登录密码都是: 123456');
    
  } catch (error) {
    console.error('❌ 错误:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

fixAllPasswords();