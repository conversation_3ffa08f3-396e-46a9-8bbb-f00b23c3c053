# OAuth 第三方登录配置说明

## 概述

本系统已实现 GitHub 和 Google 第三方登录功能。用户可以通过这些平台快速登录系统，无需单独注册账户。

## 功能特性

### 已实现功能

1. **完整的登录/注册系统**
   - 邮箱密码登录
   - 用户注册（包含姓名、手机号验证）
   - 表单验证（邮箱格式、密码强度、手机号格式）
   - 登录状态管理

2. **第三方登录支持**
   - GitHub OAuth 登录
   - Google OAuth 登录
   - 自动用户创建和信息同步
   - 安全的 token 管理

3. **用户体验优化**
   - 动态表单（登录/注册模式切换）
   - 加载动画和状态提示
   - 错误处理和用户反馈
   - 响应式设计

## 配置步骤

### 1. GitHub OAuth 配置

#### 创建 GitHub OAuth App

1. 访问 [GitHub Developer Settings](https://github.com/settings/developers)
2. 点击 "New OAuth App"
3. 填写应用信息：
   - **Application name**: 新能源汽车管理系统
   - **Homepage URL**: `http://localhost:5173`
   - **Authorization callback URL**: `http://localhost:5173/auth/callback/github`
4. 创建后获取 `Client ID` 和 `Client Secret`

#### 更新环境变量

在 `.env` 文件中更新：

```env
# GitHub OAuth
VITE_GITHUB_CLIENT_ID=your_actual_github_client_id
GITHUB_CLIENT_SECRET=your_actual_github_client_secret
```

### 2. Google OAuth 配置

#### 创建 Google OAuth 凭据

1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建新项目或选择现有项目
3. 启用 Google+ API
4. 转到 "凭据" 页面
5. 创建 "OAuth 2.0 客户端 ID"：
   - **应用类型**: Web 应用
   - **授权重定向 URI**: `http://localhost:5173/auth/callback/google`
6. 获取 `Client ID` 和 `Client Secret`

#### 更新环境变量

在 `.env` 文件中更新：

```env
# Google OAuth
VITE_GOOGLE_CLIENT_ID=your_actual_google_client_id
GOOGLE_CLIENT_SECRET=your_actual_google_client_secret
```

### 3. 生产环境配置

对于生产环境，需要更新回调 URL：

- GitHub: `https://yourdomain.com/auth/callback/github`
- Google: `https://yourdomain.com/auth/callback/google`

并在 `.env` 中添加：

```env
BASE_URL=https://yourdomain.com
```

## 使用说明

### 用户登录流程

1. **普通登录**
   - 输入邮箱和密码
   - 点击 "ENTER ZONE" 登录

2. **用户注册**
   - 点击 "CREATE ACCOUNT" 切换到注册模式
   - 填写邮箱、姓名、手机号、密码
   - 点击 "创建账户" 完成注册

3. **第三方登录**
   - 点击 "GITHUB" 或 "GOOGLE" 按钮
   - 跳转到对应平台进行授权
   - 授权成功后自动返回系统

### 安全特性

1. **密码安全**
   - 密码使用 bcrypt 加密存储
   - 最少 6 位密码要求

2. **Token 管理**
   - JWT token 用于身份验证
   - 支持 refresh token 自动续期
   - 安全的 token 存储和传输

3. **数据验证**
   - 前端表单验证
   - 后端数据验证
   - SQL 注入防护

## 技术实现

### 前端技术栈

- **Vue 3** + **TypeScript**
- **Pinia** 状态管理
- **Vue Router** 路由管理
- **Element Plus** UI 组件

### 后端技术栈

- **Node.js** + **Express**
- **JWT** 身份验证
- **bcrypt** 密码加密
- **MySQL** 数据存储

### OAuth 流程

1. 用户点击第三方登录按钮
2. 跳转到第三方授权页面
3. 用户授权后返回回调页面
4. 后端验证授权码并获取用户信息
5. 创建或更新用户账户
6. 生成 JWT token
7. 前端保存 token 并跳转到主页

## 故障排除

### 常见问题

1. **OAuth 登录失败**
   - 检查 Client ID 和 Secret 是否正确
   - 确认回调 URL 配置正确
   - 检查网络连接

2. **注册失败**
   - 检查邮箱格式是否正确
   - 确认密码长度至少 6 位
   - 验证手机号格式

3. **登录状态丢失**
   - 检查 localStorage 中的 token
   - 确认 token 未过期
   - 检查网络连接

### 调试方法

1. 打开浏览器开发者工具
2. 查看 Console 中的错误信息
3. 检查 Network 标签中的 API 请求
4. 查看 Application 标签中的 localStorage

## 后续优化

### 计划功能

1. **更多第三方登录**
   - 微信登录
   - QQ 登录
   - 微博登录

2. **安全增强**
   - 两步验证
   - 登录设备管理
   - 异常登录检测

3. **用户体验**
   - 记住登录状态
   - 自动登录
   - 密码找回功能

---

**注意**: 在生产环境中使用前，请确保所有敏感信息（如 Client Secret）都通过环境变量安全配置，不要提交到代码仓库中。