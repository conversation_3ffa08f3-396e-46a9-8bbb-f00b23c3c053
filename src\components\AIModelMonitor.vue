<template>
  <div class="ai-model-monitor">
    <!-- AI模型状态概览 -->
    <div class="status-overview">
      <h3 class="section-title">
        <Brain class="title-icon" />
        AI模型状态概览
      </h3>
      <div class="status-grid">
        <div class="status-card">
          <div class="status-header">
            <Activity class="status-icon active" />
            <span class="status-label">运行状态</span>
          </div>
          <div class="status-value">{{ modelHealth.active }}/{{ modelHealth.total }}</div>
          <div class="status-desc">活跃模型</div>
        </div>
        <div class="status-card">
          <div class="status-header">
            <Target class="status-icon" />
            <span class="status-label">平均准确率</span>
          </div>
          <div class="status-value">{{ modelHealth.avgAccuracy.toFixed(1) }}%</div>
          <div class="status-desc">模型精度</div>
        </div>
        <div class="status-card">
          <div class="status-header">
            <TrendingUp class="status-icon" />
            <span class="status-label">预测次数</span>
          </div>
          <div class="status-value">{{ systemStats.predictions }}</div>
          <div class="status-desc">今日预测</div>
        </div>
        <div class="status-card">
          <div class="status-header">
            <AlertTriangle class="status-icon warning" />
            <span class="status-label">诊断次数</span>
          </div>
          <div class="status-value">{{ systemStats.diagnoses }}</div>
          <div class="status-desc">故障诊断</div>
        </div>
      </div>
    </div>

    <!-- AI模型列表 -->
    <div class="models-section">
      <div class="section-header">
        <h3 class="section-title">
          <Cpu class="title-icon" />
          AI模型管理
        </h3>
        <div class="section-actions">
          <div style="position: relative;">
            <ModernLoader v-if="loading" />
            <ModernButton @click="refreshModels" :disabled="loading" size="small" variant="ghost">
              <template #icon>
                <RefreshCw class="btn-icon" />
              </template>
              刷新
            </ModernButton>
          </div>
          <ModernButton @click="showTrainDialog = true" variant="primary" size="small">
            <template #icon>
              <Play class="btn-icon" />
            </template>
            训练模型
          </ModernButton>
        </div>
      </div>
      <div class="models-grid">
        <div 
          v-for="model in models" 
          :key="model.id" 
          class="model-card"
          :class="{ active: selectedModel?.id === model.id }"
          @click="selectModel(model)"
        >
          <div class="model-header">
            <div class="model-info">
              <h4 class="model-name">{{ model.name }}</h4>
              <span class="model-version">v{{ model.version }}</span>
            </div>
            <div class="model-status" :class="model.status">
              <div class="status-dot"></div>
              <span>{{ getStatusText(model.status) }}</span>
            </div>
          </div>
          <div class="model-body">
            <div class="model-type">
              <component :is="getTypeIcon(model.type)" class="type-icon" />
              <span>{{ getTypeText(model.type) }}</span>
            </div>
            <div class="model-accuracy">
              <span class="accuracy-label">准确率:</span>
              <span class="accuracy-value">{{ model.accuracy.toFixed(1) }}%</span>
            </div>
            <div class="model-trained">
              <Clock class="time-icon" />
              <span>{{ formatTime(model.lastTrained) }}</span>
            </div>
          </div>
          <div class="model-actions">
            <ModernButton @click.stop="testModel(model)" size="small" variant="primary">
              <template #icon>
                <Zap class="btn-icon" />
              </template>
              测试
            </ModernButton>
            <ModernButton @click.stop="viewModelStats(model)" size="small" variant="ghost">
              <template #icon>
                <BarChart3 class="btn-icon" />
              </template>
              统计
            </ModernButton>
          </div>
        </div>
      </div>
    </div>

    <!-- 预测结果 -->
    <div class="predictions-section">
      <div class="section-header">
        <h3 class="section-title">
          <TrendingUp class="title-icon" />
          预测结果
        </h3>
        <div class="section-actions">
          <el-select v-model="selectedPredictionModel" placeholder="选择模型" size="small" style="width: 150px">
            <el-option 
              v-for="model in predictionModels" 
              :key="model.id" 
              :label="model.name" 
              :value="model.id"
            />
          </el-select>
          <ModernButton @click="runPrediction" variant="primary" size="small">
            <template #icon>
              <Play class="btn-icon" />
            </template>
            运行预测
          </ModernButton>
        </div>
      </div>
      <div class="predictions-list">
        <div v-for="prediction in predictions" :key="prediction.id" class="prediction-item">
          <div class="prediction-header">
            <div class="prediction-info">
              <span class="prediction-id">{{ prediction.id }}</span>
              <span class="prediction-time">{{ formatTime(prediction.timestamp) }}</span>
            </div>
            <div class="prediction-confidence">
              <span class="confidence-label">置信度:</span>
              <span class="confidence-value" :class="getConfidenceClass(prediction.confidence)">
                {{ (prediction.confidence * 100).toFixed(1) }}%
              </span>
            </div>
          </div>
          <div class="prediction-content">
            <div class="prediction-input">
              <h5>输入参数:</h5>
              <div class="param-list">
                <span v-for="(value, key) in prediction.input" :key="key" class="param-item">
                  {{ key }}: {{ typeof value === 'number' ? value.toFixed(2) : value }}
                </span>
              </div>
            </div>
            <div class="prediction-output">
              <h5>预测结果:</h5>
              <div class="result-list">
                <span v-for="(value, key) in prediction.output" :key="key" class="result-item">
                  {{ key }}: {{ typeof value === 'number' ? value.toFixed(2) : value }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分析报告 -->
    <div class="analysis-section">
      <div class="section-header">
        <h3 class="section-title">
          <BarChart3 class="title-icon" />
          分析报告
        </h3>
        <div class="section-actions">
          <ModernButton @click="runAnalysis" variant="primary" size="small">
            <template #icon>
              <Search class="btn-icon" />
            </template>
            运行分析
          </ModernButton>
        </div>
      </div>
      <div class="analysis-list">
        <div v-for="analysis in analyses" :key="analysis.id" class="analysis-item">
          <div class="analysis-header">
            <div class="analysis-type">
              <component :is="getAnalysisIcon(analysis.type)" class="type-icon" />
              <span>{{ getAnalysisTypeText(analysis.type) }}</span>
            </div>
            <div class="analysis-confidence">
              <span class="confidence-value" :class="getConfidenceClass(analysis.confidence)">
                {{ (analysis.confidence * 100).toFixed(1) }}%
              </span>
            </div>
          </div>
          <div class="analysis-content">
            <div class="analysis-insights">
              <h5>分析洞察:</h5>
              <ul class="insights-list">
                <li v-for="insight in analysis.insights" :key="insight">{{ insight }}</li>
              </ul>
            </div>
            <div class="analysis-recommendations">
              <h5>建议措施:</h5>
              <ul class="recommendations-list">
                <li v-for="rec in analysis.recommendations" :key="rec">{{ rec }}</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 故障诊断 -->
    <div class="diagnosis-section">
      <div class="section-header">
        <h3 class="section-title">
          <AlertTriangle class="title-icon" />
          故障诊断
        </h3>
        <div class="section-actions">
          <ModernButton @click="showDiagnosisDialog = true" variant="primary" size="small">
            <template #icon>
              <Stethoscope class="btn-icon" />
            </template>
            新建诊断
          </ModernButton>
        </div>
      </div>
      <div class="diagnosis-list">
        <div v-for="diagnosis in diagnoses" :key="diagnosis.id" class="diagnosis-item">
          <div class="diagnosis-header">
            <div class="diagnosis-info">
              <span class="vehicle-id">车辆: {{ diagnosis.vehicleId }}</span>
              <span class="component">{{ diagnosis.component }}</span>
            </div>
            <div class="diagnosis-severity" :class="diagnosis.severity">
              <AlertTriangle class="severity-icon" />
              <span>{{ getSeverityText(diagnosis.severity) }}</span>
            </div>
          </div>
          <div class="diagnosis-content">
            <div class="diagnosis-issue">
              <h5>问题描述:</h5>
              <p>{{ diagnosis.issue }}</p>
            </div>
            <div class="diagnosis-details">
              <div class="diagnosis-probability">
                <span class="label">故障概率:</span>
                <span class="value">{{ (diagnosis.probability * 100).toFixed(1) }}%</span>
              </div>
              <div class="diagnosis-cost" v-if="diagnosis.estimatedCost">
                <span class="label">预估费用:</span>
                <span class="value">¥{{ diagnosis.estimatedCost.toFixed(0) }}</span>
              </div>
              <div class="diagnosis-urgency">
                <span class="label">紧急程度:</span>
                <span class="value">{{ diagnosis.urgency }}/10</span>
              </div>
            </div>
            <div class="diagnosis-solutions">
              <h5>解决方案:</h5>
              <ul class="solutions-list">
                <li v-for="solution in diagnosis.solutions" :key="solution">{{ solution }}</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 个性化推荐 -->
    <div class="recommendations-section">
      <div class="section-header">
        <h3 class="section-title">
          <Lightbulb class="title-icon" />
          个性化推荐
        </h3>
        <div class="section-actions">
          <ModernButton @click="generateRecommendations" variant="primary" size="small">
            <template #icon>
              <Sparkles class="btn-icon" />
            </template>
            生成推荐
          </ModernButton>
        </div>
      </div>
      <div class="recommendations-grid">
        <div v-for="rec in recommendations" :key="rec.id" class="recommendation-card">
          <div class="rec-header">
            <div class="rec-type">
              <component :is="getRecommendationIcon(rec.type)" class="type-icon" />
              <span>{{ getRecommendationTypeText(rec.type) }}</span>
            </div>
            <div class="rec-priority" :class="getPriorityClass(rec.priority)">
              <span>{{ rec.priority }}</span>
            </div>
          </div>
          <div class="rec-content">
            <h4 class="rec-title">{{ rec.title }}</h4>
            <p class="rec-description">{{ rec.description }}</p>
            <div class="rec-benefit">
              <TrendingUp class="benefit-icon" />
              <span>{{ rec.expectedBenefit }}</span>
            </div>
          </div>
          <div class="rec-actions">
            <ModernButton v-if="rec.actionRequired" variant="primary" size="small">
              <template #icon>
                <CheckCircle class="btn-icon" />
              </template>
              执行
            </ModernButton>
            <ModernButton size="small" variant="ghost">
              <template #icon>
                <Eye class="btn-icon" />
              </template>
              详情
            </ModernButton>
          </div>
        </div>
      </div>
    </div>

    <!-- 训练模型对话框 -->
    <el-dialog v-model="showTrainDialog" title="训练AI模型" width="500px">
      <el-form :model="trainForm" label-width="100px">
        <el-form-item label="选择模型">
          <el-select v-model="trainForm.modelId" placeholder="请选择要训练的模型">
            <el-option 
              v-for="model in models" 
              :key="model.id" 
              :label="model.name" 
              :value="model.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="训练数据">
          <ModernInput 
            v-model="trainForm.dataSize" 
            type="number" 
            placeholder="训练数据量"
            variant="glass"
          />
        </el-form-item>
        <el-form-item label="训练轮次">
          <ModernInput 
            v-model="trainForm.epochs" 
            type="number" 
            placeholder="训练轮次"
            variant="glass"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <ModernButton @click="showTrainDialog = false" variant="ghost">取消</ModernButton>
        <ModernButton @click="startTraining" variant="primary" :loading="training">
          开始训练
        </ModernButton>
      </template>
    </el-dialog>

    <!-- 故障诊断对话框 -->
    <el-dialog v-model="showDiagnosisDialog" title="故障诊断" width="500px">
      <el-form :model="diagnosisForm" label-width="100px">
        <el-form-item label="车辆ID">
          <ModernInput v-model="diagnosisForm.vehicleId" placeholder="请输入车辆ID" variant="glass" />
        </el-form-item>
        <el-form-item label="故障症状">
          <el-select 
            v-model="diagnosisForm.symptoms" 
            multiple 
            placeholder="请选择故障症状"
          >
            <el-option label="温度异常" value="温度异常" />
            <el-option label="性能下降" value="性能下降" />
            <el-option label="异常噪音" value="异常噪音" />
            <el-option label="充电异常" value="充电异常" />
            <el-option label="制动异常" value="制动异常" />
          </el-select>
        </el-form-item>
        <el-form-item label="详细描述">
          <ModernInput 
            v-model="diagnosisForm.description" 
            type="textarea" 
            placeholder="请详细描述故障现象"
            variant="glass"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <ModernButton @click="showDiagnosisDialog = false" variant="ghost">取消</ModernButton>
        <div style="position: relative;">
          <ModernLoader v-if="diagnosing" />
          <ModernButton @click="startDiagnosis" variant="primary" :disabled="diagnosing">
            开始诊断
          </ModernButton>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { ModernButton, ModernLoader, ModernInput } from '@/components/ui'
import {
  Brain, Activity, Target, TrendingUp, AlertTriangle, Cpu, RefreshCw, Play,
  Clock, Zap, BarChart3, Search, Stethoscope, Lightbulb, Sparkles,
  CheckCircle, Eye, Database, Gauge, Route, Wrench, Zap as Lightning
} from 'lucide-vue-next'
import { aiModelService } from '@/services/aiModelService'
import type { AIModel, PredictionResult, AnalysisResult, DiagnosisResult, Recommendation } from '@/services/aiModelService'

// 响应式数据
const loading = ref(false)
const training = ref(false)
const diagnosing = ref(false)
const showTrainDialog = ref(false)
const showDiagnosisDialog = ref(false)

const models = ref<AIModel[]>([])
const predictions = ref<PredictionResult[]>([])
const analyses = ref<AnalysisResult[]>([])
const diagnoses = ref<DiagnosisResult[]>([])
const recommendations = ref<Recommendation[]>([])

const selectedModel = ref<AIModel | null>(null)
const selectedPredictionModel = ref('')

// 表单数据
const trainForm = reactive({
  modelId: '',
  dataSize: 1000,
  epochs: 50
})

const diagnosisForm = reactive({
  vehicleId: '',
  symptoms: [] as string[],
  description: ''
})

// 计算属性
const modelHealth = computed(() => {
  const total = models.value.length
  const active = models.value.filter(m => m.status === 'active').length
  const inactive = models.value.filter(m => m.status === 'inactive').length
  const training = models.value.filter(m => m.status === 'training').length
  const error = models.value.filter(m => m.status === 'error').length
  const avgAccuracy = total > 0 ? models.value.reduce((sum, m) => sum + m.accuracy, 0) / total : 0
  
  return { total, active, inactive, training, error, avgAccuracy }
})

const systemStats = computed(() => {
  return {
    predictions: predictions.value.length,
    analyses: analyses.value.length,
    diagnoses: diagnoses.value.length,
    recommendations: recommendations.value.length
  }
})

const predictionModels = computed(() => {
  return models.value.filter(m => m.type === 'prediction')
})

// 生命周期
onMounted(() => {
  loadData()
})

// 方法
const loadData = async () => {
  try {
    loading.value = true
    models.value = aiModelService.getModels()
    predictions.value = aiModelService.getPredictionHistory()
    analyses.value = aiModelService.getAnalysisHistory()
    diagnoses.value = aiModelService.getDiagnosisHistory()
    recommendations.value = aiModelService.getRecommendations()
  } catch (error) {
    console.error('加载AI模型数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const refreshModels = () => {
  loadData()
  ElMessage.success('数据已刷新')
}

const selectModel = (model: AIModel) => {
  selectedModel.value = model
}

const testModel = async (model: AIModel) => {
  try {
    if (model.type === 'prediction') {
      const testInput = {
        temperature: 25,
        voltage: 3.7,
        current: 15,
        cycles: 200,
        age: 18
      }
      const result = await aiModelService.predict(model.id, testInput)
      predictions.value.unshift(result)
      ElMessage.success('模型测试完成')
    } else if (model.type === 'analysis') {
      const testData = {
        speed: 60,
        acceleration: 2.5,
        braking: 1.2
      }
      const result = await aiModelService.analyze(model.id, testData)
      analyses.value.unshift(result)
      ElMessage.success('分析测试完成')
    }
  } catch (error) {
    console.error('模型测试失败:', error)
    ElMessage.error('模型测试失败')
  }
}

const viewModelStats = (model: AIModel) => {
  const stats = aiModelService.getModelStats(model.id)
  ElMessage.info(`模型 ${model.name} 使用次数: ${stats.usageCount}, 平均置信度: ${(stats.avgConfidence * 100).toFixed(1)}%`)
}

const runPrediction = async () => {
  if (!selectedPredictionModel.value) {
    ElMessage.warning('请选择预测模型')
    return
  }
  
  try {
    const testInput = {
      temperature: 20 + Math.random() * 30,
      voltage: 3.5 + Math.random() * 0.7,
      current: 10 + Math.random() * 25,
      cycles: 50 + Math.random() * 400,
      age: 6 + Math.random() * 30
    }
    
    const result = await aiModelService.predict(selectedPredictionModel.value, testInput)
    predictions.value.unshift(result)
    ElMessage.success('预测完成')
  } catch (error) {
    console.error('预测失败:', error)
    ElMessage.error('预测失败')
  }
}

const runAnalysis = async () => {
  try {
    const testData = {
      speed: 40 + Math.random() * 80,
      acceleration: Math.random() * 5,
      braking: Math.random() * 3,
      steering: Math.random() * 2
    }
    
    const result = await aiModelService.analyze('driving-behavior-v2', testData)
    analyses.value.unshift(result)
    ElMessage.success('分析完成')
  } catch (error) {
    console.error('分析失败:', error)
    ElMessage.error('分析失败')
  }
}

const startTraining = async () => {
  if (!trainForm.modelId) {
    ElMessage.warning('请选择要训练的模型')
    return
  }
  
  try {
    training.value = true
    const trainingData = Array.from({ length: trainForm.dataSize }, () => ({}))
    await aiModelService.trainModel(trainForm.modelId, trainingData)
    
    // 更新模型列表
    models.value = aiModelService.getModels()
    showTrainDialog.value = false
    ElMessage.success('模型训练完成')
  } catch (error) {
    console.error('模型训练失败:', error)
    ElMessage.error('模型训练失败')
  } finally {
    training.value = false
  }
}

const startDiagnosis = async () => {
  if (!diagnosisForm.vehicleId || diagnosisForm.symptoms.length === 0) {
    ElMessage.warning('请填写车辆ID和故障症状')
    return
  }
  
  try {
    diagnosing.value = true
    const result = await aiModelService.diagnose(diagnosisForm.vehicleId, diagnosisForm.symptoms)
    diagnoses.value.unshift(result)
    showDiagnosisDialog.value = false
    
    // 重置表单
    diagnosisForm.vehicleId = ''
    diagnosisForm.symptoms = []
    diagnosisForm.description = ''
    
    ElMessage.success('故障诊断完成')
  } catch (error) {
    console.error('故障诊断失败:', error)
    ElMessage.error('故障诊断失败')
  } finally {
    diagnosing.value = false
  }
}

const generateRecommendations = async () => {
  try {
    const userId = `user_${Math.floor(Math.random() * 1000)}`
    const newRecs = await aiModelService.generateRecommendations(userId)
    recommendations.value = [...newRecs, ...recommendations.value].slice(0, 20)
    ElMessage.success('推荐生成完成')
  } catch (error) {
    console.error('生成推荐失败:', error)
    ElMessage.error('生成推荐失败')
  }
}

// 辅助方法
const formatTime = (date: Date) => {
  return new Intl.DateTimeFormat('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    active: '运行中',
    inactive: '已停用',
    training: '训练中',
    error: '错误'
  }
  return statusMap[status] || status
}

const getTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    prediction: '预测模型',
    analysis: '分析模型',
    diagnosis: '诊断模型',
    recommendation: '推荐模型'
  }
  return typeMap[type] || type
}

const getTypeIcon = (type: string) => {
  const iconMap: Record<string, any> = {
    prediction: TrendingUp,
    analysis: BarChart3,
    diagnosis: Stethoscope,
    recommendation: Lightbulb
  }
  return iconMap[type] || Database
}

const getAnalysisTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    behavior: '行为分析',
    pattern: '模式识别',
    anomaly: '异常检测',
    trend: '趋势分析'
  }
  return typeMap[type] || type
}

const getAnalysisIcon = (type: string) => {
  const iconMap: Record<string, any> = {
    behavior: Activity,
    pattern: Target,
    anomaly: AlertTriangle,
    trend: TrendingUp
  }
  return iconMap[type] || BarChart3
}

const getSeverityText = (severity: string) => {
  const severityMap: Record<string, string> = {
    low: '低',
    medium: '中',
    high: '高',
    critical: '严重'
  }
  return severityMap[severity] || severity
}

const getRecommendationTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    charging: '充电建议',
    route: '路线推荐',
    maintenance: '维护提醒',
    driving: '驾驶建议',
    energy: '节能建议'
  }
  return typeMap[type] || type
}

const getRecommendationIcon = (type: string) => {
  const iconMap: Record<string, any> = {
    charging: Lightning,
    route: Route,
    maintenance: Wrench,
    driving: Gauge,
    energy: TrendingUp
  }
  return iconMap[type] || Lightbulb
}

const getConfidenceClass = (confidence: number) => {
  if (confidence >= 0.9) return 'high'
  if (confidence >= 0.7) return 'medium'
  return 'low'
}

const getPriorityClass = (priority: number) => {
  if (priority >= 8) return 'high'
  if (priority >= 5) return 'medium'
  return 'low'
}
</script>

<style scoped>
.ai-model-monitor {
  padding: 20px;
  background: #f8fafc;
  min-height: 100vh;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.title-icon {
  width: 20px;
  height: 20px;
  color: #3b82f6;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.btn-icon {
  width: 14px;
  height: 14px;
  margin-right: 4px;
}

/* 状态概览 */
.status-overview {
  margin-bottom: 30px;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.status-card {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.status-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.status-icon {
  width: 16px;
  height: 16px;
}

.status-icon.active {
  color: #10b981;
}

.status-icon.warning {
  color: #f59e0b;
}

.status-label {
  font-size: 14px;
  color: #6b7280;
}

.status-value {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4px;
}

.status-desc {
  font-size: 12px;
  color: #9ca3af;
}

/* 模型列表 */
.models-section {
  margin-bottom: 30px;
}

.models-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.model-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
}

.model-card:hover {
  border-color: #3b82f6;
  transform: translateY(-2px);
}

.model-card.active {
  border-color: #3b82f6;
  background: #f0f9ff;
}

.model-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.model-info {
  flex: 1;
}

.model-name {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.model-version {
  font-size: 12px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
}

.model-status {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 6px;
}

.model-status.active {
  background: #d1fae5;
  color: #065f46;
}

.model-status.inactive {
  background: #f3f4f6;
  color: #6b7280;
}

.model-status.training {
  background: #fef3c7;
  color: #92400e;
}

.model-status.error {
  background: #fee2e2;
  color: #991b1b;
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
}

.model-body {
  margin-bottom: 15px;
}

.model-type,
.model-accuracy,
.model-trained {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 8px;
  font-size: 14px;
  color: #6b7280;
}

.type-icon,
.time-icon {
  width: 14px;
  height: 14px;
}

.accuracy-value {
  font-weight: 600;
  color: #1f2937;
}

.model-actions {
  display: flex;
  gap: 8px;
}

/* 预测结果 */
.predictions-section {
  margin-bottom: 30px;
}

.predictions-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.prediction-item {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.prediction-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e5e7eb;
}

.prediction-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.prediction-id {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.prediction-time {
  font-size: 12px;
  color: #6b7280;
}

.prediction-confidence,
.analysis-confidence {
  display: flex;
  align-items: center;
  gap: 6px;
}

.confidence-label {
  font-size: 12px;
  color: #6b7280;
}

.confidence-value {
  font-size: 14px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
}

.confidence-value.high {
  background: #d1fae5;
  color: #065f46;
}

.confidence-value.medium {
  background: #fef3c7;
  color: #92400e;
}

.confidence-value.low {
  background: #fee2e2;
  color: #991b1b;
}

.prediction-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.prediction-input h5,
.prediction-output h5,
.analysis-insights h5,
.analysis-recommendations h5,
.diagnosis-issue h5,
.diagnosis-solutions h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.param-list,
.result-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.param-item,
.result-item {
  background: #f3f4f6;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  color: #374151;
}

/* 分析报告 */
.analysis-section {
  margin-bottom: 30px;
}

.analysis-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.analysis-item {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.analysis-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.analysis-type {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.analysis-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.insights-list,
.recommendations-list,
.solutions-list {
  margin: 0;
  padding-left: 16px;
}

.insights-list li,
.recommendations-list li,
.solutions-list li {
  margin-bottom: 4px;
  font-size: 14px;
  color: #374151;
}

/* 故障诊断 */
.diagnosis-section {
  margin-bottom: 30px;
}

.diagnosis-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.diagnosis-item {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.diagnosis-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.diagnosis-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.vehicle-id {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.component {
  font-size: 12px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
  width: fit-content;
}

.diagnosis-severity {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 6px;
}

.diagnosis-severity.low {
  background: #d1fae5;
  color: #065f46;
}

.diagnosis-severity.medium {
  background: #fef3c7;
  color: #92400e;
}

.diagnosis-severity.high {
  background: #fed7aa;
  color: #9a3412;
}

.diagnosis-severity.critical {
  background: #fee2e2;
  color: #991b1b;
}

.severity-icon {
  width: 12px;
  height: 12px;
}

.diagnosis-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.diagnosis-issue {
  grid-column: 1 / -1;
  margin-bottom: 15px;
}

.diagnosis-issue p {
  margin: 0;
  font-size: 14px;
  color: #374151;
}

.diagnosis-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.diagnosis-probability,
.diagnosis-cost,
.diagnosis-urgency {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.diagnosis-details .label {
  color: #6b7280;
}

.diagnosis-details .value {
  font-weight: 600;
  color: #1f2937;
}

/* 个性化推荐 */
.recommendations-section {
  margin-bottom: 30px;
}

.recommendations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.recommendation-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #3b82f6;
}

.rec-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.rec-type {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #6b7280;
}

.rec-priority {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
}

.rec-priority.high {
  background: #fee2e2;
  color: #991b1b;
}

.rec-priority.medium {
  background: #fef3c7;
  color: #92400e;
}

.rec-priority.low {
  background: #d1fae5;
  color: #065f46;
}

.rec-content {
  margin-bottom: 15px;
}

.rec-title {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.rec-description {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #6b7280;
  line-height: 1.5;
}

.rec-benefit {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #059669;
  background: #ecfdf5;
  padding: 4px 8px;
  border-radius: 6px;
  width: fit-content;
}

.benefit-icon {
  width: 12px;
  height: 12px;
}

.rec-actions {
  display: flex;
  gap: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ai-model-monitor {
    padding: 15px;
  }
  
  .status-grid {
    grid-template-columns: 1fr;
  }
  
  .models-grid {
    grid-template-columns: 1fr;
  }
  
  .prediction-content,
  .analysis-content,
  .diagnosis-content {
    grid-template-columns: 1fr;
  }
  
  .recommendations-grid {
    grid-template-columns: 1fr;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .section-actions {
    width: 100%;
    justify-content: flex-start;
  }
}
</style>