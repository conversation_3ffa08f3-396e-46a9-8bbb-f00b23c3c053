<template>
  <div style="padding: 20px; background: white; min-height: 100vh;">
    <h1 style="color: red; font-size: 32px;">测试页面 - 如果你能看到这个，说明Vue组件正常工作</h1>
    <p style="color: blue; font-size: 18px;">当前时间: {{ currentTime }}</p>
    <div style="background: #f0f0f0; padding: 20px; margin: 20px 0;">
      <h2>API测试</h2>
      <button @click="testAPI" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px;">测试API</button>
      <div v-if="apiResult" style="margin-top: 10px; padding: 10px; background: #e8f5e8; border-radius: 4px;">
        <pre>{{ JSON.stringify(apiResult, null, 2) }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

const currentTime = ref('')
const apiResult = ref(null)

const updateTime = () => {
  currentTime.value = new Date().toLocaleString('zh-CN')
}

const testAPI = async () => {
  try {
    const response = await fetch('/api/edge-devices/data?deviceIds=EV001,EV002&limit=5')
    const result = await response.json()
    apiResult.value = result
  } catch (error) {
    apiResult.value = { error: error.message }
  }
}

onMounted(() => {
  updateTime()
  setInterval(updateTime, 1000)
})
</script>
